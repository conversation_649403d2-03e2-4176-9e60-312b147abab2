{
  "Logging": {
    "LogLevel": {
      "Default": "Information",
      "Microsoft.Hosting.Lifetime": "Information"
    }
  },
  "WorkerConfig": {
    "DelaySeconds": 5,

    "pathReflagApp": "D:\\WORK\\APPLIS\\SERVICES_RATTRAPE_PANIERS\\reflag\\bin\\debug\\servicereflag.exe",
    "pathDoCommandeApp": "D:\\WORK\\APPLIS\\SERVICES_RATTRAPE_PANIERS\\serviceCreateCmd\\bin\\Debug\\serviceCreateCmd.exe",
    "pathDoPaiementApp": "D:\\WORK\\APPLIS\\SERVICES_RATTRAPE_PANIERS\\serviceDoPaiement\\bin\\Debug\\serviceDoPaiement.exe",
    "pathEditionApp": "D:\\WORK\\APPLIS\\SERVICES_RATTRAPE_PANIERS\\serviceDoPaiement\\bin\\Debug\\serviceDoPaiement.exe",
    "pathEmailtApp": "D:\\WORK\\APPLIS\\SERVICES_RATTRAPE_PANIERS\\serviceDoPaiement\\bin\\Debug\\serviceDoPaiement.exe"
  },
  "ConnectionStrings": {
    "WsAdminDB": "Server=*************;Database=WSAdmin_test;Persist Security Info=True;User ID=SupportTest;Password=****************************************************************************************;MultipleActiveResultSets=true",
    //"GlobalOpinionDB": "Server=*************;Database=WSAdmin_test;Persist Security Info=True;User ID=SupportTest;Password=****************************************************************************************;MultipleActiveResultSets=true",
    "WebLibraryDB": "Server=*************;Database=GlobalWebLibrary_DEV;Persist Security Info=True;User ID=SphereWebTest;Password=************************************************************************************************************;MultipleActiveResultSets=true",
    "Redis": "**************:6555,password=tS4pDJfVCN3Y0j5pvrdEBCpH0gXLZsZ/QCdN2zh6lJwoi+Va4eBUwtkZEUNYCcCl"
  },
  "TypeRun": "TEST",





}
