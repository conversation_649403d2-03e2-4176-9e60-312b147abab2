﻿
/* 
declare @plangCode varchar(10) = 'fr'
declare @pbat bit = 1
declare @pcategBatId int = 110001
declare @ppriceBatId int = 9940013 
*/
 
DECLARE @langId INT
SELECT @langId = langue_id FROM langue WHERE langue_code = @plangCode and supprimer='N'

IF (@langId is null)
	SET @langId = (SELECT TOP 1 langue_id from langue WHERE supprimer='N' ORDER BY langue_id ASC)


DECLARE @utilisateurNom varchar(max)

SELECT @utilisateurNom = i.Identite_nom + ' ' + i.identite_prenom
FROM Entree_Complement ec 
INNER JOIN identite i on ec.consommateur_id = i.identite_id   /* nom de l'identité liée par Entree_Complement (col consommateur_id) ?!? */	
INNER JOIN seance s on s.seance_id = ec.Seance_id 
INNER JOIN entree_[MANIFID] e on e.entree_id = ec.entree_id and e.dossier_id = ec.dossier_id
where s.manifestation_id =[MANIFID] and e.entree_id = [ENTREEID] AND (ec.consommateur_id_IS_consumer = 0 OR ec.consommateur_id_IS_consumer is null)

if (@utilisateurNom is null)
begin 
	/* nom de consumers liée par Entree_Complement ?!? */	
	select @utilisateurNom = con.name + ' ' +  con.surname 
	from Entree_Complement ec 
	inner join entree_[MANIFID] e on e.entree_id = ec.entree_id and e.dossier_id = ec.dossier_id
	inner join consumers con on con.consumer_id = ec.Consommateur_ID
	inner join seance s on s.seance_id = ec.Seance_id 
	where s.manifestation_id = [MANIFID] and e.entree_id = [ENTREEID] AND ec.consommateur_id_IS_consumer = 1
end


if (@utilisateurNom is null)
begin
	/* nom de consumers liée par dossier_consommateur (via colonne consumer_id) */	
	select @utilisateurNom = con.name + ' ' +  con.surname 
	FROM dossier_consommateur dc 	
	inner join consumers con on con.consumer_id = dc.consumer_id
	inner join entree_[MANIFID] e on e.dossier_id = dc.dossier_id
	where dc.manifestation_id =  [MANIFID] and e.entree_id = [ENTREEID]
end
if (@utilisateurNom is null)
begin
	/* nom de identite liée par dossier_consommateur (via colonne ConsoID) */
	select @utilisateurNom = i.identite_nom + ' ' +  i.identite_prenom
	FROM dossier_consommateur dc 	
	inner join identite i on i.identite_id = dc.ConsoID
	inner join entree_[MANIFID] e on e.dossier_id = dc.dossier_id
	where dc.manifestation_id =  [MANIFID] and e.entree_id = [ENTREEID] and dc.ConsoID > 0
end

if (@utilisateurNom is null)
begin
	/* identite dossier */
	select @utilisateurNom = i.identite_nom + ' ' +  i.identite_prenom
	FROM dossier_[MANIFID] dc 	
	inner join identite i on i.identite_id = dc.identite_id
	inner join entree_[MANIFID] e on e.dossier_id = dc.dossier_id
	where e.entree_id =  [ENTREEID]
end



declare @billetNum int
declare @codeBarre varchar(max)
SELECT  @billetNum = numbillet, @codeBarre = case when motif <> '' then motif else externe end from recette r  WHERE manifestation_id = [MANIFID] AND entree_id =  [ENTREEID]
	AND type_operation in ('D','E')
	AND r.recette_id = (SELECT MAX(recette_id) FROM recette rhisto WHERE rhisto.entree_id = r.entree_id AND rhisto.seance_id = r.seance_id) -- check que c''est bien la derniere recette

if (@pbat = 0 and (@billetNum is null OR @codeBarre is null))
		THROW 50001, 'Ticket not edited e=[ENTREEID], m=[MANIFID] !',1

IF (@pbat = 1)
BEGIN
	SET @billetNum = 123456
	SET @codeBarre ='brPProof901234'
	IF (@utilisateurNom is null)
	BEGIN	
		SET @utilisateurNom = 'bat.utilisateurName'
	END
END


DECLARE @ver NVARCHAR(128) 
 DECLARE @majorVersion int 
 SET @ver = CAST(SERVERPROPERTY('productversion') AS NVARCHAR) 
 SET @ver = SUBSTRING(@ver,1,CHARINDEX('.',@ver)-1) 
 SET @majorVersion  = CAST(@ver AS INT) 
 print @majorVersion
IF @majorVersion >=10 
BEGIN 
 DECLARE @sql varchar(max); 
 
   declare @AdhesionsTablesExists int
  
  select @AdhesionsTablesExists = OBJECT_ID(N'Adhesion_Dossier_Entree', N'U')
  IF (@AdhesionsTablesExists> 0)
	 BEGIN
		 declare  @sql1 varchar(max) ='DECLARE @const_placementlibre int; set @const_placementlibre =32;
		 SELECT '

		 if (@pbat=1)
			SET @sql1 = @sql1 + ' TOP 1 '


		 SET @sql1 = @sql1 + '  ''' + replace(@utilisateurNom,'''','''''') + ''' as utilisateur_nom, 

		''' +  replace(ltrim(rtrim(@codeBarre)),'''','''''')  + ''' as CRYPT_BILLET_NUMERO,
		''' +  replace(ltrim(rtrim(@codeBarre)),'''','''''')  + ''' as WEB_TICKET_NUMBER,
		''' +	replace(@billetNum,'''','''''') + ''' as BILLET_NUMERO ,

		d.dossier_c as dossier_commentaire,
		
		case WHEN link_identite_adherent.adherent_id is null then ''0'' else link_identite_adherent.adherent_id end as ADHERENT_NUM ,

		case WHEN adh_entree.adhesion_adherent_catalog_id is null then ''0'' else adh_entree.adhesion_adherent_catalog_id end as ADHESION_NUM ,
		case WHEN adhcat.catalog_libelle is null then ''0'' else adhcat.catalog_libelle end as ADHESION_NOM ,


		 e.dossier_id as dossier_numero,rang as place_rang, siege as place_numero,
		CONVERT(varchar(20),CAST(montant1 as decimal(18,2))) as place_montant_eu,CONVERT(varchar(20),CAST(montant2 as decimal(18,2))) as place_montant_dg_eu,CONVERT(varchar(20),CAST(montant3 as decimal(18,2))) as place_montant_3_eu, CONVERT(varchar(20),CAST(montant4 as decimal(18,2))) as place_montant_4_eu,CONVERT(varchar(20),CAST(montant5 as decimal(18,2))) as place_montant_5_eu,CONVERT(varchar(20),CAST(montant6 as decimal(18,2))) as place_montant_6_eu,CONVERT(varchar(20),CAST(montant7 as decimal(18,2))) as place_montant_7_eu,CONVERT(varchar(20),CAST(montant8 as decimal(18,2))) as place_montant_8_eu,CONVERT(varchar(20),CAST(montant9 as decimal(18,2))) as place_montant_9_eu,CONVERT(varchar(20),CAST(montant10 as decimal(18,2))) as place_montant_10_eu,
		CONVERT(varchar(20),CAST((Montant1 + Montant2) as decimal(18,2))) as place_montant_global_eu,place_montant_remise_eu=CONVERT(varchar(20),CAST(( case when modecol4=''REMISE'' then  montant4 else 0 END + case  when modecol5=''REMISE'' then montant5 else 0 END + case  when modecol6=''REMISE'' then montant6 else 0 END + case  when modecol7=''REMISE''  then montant7 else 0 END + case  when modecol8=''REMISE'' then montant8 else 0 END + case  when modecol9=''REMISE'' then montant9 else 0 END + case  when modecol10=''REMISE'' then montant10 else 0 END
		) as decimal(18,2))),place_montant_commission_eu=CONVERT(varchar(20),CAST(( case  when modecol4=''COMMISSION'' then  montant4 else 0 END + case  when modecol5=''COMMISSION'' then montant5 else 0 END + case  when modecol6=''COMMISSION'' then montant6 else 0 END + case  when modecol7=''COMMISSION'' then montant7 else 0 END + case  when modecol8=''COMMISSION'' then montant8 else 0 END + case  when modecol9=''COMMISSION'' then montant9 else 0 END + case  when modecol10=''COMMISSION'' then montant10 else 0 END ) as decimal(18,2) )),
		 place_montant_avec_remise_commission_eu=CONVERT(varchar(20), CAST(( montant1+montant2+ case  when modecol4=''REMISE''  then - montant4 when modecol4=''TAXE'' or modecol4=''COMMISSION'' then montant4 else 0 END + case  when modecol5=''REMISE'' then - montant5 when modecol5=''TAXE'' or modecol5=''COMMISSION'' then montant5 else 0 END + case  when modecol6=''REMISE'' then - montant6 when modecol6=''TAXE'' or modecol6=''COMMISSION'' then montant6 else 0 END + case  when modecol7=''REMISE'' then - montant7 when modecol7=''TAXE'' or modecol7=''COMMISSION'' then montant7 else 0 END + case  when modecol8=''REMISE'' then - montant8 when modecol8=''TAXE'' or modecol8=''COMMISSION'' then montant8 else 0 END + case  when modecol9=''REMISE'' then - montant9 when modecol9=''TAXE''or modecol9=''COMMISSION'' then montant9 else 0 END + case  when modecol10=''REMISE'' then - montant10 when modecol10=''TAXE'' or modecol10=''COMMISSION'' then montant10 else 0 END) as decimal(18,2) )) , place_montant_decoupage_avec_remise_commission_eu=CONVERT(varchar(20) , CONVERT(decimal(12,2),montant1)) + '' +  ''+CONVERT(varchar(20) , CONVERT(decimal(12,2),montant2))+ '' '' + case when modecol4=''REMISE'' and  montant4>0 then '' - '' + CONVERT(varchar(20) ,CAST( montant4 as decimal(18,2) )) when (modecol4=''TAXE'' or modecol4=''COMMISSION'') and  montant4>0 then '' + '' + CONVERT(varchar(20) ,CAST(montant4 as decimal(18,2) )) else '''' END +  case when modecol5=''REMISE'' and  montant5>0 then '' - '' + CONVERT(varchar(20) ,CAST( montant5 as decimal(18,2))) when (modecol5=''TAXE'' or modecol5=''COMMISSION'') and montant5>0 then '' + '' + CONVERT(varchar(20) ,CAST(montant5 as decimal(18,2))) else '''' END+  case when modecol6=''REMISE'' and  montant6>0 then '' - '' + CONVERT(varchar(20) ,CAST( montant6 as decimal(18,2) )) when (modecol6=''TAXE'' or modecol6=''COMMISSION'') and  montant6>0 then '' + '' + CONVERT(varchar(20) ,CAST(montant6 as decimal(18,2))) else '''' END+  case when modecol7=''REMISE'' and  montant7>0 then '' - '' + CONVERT(varchar(20) ,CAST( montant7 as decimal(18,2) )) when (modecol7=''TAXE'' or modecol7=''COMMISSION'') and montant7>0 then '' + '' + CONVERT(varchar(20) ,CAST(montant7 as decimal(18,2))) else '''' END+  case  when modecol8=''REMISE'' and  montant8>0 then '' - '' + CONVERT(varchar(20) ,CAST( montant8 as decimal(18,2) )) when (modecol8=''TAXE'' or modecol8=''COMMISSION'') and montant8>0 then '' + '' + CONVERT(varchar(20) ,CAST(montant8 as decimal(18,2))) else '''' END+  case when modecol9=''REMISE'' and  montant9>0 then '' - '' + CONVERT(varchar(20) ,CAST( montant9 as decimal(18,2) )) when (modecol9=''TAXE'' or modecol9=''COMMISSION'') and montant9>0 then '' + '' + CONVERT(varchar(20) ,CAST(montant9 as decimal(18,2))) else '''' END+  case when modecol10=''REMISE'' and  montant10>0 then '' - '' + CONVERT(varchar(20) ,CAST( montant10 as decimal(18,2) )) when (modecol10=''TAXE'' or modecol10=''COMMISSION'') and montant10>0 then '' + '' + CONVERT(varchar(20) ,CAST(montant10 as decimal(18,2))) else '''' END, 
		 
		case when tt.type_tarif_code is null then t.type_tarif_code else tt.type_tarif_code end as place_code_tarif, 
		case when tt.type_tarif_nom is null then t.type_tarif_nom else tt.type_tarif_nom end as place_lib_tarif, 
		 
		e.type_tarif_id as place_id_tarif,

		ISNULL(etage_code,'' '') as place_code_etage, ISNULL(etage_nom,'' '') as place_lib_etage,rlp.etage_id as place_id_etage, 
		 
		case when tc.categ_code is null then c.categ_code else tc.categ_code end as place_code_categ, 
		case when tc.categ_nom is null then c.categ_nom else tc.categ_nom end as PLACE_LIB_CATEG ,
		e.categorie_id as PLACE_ID_CATEG,

		ISNULL(denom_code,'' '') as place_code_denom, ISNULL(denom_nom,'' '') as place_lib_denom,rlp.denomination_id as place_id_denom,
		ISNULL(conting_code,'' '') as place_code_contin, ISNULL(conting_nom,'' '') as place_lib_contin,e.contingent_id as place_id_contin, ISNULL(reserve_code,'' '') as place_code_reserve, ISNULL(reserve_nom,'' '') as place_lib_reserve,e.reserve_id as place_id_reserve,
		section_code as place_code_section, section_nom as PLACE_LIB_SECTION,rlp.section_id as PLACE_ID_SECTION, zone_code as place_code_zone, zone_nom as place_lib_zone,rlp.zone_id as place_id_zone,
		ISNULL(ptribune.code,'' '') as place_code_tribune, ISNULL(PTRIBUNE.NOM,'''') as place_lib_tribune,ISNULL(ptribune.id, 0) as place_id_tribune, ISNULL(pacces.code,'' '') as place_code_acces, ISNULL(PACCES.NOM,'''') as place_lib_acces,ISNULL(pacces.id, 0) as place_id_acces,
		ISNULL(pporte.code,'' '') as place_code_porte, ISNULL(PPORTE.NOM,'''') as place_lib_porte,ISNULL(pporte.id, 0) as place_id_porte, ISNULL(filiere_code,'' '') as code_filiere, ISNULL(filiere_nom,'' '') as lib_filiere,d.filiere_id as id_filiere, 
		ISNULL(rangFnac,'' '') as PLACE_RANG_FNAC,  ISNULL(siegeFnac,'' '') as PLACE_NUMERO_FNAC ,
		ISNULL(form.form_abon_nom,'' '') as FORMULE_ABO_NOM
		, notnumbered = ISNULL((select top 1 CASE WHEN gp.prise_place is null Then 0
							else CASE WHEN (gp.prise_place & @const_placementlibre)=0 THEN 0 ELSE 1 END 
							END
							from gestion_place gp WHERE gp.seance_id = e.seance_id AND gp.type_tarif_id = e.type_tarif_id AND gp.categ_id = e.categorie_id
							),0)'
		
		declare @sql2 varchar(max) = '
		 FROM entree_[MANIFID] e 
		INNER JOIN structure on structure_id=[STRUCTUREID]
		INNER JOIN reference_lieu_physique rlp on e.reference_unique_physique_id=rlp.ref_uniq_phy_id
		INNER JOIN section s WITH (FORCESEEK) on s.section_id=rlp.section_id
		INNER JOIN zone z WITH (FORCESEEK) on z.zone_id=rlp.zone_id
		LEFT OUTER JOIN denomination dem on dem.denom_id=rlp.denomination_id
		LEFT OUTER JOIN etage et WITH (FORCESEEK) on et.etage_id=rlp.etage_id
		INNER JOIN categorie c WITH (FORCESEEK) on c.categ_id = e.categorie_id
		LEFT JOIN traduction_categorie tc on tc.categ_id = c.categ_id and tc.langue_id =' + convert(varchar(5), @langId) + '
		'
		if @pBat = 0
			set @sql2 = @sql2 + ' INNER JOIN type_tarif t WITH (FORCESEEK) on t.type_tarif_id = e.type_tarif_id'
		else
			set @sql2 = @sql2 + ' LEFT JOIN type_tarif t on t.type_tarif_id = t.type_tarif_id' /* jointure cassée exprès pour remaner les tarifs même s'il n'y a aucune place vendue avec ce tarif */


		set @sql2 = @sql2 + ' LEFT JOIN traduction_type_tarif tt on tt.type_tarif_id = t.type_tarif_id and tt.langue_id = ' + convert(varchar(5), @langId) + '

		LEFT OUTER JOIN Adhesion_Dossier_Entree adh_entree on adh_entree.entree_id = e.entree_id and adh_entree.dossier_id = e.dossier_id and adh_entree.seance_id = e.seance_id
		LEFT OUTER JOIN Adhesion_Adherent_Catalog link_adhesion_cat on link_adhesion_cat.adhesion_adherent_catalog_id = adh_entree.adhesion_adherent_catalog_id
		LEFT OUTER JOIN Adhesion_Catalog adhcat on adhcat.adhesion_catalog_id = link_adhesion_cat.adhesion_catalog_id

		LEFT OUTER JOIN reserve r on r.reserve_id = e.reserve_id
		LEFT OUTER JOIN contingent co on co.conting_id = e.contingent_id
		LEFT OUTER JOIN propriete_physique pacces ON PACCES.ID = rlp.acces AND pacces.codetable = ''ACCES''
		LEFT OUTER JOIN propriete_physique pporte ON PPORTE.ID = rlp.porte AND pporte.codetable = ''PORTE''
		LEFT OUTER JOIN propriete_physique ptribune ON PTRIBUNE.ID = rlp.tribune AND ptribune.codetable = ''TRIBUNE'' '

		if @pBat = 0
			set @sql2 = @sql2 + ' INNER JOIN dossier_[MANIFID] d on d.dossier_id = e.dossier_id 
			INNER JOIN commande_ligne cl on cl.dossier_id = d.dossier_id and e.seance_id = cl.seance_id and cl.type_ligne=''DOS'' '
		else
			set @sql2 = @sql2 + ' LEFT JOIN dossier_[MANIFID] d on d.dossier_id = e.dossier_id
			LEFT JOIN commande_ligne cl on cl.dossier_id = d.dossier_id and e.seance_id = cl.seance_id and cl.type_ligne=''DOS'' '
			

		set @sql2 = @sql2 + ' LEFT OUTER join formule_abonnement form on cl.formule_id = form.form_abon_id
		LEFT OUTER JOIN filiere f  on f.filiere_id = d.filiere_id
		LEFT OUTER JOIN identite identiteDoss on identiteDoss.identite_id = d.identite_id 

		LEFT OUTER JOIN adhesion_adherent link_identite_adherent ON identiteDoss.identite_id = link_identite_adherent.identite_id

		LEFT OUTER JOIN dossier_consommateur dc on dc.dossier_id = d.dossier_id and dc.commande_id = d.commande_id AND dc.manifestation_id = [MANIFID]
		LEFT OUTER JOIN consumers con2 on dc.consumer_id = con2.consumer_id
		LEFT OUTER JOIN Entree_Complement ec3 on ec3.dossier_id=e.dossier_id and ec3.Entree_id=e.entree_id and ec3.seance_id = e.seance_id
		LEFT OUTER JOIN identite id3 on id3.identite_id=ec3.Consommateur_ID '

		if @pBat = 0
			set @sql2 = @sql2 + ' WHERE e.entree_id = [ENTREEID]'; 
		else
			set @sql2 = @sql2 + ' WHERE c.categ_id = ' + convert(varchar, @pcategBatId) + ' and t.type_tarif_id =' + convert(varchar, @ppriceBatId); 

		print @sql1
		print @sql2

		set @sql = @sql1 + @sql2
END
ELSE
BEGIN


	 SET @sql='SELECT 
	 CASE when id3.Identite_nom + id3.identite_prenom <>'''' 
	THEN
		id3.Identite_nom + '' '' + id3.identite_prenom /* nom de l''identite liée par Entree_Complement */
	else
		case when dc.ConsoID is not null then con2.name else
			identiteDoss.Identite_nom + '' ''+ identiteDoss.identite_prenom 
		end	  		
	end	as utilisateur_nom, 
	'' '' as ADHERENT_NUM ,
	''0'' as ADHESION_NUM,
	''0'' ADHESION_NOM,

	 e.dossier_id as dossier_numero,rang as place_rang, siege as place_numero,
	CONVERT(varchar(20),CAST(montant1 as decimal(18,2))) as place_montant_eu,CONVERT(varchar(20),CAST(montant2 as decimal(18,2))) as place_montant_dg_eu,CONVERT(varchar(20),CAST(montant3 as decimal(18,2))) as place_montant_3_eu, CONVERT(varchar(20),CAST(montant4 as decimal(18,2))) as place_montant_4_eu,CONVERT(varchar(20),CAST(montant5 as decimal(18,2))) as place_montant_5_eu,CONVERT(varchar(20),CAST(montant6 as decimal(18,2))) as place_montant_6_eu,CONVERT(varchar(20),CAST(montant7 as decimal(18,2))) as place_montant_7_eu,CONVERT(varchar(20),CAST(montant8 as decimal(18,2))) as place_montant_8_eu,CONVERT(varchar(20),CAST(montant9 as decimal(18,2))) as place_montant_9_eu,CONVERT(varchar(20),CAST(montant10 as decimal(18,2))) as place_montant_10_eu,
	CONVERT(varchar(20),CAST((Montant1 + Montant2) as decimal(18,2))) as place_montant_global_eu,place_montant_remise_eu=CONVERT(varchar(20),CAST(( case when modecol4=''REMISE'' then  montant4 else 0 END + case  when modecol5=''REMISE'' then montant5 else 0 END + case  when modecol6=''REMISE'' then montant6 else 0 END + case  when modecol7=''REMISE''  then montant7 else 0 END + case  when modecol8=''REMISE'' then montant8 else 0 END + case  when modecol9=''REMISE'' then montant9 else 0 END + case  when modecol10=''REMISE'' then montant10 else 0 END
	) as decimal(18,2))),place_montant_commission_eu=CONVERT(varchar(20),CAST(( case  when modecol4=''COMMISSION'' then  montant4 else 0 END + case  when modecol5=''COMMISSION'' then montant5 else 0 END + case  when modecol6=''COMMISSION'' then montant6 else 0 END + case  when modecol7=''COMMISSION'' then montant7 else 0 END + case  when modecol8=''COMMISSION'' then montant8 else 0 END + case  when modecol9=''COMMISSION'' then montant9 else 0 END + case  when modecol10=''COMMISSION'' then montant10 else 0 END ) as decimal(18,2) )),
	 place_montant_avec_remise_commission_eu=CONVERT(varchar(20), CAST(( montant1+montant2+ case  when modecol4=''REMISE''  then - montant4 when modecol4=''TAXE'' or modecol4=''COMMISSION'' then montant4 else 0 END + case  when modecol5=''REMISE'' then - montant5 when modecol5=''TAXE'' or modecol5=''COMMISSION'' then montant5 else 0 END + case  when modecol6=''REMISE'' then - montant6 when modecol6=''TAXE'' or modecol6=''COMMISSION'' then montant6 else 0 END + case  when modecol7=''REMISE'' then - montant7 when modecol7=''TAXE'' or modecol7=''COMMISSION'' then montant7 else 0 END + case  when modecol8=''REMISE'' then - montant8 when modecol8=''TAXE'' or modecol8=''COMMISSION'' then montant8 else 0 END + case  when modecol9=''REMISE'' then - montant9 when modecol9=''TAXE''or modecol9=''COMMISSION'' then montant9 else 0 END + case  when modecol10=''REMISE'' then - montant10 when modecol10=''TAXE'' or modecol10=''COMMISSION'' then montant10 else 0 END) as decimal(18,2) )) , place_montant_decoupage_avec_remise_commission_eu=CONVERT(varchar(20) , CONVERT(decimal(12,2),montant1)) + '' +  ''+CONVERT(varchar(20) , CONVERT(decimal(12,2),montant2))+ '' '' + case when modecol4=''REMISE'' and  montant4>0 then '' - '' + CONVERT(varchar(20) ,CAST( montant4 as decimal(18,2) )) when (modecol4=''TAXE'' or modecol4=''COMMISSION'') and  montant4>0 then '' + '' + CONVERT(varchar(20) ,CAST(montant4 as decimal(18,2) )) else '''' END +  case when modecol5=''REMISE'' and  montant5>0 then '' - '' + CONVERT(varchar(20) ,CAST( montant5 as decimal(18,2))) when (modecol5=''TAXE'' or modecol5=''COMMISSION'') and montant5>0 then '' + '' + CONVERT(varchar(20) ,CAST(montant5 as decimal(18,2))) else '''' END+  case when modecol6=''REMISE'' and  montant6>0 then '' - '' + CONVERT(varchar(20) ,CAST( montant6 as decimal(18,2) )) when (modecol6=''TAXE'' or modecol6=''COMMISSION'') and  montant6>0 then '' + '' + CONVERT(varchar(20) ,CAST(montant6 as decimal(18,2))) else '''' END+  case when modecol7=''REMISE'' and  montant7>0 then '' - '' + CONVERT(varchar(20) ,CAST( montant7 as decimal(18,2) )) when (modecol7=''TAXE'' or modecol7=''COMMISSION'') and montant7>0 then '' + '' + CONVERT(varchar(20) ,CAST(montant7 as decimal(18,2))) else '''' END+  case  when modecol8=''REMISE'' and  montant8>0 then '' - '' + CONVERT(varchar(20) ,CAST( montant8 as decimal(18,2) )) when (modecol8=''TAXE'' or modecol8=''COMMISSION'') and montant8>0 then '' + '' + CONVERT(varchar(20) ,CAST(montant8 as decimal(18,2))) else '''' END+  case when modecol9=''REMISE'' and  montant9>0 then '' - '' + CONVERT(varchar(20) ,CAST( montant9 as decimal(18,2) )) when (modecol9=''TAXE'' or modecol9=''COMMISSION'') and montant9>0 then '' + '' + CONVERT(varchar(20) ,CAST(montant9 as decimal(18,2))) else '''' END+  case when modecol10=''REMISE'' and  montant10>0 then '' - '' + CONVERT(varchar(20) ,CAST( montant10 as decimal(18,2) )) when (modecol10=''TAXE'' or modecol10=''COMMISSION'') and montant10>0 then '' + '' + CONVERT(varchar(20) ,CAST(montant10 as decimal(18,2))) else '''' END, 
	 
	case when tt.type_tarif_code is null then t.type_tarif_code else tt.type_tarif_code end as place_code_tarif, 
	case when tt.type_tarif_nom is null then t.type_tarif_nom else tt.type_tarif_nom end as place_lib_tarif, 
	 
	e.type_tarif_id as place_id_tarif,

	ISNULL(etage_code,'' '') as place_code_etage, ISNULL(etage_nom,'' '') as place_lib_etage,rlp.etage_id as place_id_etage, 
	 
	case when tc.categ_code is null then c.categ_code else tc.categ_code end as place_code_categ, 
	case when tc.categ_nom is null then c.categ_nom else tc.categ_nom end as PLACE_LIB_CATEG ,
	e.categorie_id as PLACE_ID_CATEG,

	ISNULL(denom_code,'' '') as place_code_denom, ISNULL(denom_nom,'' '') as place_lib_denom,rlp.denomination_id as place_id_denom,
	ISNULL(conting_code,'' '') as place_code_contin, ISNULL(conting_nom,'' '') as place_lib_contin,e.contingent_id as place_id_contin, ISNULL(reserve_code,'' '') as place_code_reserve, ISNULL(reserve_nom,'' '') as place_lib_reserve,e.reserve_id as place_id_reserve,
	section_code as place_code_section, section_nom as PLACE_LIB_SECTION,rlp.section_id as PLACE_ID_SECTION, zone_code as place_code_zone, zone_nom as place_lib_zone,rlp.zone_id as place_id_zone,
	ISNULL(ptribune.code,'' '') as place_code_tribune, ISNULL(PTRIBUNE.NOM,'''') as place_lib_tribune,ISNULL(ptribune.id, 0) as place_id_tribune, ISNULL(pacces.code,'' '') as place_code_acces, ISNULL(PACCES.NOM,'''') as place_lib_acces,ISNULL(pacces.id, 0) as place_id_acces,
	ISNULL(pporte.code,'' '') as place_code_porte, ISNULL(PPORTE.NOM,'''') as place_lib_porte,ISNULL(pporte.id, 0) as place_id_porte, ISNULL(filiere_code,'' '') as code_filiere, ISNULL(filiere_nom,'' '') as lib_filiere,d.filiere_id as id_filiere, 
	ISNULL(rangFnac,'' '') as PLACE_RANG_FNAC,  ISNULL(siegeFnac,'' '') as PLACE_NUMERO_FNAC ,
	ISNULL(form.form_abon_nom,'' '') as FORMULE_ABO_NOM
	 FROM entree_[MANIFID] e 
	INNER JOIN structure on structure_id=[STRUCTUREID]
	INNER JOIN reference_lieu_physique rlp on e.reference_unique_physique_id=rlp.ref_uniq_phy_id
	INNER JOIN section s WITH (FORCESEEK) on s.section_id=rlp.section_id
	INNER JOIN zone z WITH (FORCESEEK) on z.zone_id=rlp.zone_id
	LEFT OUTER JOIN denomination dem on dem.denom_id=rlp.denomination_id
	LEFT OUTER JOIN etage et WITH (FORCESEEK) on et.etage_id=rlp.etage_id
	INNER JOIN categorie c WITH (FORCESEEK) on c.categ_id = e.categorie_id
	LEFT JOIN traduction_categorie tc on tc.categ_id = c.categ_id and tc.langue_id = ' + convert(varchar(5), @langId) + '
	INNER JOIN type_tarif t WITH (FORCESEEK) on t.type_tarif_id = e.type_tarif_id
	LEFT JOIN traduction_type_tarif tt on tt.type_tarif_id = t.type_tarif_id and tt.langue_id = ' + convert(varchar(5), @langId) + '

	LEFT OUTER JOIN reserve r on r.reserve_id = e.reserve_id
	LEFT OUTER JOIN contingent co on co.conting_id = e.contingent_id
	LEFT OUTER JOIN propriete_physique pacces ON PACCES.ID = rlp.acces AND pacces.codetable = ''ACCES''
	LEFT OUTER JOIN propriete_physique pporte ON PPORTE.ID = rlp.porte AND pporte.codetable = ''PORTE''
	LEFT OUTER JOIN propriete_physique ptribune ON PTRIBUNE.ID = rlp.tribune AND ptribune.codetable = ''TRIBUNE''
	INNER JOIN dossier_[MANIFID] d on d.dossier_id = e.dossier_id
	inner join commande_ligne cl on cl.dossier_id = d.dossier_id and e.seance_id = cl.seance_id and cl.type_ligne=''DOS''
	left outer join formule_abonnement form on cl.formule_id = form.form_abon_id
	LEFT OUTER JOIN filiere f  on f.filiere_id = d.filiere_id
	LEFT OUTER JOIN identite identiteDoss on identiteDoss.identite_id = d.identite_id 

	LEFT OUTER JOIN dossier_consommateur dc on dc.dossier_id = d.dossier_id and dc.commande_id = d.commande_id AND dc.manifestation_id = [MANIFID]
	LEFT OUTER JOIN consumers con2 on dc.consumer_id = con2.consumer_id
	LEFT OUTER JOIN Entree_Complement ec3 on ec3.dossier_id=e.dossier_id and ec3.Entree_id=e.entree_id and ec3.seance_id = e.seance_id
	LEFT OUTER JOIN identite id3 on id3.identite_id=ec3.Consommateur_ID 
	WHERE e.entree_id = [ENTREEID]'; 


	END



	print @sql
	EXEC (@sql); 
 END
ELSE
BEGIN
    SELECT 
	
			 CASE when id3.Identite_nom + id3.identite_prenom <>'' 
		THEN
			id3.Identite_nom + ' ' + id3.identite_prenom /* nom de l''identite liée par Entree_Complement */
		ELSE
			case when dc.ConsoID is not null then 
				con2.name 
			else
				identiteDoss.Identite_nom + ' '+ identiteDoss.identite_prenom 
			end
		END	as utilisateur_nom, 
	
	e.dossier_id as dossier_numero,rang as place_rang, siege as place_numero,
CONVERT(varchar(20),CAST(montant1 as decimal(18,2))) as place_montant_eu,CONVERT(varchar(20),CAST(montant2 as decimal(18,2))) as place_montant_dg_eu,CONVERT(varchar(20),CAST(montant3 as decimal(18,2))) as place_montant_3_eu, CONVERT(varchar(20),CAST(montant4 as decimal(18,2))) as place_montant_4_eu,CONVERT(varchar(20),CAST(montant5 as decimal(18,2))) as place_montant_5_eu,CONVERT(varchar(20),CAST(montant6 as decimal(18,2))) as place_montant_6_eu,CONVERT(varchar(20),CAST(montant7 as decimal(18,2))) as place_montant_7_eu,CONVERT(varchar(20),CAST(montant8 as decimal(18,2))) as place_montant_8_eu,CONVERT(varchar(20),CAST(montant9 as decimal(18,2))) as place_montant_9_eu,CONVERT(varchar(20),CAST(montant10 as decimal(18,2))) as place_montant_10_eu,
CONVERT(varchar(20),CAST((Montant1 + Montant2) as decimal(18,2))) as place_montant_global_eu,place_montant_remise_eu=CONVERT(varchar(20),CAST(( case when modecol4='REMISE' then  montant4 else 0 END + case  when modecol5='REMISE' then montant5 else 0 END + case  when modecol6='REMISE' then montant6 else 0 END + case  when modecol7='REMISE'  then montant7 else 0 END + case  when modecol8='REMISE' then montant8 else 0 END + case  when modecol9='REMISE' then montant9 else 0 END + case  when modecol10='REMISE' then montant10 else 0 END) as decimal(18,2))),place_montant_commission_eu=CONVERT(varchar(20),CAST(( case  when modecol4='COMMISSION' then  montant4 else 0 END + case  when modecol5='COMMISSION' then montant5 else 0 END + case  when modecol6='COMMISSION' then montant6 else 0 END + case  when modecol7='COMMISSION' then montant7 else 0 END + case  when modecol8='COMMISSION' then montant8 else 0 END + case  when modecol9='COMMISSION' then montant9 else 0 END + case  when modecol10='COMMISSION' then montant10 else 0 END ) as decimal(18,2) )), place_montant_avec_remise_commission_eu=CONVERT(varchar(20), CAST(( montant1+montant2+ case  when modecol4='REMISE'  then - montant4 when modecol4='TAXE' or modecol4='COMMISSION' then montant4 else 0 END + case  when modecol5='REMISE' then - montant5 when modecol5='TAXE' or modecol5='COMMISSION' then montant5 else 0 END + case  when modecol6='REMISE' then - montant6 when modecol6='TAXE' or modecol6='COMMISSION' then montant6 else 0 END + case  when modecol7='REMISE' then - montant7 when modecol7='TAXE' or modecol7='COMMISSION' then montant7 else 0 END + case  when modecol8='REMISE' then - montant8 when modecol8='TAXE' or modecol8='COMMISSION' then montant8 else 0 END + case  when modecol9='REMISE' then - montant9 when modecol9='TAXE'or modecol9='COMMISSION' then montant9 else 0 END + case  when modecol10='REMISE' then - montant10 when modecol10='TAXE' or modecol10='COMMISSION' then montant10 else 0 END) as decimal(18,2) )) , place_montant_decoupage_avec_remise_commission_eu=CONVERT(varchar(20) , CONVERT(decimal(12,2),montant1)) + ' +  '+CONVERT(varchar(20) , CONVERT(decimal(12,2),montant2))+ ' ' + case when modecol4='REMISE' and  montant4>0 then ' - ' + CONVERT(varchar(20) ,CAST( montant4 as decimal(18,2) )) when (modecol4='TAXE' or modecol4='COMMISSION') and  montant4>0 then ' + ' + CONVERT(varchar(20) ,CAST(montant4 as decimal(18,2) )) else '' END +  case when modecol5='REMISE' and  montant5>0 then ' - ' + CONVERT(varchar(20) ,CAST( montant5 as decimal(18,2))) when (modecol5='TAXE' or modecol5='COMMISSION') and montant5>0 then ' + ' + CONVERT(varchar(20) ,CAST(montant5 as decimal(18,2))) else '' END+  case when modecol6='REMISE' and  montant6>0 then ' - ' + CONVERT(varchar(20) ,CAST( montant6 as decimal(18,2) )) when (modecol6='TAXE' or modecol6='COMMISSION') and  montant6>0 then ' + ' + CONVERT(varchar(20) ,CAST(montant6 as decimal(18,2))) else '' END+  case when modecol7='REMISE' and  montant7>0 then ' - ' + CONVERT(varchar(20) ,CAST( montant7 as decimal(18,2) )) when (modecol7='TAXE' or modecol7='COMMISSION') and montant7>0 then ' + ' + CONVERT(varchar(20) ,CAST(montant7 as decimal(18,2))) else '' END+  case  when modecol8='REMISE' and  montant8>0 then ' - ' + CONVERT(varchar(20) ,CAST( montant8 as decimal(18,2) )) when (modecol8='TAXE' or modecol8='COMMISSION') and montant8>0 then ' + ' + CONVERT(varchar(20) ,CAST(montant8 as decimal(18,2))) else '' END+  case when modecol9='REMISE' and  montant9>0 then ' - ' + CONVERT(varchar(20) ,CAST( montant9 as decimal(18,2) )) when (modecol9='TAXE' or modecol9='COMMISSION') and montant9>0 then ' + ' + CONVERT(varchar(20) ,CAST(montant9 as decimal(18,2))) else '' END+  case when modecol10='REMISE' and  montant10>0 then ' - ' + CONVERT(varchar(20) ,CAST( montant10 as decimal(18,2) )) when (modecol10='TAXE' or modecol10='COMMISSION') and montant10>0 then ' + ' + CONVERT(varchar(20) ,CAST(montant10 as decimal(18,2))) else '' END, 

case when tt.type_tarif_code is null then t.type_tarif_code else tt.type_tarif_code end as place_code_tarif, 
case when tt.type_tarif_nom is null then t.type_tarif_nom else tt.type_tarif_nom end as place_lib_tarif, 

e.type_tarif_id as place_id_tarif,

ISNULL(etage_code,' ') as place_code_etage, ISNULL(etage_nom,' ') as place_lib_etage,rlp.etage_id as place_id_etage, 

case when tc.categ_code is null then c.categ_code else tc.categ_code end as place_code_categ, 
case when tc.categ_nom is null then c.categ_nom else tc.categ_nom end as PLACE_LIB_CATEG ,
 
e.categorie_id as PLACE_ID_CATEG,

ISNULL(denom_code,' ') as place_code_denom, ISNULL(denom_nom,' ') as place_lib_denom,rlp.denomination_id as place_id_denom,
ISNULL(conting_code,' ') as place_code_contin, ISNULL(conting_nom,' ') as place_lib_contin,e.contingent_id as place_id_contin, ISNULL(reserve_code,' ') as place_code_reserve, ISNULL(reserve_nom,' ') as place_lib_reserve,e.reserve_id as place_id_reserve,
section_code as place_code_section, section_nom as PLACE_LIB_SECTION,rlp.section_id as PLACE_ID_SECTION, zone_code as place_code_zone, zone_nom as place_lib_zone,rlp.zone_id as place_id_zone,
ISNULL(ptribune.code,' ') as place_code_tribune, ISNULL(PTRIBUNE.NOM,'') as place_lib_tribune,ISNULL(ptribune.id, 0) as place_id_tribune, ISNULL(pacces.code,' ') as place_code_acces, ISNULL(PACCES.NOM,'') as place_lib_acces,ISNULL(pacces.id, 0) as place_id_acces,
ISNULL(pporte.code,' ') as place_code_porte, ISNULL(PPORTE.NOM,'') as place_lib_porte,ISNULL(pporte.id, 0) as place_id_porte, ISNULL(filiere_code,' ') as code_filiere, ISNULL(filiere_nom,' ') as lib_filiere,d.filiere_id as id_filiere,
ISNULL(form.form_abon_nom,' ') as FORMULE_ABO_NOM
FROM entree_[MANIFID] e 
INNER JOIN structure on structure_id=[STRUCTUREID]
INNER JOIN reference_lieu_physique rlp on e.reference_unique_physique_id=rlp.ref_uniq_phy_id
INNER JOIN section s  on s.section_id=rlp.section_id
INNER JOIN zone z  on z.zone_id=rlp.zone_id
LEFT OUTER JOIN denomination dem on dem.denom_id=rlp.denomination_id
LEFT OUTER JOIN etage et  on et.etage_id=rlp.etage_id

INNER JOIN categorie c with (INDEX(ix_categorie)) on c.categ_id = e.categorie_id
LEFT JOIN traduction_categorie tc on tc.categ_id = c.categ_id and tc.langue_id = @langId

INNER JOIN type_tarif t  on t.type_tarif_id = e.type_tarif_id
LEFT JOIN traduction_type_tarif tt on tt.type_tarif_id = t.type_tarif_id and tt.langue_id = @langId

LEFT OUTER JOIN reserve r on r.reserve_id = e.reserve_id
LEFT OUTER JOIN contingent co on co.conting_id = e.contingent_id
LEFT OUTER JOIN propriete_physique pacces ON PACCES.ID = rlp.acces AND pacces.codetable = 'ACCES'
LEFT OUTER JOIN propriete_physique pporte ON PPORTE.ID = rlp.porte AND pporte.codetable = 'PORTE'
LEFT OUTER JOIN propriete_physique ptribune ON PTRIBUNE.ID = rlp.tribune AND ptribune.codetable = 'TRIBUNE'
INNER JOIN dossier_[MANIFID] d on d.dossier_id = e.dossier_id
inner join commande_ligne cl on cl.dossier_id = d.dossier_id and e.seance_id = cl.seance_id and cl.type_ligne='DOS'
left outer join formule_abonnement form on cl.formule_id = form.form_abon_id
LEFT OUTER JOIN filiere f ON f.filiere_id = d.filiere_id
LEFT OUTER JOIN identite identiteDoss on identiteDoss.identite_id = d.identite_id 
LEFT OUTER JOIN dossier_consommateur dc on dc.dossier_id = d.dossier_id and dc.commande_id = d.commande_id AND dc.manifestation_id = [MANIFID]
LEFT OUTER JOIN consumers con2 on dc.consumer_id = con2.consumer_id
LEFT OUTER JOIN Entree_Complement ec3 on ec3.dossier_id=e.dossier_id and ec3.Entree_id=e.entree_id  and ec3.seance_id = e.seance_id
LEFT OUTER JOIN identite id3 on id3.identite_id=ec3.Consommateur_ID 


WHERE e.entree_id = [ENTREEID]
END