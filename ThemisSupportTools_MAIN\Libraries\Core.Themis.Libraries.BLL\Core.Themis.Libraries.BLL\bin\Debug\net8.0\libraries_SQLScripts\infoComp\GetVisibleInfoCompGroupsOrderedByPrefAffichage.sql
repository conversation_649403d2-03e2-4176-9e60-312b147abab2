﻿
SELECT DISTINCT icgrp.*
  FROM Info_comp_Grp icgrp
  INNER JOIN info_comp ic
    ON ic.groupe_id = icgrp.groupe_id
  LEFT JOIN Produit_Droit_Boutique pdb
    ON pdb.Droit_Boutique_ID = icgrp.groupe_id
       AND pdb.Droit_Boutique_Code LIKE 'PAGE_INFOCOMP_%'
 WHERE icgrp.systeme   = 'N'
   AND icgrp.masquer   = 'N'
   AND icgrp.groupe_id <> 0
   AND pdb.Droit_Boutique_ID IS NULL
 ORDER BY icgrp.pref_affichage;