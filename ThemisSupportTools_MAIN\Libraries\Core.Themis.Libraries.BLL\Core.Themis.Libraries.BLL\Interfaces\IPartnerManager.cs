﻿using Core.Themis.Libraries.Data.Entities.WsAdmin;
using Core.Themis.Libraries.DTO;
using Core.Themis.Libraries.DTO.Lookup;
using Core.Themis.Libraries.DTO.WSAdmin;
using Microsoft.AspNetCore.Components;
using System.Collections.Generic;
using Microsoft.Data.SqlClient;
using System.Threading.Tasks;
using StructureDTO = Core.Themis.Libraries.DTO.StructureDTO;

namespace Core.Themis.Libraries.BLL.Interfaces
{
    public interface IPartnerManager
    {
        Task<IEnumerable<string>> GetStructuresLinkedOfPartnerAsync(int partnerId);
        string GenerateRandomPassword();

        PartnerDTO? GetPartnerInfosById(int partnerId);
        Task<PartnerDTO?> GetPartnerInfosByIdAsync(int partnerId);

        PartnerDTO? GetPartnerInfosByName(string partnerName);
        Task<PartnerDTO?> GetPartnerInfosByNameAsync(string partnerName);
        PartnerDTO? GetPartnerInfosByIdWithDependancy(int partnerId, bool withoutDeletedStructure = true);
        Task<PartnerDTO?> GetPartnerInfosByIdWithDependancyAsync(int partnerId, bool withoutDeletedStructure = true);
        PartnerDTO? GetPartnerInfosByNameWithDependancy(string partnerName, bool withoutDeletedStructure = true);
        Task<PartnerDTO?> GetPartnerInfosByNameWithDependancyAsync(string partnerName, bool withoutDeletedStructure = true);

        List<PartnerDTO> GetAllPartners();
        List<PartnerDTO> GetAcivePartnersOrderByName();

        /// <summary>
        /// ramene tous les roles 
        /// select *from partenaires_roles         
        /// </summary>
        /// <returns></returns>
        List<PartnerRoleDTO> GetRoles();
        List<WsAdminStructureDTO> GetStructuresAcceptPartenaires();

        Task<List<WsAdminStructureDTO>> GetStructuresAcceptPartenairesAsync();

        /// <summary>
        /// Récupère toutes les structures actives (non supprimées)
        /// </summary>
        /// <returns>Liste des structures actives</returns>
        Task<List<WsAdminStructureDTO>> GetActiveStructuresAsync();
        Task<List<PartnerDTO>> SearchPartnersAsync(string searchTerm);
        int CreatePartner(PartnerDTO partenaireDTO);

        bool DeletePartner(PartnerDTO partenaireDTO);

        Task DeletePartnerAsync(PartnerDTO partenaireDTO);
        int InsertPartner(PartnerDTO partenaireDTO);
        Task<int> InsertPartnerAsync(PartnerDTO partenaireDTO);

        Task<int> UpdatePartenaireRolesAsync(int partid, List<int> roleIds);
        int UpdatePartenaireRoles(int partid, List<int> roleIds);

        Task<int> UpdatePartenaireStructuresAsync(int partid, List<string> structuresIds);
        int UpdatePartenaireStructures(int partid, List<string> structuresIds);

        /// <summary>
        /// Mets à jour le partenaire et ses dépendances (liste des structures + liste des roles)
        /// </summary>
        /// <param name="part"></param>
        /// <returns></returns>
        Task<bool> UpdatePartnerWithDependanciesAsync(PartnerDTO part);
        Task<bool> UpdatePartnerAsync(PartnerDTO part);




        Task<List<SelectLookup>> GetSelectLookupStructuresByPartnerAsync(int PartnerId);
        
        Task<List<SelectLookup>> GetSelectLookupRolesByPartnerAsync(int PartnerId);

        Task<IEnumerable<dynamic>> GetStructuresWithPartenairesAsync();

        /// <summary>
        /// Retourne les structures avec leurs partenaires associés, déjà groupées et traitées
        /// </summary>
        /// <returns>Liste des structures avec leurs partenaires</returns>
        Task<List<StructureDTO>> GetStructuresWithPartenairesGroupedAsync();

        /// <summary>
        /// Retourne les structures avec leurs partenaires associés, filtrées par IDs de structures
        /// </summary>
        /// <param name="structureIds">Liste des IDs de structures à filtrer</param>
        /// <returns>Liste des structures filtrées avec leurs partenaires</returns>
        Task<List<StructureDTO>> GetStructuresWithPartenairesGroupedAsync(List<string> structureIds);

        // Nouvelles méthodes pour la liaison revendeur-profil acheteur

        /// <summary>
        /// Obtient la liste des revendeurs (profils acheteurs avec is_revendeur = 1)
        /// </summary>
        /// <param name="structureId">ID de la structure</param>
        /// <returns>Liste des revendeurs</returns>
        Task<List<BuyerProfilDTO>> GetRevendeursAsync(int structureId);

        /// <summary>
        /// Obtient la liste de tous les revendeurs de toutes les structures
        /// </summary>
        /// <returns>Liste de tous les revendeurs avec leur structure</returns>
        Task<List<BuyerProfilDTO>> GetAllRevendeursAsync();

        /// <summary>
        /// Obtient les profils acheteurs liés à un revendeur spécifique
        /// </summary>
        /// <param name="structureId">ID de la structure</param>
        /// <param name="revendeurId">ID du revendeur</param>
        /// <returns>Liste des profils acheteurs liés au revendeur</returns>
        Task<List<BuyerProfilDTO>> GetProfilsAcheteursLiesAuRevendeurAsync(int revendeurId);

        /// <summary>
        /// Obtient tous les profils acheteurs d'une structure
        /// </summary>
        /// <param name="structureId">ID de la structure</param>
        /// <returns>Liste des profils acheteurs de la structure</returns>
        Task<List<BuyerProfilDTO>> GetProfilsAcheteursDeStructureAsync(int structureId);

        

        /// <summary>
        /// Sauvegarde une liaison entre un revendeur et un profil acheteur sur une structure
        /// </summary>
        /// <param name="liaison">DTO de la liaison à sauvegarder</param>
        /// <returns>True si la sauvegarde a réussi</returns>
        Task<bool> SaveLiaisonRevendeurProfilAcheteurStructureAsync(RevendeurIsProfilAcheteurOnStructureDTO liaison);

        /// <summary>
        /// Vérifie si une liaison existe déjà
        /// </summary>
        /// <param name="structureId">ID de la structure</param>
        /// <param name="revendeurId">ID du revendeur</param>
        /// <param name="profilAcheteurId">ID du profil acheteur</param>
        /// <returns>True si la liaison existe</returns>
        Task<bool> LiaisonExistsAsync(int structureId, int revendeurId, int profilAcheteurId);

        /// <summary>
        /// Obtient les IDs des profils acheteurs déjà liés à un revendeur sur une structure spécifique
        /// </summary>
        /// <param name="structureId">ID de la structure</param>
        /// <param name="revendeurId">ID du revendeur</param>
        /// <returns>Liste des IDs des profils acheteurs déjà liés</returns>
        Task<List<int>> GetProfilsAcheteursLiesIdsAsync(int structureId, int revendeurId);

    }
}