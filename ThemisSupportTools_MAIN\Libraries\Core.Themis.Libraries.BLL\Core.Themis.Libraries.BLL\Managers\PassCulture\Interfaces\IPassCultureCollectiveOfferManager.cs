﻿using Core.Themis.Libraries.DTO.Lookup;
using Core.Themis.Libraries.DTO.PassCulture;
using Core.Themis.Libraries.DTO.PassCulture.ApiExchange.Response;
using Core.Themis.Libraries.DTO.PassCulture.Forms;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace Core.Themis.Libraries.BLL.Managers.PassCulture.Interfaces
{
    public interface IPassCultureCollectiveOfferManager
    {
        /// <summary>
        /// Create an offer in pass culture platform
        /// </summary>
        /// <param name="structureId"></param>
        /// <param name="model"></param>
        /// <returns></returns>
        Task<PassCultureResponse<CollectiveOfferConsultation>> CreateCollectiveOfferAsync(int structureId, CollectiveOfferFormModel model);

        /// <summary>
        /// Update an offer in pass culture platform
        /// </summary>
        /// <param name="structureId"></param>
        /// <param name="model"></param>
        /// <returns></returns>
        Task<PassCultureResponse<CollectiveOfferConsultation>> UpdateCollectiveOfferAsync(int structureId, CollectiveOfferFormModel model);

        /// <summary>
        /// Get collective offer form with pass culture datas linked to an order for update
        /// </summary>
        /// <param name="structureId"></param>
        /// <param name="orderId"></param>
        /// <returns></returns>
        Task<CollectiveOfferFormModel> GetCollectiveOfferFormModelByOrderIdAsync(int structureId, int orderId);

        ReservationInfosForPassCulture GetReservationInfosForPassCulture(int structureId, int orderId);

        string GenerateAutoDescriptionForPassCulture(int structureId, int orderId);

        string GenerateAutoPriceDetailForPassCulture(int structureId, int orderId);

        Task<List<SelectLookup>> SelectLookupReservationOffreCollectivePassCultureAsync(int structureId, bool onlyForUpdate = false);
    }
}
