﻿using Core.Themis.Libraries.DTO.EventsSessions;
using Core.Themis.Libraries.DTO.Lookup;
using Core.Themis.Libraries.DTO.Offers;
using Core.Themis.Libraries.DTO.PassCulture;
using Core.Themis.Libraries.DTO.PassCulture.ApiExchange.Response;
using Core.Themis.Libraries.DTO.PassCulture.Forms;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace Core.Themis.Libraries.BLL.Interfaces
{
    public interface IOfferManager
    {
        Task<IEnumerable<OfferDTO>> GetOffersOnSales(int structureId);

        Task<List<EventDTO>> GetAllEventsLinkedToAnOfferAsync(int structureId, int offerId);

        List<OffreGestionPlaceDTO> GetAllGestionPlaceLinkedToAnOffer(int structureId, int offerId);
    }
}
