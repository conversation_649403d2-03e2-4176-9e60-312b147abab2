﻿using Core.Themis.Libraries.DTO.Lookup;
using Core.Themis.Libraries.DTO.WSAdmin;
using Core.Themis.Libraries.DTO.WSAdmin.ConnexionsDTO;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using static Core.Themis.Libraries.BLL.WsAdmin.WsAdminStructuresManager;

namespace Core.Themis.Libraries.BLL.WsAdmin.Interfaces
{
    public interface IWsAdminStructuresManager
    {
        /// <summary>
        /// Liste des structures présentes en PROD et en TEST 
        /// </summary>
        /// <returns></returns>
        Task<List<WsAdminStructureDTO>> GetWsAdminStructuresGlobalesAsync(bool withDeletedStructures = false);


        Task<List<WsAdminStructureDTO>> GetWsAdminStructuresFileAttenteInfosAsync(bool withDeletedStructures = false);


        List<WsAdminStructureDTO> GetAllWsAdminStructures();

        Task<List<WsAdminStructureDTO>> GetActivesWsAdminStructuresAsync();

        Task<List<WsAdminStructureDTO>> SearchWsAdminStructuresAsync(string searchTerm);
        //Task<List<WsAdminStructureDTO>>? GetWsAdminStructuresAsync();

        #region Lookup
        Task<List<SelectLookup>> GetSelectLookupStructuresAsync();

        Task<List<SelectLookup>> GetSelectLookupRolesAsync();

        Task<List<SelectLookup>> SearchSelectLookupStructuresAsync(string searchTerm);

        Task<List<SelectLookup>> SearchSelectLookupRolesAsync(string searchTerm);

        Task<List<WsAdminStructureDTO>> GetWsAdminStructuresWithWebTracingAsync();

        [Obsolete("Cette méthode ramene les webtracing ?!!?", true)]
        Task<List<ConnectionStructureDatabaseInfoDTO>> GetConnectionStructureDatabaseInfosAsync();

        Task<List<ConnexionswebTracingOfStructureDTO>> GetConnexionswebTracingOfStructuresAsync();

        Task<List<UnifiedWebTracingDTO>> GetUnifiedWebTracingDTOAsync();



        Task DeleteWebTracingAsync(string structureId);
        #endregion

        Task<string> GetDeletedByAsync(string structureId);

        Task<List<string>> LoadStructuresFromXmlAsync(string filePath);
        Task<Dictionary<string, List<string>>> LoadXmlFilesForServiceAsync(ServiceType service, string preprodPath, string prodPath);
        Task<bool> SaveStructureConfigurationAsync(ServiceType serviceType, bool isPreProd, bool isInclusion, string structureId, string basePath);
        Task<bool> RemoveStructureAsync(ServiceType activeService, bool isPreProd, bool isInclusion, string structureId, string basePath);
    }
}
