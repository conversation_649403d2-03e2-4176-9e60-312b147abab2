﻿/*
DECLARE @pEventId int = 46 -- 69
DECLARE @pSessionId int =  1880 --2409
DECLARE @pOffersId varchar(max) = '8,10,16'
*/

	SELECT gp.* FROM gestion_place gp
	INNER JOIN offre_gestion_place  ogp ON ogp.gestion_place_id = gp.gestion_place_id 
	WHERE manif_id = @pEventId 
	AND (@pSessionId = 0 OR gp.seance_id = @pSessionId)
	AND offre_id IN ( select Name from splitstring(@pOffersId, ',')) 
			AND isContrainteIdentite = 1  AND formule_id IS NULL
