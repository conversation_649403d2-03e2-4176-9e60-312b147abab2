﻿using Core.Themis.Libraries.DTO.Identity;
using System.Collections.Generic;
using Microsoft.Data.SqlClient;

namespace Core.Themis.Libraries.BLL.Interfaces
{
    public interface IIdentiteManager
    {
        public const string WhiteCardPassword = "don't check it";

        /// <summary>
        /// get identity via specifique partenaires (cf unidy)
        /// </summary>
        /// <param name="structureId"></param>
        /// <param name="partner"></param>
        /// <param name="externeId"></param>
        /// <returns></returns>
        IdentityDTO GetViaExterne(int structureId, string partner, string externeId);

        /// <summary>
        /// get from email ou identite ou adherent, check password
        /// </summary>
        /// <param name="structureId"></param>
        /// <param name="email">email</param>
        /// <param name="langIso"></param>
        /// <param name="salthash">n'est utilisé que pour encrypt <> default</param>
        /// <param name="clearPassWord">password renseigné par internaute</param>
        /// <returns></returns>
        public IdentityDTO Get(int structureId, string email, int identiteId, string adherentId, string langIso, string salthash, string clearPassWord = WhiteCardPassword);


        /// <summary>
        /// get from email ou identite ou adherent, check password : methode ultime
        /// </summary>
        /// <param name="structureId"></param>
        /// <param name="identifiant">email ou identite ou adherent</param>
        /// <param name="langIso"></param>
        /// <param name="salthash">n'est utilisé que pour encrypt <> default</param>
        /// <param name="clearPassWord">password renseigné par internaute</param>
        /// <returns></returns>
        /// 
        public IdentityDTO Get(int structureId, string identifiant, string langIso, string salthash, string clearPassWord = WhiteCardPassword);


        /// <summary>
        /// get by email
        /// </summary>
        /// <param name="structureId"></param>
        /// <param name="email"></param>
        /// <param name="ipostalTelEmail"></param>
        /// <param name="langIso"></param>
        /// <param name="salthash"></param>
        /// <returns></returns>
        //     IdentityDTO Get(int structureId, string email, string langIso, string salthash);

        /// <summary>
        /// get by id
        /// </summary>
        /// <param name="structureId"></param>
        /// <param name="email"></param>
        /// <param name="ipostalTelEmail"></param>
        /// <param name="langIso"></param>
        /// <param name="salthash"></param>
        /// <returns></returns>

        //IdentityDTO Get(int structureId, int identiteId, string langIso, string salthash);

        /// <summary>
        /// get by adherent ou id
        /// </summary>
        /// <param name="structureId"></param>
        /// <param name="identiteId"></param>
        /// <param name="adherentId"></param>
        /// <param name="langIso"></param>
        /// <param name="salthash"></param>
        /// <returns></returns>
        //    IdentityDTO Get(int structureId, int identiteId, string adherentId, string langIso, string salthash);

        /// <summary>
        /// Load from ids ou adherent
        /// </summary>
        /// <param name="structureId"></param>
        /// <param name="listIdentiteId"></param>
        /// <param name="adherentId"></param>
        /// <param name="ipostalTelEmail"></param>
        /// <param name="langIso"></param>
        /// <param name="salthash"></param>
        /// <returns></returns>

        List<IdentityDTO> Load(int structureId, List<int> listIdentiteId, string adherentId, int ipostalTelEmail, string langIso, string salthash);

        int AddIdentity(int structureId, IdentityDTO identity, bool? pcheckEmailUnicity);


        bool ThisEmailExisteDeja(int structureId, string emailToCheck);

        /// <summary>
        /// update l'identite
        /// </summary>
        /// <param name="structureId"></param>
        /// <param name="identity"></param>
        /// <param name="checkEmailUnicity"></param>
        /// <returns></returns>
        int Update(int structureId, IdentityDTOUpdate identity, bool? pcheckEmailUnicity);

        /// <summary>
        /// maj du password : /!\ b64password doit être une chaine b64
        /// </summary>
        /// <param name="structureId"></param>
        /// <param name="identiteId"></param>
        /// <param name="b64password"></param>
        int UpdatePassword(int structureId, int identiteId, string b64password);


        /// <summary>
        /// UPDATE identite SET montant_debit=montant_debit + amount
        /// </summary>
        /// <param name="structureId"></param>
        /// <param name="identiteId"></param>
        /// <param name="amount"></param>
        /// <returns></returns>
        int UpdateDebitAmount(int structureId, int identiteId, int amount, SqlConnection cnxOpen, SqlTransaction? sqlTransaction = null);

        List<IdentityDTO> GetConsumers(int structureId, int identiteId, int ipostalTelEmail = 0, string salthash = "");



        int AddConsumer(int structureId, int identiteMaitre, IdentityDTO consumer);

        int SearchAndLinkConsumer(int structureId, int identityIdConnected, int identityId, string initial, string email, string adherentId);

        int LinkConsumer(int structureId, int identityIdConnected, int identityId);

        /// <summary>
        /// Ajout une info complementaire sur une identite donnée (identite_complement)
        /// </summary>
        /// <param name="structureId"></param>
        /// <param name="identiteId"></param>
        /// <param name="langueId"></param>
        /// <param name="comment"></param>
        /// <param name="sex"></param>
        /// <returns></returns>
        int AddIdentiteComplement(int structureId, int identiteId, int langueId = 0, string comment = "", string sex = "");

        /// <summary>
        /// Ajout une info complementaire WEB sur une identite donnée (identite_compl_web)
        /// </summary>
        /// <returns></returns>
        int AddIdentiteComplementWeb(int structureId, int identiteId, string login = "", string password = "", bool supprimer = false, bool doitchanger = false);

        /// <summary>
        /// retourne l'identite via le password de complement web (cf unidy-sv98)
        /// </summary>
        /// <param name="structureId"></param>
        /// <param name="passwordComplementWeb"></param>
        /// <returns></returns>
        List<int> GetIdIdentiteComplementWebPassword(int structureId, string passwordComplementWeb);

        /// <summary>
        /// Ajout une infocomp sur une identite donnée ([identite_info_comp])
        /// </summary>
        /// <param name="structureId"></param>
        /// <param name="identiteId"></param>
        /// <param name="infoComp"></param>
        /// <returns></returns>
        int AddInfoCompOnIdentite(int structureId, int identiteId, InfoCompDTO infoComp);

        /// <summary>
        /// Ajout identite_compl_web
        /// </summary>
        /// <param name="structureId"></param>
        /// <param name="identiteId"></param>
        /// <param name="complWeb"></param>
        /// <returns></returns>
        int AddComplementWebOnIdentite(int structureId, int identiteId, IdentityDTO.IdentiteComplementWebcl complWeb);

        List<IdentityDTO> LoadConsumers(int structureId, int identityId);
    }
}
