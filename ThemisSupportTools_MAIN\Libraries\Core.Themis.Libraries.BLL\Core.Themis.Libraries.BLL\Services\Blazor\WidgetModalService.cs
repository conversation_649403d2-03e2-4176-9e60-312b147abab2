﻿using Core.Themis.Libraries.BLL.Services.Blazor.Models;
using Core.Themis.Libraries.BLL.Services.Interfaces;
using Microsoft.JSInterop;
using System.Threading.Tasks;

namespace Core.Themis.Libraries.BLL.Services.Blazor
{
    public class WidgetModalService(IJSRuntime jsRuntime, IRazorViewRenderService razorViewRenderService)
    {
        //public async Task<bool> CreateConfirmationModal(ConfirmationModalSettings modalSettings)
        //{
        //    string html = await razorViewRenderService.RenderPartialToStringAsync("/Common/Views/ConfirmationModal.cshtml", modalSettings);

        //    var modalInfo = new ModalInfos()
        //    {
        //        IframeSelector = modalSettings.IframeSelector,
        //        ModalIdSelector = modalSettings.Id.ToString(),
        //        HtmlContent = html,
        //        IsConfirmation = true
        //    };

        //    return await jsRuntime.InvokeAsync<bool>("useConfirmationModal", modalInfo);
        //}

        public async Task CreateModalAsync(string modalViewPath, string modalId)
        {
            WidgetModalSettings widgetModalSettings = new()
            {
                Id = modalId
            };

            string html = await razorViewRenderService.RenderPartialToStringAsync(modalViewPath, widgetModalSettings);

            await jsRuntime.InvokeVoidAsync("createModal", html, modalId);
        }

        public async Task CloseModalAsync(string modalId)
        {
            await jsRuntime.InvokeVoidAsync("closeModal", modalId);
        }

        //public async Task<T?> CreateModal<T>(ModalInfos modalSettings)
        //{
        //    string? result = await jsRuntime.InvokeAsync<string?>("useCustomModal", modalSettings);

        //    if (result is not null)
        //        return JsonConvert.DeserializeObject<T>(result);

        //    return default;
        //}
    }
}
