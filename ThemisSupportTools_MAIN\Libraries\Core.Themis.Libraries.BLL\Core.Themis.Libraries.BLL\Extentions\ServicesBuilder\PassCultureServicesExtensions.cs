﻿using Core.Themis.Libraries.BLL.Services.PassCulture.Interfaces;
using Core.Themis.Libraries.BLL.Services.PassCulture;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Configuration;
using Core.Themis.Libraries.Utilities.API.Factories;
using Core.Themis.Libraries.Utilities.API.Interfaces;
using Core.Themis.Libraries.BLL.Managers.PassCulture.Interfaces;
using Core.Themis.Libraries.BLL.Managers.PassCulture;

namespace Core.Themis.Libraries.BLL.Extentions.ServicesBuilder
{
    public static class PassCultureServicesExtensions
    {
        /// <summary>
        /// Extention method for add Pass culture dependancy in <see cref="IServiceCollection"/>
        /// </summary>
        /// <param name="services"></param>
        public static void AddPassCultureServices(this IServiceCollection services)
        {
            var builder = services.BuildServiceProvider();

            var configuration = builder.GetRequiredService<IConfiguration>();

            services.AddTransient<IPassCultureService, PassCultureService>();
            services.AddTransient<IPassCultureCollectiveOfferManager, PassCultureCollectiveOfferManager>();
            services.AddTransient<IPassCultureIndividualOfferManager, PassCultureIndividualOfferManager>();

            services.AddSingleton<IApiRateLimiter<PassCultureService>>(provider =>
                ApiRateLimiterFactory.Create<PassCultureService>(configuration["PassCulture:ApiPath"]!));
        }
    }
}
