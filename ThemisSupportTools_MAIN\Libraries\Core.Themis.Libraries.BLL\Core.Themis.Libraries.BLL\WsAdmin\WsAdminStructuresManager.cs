﻿using AutoMapper;
using Core.Themis.Libraries.BLL.WsAdmin.Interfaces;
using Core.Themis.Libraries.Data.DBContext;
using Core.Themis.Libraries.Data.DBContext.Interfaces;
using Core.Themis.Libraries.Data.Entities.WsAdmin.Login;
using Core.Themis.Libraries.Data.Entities.WsAdmin.Partner;
using Core.Themis.Libraries.Data.Entities.WsAdmin.Structure;
using Core.Themis.Libraries.Data.Repositories.Open.Structure.Interfaces;
using Core.Themis.Libraries.Data.Repositories.WSAdmin;
using Core.Themis.Libraries.Data.Repositories.WSAdmin.Interfaces;
using Core.Themis.Libraries.Data.Repositories.WSAdmin.ThemisSupportTools.Interfaces;
using Core.Themis.Libraries.DTO.Enums.ThemisSupportTools;
using Core.Themis.Libraries.DTO.Lookup;
using Core.Themis.Libraries.DTO.WSAdmin;
using Core.Themis.Libraries.DTO.WSAdmin.ConnexionsDTO;
using Core.Themis.Libraries.Utilities.Extensions;
using Core.Themis.Libraries.Utilities.Helpers;
using Core.Themis.Libraries.Utilities.Logging;
using Dapper;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Http.HttpResults;
using Microsoft.Extensions.Caching.Memory;
using NLog;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Net.Http;
using System.Threading.Tasks;
using System.Xml.Linq;

namespace Core.Themis.Libraries.BLL.WsAdmin
{
    public class WsAdminStructuresManager : IWsAdminStructuresManager
    {
        private readonly IMapper _mapper;
        private readonly IStructuresRepository _structureRepository;
        private readonly IPartenairesRolesRepository _partenairesRolesRepository;
        private readonly IMemoryCache _memoryCache;
        private readonly IThemisSupportToolsDbContext _themisSupportToolsDbContext;
        private readonly IRepositoryFactory _repositoryFactory;
        private readonly IConnectionStructureDatabaseInfoRepository _connectionStructureDatabaseInfoRepository;
        private readonly IConnexionswebTracingOfStructureRepository _connexionswebTracingOfStructureRepository;
        private readonly IHttpContextAccessor _httpContextAccessor;
        private readonly ITstLoginRepository _tstLoginRepository;

        private static readonly RodrigueNLogger RodrigueLogger = new();

        public WsAdminStructuresManager(IStructuresRepository structureRepository,
            IPartenairesRolesRepository partenairesRolesRepository,
            IMapper mapper,
            IMemoryCache memoryCache,
            IThemisSupportToolsDbContext themisSupportToolsDbContext,
            IRepositoryFactory repositoryFactory, IConnectionStructureDatabaseInfoRepository connectionStructureDatabaseInfoRepository,
             IConnexionswebTracingOfStructureRepository connexionswebTracingOfStructureRepository, IHttpContextAccessor httpContextAccessor,
             ITstLoginRepository tstLoginRepository)
        {
            _structureRepository = structureRepository;
            _partenairesRolesRepository = partenairesRolesRepository;
            _mapper = mapper;
            _memoryCache = memoryCache;
            _themisSupportToolsDbContext = themisSupportToolsDbContext;
            _repositoryFactory = repositoryFactory;
            _connectionStructureDatabaseInfoRepository = connectionStructureDatabaseInfoRepository;
            _connexionswebTracingOfStructureRepository = connexionswebTracingOfStructureRepository;
            _httpContextAccessor = httpContextAccessor;
            _tstLoginRepository = tstLoginRepository;

        }

        private static readonly HttpClient client = new HttpClient();

        public static async Task<bool> IsUrlOkAsync(string url)
        {
            
            try
            {
                var response = await client.SendAsync(new HttpRequestMessage(HttpMethod.Head, url));
                return response.StatusCode == System.Net.HttpStatusCode.OK;
            }
            catch
            {
                return false;
            }
        }


        public async Task<List<WsAdminStructureDTO>> GetWsAdminStructuresFileAttenteInfosAsync(bool withDeletedStructures = false)
        {

            var listStruc = GetAllWsAdminStructures().Where(s => s.Supp != 'O').ToList();





            foreach (var s in listStruc)
            {
                var vFA = RodrigueConfigIniDictionnary.GetVersionFileAttenteFromConfig(int.Parse(s.StructureId));
                s.versionFileAttente = vFA;


                var urlV1 = RodrigueConfigIniDictionnary.GetUrlFileAttenteFromConfig(int.Parse(s.StructureId), "v1");
                var urlV2 = RodrigueConfigIniDictionnary.GetUrlFileAttenteFromConfig(int.Parse(s.StructureId), "v2");
                var urlMaintenance = RodrigueConfigIniDictionnary.GetUrlFileAttenteFromConfig(int.Parse(s.StructureId), "maintenance");

                s.urlV1 = urlV1;
                s.urlV2 = urlV2;
                s.urlMaintenance = urlMaintenance;

                if (string.IsNullOrWhiteSpace(urlV1))
                    s.isUrlV1OK = false;
                else
                    s.isUrlV1OK = await IsUrlOkAsync(urlV1);

                if (string.IsNullOrWhiteSpace(urlV2))
                    s.isUrlV2OK = false;
                else
                    s.isUrlV2OK = await IsUrlOkAsync(urlV2);

                if (string.IsNullOrWhiteSpace(urlMaintenance))
                    s.isUrlMaintenanceOK = false;
                else
                    s.isUrlMaintenanceOK = await IsUrlOkAsync(urlMaintenance);



                // Append text asynchronously



            }

            return listStruc;
        }

        /// <summary>
        /// Liste des structures présentes en PROD et en TEST 
        /// </summary>
        /// <returns></returns>
        public async Task<List<WsAdminStructureDTO>> GetWsAdminStructuresGlobalesAsync(bool withDeletedStructures = false)
        {
            string wsAdminStructuresCache = $"GetWsAdminStructuresGlobales_{withDeletedStructures}";

            var cacheResult = _memoryCache.Get<List<WsAdminStructureDTO>>(wsAdminStructuresCache);
            if (cacheResult is not null)
                return cacheResult;

            // Utiliser le contexte ThemisSupportTools
            var structureRepository = _repositoryFactory.CreateRepository<StructuresRepository>(DbContextType.ThemisSupportTools);


            //GetViaExterne structures en TEST
            using var db = _themisSupportToolsDbContext.UseEnvironment(EnumEnvironment.Test);
            var wsAdminStructuresTest = await structureRepository.GetWsAdminStructuresAsync();


            _memoryCache.RemoveKeysContainPartOfKey("GetAll_");

            //get structures en PROD
            using var dbProd = _themisSupportToolsDbContext.UseEnvironment(EnumEnvironment.Prod);
            var wsAdminStructuresProd = await structureRepository.GetWsAdminStructuresAsync();

            if (wsAdminStructuresTest is null || wsAdminStructuresProd is null)
            {
                return new List<WsAdminStructureDTO>();
            }

            var wsAdminStructuresGlobale = wsAdminStructuresTest.Concat(wsAdminStructuresProd).DistinctBy(s => s.StructureId);

            List<WsAdminStructureDTO> structures = _mapper.Map<List<WsAdminStructureDTO>>(wsAdminStructuresGlobale);
            structures.ForEach(wsAdminStructure =>
            {
                wsAdminStructure.ExistInTEST = wsAdminStructuresTest.Any(wsAdminTest => wsAdminStructure.StructureId == wsAdminTest.StructureId);
                wsAdminStructure.ExistInPROD = wsAdminStructuresProd.Any(wsAdminProd => wsAdminStructure.StructureId == wsAdminProd.StructureId);
            });


            List<WsAdminStructureDTO> result;

            if (withDeletedStructures)
                result = structures;

            result = structures.Where(s => !s.IsDeleted).ToList();


            //Mets en cache 
            var cacheExpiryOptions = new MemoryCacheEntryOptions
            {
                AbsoluteExpiration = DateTime.Now.AddSeconds(30),
                Priority = CacheItemPriority.High,
                SlidingExpiration = TimeSpan.FromSeconds(50)
            };

            _memoryCache.Set(wsAdminStructuresCache, result, cacheExpiryOptions);

            return result;
        }



        /// <summary>
        /// Liste des structures de WsAdmin
        /// </summary>
        /// <returns></returns>
        public List<WsAdminStructureDTO> GetAllWsAdminStructures()
        {
            //var structuresInDB = _structureRepository.GetWsAdminStructuresAsync().Result;
            var structuresInDB = _structureRepository.GetWsAdminStructures();



            List<WsAdminStructureDTO> structures = _mapper.Map<List<WsAdminStructureDTO>>(structuresInDB);
            return structures;
        }

        /// <summary>
        /// Liste des structures de WsAdmin active (non supprimée)
        /// </summary>
        /// <returns></returns>
        public async Task<List<WsAdminStructureDTO>> GetActivesWsAdminStructuresAsync()
        {
            var structuresInDB = await _structureRepository.GetWsAdminStructuresAsync().ConfigureAwait(false);
            List<WsAdminStructureDTO> structures = _mapper.Map<List<WsAdminStructureDTO>>(structuresInDB.Where(s => s.Supp != 'O'));

            return structures.OrderBy(o => o.Name).ToList();
        }


        public async Task<List<WsAdminStructureDTO>> SearchWsAdminStructuresAsync(string searchTerm)
        {
            var structuresInDB = await GetWsAdminStructuresGlobalesAsync();

            if (int.TryParse(searchTerm, out var structure) && !string.IsNullOrWhiteSpace(searchTerm))
                searchTerm = structure.ToString("0000");

            if (!string.IsNullOrWhiteSpace(searchTerm))
                structuresInDB = structuresInDB.Where(s => s.StructureId.StartsWith(searchTerm) || s.Name.ToUpper().Contains(searchTerm.ToUpper())).ToList();

            return _mapper.Map<List<WsAdminStructureDTO>>(structuresInDB);
        }
        #region Select Lookup
        #endregion
        public async Task<List<SelectLookup>> GetSelectLookupStructuresAsync()
        {
            try
            {
                var structures = await _structureRepository.GetWsAdminStructuresAsync().ConfigureAwait(false);
                return structures.Select(s => new SelectLookup()
                {
                    Value = s.StructureId,
                    Libelle = $"{s.Name} - ({s.StructureId})",
                }).OrderBy(s => s.Libelle).ToList();

            }
            catch
            {
                throw;
            }
        }
        public async Task<List<SelectLookup>> SearchSelectLookupStructuresAsync(string searchTerm)
        {
            var structures = await _structureRepository.GetWsAdminStructuresAsync().ConfigureAwait(false);

            var structuresLookup = await GetSelectLookupStructuresAsync();

            if (int.TryParse(searchTerm, out var structure) && !string.IsNullOrWhiteSpace(searchTerm))
                searchTerm = structure.ToString("0000");

            if (!string.IsNullOrWhiteSpace(searchTerm))
                structuresLookup = structuresLookup.Where(s => int.Parse(s.Value) == structure || s.Libelle.ToUpper().Contains(searchTerm.ToUpper())).ToList();

            return structuresLookup;
        }

        public async Task<List<SelectLookup>> GetSelectLookupRolesAsync()
        {
            try
            {
                var roles = await _partenairesRolesRepository.GetAllAsync().ConfigureAwait(false);
                return roles.Select(r => new SelectLookup()
                {
                    Value = r.Id.ToString(),
                    Libelle = r.Code
                }).OrderBy(s => s.Libelle).ToList();
            }
            catch
            {
                throw;
            }
        }
        public async Task<List<SelectLookup>> SearchSelectLookupRolesAsync(string searchTerm)
        {
            var roles = await _partenairesRolesRepository.GetWsAdminPartenaireRolesAsync().ConfigureAwait(false);

            var rolesLookup = roles?.Select(r => new SelectLookup()
            {
                Value = r.Id.ToString(),
                Libelle = r.Code

            }).OrderBy(s => s.Libelle).ToList();

            if (int.TryParse(searchTerm, out var value) && !string.IsNullOrWhiteSpace(searchTerm))
                searchTerm = value.ToString();

            if (!string.IsNullOrWhiteSpace(searchTerm))
                rolesLookup = rolesLookup!.Where(r => int.Parse(r.Value) == value || r.Libelle.ToUpper().Contains(searchTerm.ToUpper())).ToList();

            return rolesLookup!;
        }


        //Permet de récupérer les structures qui ont une connexion webtracing
        public async Task<List<WsAdminStructureDTO>> GetWsAdminStructuresWithWebTracingAsync()
        {
            var structures = await _structureRepository.GetWsAdminStructuresAsync();
            return _mapper.Map<List<WsAdminStructureDTO>>(structures);

        }
        [Obsolete]
        public async Task<List<ConnectionStructureDatabaseInfoDTO>> GetConnectionStructureDatabaseInfosAsync()
        {
            var connectionInfos = await _connectionStructureDatabaseInfoRepository.GetAllAsync();

            return _mapper.Map<List<ConnectionStructureDatabaseInfoDTO>>(connectionInfos);
        }

        public async Task<List<ConnexionswebTracingOfStructureDTO>> GetConnexionswebTracingOfStructuresAsync()
        {
            if (_connexionswebTracingOfStructureRepository == null)
            {
                throw new InvalidOperationException("Repository is not initialized.");
            }

            var connexions = await _connexionswebTracingOfStructureRepository.GetAllAsync();

            return _mapper.Map<List<ConnexionswebTracingOfStructureDTO>>(connexions);
        }


        public async Task<List<UnifiedWebTracingDTO>> GetUnifiedWebTracingDTOAsync()
        {
            var structures = await _structureRepository.GetWsAdminStructuresAsync();
            var connexionsWebTracingOfStructures = await _connexionswebTracingOfStructureRepository.GetAllAsync();
            var connectionStructureDatabaseInfos = await _connectionStructureDatabaseInfoRepository.GetAllAsync();

            var result = structures.SelectMany(structure =>
            {
                var connexions = connexionsWebTracingOfStructures
                    .Where(cws => cws.StructureId == int.Parse(structure.StructureId))
                    .ToList();

                return connexions.Select(connexion =>
                {
                    var dbInfo = connectionStructureDatabaseInfos
                        .FirstOrDefault(db => db.ConnexionId == connexion.ConnexionId);

                    var dto = _mapper.Map<UnifiedWebTracingDTO>(structure);
                    _mapper.Map(connexion, dto);
                    _mapper.Map(dbInfo, dto);

                    return dto;
                });
            }).OrderBy(dto => dto.StructureName).ToList();

            return result;
        }


        public async Task DeleteWebTracingAsync(string structureId)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(structureId))
                {
                    throw new ArgumentException("L'ID de la structure ne peut pas être vide.", nameof(structureId));
                }

                // Formatage de l'ID avec des zéros à gauche
                string formattedStructureId = structureId.PadLeft(4, '0');

                var structure = await _structureRepository.GetStructureByIdAsync(formattedStructureId);
                if (structure == null)
                {
                    throw new InvalidOperationException($"La structure avec l'ID {formattedStructureId} n'existe pas.");
                }

                if (structure.Supp == 'O')
                {
                    throw new InvalidOperationException($"La structure avec l'ID {formattedStructureId} a déjà été supprimée.");
                }

                var userName = _httpContextAccessor.HttpContext?.User?.Identity?.Name;
                if (string.IsNullOrEmpty(userName))
                {
                    throw new InvalidOperationException("L'utilisateur connecté est introuvable.");
                }

                // Mise à jour de la structure pour la suppression logique
                structure.Supp = 'O';
                structure.DateSupp = DateTime.UtcNow;
                await _structureRepository.UpdateAsync(structure);

                 await _tstLoginRepository.RecordDeleteStructureActionAsync(userName, formattedStructureId, structure.Name);

            }
            catch (Exception ex)
            {
                Console.WriteLine($"Erreur lors de la suppression de la structure {structureId} : {ex.Message}");
                throw;
            }
        
        }

        public async Task<string> GetDeletedByAsync(string structureId)
        {
            return await _tstLoginRepository.GetDeletedByAsync(structureId);
        }



        
        /// <summary>
        /// Charge les structures à partir d'un fichier XML
        /// </summary>
        /// <param name="filePath">Chemin du fichier XML</param>
        /// <returns>Liste des identifiants de structures</returns>
        public async Task<List<string>> LoadStructuresFromXmlAsync(string filePath)
        {
            if (!File.Exists(filePath))
                return new List<string>();

            try
            {
                string content = await File.ReadAllTextAsync(filePath);
                XDocument doc = XDocument.Parse(content);

                List<string> structures = new();
                foreach (var element in doc.Descendants("id"))
                {
                    structures.Add(element.Value);
                }

                return structures;
            }
            catch (Exception ex)
            {
                // Log error
                return new List<string>();
            }
        }

        /// <summary>
        /// Charge les fichiers XML pour un service spécifique
        /// </summary>
        /// <param name="service">Type de service</param>
        /// <param name="preprodPath">Chemin du répertoire PREPROD</param>
        /// <param name="prodPath">Chemin du répertoire PROD</param>
        /// <returns>Dictionnaire contenant les listes d'inclusion/exclusion</returns>
        public async Task<Dictionary<string, List<string>>> LoadXmlFilesForServiceAsync(ServiceType service, string preprodPath, string prodPath)
        {
            try
            {
                var result = new Dictionary<string, List<string>>();

                string servicePath = GetServicePath(service);

                // Charger les fichiers pour PREPROD
                string preprodServicePath = Path.Combine(preprodPath, servicePath);
                var inclusionsPreProd = await LoadStructuresFromXmlAsync(Path.Combine(preprodServicePath, "inclusionsStructures.xml"));
                var exclusionsPreProd = await LoadStructuresFromXmlAsync(Path.Combine(preprodServicePath, "exclusionsStructures.xml"));

                // Charger les fichiers pour PROD
                string prodServicePath = Path.Combine(prodPath, servicePath);
                var inclusionsProd = await LoadStructuresFromXmlAsync(Path.Combine(prodServicePath, "inclusionsStructures.xml"));
                var exclusionsProd = await LoadStructuresFromXmlAsync(Path.Combine(prodServicePath, "exclusionsStructures.xml"));

                result.Add("InclusionsPreProd", inclusionsPreProd);
                result.Add("ExclusionsPreProd", exclusionsPreProd);
                result.Add("InclusionsProd", inclusionsProd);
                result.Add("ExclusionsProd", exclusionsProd);

                return result;
            }
            catch (Exception ex)
            {
                string errorMsg = $"Erreur lors du chargement des fichiers XML pour le service {service}: {ex.Message}";
                throw new Exception(errorMsg);
            }
        }


        /// <summary>
        /// Sauvegarde la configuration d'une structure dans un fichier XML
        /// </summary>
        public async Task<bool> SaveStructureConfigurationAsync(ServiceType serviceType, bool isPreProd, bool isInclusion, string structureId, string basePath)
        {
            try
            {
                string servicePath = GetServicePath(serviceType);
                string fullServicePath = Path.Combine(basePath, servicePath);

                // Vérifier si le répertoire existe, sinon le créer
                if (!Directory.Exists(fullServicePath))
                {
                    Directory.CreateDirectory(fullServicePath);
                }

                string fileName = isInclusion ? "inclusionsStructures.xml" : "exclusionsStructures.xml";
                string filePath = Path.Combine(fullServicePath, fileName);

                // Charger le fichier XML existant ou créer un nouveau
                XDocument xmlDoc;
                if (File.Exists(filePath))
                {
                    string content = await File.ReadAllTextAsync(filePath);
                    xmlDoc = XDocument.Parse(content);
                }
                else
                {
                    xmlDoc = new XDocument(
                        new XElement("structures")
                    );
                }

                // Vérifier si l'ID existe déjà
                bool idExists = xmlDoc.Descendants("id").Any(e => e.Value == structureId);

                if (!idExists)
                {
                    // Ajouter l'ID à la liste
                    xmlDoc.Root.Add(
                        new XElement("id", structureId)
                    );

                    // Enregistrer le fichier
                    await File.WriteAllTextAsync(filePath, xmlDoc.ToString());

                    string updateCachePath = Path.Combine(Path.GetDirectoryName(filePath), "updatecache.txt");
                    await File.WriteAllTextAsync(updateCachePath, DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"));

                    return true;
                }

                return false;
            }
            catch (Exception ex)
            {
                throw new Exception($"Erreur lors de l'ajout de la structure: {ex.Message}");
            }
        }


        /// <summary>
        /// Supprime une structure d'un fichier XML
        /// </summary>
        public async Task<bool> RemoveStructureAsync(ServiceType activeService, bool isPreProd, bool isInclusion, string structureId, string basePath)
        {
            try
            {
                string servicePath = GetServicePath(activeService);
                string fullServicePath = Path.Combine(basePath, servicePath);

                string fileName = isInclusion ? "inclusionsStructures.xml" : "exclusionsStructures.xml";
                string filePath = Path.Combine(fullServicePath, fileName);

                if (!File.Exists(filePath))
                {
                    return false;
                }

                string content = await File.ReadAllTextAsync(filePath);
                XDocument xmlDoc = XDocument.Parse(content);

                var elementsToRemove = xmlDoc.Descendants("id").Where(e => e.Value == structureId).ToList();

                if (elementsToRemove.Any())
                {
                    foreach (var element in elementsToRemove)
                    {
                        element.Remove();
                    }

                    await File.WriteAllTextAsync(filePath, xmlDoc.ToString());

                    string updateCachePath = Path.Combine(Path.GetDirectoryName(filePath), "updatecache.txt");
                    await File.WriteAllTextAsync(updateCachePath, DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"));

                    return true;
                }

                return false;
            }
            catch (Exception ex)
            {
                throw new Exception($"Erreur lors de la suppression de la structure: {ex.Message}");
            }
        }

        /// <summary>
        /// Obtient le chemin du service
        /// </summary>
        private string GetServicePath(ServiceType serviceType)
        {
            return serviceType switch
            {
                ServiceType.CreateCmd => "createCmd",
                ServiceType.Edition => "doEdition",
                ServiceType.Paiement => "doPaiement",
                ServiceType.EnvoiMail => "envoiemail",
                ServiceType.EnvoiMailInfo => "envoiemailInfo",
                _ => throw new NotImplementedException()
            };
        }

    }

}