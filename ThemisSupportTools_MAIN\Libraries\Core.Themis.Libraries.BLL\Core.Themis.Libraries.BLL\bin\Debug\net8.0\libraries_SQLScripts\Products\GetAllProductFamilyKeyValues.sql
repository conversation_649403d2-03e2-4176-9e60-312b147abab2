﻿--DECLARE @pOnlyProductFamillyWithParams BIT = 1
--DECLARE @pLangCode VARCHAR(5) = 'fr'

DECLARE @langueId INT = (SELECT langue_id FROM langue WHERE langue_code = @pLangCode)

If(@pOnlyProductFamillyWithParams = 1)
BEGIN
	SELECT DISTINCT pf.Produit_Famille_ID AS Id,
	IIF(t.Traduction = '' OR t.Traduction IS NULL, pf.Produit_Famille_Nom , t.traduction Collate database_default) AS Name
	FROM  produit p 
	INNER JOIN Produit_Lien_Sous_Famille plsf ON plsf.Produit_id = p.produit_id
	INNER JOIN Produit_Famille pf ON pf.Produit_Famille_ID = plsf.Produit_Famille_ID
	INNER JOIN produit_stock ps ON p.produit_id = ps.produit_id
	LEFT JOIN traduction_langue t on t.traduction_id = pf.TraductionNom_ID AND t.langue_id = @langueId
	WHERE p.internet = 1
	AND pf.Masquer = 0
	AND ps.manifestation_id = 0
	AND ps.seance_id = 0
	AND 1 = ANY (SELECT DISTINCT
				psf_any.Regroupement_couleur
				FROM Produit_Lien_Sous_Famille plsf_any
				INNER JOIN Produit_Sous_Famille psf_any ON plsf_any.Produit_Sous_Famille_ID = psf_any.Produit_Sous_Famille_ID
				WHERE plsf_any.Produit_Famille_ID = pf.Produit_Famille_ID
				AND psf_any.Masquer = 0)
END
ELSE
BEGIN
	SELECT DISTINCT pf.Produit_Famille_ID AS Id,
	IIF(t.Traduction = '' OR t.Traduction IS NULL, pf.Produit_Famille_Nom , t.traduction Collate database_default) AS Name
	FROM produit p 
	INNER JOIN Produit_Lien_Sous_Famille plsf ON plsf.Produit_id = p.produit_id
	INNER JOIN Produit_Famille pf ON pf.Produit_Famille_ID = plsf.Produit_Famille_ID
	INNER JOIN produit_stock ps ON p.produit_id = ps.produit_id
	LEFT JOIN traduction_langue t on t.traduction_id = pf.TraductionNom_ID AND t.langue_id = @langueId
	WHERE p.internet = 1
	AND pf.Masquer = 0
	AND ps.manifestation_id = 0
	AND ps.seance_id = 0
	AND 0 = ALL (SELECT DISTINCT
				psf_any.Regroupement_couleur
				FROM Produit_Lien_Sous_Famille plsf_any
				INNER JOIN Produit_Sous_Famille psf_any ON plsf_any.Produit_Sous_Famille_ID = psf_any.Produit_Sous_Famille_ID
				WHERE plsf_any.Produit_Famille_ID = pf.Produit_Famille_ID
				AND psf_any.Masquer = 0)
END