﻿using Core.Themis.Libraries.DTO.Lookup;
using Core.Themis.Libraries.DTO.PassCulture.ApiExchange.Response;
using Core.Themis.Libraries.DTO.PassCulture;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace Core.Themis.Libraries.BLL.Managers.PassCulture.Interfaces
{
    public interface IPassCultureIndividualOfferManager
    {
        /// <summary>
        /// Get Forms model for individual offer update
        /// </summary>
        /// <param name="structureId"></param>
        /// <param name="offerId"></param>
        /// <returns></returns>
        Task<(List<InfoSuppForm>, List<CategoryEventInfoForm>)> GetIndividualOfferFormsForUpdateAsync(int structureId, int offerId);

        /// <summary>
        /// Create individual offer from rodrigue to passculture platform
        /// </summary>
        /// <param name="structureId"></param>
        /// <param name="offerId"></param>
        /// <param name="infoSuppPassCultures"></param>
        /// <returns></returns>
        Task<PassCultureResponse<List<EventOffer>>> CreateIndividualOffersAsync(int structureId, int offerId, List<InfoSuppForm> infoSuppPassCultures, List<CategoryEventInfoForm> categoryEventPassCultures);

        /// <summary>
        /// Update individual offer from rodrigue to passculture platform
        /// </summary>
        /// <param name="structureId"></param>
        /// <param name="offerId"></param>
        /// <param name="infoSuppPassCultures"></param>
        /// <returns></returns>
        Task<PassCultureResponse<List<EventOffer>>> UpdateIndividualOffersAsync(int structureId, int offerId, List<InfoSuppForm> infoSuppPassCultures, List<CategoryEventInfoForm> categoryEventPassCultures);

        Task<List<KeyValuePair<int, string>>> GetOfferEventsKeyValuesByOfferIdAsync(int structureId, int offerId);

        Task<List<SelectLookup>> SelectLookupOfferEventsAsync(int structureId, int offerId);

        List<SelectLookup> SelectLookupOfferForPassCulture(int structureId, bool forUpdate = false);
    }
}
