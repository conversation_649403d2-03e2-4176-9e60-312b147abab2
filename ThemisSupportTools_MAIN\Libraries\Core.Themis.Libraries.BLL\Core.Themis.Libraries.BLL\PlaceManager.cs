﻿using Core.Themis.Libraries.DTO;
using Core.Themis.Libraries.Utilities.Extensions;
using Core.Themis.Libraries.Utilities.Helpers;
using Core.Themis.Libraries.Utilities.ThemisSql;
using System;
using System.Collections.Generic;
using System.Data;
using Microsoft.Data.SqlClient;
using System.Linq;

namespace Core.Themis.Libraries.BLL
{
    /// <summary>
    ///  gestion des lieus
    /// </summary>
    public class PlaceManager
    {

        /// <summary>
        /// dictionnaire colonnes / valeur pour utilisation dans les maquettes
        /// </summary>
        public static DictionaryExtented GetDictionnaryForPdf(SqlConnection cnxOpen, string scriptPathSqlCommons, int structureId, string langCode, int eventId, int sessionId)
        {
            DictionaryExtented dicToReturn = new DictionaryExtented();
            if (cnxOpen.State == ConnectionState.Closed)
                cnxOpen.Open();
            string sql = FilesForSqlRequestsManager.GetScript(structureId, "Places\\", "getPlacesFromSessionsIds", scriptPathSqlCommons);

            sql = sql.Replace("[EVENTID]", eventId.ToString()); // au cas ou
            using (SqlCommand cmd = new SqlCommand())
            {
                cmd.Connection = cnxOpen;
                cmd.CommandText = sql;
                cmd.Parameters.Add(new SqlParameter("@plangCode", langCode));

                cmd.AddArrayParameters("sessionsids", new List<int> { sessionId });

                cmd.Parameters.Add(new SqlParameter("@pstructureId", structureId));
                using (SqlDataReader reader = cmd.ExecuteReader())
                {
                    if (reader.HasRows)
                    {
                        while (reader.Read())
                        {
                            //var columns = new List<string>();
                            var columns = Enumerable.Range(0, reader.FieldCount).Select(reader.GetName).ToList();
                            foreach (var col in columns)
                            {
                                dicToReturn.Add(col.ToUpper(), reader[col].ToString());
                            }

                            //structure.StructureId = int.Parse(reader["structure_id"].ToString());
                            //structure.StructureName = reader["name"].ToString();
                        }
                    }
                }

            }
            return dicToReturn;
        }


        public static List<PlaceDTO> getPlacesBySessionsList(SqlConnection cnxOpen, int structureId, List<int> listSessionId, string langCode, string scriptPathSqlCommons)
        {
            try
            {
                List<PlaceDTO> listReturn = new List<PlaceDTO>();
                string sql = FilesForSqlRequestsManager.GetScript(structureId, "Places\\", "getPlacesFromSessionsIds", scriptPathSqlCommons);

                if (cnxOpen.State == ConnectionState.Closed)
                    cnxOpen.Open();

                using (SqlCommand cmd = new SqlCommand())
                {
                    cmd.CommandText = sql;

                    cmd.Parameters.AddWithValue("plangCode", langCode);
                    cmd.AddArrayParameters("sessionsids", listSessionId);

                    cmd.Connection = cnxOpen;
                    using (SqlDataReader reader1 = cmd.ExecuteReader())
                    {
                        while (reader1.Read())
                        {
                            PlaceDTO pl = new PlaceDTO()
                            {
                                PlaceId = int.Parse(reader1["lieu_id"].ToString()),
                                PlaceName = reader1["lieu_nom"].ToString().Trim(),
                                Address_address1 = reader1["lieu_rue1"].ToString().Trim(),
                                Address_address2 = reader1["lieu_rue2"].ToString().Trim(),
                                Address_address3 = reader1["lieu_rue3"].ToString().Trim(),
                                Address_address4 = reader1["lieu_rue4"].ToString().Trim(),
                                Address_postal_code = reader1["lieu_cp"].ToString().Trim(),
                                Address_city = reader1["lieu_ville"].ToString().Trim(),
                                Address_country = reader1["lieu_pays"].ToString().Trim(),
                                Address_region = reader1["lieu_region"].ToString().Trim(),
                                AuthorizationCnc = reader1["numero_autorisation_cnc"].ToString().Trim()
                            };
                            listReturn.Add(pl);
                        }
                    }
                }
                return listReturn;
            }
            catch (Exception ex)
            {
                throw;
            }
            finally
            {
                if (cnxOpen.State == ConnectionState.Open)
                    cnxOpen.Close();
                //cnxOpen.Dispose();
            }
        }
    }
}
