﻿using Core.Themis.Libraries.DTO.Orders.Details;
using System.Collections.Generic;
using Microsoft.Data.SqlClient;

namespace Core.Themis.Libraries.BLL.OrderDetails.Interfaces
{
    public interface IDossierManager
    {
        int InstancieNumeroIdDossier(int structureId, int manif_id, int seance_id, SqlConnection cnxOpen, SqlTransaction? mysqlts = null);

        /// <summary>
        /// insert into dossier_xx
        /// </summary>
        /// <param name="structureId"></param>
        /// <param name="doss"></param>
        /// <returns></returns>
        int CreateSeatDossier(int structureId, DossierSeatDTO doss, SqlConnection cnxOpen, SqlTransaction? mysqlts = null);

        /// <summary>
        /// insert into dossiersvg_xx à partir du dossier_xx
        /// </summary>
        /// <param name="structureId"></param>
        /// <param name="doss"></param>
        /// <param name="typeOperation"></param>
        /// <returns></returns>
        int CreateSeatDossierSVG(int structureId, DossierSeatDTO doss, string typeOperation, SqlConnection cnxOpen, SqlTransaction? mysqlts = null);

        /// <summary>
        /// insert into histodossier
        /// </summary>
        /// <param name="structureId"></param>
        /// <param name="doss"></param>
        /// <returns></returns>
        int CreateSeatDossierHisto(int structureId, DossierSeatDTO doss, SqlConnection cnxOpen, SqlTransaction? mysqlts = null);

        /// <summary>
        /// passer le dossier en P + version + operateur
        /// </summary>
        /// <param name="structureId"></param>
        /// <param name="doss"></param>
        /// <param name="newVersion"></param>
        /// <param name="numPaiement"></param>
        /// <param name="operateurId"></param>
        /// <returns></returns>
        int UpdateDossierEnP(int structureId, DossierSeatDTO doss, int newVersion, long numPaiement, int operateurId, SqlConnection cnxOpen, SqlTransaction? mysqlts = null);

        bool UpdateDossierEnB(int structureId, DossierSeatDTO doss, int newVersion, int operateurId, bool printAtHome);

        List<DossierSeatDTO> GetDossierSeats(int structureId, int OrderId, int EventId);

        List<DossierProductDTO> GetDossierProducts(int structureId, int OrderId);

        public bool UpdateDossierC(int structureId, int dossierId, int EventId, string dossierC);

    }
}
