{
  "Logging": {
    "LogLevel": {
      "Default": "Information",
      "Microsoft": "Warning",
      "Microsoft.Hosting.Lifetime": "Information"
    },
    "Console": {
      "LogLevel": {
        "Default": "Error",
        "Microsoft": "Error",
        "Core.Themis.API.Customers.Controllers": "Trace"
      }
    },
    "File": {
      "Default": "Information",
      "Microsoft": "Warning",
      "Microsoft.Hosting.Lifetime": "Information"
    },
    "RodrigueFileLogger": {
      "Options": {
        "FolderPath": "D:\\LOGS\\Webservices\\API\\CORE_CUSTOMERS",
        "FilePath": "log_{structureid}_{date}.log"
      },
      "LogLevel": {
        "Default": "Error",
        "Microsoft": "Error",
        "Core.Themis.API.Customers.Controllers": "Information"
      }
    }
  },
  "AllowedHosts": "*",
  "ApiSecretKey": "secret401b09eab3c013d4ca54922bb802bec8fd5318192b0a75f201d8b3727429090fb337591abd3e44453b954555b7a0812e1081c39b740293f765eae731f5a65ed1",
  "ConnectionStrings": {
    "WsAdminDB": "Server=*************;Database=WSAdmin_test;Persist Security Info=True;User ID=SupportTest;Password=****************************************************************************************;MultipleActiveResultSets=true;TrustServerCertificate=True;",
    "GlobalOpinionDB": "Server=*************;Database=GlobalOpinions_DEV;Persist Security Info=True;User ID=SupportTest;Password=****************************************************************************************;MultipleActiveResultSets=true;TrustServerCertificate=True;",
    "WebLibraryDB": "Server=*************;Database=GlobalWebLibrary_DEV;Persist Security Info=True;User ID=SupportTest;Password=****************************************************************************************;MultipleActiveResultSets=true;TrustServerCertificate=True;",
    "Redis": "**************:6555,password=tS4pDJfVCN3Y0j5pvrdEBCpH0gXLZsZ/QCdN2zh6lJwoi+Va4eBUwtkZEUNYCcCl"
  },
  "Cache": {
    //Cache pour la liste des adhésions en secondes
    "AdhesionsAbsoluteExpiration": 10,
    "AdhesionsSlidingExpiration": 2
  },

  "WidgetCustomerUrl": "https://localhost:44354/",
  "PartnerRodSK": "ser5#E6V6Z#Mp-7",


  //charge les traductions filtrées par areas
  "TranslationsAreas": {
    "PassBook": [ "PassBook" ]
  },
  "ConfigIniPath": "\\\\Srv-paiement64\\customerfiles\\TEST\\[structureId]\\CONFIGSERVER\\config.ini.xml",
  "TypeRun": "TEST",
  "CryptoKey": "RodWebShop95",

  "PathForSqlScript": "..\\..\\..\\..\\Libraries\\Core.Themis.Libraries.Data\\Core.Themis.Librairies.Data\\LIBRARIES_SQLSCRIPTS\\{0}\\{1}.sql",
  //"PathScriptSqlCommonsLocal": "D:\\WORK\\LIBRARIES\\THEMIS_LIBRARIES_SQLSCRIPTS\\Themis.Libraries.SqlScripts\\Themis.Libraries.SqlScripts\\[directory\\][filename][.structureid].sql",

  "WsAdminConnectionCache": 300,
  "PathPdfSkipQueue": "\\\\*************\\emails\\TEST\\[structureId]\\pdf\\",
  "PathImagesLogos": "\\\\*************\\customerfiles\\TEST\\[structureId]\\paiement\\images\\logosMaquettes\\",

  "PathPdfForViewMaquette": "\\\\**************\\sites\\TEST\\ReprintPDF\\",
  "PathPdf": "\\\\*************\\emails\\TEST\\[structureId]\\pdf\\",
  "PathEmails": "\\\\*************\\emails\\TEST\\[structureId]\\envoisok\\",

  "MTicket": {
    "Google": {
      "AccountFile": "\\\\**************\\d\\CERTIFICATS\\GoogleWallet\\Local\\mtickettest-391714-ff238abc11e4.json",
      "IssuerId": 3388000000022255685
    },
    "Apple": {
      "AccountFile": "\\\\**************\\d\\CERTIFICATS\\AppleWallet\\Local\\AppleCredentialParam.json",
      "IconPath": "\\\\**************\\d\\API\\DEV\\CUSTOMERS\\1.0.0\\Icons"
    },
    "Samsung": {
      "AccountFile": "\\\\**************\\d\\CERTIFICATS\\SamsungWallet\\Local\\SamsungCredentialParam.json",
      "PartnerId": *******************,
      "CardId": "3gl7tatkio500"
    }
  }
}
  