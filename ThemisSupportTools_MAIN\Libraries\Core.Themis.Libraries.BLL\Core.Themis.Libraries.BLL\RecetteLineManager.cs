﻿using Core.Themis.Libraries.DTO;
using Core.Themis.Libraries.Utilities.Extensions;
using Core.Themis.Libraries.Utilities.ThemisSql;
using System;
using System.Collections.Generic;
using System.Data;
using Microsoft.Data.SqlClient;

namespace Core.Themis.Libraries.BLL
{
    public class RecetteLineManager
    {

        public static List<RecetteLineDTO> Create(SqlConnection cnxOpen, SqlTransaction mysqlts, int structureId, string scriptPathSqlCommons,
            int maquetteId,
             int operatorId, int orderId,
            int eventId,
    int sessionId, int dossierId, int seatId)
        {
            return null;
        }


        /// <summary>
        /// get ligne recettes entrees
        /// </summary>
        /// <param name="cnxOpen"></param>
        /// <param name="structureId"></param>
        /// <param name="langCode"></param>
        /// <param name="scriptPathSqlCommons"></param>
        /// <param name="eventId"></param>
        /// <param name="sessionId"></param>
        /// <param name="dossierId"></param>
        /// <param name="seatId"></param>
        /// <returns></returns>
        public static List<RecetteLineDTO> Load(SqlConnection cnxOpen, SqlTransaction mysqlts, int structureId, string scriptPathSqlCommons, int eventId,
            int sessionId, int dossierId, int seatId)
        {

            List<RecetteLineDTO> listRet = new List<RecetteLineDTO>();
            string sql = FilesForSqlRequestsManager.GetScript(structureId, "Recettes\\", "getRecettes", scriptPathSqlCommons);

            if (cnxOpen.State == ConnectionState.Closed)
                cnxOpen.Open();

            using (SqlCommand cmd = new SqlCommand())
            {
                cmd.Connection = cnxOpen;

                cmd.CommandText = sql;
                if (mysqlts != null)
                    cmd.Transaction = mysqlts;


                // cmd.AddArrayParameters("eventids", ListEvents);

                cmd.Parameters.Add(new SqlParameter("@peventId", eventId));
                cmd.Parameters.Add(new SqlParameter("@psessionId", sessionId));
                cmd.Parameters.Add(new SqlParameter("@pdossierId", dossierId));
                cmd.Parameters.Add(new SqlParameter("@pseatId", seatId));

                using (SqlDataReader reader = cmd.ExecuteReader())
                {
                    while (reader.Read())
                    {

                        RecetteLineDTO rec = new RecetteLineDTO()
                        {
                            EventId = int.Parse(reader["manifestation_id"].ToString()),
                            SessionId = int.Parse(reader["seance_id"].ToString()),
                            RecetteId = int.Parse(reader["entree_id"].ToString()),
                            SeatId = int.Parse(reader["entree_id"].ToString()),
                            TypeOperation = reader["recette_id"].ToString().Trim(),
                            NumBillet = int.Parse(reader["numbillet"].ToString()),
                            DateOperation = (DateTime)reader["date_operation"],
                            Externe = reader["externe"].ToString().Trim(),
                            Motif = reader["motif"].ToString().Trim(),
                        };

                        //////// code barre 
                        if (string.IsNullOrEmpty(rec.Externe) && !string.IsNullOrEmpty(rec.Motif))
                        {
                            // externe est nul et motif n'est pas nul => billet generé Rod, le code barre est dans Motif 
                            rec.CodeBarre = rec.Motif;
                        }
                        else
                        {
                            rec.CodeBarre = rec.Externe;
                        }


                        listRet.Add(rec);

                    }
                }


            }
            return listRet;


        }

        /// <summary>
        /// get ligne recettes produits
        /// </summary>
        /// <param name="cnxOpen"></param>
        /// <param name="structureId"></param>
        /// <param name="langCode"></param>
        /// <param name="scriptPathSqlCommons"></param>
        /// <param name="eventId"></param>
        /// <param name="sessionId"></param>
        /// <param name="dossierId"></param>
        /// <param name="seatId"></param>
        /// <returns></returns>
        public static List<RecetteLineDTO> LoadProducts(SqlConnection cnxOpen, int structureId, string langCode, string scriptPathSqlCommons, int orderId, int eventId,
            int sessionId, int dossierId, int produit_id, int basketLineId, List<int> dosProdDejaTraites)
        {

            if (dosProdDejaTraites.Count == 0)
            {
                dosProdDejaTraites.Add(0);
            }

            List<RecetteLineDTO> listRet = new List<RecetteLineDTO>();
            try

            {
                string sql = FilesForSqlRequestsManager.GetScript(structureId, "Recettes\\", "getRecettesProducts", scriptPathSqlCommons);

                if (cnxOpen.State == ConnectionState.Closed)
                    cnxOpen.Open();

                using (SqlCommand cmd = new SqlCommand())
                {
                    cmd.Connection = cnxOpen;

                    cmd.CommandText = sql;
                    // cmd.AddArrayParameters("eventids", ListEvents);
                    cmd.Parameters.Add(new SqlParameter("@plangCode", langCode));
                    cmd.Parameters.Add(new SqlParameter("@porderId", orderId));
                    cmd.Parameters.Add(new SqlParameter("@peventId", eventId));
                    cmd.Parameters.Add(new SqlParameter("@psessionId", sessionId));
                    cmd.Parameters.Add(new SqlParameter("@pdossierId", dossierId));
                    cmd.Parameters.Add(new SqlParameter("@pproductId", produit_id));
                    cmd.Parameters.Add(new SqlParameter("@pbasketLineId", basketLineId));
                    cmd.AddArrayParameters("listdosproddejatraites", dosProdDejaTraites);

                    using (SqlDataReader reader = cmd.ExecuteReader())
                    {
                        while (reader.Read())
                        {

                            RecetteLineDTO rec = new RecetteLineDTO()
                            {

                                SessionId = int.Parse(reader["seance_id"].ToString()),



                                NumBillet = int.Parse(reader["numbillet"].ToString()),


                                Externe = reader["externe"].ToString().Trim(),

                            };

                            //////// code barre 
                            if (string.IsNullOrEmpty(rec.Externe) && !string.IsNullOrEmpty(rec.Motif))
                            {
                                // externe est nul et motif n'est pas nul => billet generé Rod, le code barre est dans Motif 
                                rec.CodeBarre = rec.Motif;
                            }
                            else
                            {
                                rec.CodeBarre = rec.Externe;
                            }


                            listRet.Add(rec);

                        }
                    }


                }
                return listRet;


            }
            catch (Exception ex)
            {
                throw;
            }
        }


        /// <summary>
        /// renvoie et Met à jour le compteur NUMBILLET de la table RODRIGUE 
        /// </summary>
        /// <param name="cnx">connexion</param>
        /// <returns>NUMBILLET</returns>
        public static string SQLTR_GetNumBillet(int structureId, string scriptPathSqlCommons, SqlConnection cnxOpen, SqlTransaction mysqlts)
        {
            try

            {
                string numBillet = "";
                string sql = "UPDATE rodrigue SET numbillet = numbillet +1;" +
                    "SELECT CONVERT(varchar(50),max(NUMBILLET)) FROM rodrigue";


                if (cnxOpen.State == ConnectionState.Closed)
                    cnxOpen.Open();

                using (SqlCommand cmd = new SqlCommand())
                {
                    cmd.Connection = cnxOpen;
                    cmd.Transaction = mysqlts;
                    cmd.CommandText = sql;
                    numBillet = (string)cmd.ExecuteScalar();
                }
                return numBillet;

            }
            catch (Exception ex)
            {
                throw;
            }
        }

        public static int SQLTR_CreerLigneRecette(int structureId, string scriptPathSqlCommons, SqlConnection cnxOpen, SqlTransaction mysqlts,
            int maquette_id, int operateur_id, int commande_id, int manifestation_id, int seance_id, int dossier_id, int entree_id,
    string numbillet, string numexterne, string codebarre, string tag_rfid, string emetteur_identifiant, string typesupport

            )
        {
            //string sql = utilitaires2010.FilesForSqlRequestsManager.GET_SCRIPT(structureId.ToString("0000"), "recettes\\", "insertLigneRecetteBillet");
            string sql = FilesForSqlRequestsManager.GetScript(structureId, "Recettes\\", "insertLigneRecetteBillet", scriptPathSqlCommons);

            sql = sql.Replace("[MANIFID]", manifestation_id.ToString());

            if (cnxOpen.State == ConnectionState.Closed)
                cnxOpen.Open();

            int recetteId = 0;
            using (SqlCommand cmd = new SqlCommand())
            {

                cmd.Parameters.Add(new SqlParameter("@pmanifestation_id", manifestation_id));
                cmd.Parameters.Add(new SqlParameter("@poperateur_id", operateur_id));
                cmd.Parameters.Add(new SqlParameter("@pnumbillet", numbillet));

                cmd.Parameters.Add(new SqlParameter("@pcommande_id", commande_id));
                cmd.Parameters.Add(new SqlParameter("@pentree_id", entree_id));
                cmd.Parameters.Add(new SqlParameter("@pseance_id", seance_id));
                cmd.Parameters.Add(new SqlParameter("@pdossier_id", dossier_id));

                cmd.Parameters.Add(new SqlParameter("@ptypesupport", typesupport));

                cmd.Parameters.Add(new SqlParameter("@pnumexterne", numexterne));

                cmd.Parameters.Add(new SqlParameter("@pemetteur_identifiant", emetteur_identifiant));
                cmd.Parameters.Add(new SqlParameter("@pmaquette_id", maquette_id));

                cmd.Connection = cnxOpen;
                cmd.Transaction = mysqlts;
                cmd.CommandText = sql;
                recetteId = int.Parse(cmd.ExecuteScalar().ToString());
            }
            return recetteId;

        }


        private static string GenerateCryptNumero(int structure_id, string numbillet)
        {
            string szCryptnumero = "";
            Random MyRandom = new Random(); //nombre aléatoire de 0 à 11
            int rndcrypt = 0;
            if (structure_id == 283 || structure_id == 430)
            {
                rndcrypt = MyRandom.Next(0, 9);
                szCryptnumero = CRYPT_ScrambleNumber10(numbillet, rndcrypt).Trim();

                // ICI pour Nausicaa, le sCryptNumero ne doit pas commencer par 11 (=billets CE), Cf pb du 14/02/2013

                if (szCryptnumero.StartsWith("11"))
                {
                    return GenerateCryptNumero(structure_id, numbillet); // on regenere un numero
                }
                else
                {
                    return szCryptnumero;
                }
            }
            else
            {
                rndcrypt = MyRandom.Next(0, 11);
                string szOriginalNumero = structure_id.ToString("000") + "4" + numbillet;
                szCryptnumero = CRYPT_ScrambleNumber12(szOriginalNumero, rndcrypt).Trim();

                return szCryptnumero;

            }
        }


        public static string GenerateCryptNumeroModeRodrigue(int structure_id, string numbillet)
        {
            string szCryptnumero = "";
            Random MyRandom = new Random(); //nombre aléatoire de 0 à 11
            int rndcrypt = 0;

            rndcrypt = MyRandom.Next(1, 10);
            if (rndcrypt == 1)
            {
                rndcrypt = 2; // juste pour éviter que ça ne commence par 1, et donc que ça commence par 11 interdit chez Nausicaa
            }

            //string szOriginalNumero = "000000000" + numbillet;
            string szOriginalNumero = numbillet.PadLeft(9, '0');
            szCryptnumero = rndcrypt.ToString() + CRYPT_ScrambleNumber9(szOriginalNumero, rndcrypt).Trim();

            return szCryptnumero;
        }


        /// <summary>
        /// reprend le scramble billet rodrigue : num billet sur 9 -> index de la matrice utilisée + numero billet scramblé avec cette matrice sur 9
        /// </summary>
        /// <param name="structure_id"></param>
        /// <param name="numbillet"></param>
        /// <returns></returns>
        private static string GenerateCryptNumero10(int structure_id, string numbillet)
        {
            string szCryptnumero = "";
            Random MyRandom = new Random(); //nombre aléatoire de 0 à 11
            int rndcrypt = 0;
            if (structure_id == 283 || structure_id == 430)
            {
                rndcrypt = MyRandom.Next(0, 9);
                szCryptnumero = CRYPT_ScrambleNumber10(numbillet, rndcrypt).Trim();

                // ICI pour Nausicaa, le sCryptNumero ne doit pas commencer par 11 (=billets CE), Cf pb du 14/02/2013

                if (szCryptnumero.StartsWith("11"))
                {
                    return GenerateCryptNumero(structure_id, numbillet); // on regenere un numero
                }
                else
                {
                    return szCryptnumero;
                }
            }
            else
            {
                rndcrypt = MyRandom.Next(0, 11);
                string szOriginalNumero = structure_id.ToString("000") + "4" + numbillet;
                szCryptnumero = CRYPT_ScrambleNumber12(szOriginalNumero, rndcrypt).Trim();

                return szCryptnumero;

            }
        }

        /// <summary>
        /// scramble table mode Rodrigue (fonctions_communes, ligne 519)
        /// </summary>
        /// <returns></returns>
        private static string[] CRYPT_ScrambleInit9()
        {
            //' *** Init de la table de codage
            string[] scrambletable = new string[10];

            scrambletable[0] = "264731598";
            scrambletable[1] = "439617852";
            scrambletable[2] = "536294187";
            scrambletable[3] = "681235749";
            scrambletable[4] = "517423698";
            scrambletable[5] = "731854269";
            scrambletable[6] = "351649287";
            scrambletable[7] = "794528361";
            scrambletable[8] = "184527693";
            scrambletable[9] = "987432615";

            return scrambletable;
        }


        private static string[] CRYPT_ScrambleInit10()
        {
            //' *** Init de la table de codage
            string[] scrambletable = new string[10];

            scrambletable[0] = "264731598A";
            scrambletable[1] = "43961785A2";
            scrambletable[2] = "536294A187";
            scrambletable[3] = "68123A5749";
            scrambletable[4] = "5174A23698";
            scrambletable[5] = "731A854269";
            scrambletable[6] = "35A1649287";
            scrambletable[7] = "7A94528361";
            scrambletable[8] = "A184527693";
            scrambletable[9] = "98743A2615";

            return scrambletable;
        }

        private static string[] CRYPT_ScrambleInit12()
        {
            //' *** Init de la table de codage
            string[] scrambletable = new string[12];

            scrambletable[0] = "A2B6C4731598";
            scrambletable[1] = "4A3B9C617852";
            scrambletable[2] = "536C29AB4187";
            scrambletable[3] = "68123CB57A49";
            scrambletable[4] = "51AC7B423698";
            scrambletable[5] = "73185C42B6A9";
            scrambletable[6] = "3516492C8B7A";
            scrambletable[7] = "79B4CA528361";
            scrambletable[8] = "184527B6A9C3";
            scrambletable[9] = "98743CB261A5";
            scrambletable[10] = "2369851AC7B4";
            scrambletable[11] = "CB261A598743";
            return scrambletable;
        }

        private static string CRYPT_ScrambleNumber9(string nombre, int rndcrypt)
        {
            //' *** Scramble d'un code sur 16c
            string[] scrambletable;
            int valeur;
            string sz = "", szout = "", codetable;


            //' *** Génération d'un nombre sur 10c aléatoire en fonction d'une table de transcodage
            scrambletable = CRYPT_ScrambleInit9();

            sz = nombre;

            codetable = scrambletable[rndcrypt];

            for (int i = 0; i < codetable.Length; i++)
            {
                string strValeur = codetable.Substring(i, 1);
                valeur = Convert.ToInt32(strValeur);
                szout += sz.Substring(valeur - 1, 1);
            }
            return szout;
        }


        private static string CRYPT_ScrambleNumber10(string nombre, int rndcrypt)
        {
            //' *** Scramble d'un code sur 16c
            string[] scrambletable;
            int valeur;
            string sz = "", szout = "", codetable;


            //' *** Génération d'un nombre sur 10c aléatoire en fonction d'une table de transcodage
            scrambletable = CRYPT_ScrambleInit10();

            sz = nombre;

            codetable = scrambletable[rndcrypt];

            for (int i = 0; i < codetable.Length; i++)
            {
                string strValeur = codetable.Substring(i, 1);
                switch (strValeur)
                {
                    case "A":
                        valeur = 10;
                        szout += sz.Substring(valeur - 1, 1);
                        break;
                    default:
                        valeur = Convert.ToInt32(strValeur);
                        szout += sz.Substring(valeur - 1, 1);
                        break;
                }
            }
            return szout;
        }


        private static string CRYPT_ScrambleNumber12(string nombre, int rndcrypt)
        {
            //' *** Scramble d'un code sur 16c
            string[] scrambletable;
            int valeur;
            string sz = "", szout = "", codetable;
            //string ValidUniqueNumber = "";

            //' *** Génération d'un nombre sur 10c aléatoire en fonction d'une table de transcodage
            scrambletable = CRYPT_ScrambleInit12();
            sz = nombre;
            codetable = scrambletable[rndcrypt];

            for (int i = 0; i < codetable.Length; i++)
            {
                string strValeur = codetable.Substring(i, 1);
                switch (strValeur)
                {
                    case "A":
                        valeur = 10;
                        szout += sz.Substring(valeur - 1, 1);
                        break;
                    case "B":
                        valeur = 11;
                        szout += sz.Substring(valeur - 1, 1);
                        break;
                    case "C":
                        valeur = 12;
                        szout += sz.Substring(valeur - 1, 1);
                        break;
                    default:
                        valeur = Convert.ToInt32(strValeur);
                        szout += sz.Substring(valeur - 1, 1);
                        break;
                }
            }
            szout = rndcrypt.ToString("00") + szout;
            return szout;
        }


        private string CRYPT_ScrambleNumber12xx(string nombre)
        {
            //' *** Scramble d'un code sur 16c
            string[] scrambletable;
            int valeur;
            string sz = "", szout = "", codetable;

            //' *** Génération d'un nombre sur 10c aléatoire en fonction d'une table de transcodage
            scrambletable = CRYPT_ScrambleInit12();

            sz = nombre;
            Random MyRandom = new Random(); //nombre aléatoire de 0 à 11
            int rndcrypt = 0;
            rndcrypt = MyRandom.Next(11);

            codetable = scrambletable[rndcrypt];

            for (int i = 0; i < codetable.Length; i++)
            {
                string strValeur = codetable.Substring(i, 1);
                valeur = Convert.ToInt32(strValeur, 16);
                if (sz.Length >= valeur && valeur > 0)
                {
                    szout += sz.Substring(valeur - 1, 1);
                }
            }
            szout = rndcrypt.ToString("00") + szout;

            return szout;
        }

        #region CNC

        public static int PushCNCrecette(SqlConnection cnx, int structureId, string scriptPathSqlCommons, int recetteId, int seanceId)
        {
            int result = 0;
            string sql = FilesForSqlRequestsManager.GetScript(structureId, "Recettes\\", "pushCNCrecette", scriptPathSqlCommons);

            //string sql = utilitaires2010.FilesForSqlRequestsManager.GET_SCRIPT(structureId.ToString("0000"), "recettes\\", "pushCNCrecette");
            sql = sql.Replace("[RECETTEID]", recetteId.ToString());
            sql = sql.Replace("[SEANCEID]", seanceId.ToString());

            // logger.Debug("sql de PushCNCrecette=" + sql);


            using (SqlCommand cmd = new SqlCommand())
            {
                cmd.CommandText = sql;
                cmd.Connection = cnx;
                using (SqlDataReader rdr = cmd.ExecuteReader())
                {
                    while (rdr.Read())
                    {
                        result = int.Parse(rdr["compteur"].ToString());


                    }
                }
            }

            return result;
        }

        #endregion
    }
}
