DECLARE @partnerIdProviderType_PasscultureOffreCollective INT = 1
DECLARE @partnerTableRodrigueType_Commande INT = 1

SELECT DISTINCT c.commande_id AS OrderId, cl.identite_id AS identityId, i.identite_nom AS identityName, pip.partner_ids_provider_id
FROM commande c
INNER JOIN commande_ligne cl ON c.commande_id = cl.commande_id
INNER JOIN identite i ON cl.identite_id = i.identite_id
INNER JOIN seance s ON cl.seance_id = s.seance_Id
LEFT JOIN partner_ids_provider pip ON c.commande_id = pip.rodrigue_id 
	AND pip.partner_ids_provider_type_id = @partnerIdProviderType_PasscultureOffreCollective
	AND pip.partner_table_rodrigue_type_id = @partnerTableRodrigueType_Commande
WHERE cl.type_ligne ='DOS'
AND s.seance_date_deb > GETDATE()
AND 'R' = ALL(SELECT clp.Etat FROM Commande_Ligne_comp clp WHERE cl.commande_ligne_id = clp.commande_ligne_id)
AND (
	NOT EXISTS (SELECT(1) dpr
				FROM dossier_produit dp
				INNER JOIN dossier_produit_reservation dpr ON dpr.dos_prod_id = dp.dos_prod_id
				WHERE dp.commande_id = c.commande_id)
)
AND partner_ids_provider_id IS NULL
ORDER BY c.commande_id DESC