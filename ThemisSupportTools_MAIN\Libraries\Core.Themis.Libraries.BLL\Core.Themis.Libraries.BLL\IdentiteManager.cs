﻿using Core.Themis.Libraries.BLL.Helpers.Widgets;
using Core.Themis.Libraries.BLL.Interfaces;
using Core.Themis.Libraries.Data.Entities.Open.Identite;
using Core.Themis.Libraries.Data.Repositories.Open.Adhesion.Interface;
using Core.Themis.Libraries.Data.Repositories.Open.Identities.Interfaces;
using Core.Themis.Libraries.Data.Repositories.WebTracing.Interfaces;
using Core.Themis.Libraries.DTO.adhesion_offres;
using Core.Themis.Libraries.DTO.Identity;
using Core.Themis.Libraries.Utilities.Crypto;
using Core.Themis.Libraries.Utilities.CustomErrors;
using Core.Themis.Libraries.Utilities.Helpers;
using Core.Themis.Libraries.Utilities.Helpers.Interfaces;
using Core.Themis.Libraries.Utilities.Logging;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.Configuration;
using System;
using System.Collections.Generic;
using System.Data;
using Microsoft.Data.SqlClient;
using System.Linq;

namespace Core.Themis.Libraries.BLL
{
    public class IdentiteManager : IIdentiteManager
    {

        public const string WhiteCardPassword = "don't check it";

        private static readonly RodrigueNLogger _rodrigueNLogger = new();


        private readonly IMemoryCache _memoryCache;
        private readonly IIdentityRepository _identityRepository;
        private readonly IAdhesionCatalogRepository _adhesionCatalogRepository;
        private readonly IBasketRepository _basketRepository;
        private readonly IConfiguration _configuration;
        private readonly IRodrigueConfigIniDictionnary _rodrigueConfigIniDictionnary;

        public IdentiteManager(
            IMemoryCache memoryCache,
            IIdentityRepository identityRepository,
            IAdhesionCatalogRepository adhesionCatalogRepository,
            IBasketRepository basketRepository,
            IConfiguration configuration,
            IRodrigueConfigIniDictionnary rodrigueConfigIniDictionnary)
        {
            _memoryCache = memoryCache;
            _identityRepository = identityRepository;
            _adhesionCatalogRepository = adhesionCatalogRepository;
            _basketRepository = basketRepository;
            _configuration = configuration;
            _rodrigueConfigIniDictionnary = rodrigueConfigIniDictionnary;
        }


        private static readonly RodrigueNLogger RodrigueLogger = new();


        /// get identity via specifique partenaires (cf unidy)
        public IdentityDTO GetViaExterne(int structureId, string partner, string externeId)
        {
            try
            {
                return _identityRepository.GetSpecif(structureId, partner, externeId);
            }
            catch (Exception ex)
            {
                //logger
                RodrigueLogger.Error(structureId, $"Load({structureId}): {ex.Message} {ex.StackTrace}");

                throw;
            }
        }

        /// <summary>
        /// get from id ou adherent
        /// </summary>
        /// <param name="structureId"></param>
        /// <param name="identiteId"></param>
        /// <param name="adherentId"></param>
        /// <param name="ipostalTelEmail"></param>
        /// <param name="langIso"></param>
        /// <param name="salthash"></param>
        /// <returns></returns>
        private IdentityDTO Get(int structureId, int identiteId, string adherentId, string langIso, string salthash)
        {
            int ipostalTelEmail = _rodrigueConfigIniDictionnary.GetIndexPostalTelEmail(structureId);

            List<IdentityDTO> listIdent = Load(structureId, new List<int>() { identiteId }, adherentId, ipostalTelEmail, langIso, salthash);
            if (listIdent.Count != 1)
                return null;
            else
            {
                var ident = listIdent[0];
                return ident;

            }

        }


        /// <summary>
        /// get from email, pas de verif password
        /// </summary>
        /// <param name="structureId"></param>
        /// <param name="identiteId"></param>
        /// <param name="adherentId"></param>
        /// <param name="ipostalTelEmail"></param>
        /// <param name="langIso"></param>
        /// <param name="salthash"></param>
        /// <returns></returns>
        private IdentityDTO Get(int structureId, string email, string langIso, string salthash)
        {

            int ipostalTelEmail = _rodrigueConfigIniDictionnary.GetIndexPostalTelEmail(structureId);

            var listIdes = _identityRepository.Load(structureId, new List<string>() { email }, ipostalTelEmail, langIso, salthash).Where(i => i.FicheSupprimer != "O").ToList();

            if (listIdes.Count != 1)
            {
                return null;
            }
            listIdes.FirstOrDefault().PostalTelEmail = ipostalTelEmail;
            return listIdes.FirstOrDefault();
        }


        /// <summary>
        /// get from email ou id ou adherentId, check password
        /// </summary>
        /// <param name="structureId"></param>
        /// <param name="identiteId"></param>
        /// <param name="adherentId"></param>
        /// <param name="ipostalTelEmail"></param>
        /// <param name="langIso"></param>
        /// <param name="salthash"></param>
        /// <returns></returns>
        public IdentityDTO Get(int structureId, string identifiant, string langIso, string salthash, string clearPassWord = WhiteCardPassword)
        {

            int LoginIdentiteId = 0;
            int.TryParse(identifiant, out LoginIdentiteId);

            return Get(structureId, identifiant, LoginIdentiteId, identifiant, langIso, salthash, clearPassWord);

        }

  
        public IdentityDTO Get(int structureId, string email, int identiteId, string adherentId, string langIso, string salthash, string clearPassWord = WhiteCardPassword)
        {
            IdentityDTO ident = new();
            if (string.IsNullOrEmpty(email) || !email.Contains('@'))
            {
                if (string.IsNullOrEmpty(adherentId) || long.TryParse(adherentId, out long iadherentId))
                {
                    ident = Get(structureId, identiteId, adherentId, langIso, salthash);
                }
                else
                {
                    return null;
                }

                
            }
            else
            {
                ident = Get(structureId, email, langIso, salthash);
            }

            if (ident == null)
                return null;


            int ipostalTelEmail = _rodrigueConfigIniDictionnary.GetIndexPostalTelEmail(structureId);
            ident.PostalTelEmail = ipostalTelEmail;


            if (clearPassWord == WhiteCardPassword)
            {
                return ident;
            }
            else
            {

                string calcFromClearPass = IdentiteCrypt.EncryptionPassword(clearPassWord);
                if (ident.Password != calcFromClearPass)
                {
                    return null;
                }
                else
                {
                    return ident;
                }
            }
        }





        /// <summary>
        /// Load from ids ou adherent
        /// </summary>
        /// <param name="structureId"></param>
        /// <param name="listIdentiteId"></param>
        /// <param name="adherentId"></param>
        /// <param name="ipostalTelEmail"></param>
        /// <param name="langIso"></param>
        /// <param name="salthash"></param>
        /// <returns></returns>

        public List<IdentityDTO> Load(int structureId, List<int> listIdentiteId, string adherentId, int ipostalTelEmail, string langIso, string salthash)
        {
            try
            {
                RodrigueLogger.Trace(structureId, $"Load({structureId},{string.Join(";", listIdentiteId)},'{adherentId}',{langIso},{ipostalTelEmail})...");

                return _identityRepository.Load(structureId, listIdentiteId, adherentId, ipostalTelEmail, langIso, salthash).Where(i => i.FicheSupprimer != "O").ToList();
            }
            catch
            {
                throw;
            }
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="structureId"></param>
        /// <param name="emailToCheck"></param>
        /// <returns>true = email existe sur un compte actif</returns>
        public bool ThisEmailExisteDeja(int structureId, string emailToCheck)
        {

            int ipostalTelEmail = _rodrigueConfigIniDictionnary.GetIndexPostalTelEmail(structureId);

            List<IdentiteEntity> identsThisEmail = new();

            switch (ipostalTelEmail)
            {
                case 1:
                    identsThisEmail = _identityRepository.FindBy(i => i.PostalTel1 == emailToCheck, structureId).ToList(); break;
                case 2:
                    identsThisEmail = _identityRepository.FindBy(i => i.PostalTel2 == emailToCheck, structureId).ToList(); break;
                case 3:
                    identsThisEmail = _identityRepository.FindBy(i => i.PostalTel3 == emailToCheck, structureId).ToList(); break;
                case 4:
                    identsThisEmail = _identityRepository.FindBy(i => i.PostalTel4 == emailToCheck, structureId).ToList(); break;
                case 5:
                    identsThisEmail = _identityRepository.FindBy(i => i.PostalTel5 == emailToCheck, structureId).ToList(); break;
                case 6:
                    identsThisEmail = _identityRepository.FindBy(i => i.PostalTel6 == emailToCheck, structureId).ToList(); break;
                case 7:
                    identsThisEmail = _identityRepository.FindBy(i => i.PostalTel6 == emailToCheck, structureId).ToList(); break;
                default:
                    identsThisEmail = _identityRepository.FindBy(i => i.PostalTel5 == emailToCheck, structureId).ToList(); break;

            }

            if (identsThisEmail == null || identsThisEmail.Count == 0)
                return false;
            else
            {

                var ids = identsThisEmail.Where(i => i.FicheSupprimer == "N").Count();
                return ids > 0;


                //return true;
            }

        }


        /// <summary>
        /// Ajout d'une nouvelle identite
        /// </summary>
        /// <param name="structureId"></param>
        /// <param name="identity"></param>
        /// <param name="checkEmailUnicity"></param>
        /// <returns>Retourne la nouvelle identite id qui vient d'être créée</returns>
        public int AddIdentity(int structureId, IdentityDTO identity, bool? pcheckEmailUnicity)
        {
            try
            {
                var configIniXml = _rodrigueConfigIniDictionnary.GetDictionaryFromCache(structureId);
                fillNumerosColonnesEmailEtTels(structureId, configIniXml, ref identity);

                bool checkEmailUnicity = needToCheckEmailUnicity(configIniXml, pcheckEmailUnicity);

                if (checkEmailUnicity && !string.IsNullOrEmpty(identity.Email))
                {
                    if (ThisEmailExisteDeja(structureId, identity.Email))
                    {
                        Exception ex = new IdentityConflitEmail();
                        throw ex;
                    }
                }

                return _identityRepository.AddIdentity(structureId, identity, checkEmailUnicity);
            }
            catch (Exception ex)
            {
                //logger
                RodrigueLogger.Error(structureId, $"AddIdentity({structureId}): {ex.Message} {ex.StackTrace}");
                throw;
            }
        }

        /// <summary>
        /// maj : /!\ b64password doit être une chaine b64
        /// </summary>
        /// <param name="structureId"></param>
        /// <param name="identiteId"></param>
        /// <param name="b64password"></param>
        public int Update(int structureId, IdentityDTOUpdate identity, bool? pcheckEmailUnicity)
        {
            try
            {
                if (identity.IdentiteId == 0)
                {
                    Exception exception = new Exception("update identite, identity.identiteId need to be set");
                }

                var configIniXml = _rodrigueConfigIniDictionnary.GetDictionaryFromCache(structureId);
                fillNumerosColonnesEmailEtTels(structureId, configIniXml, ref identity);

                bool checkEmailUnicity = needToCheckEmailUnicity(configIniXml, pcheckEmailUnicity);

                if (checkEmailUnicity && identity.Email != "unchanged")
                {
                    IdentityDTO identCheckEmail = Get(structureId, identity.Email, "fr", "");

                    if (identCheckEmail != null && identCheckEmail.IdentiteId != identity.IdentiteId)
                    {
                        Exception ex = new IdentityConflitEmail();
                        throw ex;
                        //return Problem($"this email {identity.Email} already exists (id {identCheckEmail.IdentiteId})", null, StatusCodes.Status409Conflict);
                    }
                }

                return _identityRepository.Update(structureId, identity, checkEmailUnicity);
            }
            catch (Exception ex)
            {
                RodrigueLogger.Error(structureId, $"Update({structureId}): {ex.Message} {ex.StackTrace}");
                throw;
            }
        }



        /// <summary>
        /// maj du password : /!\ b64password doit être une chaine b64
        /// </summary>
        /// <param name="structureId"></param>
        /// <param name="identiteId"></param>
        /// <param name="b64password"></param>
        public int UpdatePassword(int structureId, int identiteId, string b64password)
        {
            try
            {
                if (!Encryption64.IsBase64String(b64password))
                {
                    Exception ex = new ArgumentException($"UpdatePassword, b64password received is not a correct b64 string :(");
                    throw ex;
                }


                return _identityRepository.UpdatePassword(structureId, identiteId, b64password);
            }
            catch
            {
                throw;
            }

        }


        /// <summary>
        /// UPDATE identite SET montant_debit=montant_debit + amount
        /// </summary>
        /// <param name="structureId"></param>
        /// <param name="identiteId"></param>
        /// <param name="amount"></param>
        /// <returns></returns>
        public int UpdateDebitAmount(int structureId, int identiteId, int amount, SqlConnection cnxOpen, SqlTransaction? sqlTransaction = null)
        {
            try
            {
                return _identityRepository.UpdateDebitAmount(structureId, identiteId, amount, cnxOpen, sqlTransaction);
            }
            catch
            {
                throw;
            }

        }





        #region gestion des consumers

        /// <summary>
        /// Charge la liste des consommateurs pour une identite Id donné
        /// </summary>
        /// <param name="structureId"></param>
        /// <param name="identiteId">identite Id</param>
        /// <param name="ipostalTelEmail">numéro du postal tel de l'adresse email</param>
        /// <param name="salthash">hash du crypto key de Rodrigue</param>
        /// <returns>Retourne une liste de consommateurs reliés à l'indentite</returns>
        public List<IdentityDTO> GetConsumers(int structureId, int identiteId, int ipostalTelEmail = 0, string salthash = "")
        {
            try
            {
                return _identityRepository.GetConsumersOfIdentityId(structureId, identiteId, ipostalTelEmail, salthash);
            }
            catch (Exception ex)
            {
                RodrigueLogger.Error(structureId, $"GetConsumers({structureId},{identiteId},{ipostalTelEmail}): {ex.Message} {ex.StackTrace}");
                throw;
            }
        }

        public int AddConsumer(int structureId, int identiteMaitre, IdentityDTO consumer)
        {
            try
            {

                bool checkEmailUnicity = true;

                var configIniXml = _rodrigueConfigIniDictionnary.GetDictionaryFromCache(structureId);

                if (configIniXml.ContainsKey("CREATEPROFILLINKMAILUNICITY") && configIniXml["CREATEPROFILLINKMAILUNICITY"] != null && configIniXml["CREATEPROFILLINKMAILUNICITY"] != "")
                    checkEmailUnicity = !(int.Parse(configIniXml["CREATEPROFILLINKMAILUNICITY"].ToString()) == -1);

                if (string.IsNullOrEmpty(consumer.Email))
                    checkEmailUnicity = false;



                int ipostalTelEmail = _rodrigueConfigIniDictionnary.GetIndexPostalTelEmail(structureId);

                int ipostalTelLibEmail = 0;
                if (configIniXml.ContainsKey("VARIABLESTYPE_EMAIL") && configIniXml["VARIABLESTYPE_EMAIL"] != null && configIniXml["VARIABLESTYPE_EMAIL"] != "")
                    ipostalTelLibEmail = int.Parse(configIniXml["VARIABLESTYPE_EMAIL"].ToString());


                int filiereId = 0;
                if (configIniXml.ContainsKey("CREATEPROFILWEBFILIEREID") && configIniXml["CREATEPROFILWEBFILIEREID"] != null && configIniXml["CREATEPROFILWEBFILIEREID"] != "")
                    filiereId = int.Parse(configIniXml["CREATEPROFILWEBFILIEREID"].ToString());

                int operateurId = 0;
                if (configIniXml.ContainsKey("PAIEMENTWEBOPERATORID") && configIniXml["PAIEMENTWEBOPERATORID"] != null && configIniXml["PAIEMENTWEBOPERATORID"] != "")
                    operateurId = int.Parse(configIniXml["PAIEMENTWEBOPERATORID"].ToString());

                consumer.OperateurId = operateurId;
                consumer.FiliereId = filiereId;

                WidgetUtilitiesHelper.SetValuesOfIdentity(structureId, ref consumer, "Portable", consumer.MobilePhoneNumber ?? "");
                WidgetUtilitiesHelper.SetValuesOfIdentity(structureId, ref consumer, "Telephone", consumer.PhoneNumber ?? "");
                WidgetUtilitiesHelper.SetValuesOfIdentity(structureId, ref consumer, "Fax", consumer.FaxPhoneNumber ?? "");
                WidgetUtilitiesHelper.SetValuesOfIdentity(structureId, ref consumer, "Email", consumer.Email);



                return _identityRepository.AddConsumer(structureId, identiteMaitre, ipostalTelEmail, consumer, checkEmailUnicity);
            }
            catch (Exception ex)
            {
                //logger
                RodrigueLogger.Error(structureId, $"AddConsumer({structureId},{identiteMaitre}): {ex.Message} {ex.StackTrace}");
                throw;
            }
        }

        /// <summary>
        /// Recherche un consommateur grâce à un combo (IDENTITE ID + EMAIL ou EMAIL + INITIALES ou IDENTTIE ID + INITIALES) et ajoute à l'identité connecté
        /// </summary>
        /// <param name="structureId"></param>
        /// <param name="identityIdConnected"></param>
        /// <param name="identityId"></param>
        /// <param name="initial"></param>
        /// <param name="email"></param>
        /// <param name="ipostalTelEmail"></param>
        /// <returns></returns>
        public int SearchAndLinkConsumer(int structureId, int identityIdConnected, int identityId, string initial, string email, string adherentId)
        {
            try
            {

                var configIniXml = _rodrigueConfigIniDictionnary.GetDictionaryFromCache(structureId);

                int ipostalTelEmail = _rodrigueConfigIniDictionnary.GetIndexPostalTelEmail(structureId);

                int ipostalTelLibEmail = 0;
                if (configIniXml.ContainsKey("VARIABLESTYPE_EMAIL") && configIniXml["VARIABLESTYPE_EMAIL"] != null && configIniXml["VARIABLESTYPE_EMAIL"] != "")
                    ipostalTelLibEmail = int.Parse(configIniXml["VARIABLESTYPE_EMAIL"].ToString());


                return _identityRepository.SearchAndLinkConsumer(structureId, identityIdConnected, identityId, initial, email, ipostalTelEmail, adherentId);
            }
            catch (Exception ex)
            {
                //logger
                RodrigueLogger.Error(structureId, $"SearchConsumer({structureId},{identityId}): {ex.Message} {ex.StackTrace}");
                throw;
            }
        }

        /// <summary>
        /// Ajout le consommateur au pool de l'identite connecté
        /// </summary>
        /// <param name="structureId"></param>
        /// <param name="identityIdConnected"></param>
        /// <param name="identityId"></param>
        /// <returns></returns>
        public int LinkConsumer(int structureId, int identityIdConnected, int identityId)
        {
            try
            {
                return _identityRepository.LinkConsumer(structureId, identityIdConnected, identityId);
            }
            catch (Exception ex)
            {
                //logger
                RodrigueLogger.Error(structureId, $"LinkConsumer({structureId},{identityId}): {ex.Message} {ex.StackTrace}");
                throw;
            }
        }

        #endregion

        /// <summary>
        /// Ajout une info complementaire sur une identite donnée (identite_complement)
        /// </summary>
        /// <param name="structureId"></param>
        /// <param name="identiteId"></param>
        /// <param name="langueId"></param>
        /// <param name="comment"></param>
        /// <param name="sex"></param>
        /// <returns></returns>
        public int AddIdentiteComplement(int structureId, int identiteId, int langueId = 0, string comment = "", string sex = "")
        {
            try
            {
                return _identityRepository.AddIdentiteComplement(structureId, identiteId, langueId, comment, sex);
            }
            catch (Exception ex)
            {
                RodrigueLogger.Error(structureId, $"AddIdentiteComplement({structureId}): {ex.Message} {ex.StackTrace}");
                throw;
            }
        }

        /// <summary>
        /// Ajout une info complementaire WEB sur une identite donnée (identite_compl_web)
        /// </summary>
        /// <returns></returns>
        public int AddIdentiteComplementWeb(int structureId, int identiteId, string login = "", string password = "", bool supprimer = false, bool doitchanger = false)
        {
            try
            {
                return _identityRepository.AddIdentiteComplementWeb(structureId, identiteId, login, password, supprimer, doitchanger);
            }
            catch (Exception ex)
            {
                RodrigueLogger.Error(structureId, $"AddIdentiteComplement({structureId}): {ex.Message} {ex.StackTrace}");
                throw;
            }
        }

        /// <summary>
        /// retourne l'identite via le password de complement web (cf unidy-sv98)
        /// </summary>
        /// <param name="structureId"></param>
        /// <param name="passwordComplementWeb"></param>
        /// <returns></returns>
        public List<int> GetIdIdentiteComplementWebPassword(int structureId, string passwordComplementWeb)
        {
            try
            {
                return _identityRepository.GetIdIdentiteComplementWebPassword(structureId, passwordComplementWeb);
            }
            catch (Exception ex)
            {
                //logger
                RodrigueLogger.Error(structureId, $"AddIdentiteComplement({structureId}): {ex.Message} {ex.StackTrace}");
                throw;
            }
        }

        /// <summary>
        /// Ajout identite_compl_web
        /// </summary>
        /// <param name="structureId"></param>
        /// <param name="identiteId"></param>
        /// <param name="complWeb"></param>
        /// <returns></returns>
        public int AddComplementWebOnIdentite(int structureId, int identiteId, IdentityDTO.IdentiteComplementWebcl complWeb)
        {
            try
            {
                return _identityRepository.AddComplementWebOnIdentite(structureId, identiteId, complWeb);
            }
            catch (Exception ex)
            {
                //logger
                RodrigueLogger.Error(structureId, $"AddComplementWebOnIdentite({structureId}): {ex.Message} {ex.StackTrace}");
                throw;
            }
        }

        /// <summary>
        /// Ajout une infocomp sur une identite donnée
        /// </summary>
        /// <param name="structureId"></param>
        /// <param name="scriptPathSqlCommons"></param>
        /// <param name="openCnx"></param>
        /// <param name="identiteId"></param>
        /// <param name="infoComp"></param>
        /// <returns></returns>
        public int AddInfoCompOnIdentite(int structureId, int identiteId, InfoCompDTO infoComp)
        {
            try
            {
                return _identityRepository.AddInfoCompOnIdentite(structureId, identiteId, infoComp);
            }
            catch (Exception ex)
            {
                //logger
                RodrigueLogger.Error(structureId, $"AddInfoCompOnIdentite({structureId}): {ex.Message} {ex.StackTrace}");
                throw;
            }
        }

        /// <summary>
        /// dictionnaire colonnes / valeur pour utilisation dans les maquettes
        /// </summary>
        /// <param name="structureId"></param>
        /// <param name="langCode"></param>
        /// <param name="identiteId"></param>
        /// <param name="orderId"></param>
        /// <param name="eventId"></param>
        /// <param name="sessionId"></param>
        /// <param name="dossierId"></param>
        /// <param name="seatId"></param>
        /// <returns></returns>
        public DictionaryExtented GetDictionnaryForPdf(int structureId, string langCode, int identiteId, int orderId, int eventId, int sessionId, int dossierId, int seatId)
        {
            try
            {
                return _identityRepository.GetDictionnaryForPdf(structureId, langCode, identiteId, orderId, eventId, sessionId, dossierId, seatId);
            }
            catch
            {
                throw;
            }
        }


        public List<IdentityDTO> LoadConsumers(int structureId, int identityId)
        {
            List<IdentityDTO> consumers = new();
            try
            {
                string cryptoKey = _configuration["CryptoKey"]!;
                var configIniXml = _rodrigueConfigIniDictionnary.GetDictionaryFromCache(structureId);

                int ipostalTelEmail = _rodrigueConfigIniDictionnary.GetIndexPostalTelEmail(structureId);

                consumers = _identityRepository.GetConsumersOfIdentityId(structureId, identityId, ipostalTelEmail, cryptoKey);

                List<int> myListConsumersId = consumers.Select(c => c.IdentiteId).ToList();
                if (myListConsumersId.Count > 0)
                {
                    Dictionary<int, List<int>> adhEnCours = _basketRepository.CheckBasketAdhesions(structureId, myListConsumersId);

                    foreach (var consumer in consumers)
                    {
                        consumer.AdhesionCatalogs = _adhesionCatalogRepository.GetAdhesionCatalogsOfIdentity(structureId, consumer.IdentiteId);

                        /// adhesion en cours de cette identité
                        List<int> catalogEnCours = adhEnCours.Where(i => i.Key == consumer.IdentiteId).SelectMany(a => a.Value).ToList();
                        foreach (int catId in catalogEnCours)
                        {
                            consumer.AdhesionEnCours.Add(new AdhesionCatalogDTO()
                            {
                                AdhesionCatalogId = catId
                            });
                        }
                    }
                }

                return consumers;

            }
            catch (IdentityIdNotFoundException ex)
            {
                _rodrigueNLogger.Trace(structureId, $"IdentityIdNotFoundException Message = {ex.Message} - statTrace = {ex.StackTrace}");
                throw;
            }
            catch (Exception ex)
            {
                _rodrigueNLogger.Trace(structureId, $"Message = {ex.Message} - stackTrace = {ex.StackTrace}");
                throw;
            }
        }



        /// <summary>
        /// Renseigne dans l'objet identité les propriétés qui sont dans le config.ini (EMAIL, TYPE_EMAIL, ...)
        /// </summary>
        /// <param name="structureId"></param>
        /// <param name="configIniXml"></param>
        /// <param name="identity"></param>
        private void fillNumerosColonnesEmailEtTels(int structureId, Dictionary<string, string> configIniXml, ref IdentityDTOUpdate identity)
        {


            if (configIniXml.ContainsKey("VARIABLESEMAIL") && configIniXml["VARIABLESEMAIL"] != null && configIniXml["VARIABLESEMAIL"] != "")
            {
                identity.PostalTelEmail = int.Parse(configIniXml["VARIABLESEMAIL"].ToString());
            }

            if (configIniXml.ContainsKey("VARIABLESTYPE_EMAIL") && configIniXml["VARIABLESTYPE_EMAIL"] != null && configIniXml["VARIABLESTYPE_EMAIL"] != "")
            {
                identity.PostalTelLibEmail = int.Parse(configIniXml["VARIABLESTYPE_EMAIL"].ToString());
            }

            if (configIniXml.ContainsKey("VARIABLESTELEPHONE") && configIniXml["VARIABLESTELEPHONE"] != null && configIniXml["VARIABLESTELEPHONE"] != "")
            {
                identity.PostalTelPhoneNumber = int.Parse(configIniXml["VARIABLESTELEPHONE"].ToString());
            }

            if (configIniXml.ContainsKey("VARIABLESTYPE_TELEPHONE") && configIniXml["VARIABLESTYPE_TELEPHONE"] != null && configIniXml["VARIABLESTYPE_TELEPHONE"] != "")
            {
                identity.PostalTelLibPhoneNumber = int.Parse(configIniXml["VARIABLESTYPE_TELEPHONE"].ToString());
            }

            if (configIniXml.ContainsKey("VARIABLESFAX") && configIniXml["VARIABLESFAX"] != null && configIniXml["VARIABLESFAX"] != "")
            {
                identity.PostalTelFax = int.Parse(configIniXml["VARIABLESFAX"].ToString());
            }

            if (configIniXml.ContainsKey("VARIABLESTYPE_FAX") && configIniXml["VARIABLESTYPE_FAX"] != null && configIniXml["VARIABLESTYPE_FAX"] != "")
            {
                identity.PostalTelLibFax = int.Parse(configIniXml["VARIABLESTYPE_FAX"].ToString());
            }

            if (configIniXml.ContainsKey("VARIABLESPORTABLE") && configIniXml["VARIABLESPORTABLE"] != null && configIniXml["VARIABLESPORTABLE"] != "")
            {
                identity.PostalTelMobilePhone = int.Parse(configIniXml["VARIABLESPORTABLE"].ToString());
            }

            if (configIniXml.ContainsKey("VARIABLESTYPE_PORTABLE") && configIniXml["VARIABLESTYPE_PORTABLE"] != null && configIniXml["VARIABLESTYPE_PORTABLE"] != "")
            {
                identity.PostalTelLibMobilePhone = int.Parse(configIniXml["VARIABLESTYPE_PORTABLE"].ToString());
            }


            if (configIniXml.ContainsKey("CREATEPROFILWEBFILIEREID") && configIniXml["CREATEPROFILWEBFILIEREID"] != null && configIniXml["CREATEPROFILWEBFILIEREID"] != "")
            {
                identity.FiliereId = int.Parse(configIniXml["CREATEPROFILWEBFILIEREID"].ToString());
            }


        }

        /// <summary>
        /// Renseigne dans l'objet identité les propriétés qui sont dans le config.ini (EMAIL, TYPE_EMAIL, ...)
        /// </summary>
        /// <param name="structureId"></param>
        /// <param name="configIniXml"></param>
        /// <param name="identity"></param>
        private void fillNumerosColonnesEmailEtTels(int structureId, Dictionary<string, string> configIniXml, ref IdentityDTO identity)
        {


            if (configIniXml.ContainsKey("VARIABLESEMAIL") && configIniXml["VARIABLESEMAIL"] != null && configIniXml["VARIABLESEMAIL"] != "")
            {
                identity.PostalTelEmail = int.Parse(configIniXml["VARIABLESEMAIL"].ToString());
            }

            if (configIniXml.ContainsKey("VARIABLESTYPE_EMAIL") && configIniXml["VARIABLESTYPE_EMAIL"] != null && configIniXml["VARIABLESTYPE_EMAIL"] != "")
            {
                identity.PostalTelLibEmail = int.Parse(configIniXml["VARIABLESTYPE_EMAIL"].ToString());
            }

            if (configIniXml.ContainsKey("VARIABLESTELEPHONE") && configIniXml["VARIABLESTELEPHONE"] != null && configIniXml["VARIABLESTELEPHONE"] != "")
            {
                identity.PostalTelPhoneNumber = int.Parse(configIniXml["VARIABLESTELEPHONE"].ToString());
            }

            if (configIniXml.ContainsKey("VARIABLESTYPE_TELEPHONE") && configIniXml["VARIABLESTYPE_TELEPHONE"] != null && configIniXml["VARIABLESTYPE_TELEPHONE"] != "")
            {
                identity.PostalTelLibPhoneNumber = int.Parse(configIniXml["VARIABLESTYPE_TELEPHONE"].ToString());
            }

            if (configIniXml.ContainsKey("VARIABLESFAX") && configIniXml["VARIABLESFAX"] != null && configIniXml["VARIABLESFAX"] != "")
            {
                identity.PostalTelFax = int.Parse(configIniXml["VARIABLESFAX"].ToString());
            }

            if (configIniXml.ContainsKey("VARIABLESTYPE_FAX") && configIniXml["VARIABLESTYPE_FAX"] != null && configIniXml["VARIABLESTYPE_FAX"] != "")
            {
                identity.PostalTelLibFax = int.Parse(configIniXml["VARIABLESTYPE_FAX"].ToString());
            }

            if (configIniXml.ContainsKey("VARIABLESPORTABLE") && configIniXml["VARIABLESPORTABLE"] != null && configIniXml["VARIABLESPORTABLE"] != "")
            {
                identity.PostalTelMobilePhone = int.Parse(configIniXml["VARIABLESPORTABLE"].ToString());
            }

            if (configIniXml.ContainsKey("VARIABLESTYPE_PORTABLE") && configIniXml["VARIABLESTYPE_PORTABLE"] != null && configIniXml["VARIABLESTYPE_PORTABLE"] != "")
            {
                identity.PostalTelLibMobilePhone = int.Parse(configIniXml["VARIABLESTYPE_PORTABLE"].ToString());
            }


            if (configIniXml.ContainsKey("CREATEPROFILWEBFILIEREID") && configIniXml["CREATEPROFILWEBFILIEREID"] != null && configIniXml["CREATEPROFILWEBFILIEREID"] != "")
            {
                identity.FiliereId = int.Parse(configIniXml["CREATEPROFILWEBFILIEREID"].ToString());
            }


        }

        /// <summary>
        /// si n'est pas forcé, cherche dans le config.ini
        /// </summary>
        /// <param name="configIniXml">config.ini</param>
        /// <param name="pcheckEmailUnicity">forcer le bool en amont</param>
        /// <returns></returns>
        private bool needToCheckEmailUnicity(Dictionary<string, string> configIniXml, bool? pcheckEmailUnicity)
        {
            bool checkEmailUnicity = true;
            if (pcheckEmailUnicity != null)
            {
                checkEmailUnicity = (bool)pcheckEmailUnicity;
            }
            else
            {

                if (configIniXml.ContainsKey("CREATEPROFILLINKMAILUNICITY") && configIniXml["CREATEPROFILLINKMAILUNICITY"] != null && configIniXml["CREATEPROFILLINKMAILUNICITY"] != "")
                    checkEmailUnicity = !(int.Parse(configIniXml["CREATEPROFILLINKMAILUNICITY"].ToString()) == -1);
            }

            return checkEmailUnicity;
        }


    }
}
