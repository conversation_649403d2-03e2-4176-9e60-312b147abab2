﻿using AutoMapper;
using Core.Themis.Libraries.Data.Entities.Open.Abonnement;
using Core.Themis.Libraries.Data.Entities.Open.Cinema;
using Core.Themis.Libraries.Data.Entities.Open.EventsSessions;
using Core.Themis.Libraries.Data.Entities.Open.Offers;
using Core.Themis.Libraries.Data.Entities.Open.Traduction;
using Core.Themis.Libraries.Data.Entities.WebLibrary.Catalog;
using Core.Themis.Libraries.DTO;
using Core.Themis.Libraries.DTO.AdminSphere.VenteIndiv;
using Core.Themis.Libraries.DTO.Catalog.Event;
using Core.Themis.Libraries.DTO.Catalog.Filter;
using Core.Themis.Libraries.DTO.Catalog.Models;
using Core.Themis.Libraries.DTO.Catalog.Product;
using Core.Themis.Libraries.DTO.Enums;
using Core.Themis.Libraries.DTO.Enums.Catalog;
using Core.Themis.Libraries.DTO.EventsSessions;
using Core.Themis.Libraries.DTO.Offers;
using System;
using System.Collections.Generic;
using System.Linq;


namespace Core.Themis.Libraries.BLL.AutoMapperProfiles
{
    public class CatalogProfiles : Profile
    {
        public CatalogProfiles()
        {
            #region Card Event

            CreateMap<ManifestationEntity, EventCardInfo>()
               .ForMember(dest => dest.EventId, opt => opt.MapFrom(src => src.ManifestationId))
               .ForMember(dest => dest.Name, opt => opt.MapFrom(src => src.ManifestationNom.Trim()))
               .ForMember(dest => dest.StartDate, opt => opt.MapFrom(src => src.Seances.Min(d => d.SeanceDateDeb)))
               .ForMember(dest => dest.EndDate, opt => opt.MapFrom(src => src.Seances.Max(d => d.SeanceDateFin)))
               .ForMember(dest => dest.NumberOfRemainingSessionsOnSale, opt => opt.MapFrom(src => src.Seances.Count()))
               .ForMember(dest => dest.SubGenreName, opt => opt.MapFrom(src => src.SousGenre != null ? GetTranslationSousGenre(src.SousGenre) : string.Empty))
               .ForMember(dest => dest.PlaceNames, opt => opt.MapFrom(src => GetPlacesNames(src.Seances)))
               .ForMember(dest => dest.IsEventLocked, opt => opt.MapFrom(src => src.GPManifestation.IsLock))
               .ForMember(dest => dest.TraductionsForLockEvents, opt => opt.MapFrom(src => EventSessionProfiles.GetTraductionsDto(src.GPManifestation.TraductionGpManifestationsForLock)))
               .ForMember(dest => dest.IsWaitingEvent, opt => opt.MapFrom(src => src.GPManifestation.IsListeAttente))
               .ForMember(dest => dest.TraductionsForWaitingEvents, opt => opt.MapFrom(src => EventSessionProfiles.GetTraductionsDto(src.GPManifestation.TraductionGpManifestationsForWaiting)))
               .ForMember(dest => dest.Resume, opt => opt.MapFrom(src => src.ManifestationInfos.ResumeManif))
               .ForMember(dest => dest.EventTypeId, opt => opt.MapFrom(src => src.ManifestationGroupe.TypeEvenement))
               .ForMember(dest => dest.IsShowDate, opt => opt.MapFrom(src => GetIsShowDate(src.Seances)))
               .ForMember(dest => dest.IsShowHour, opt => opt.MapFrom(src => GetIsShowHour(src.Seances)))
               .ForMember(dest => dest.IsSessionsFull, opt => opt.MapFrom(src => IsFullSessions(src)))
               .ForMember(dest => dest.Description1, opt => opt.MapFrom(src => src.ManifestationDescrip.Trim()))
               .ForMember(dest => dest.Description2, opt => opt.MapFrom(src => src.ManifestationDescrip2.Trim()))
               .ForMember(dest => dest.Description3, opt => opt.MapFrom(src => src.ManifestationDescrip3.Trim()))
               .ForMember(dest => dest.Description4, opt => opt.MapFrom(src => src.ManifestationDescrip4.Trim()))
               .ForMember(dest => dest.Description5, opt => opt.MapFrom(src => src.ManifestationDescrip5.Trim()));


            CreateMap<ManifestationEntity, SportCardInfo>()
               .ForMember(dest => dest.EventId, opt => opt.MapFrom(src => src.ManifestationId))
               .ForMember(dest => dest.Name, opt => opt.MapFrom(src => src.ManifestationNom))
               .ForMember(dest => dest.StartDate, opt => opt.MapFrom(src => src.Seances.Min(d => d.SeanceDateDeb)))
               .ForMember(dest => dest.EndDate, opt => opt.MapFrom(src => src.Seances.Max(d => d.SeanceDateFin)))
               .ForMember(dest => dest.NumberOfRemainingSessionsOnSale, opt => opt.MapFrom(src => src.Seances.Count()))
               .ForMember(dest => dest.SubGenreName, opt => opt.MapFrom(src => src.SousGenre != null ? GetTranslationSousGenre(src.SousGenre) : string.Empty))
               .ForMember(dest => dest.PlaceNames, opt => opt.MapFrom(src => GetPlacesNames(src.Seances)))
               .ForMember(dest => dest.IsEventLocked, opt => opt.MapFrom(src => src.GPManifestation.IsLock))
               .ForMember(dest => dest.TraductionsForLockEvents, opt => opt.MapFrom(src => EventSessionProfiles.GetTraductionsDto(src.GPManifestation.TraductionGpManifestationsForLock)))
               .ForMember(dest => dest.Resume, opt => opt.MapFrom(src => src.ManifestationInfos.ResumeManif))
               .ForMember(dest => dest.EventTypeId, opt => opt.MapFrom(src => src.ManifestationGroupe.TypeEvenement))
               .ForMember(dest => dest.IsSessionsFull, opt => opt.MapFrom(src => IsFullSessions(src)))
               .ForMember(dest => dest.TraductionsForWaitingEvents, opt => opt.MapFrom(src => EventSessionProfiles.GetTraductionsDto(src.GPManifestation.TraductionGpManifestationsForWaiting)))
               .ForMember(dest => dest.Description1, opt => opt.MapFrom(src => src.ManifestationDescrip))
               .ForMember(dest => dest.Description2, opt => opt.MapFrom(src => src.ManifestationDescrip2))
               .ForMember(dest => dest.Description3, opt => opt.MapFrom(src => src.ManifestationDescrip3))
               .ForMember(dest => dest.Description4, opt => opt.MapFrom(src => src.ManifestationDescrip4))
               .ForMember(dest => dest.Description5, opt => opt.MapFrom(src => src.ManifestationDescrip5));

            CreateMap<ManifestationEntity, CinemaCardInfo>()
                .ForMember(dest => dest.EventId, opt => opt.MapFrom(src => src.ManifestationId))
                .ForMember(dest => dest.Name, opt => opt.MapFrom(src => src.ManifestationNom))
                .ForMember(dest => dest.StartDate, opt => opt.MapFrom(src => src.Seances.Min(d => d.SeanceDateDeb)))
                .ForMember(dest => dest.EndDate, opt => opt.MapFrom(src => src.Seances.Max(d => d.SeanceDateFin)))
                .ForMember(dest => dest.NumberOfRemainingSessionsOnSale, opt => opt.MapFrom(src => src.Seances.Count()))
                .ForMember(dest => dest.SubGenreName, opt => opt.MapFrom(src => src.SousGenre != null ? GetTranslationSousGenre(src.SousGenre) : string.Empty))
                .ForMember(dest => dest.PlaceNames, opt => opt.MapFrom(src => GetPlacesNames(src.Seances)))
                .ForMember(dest => dest.IsSessionsFull, opt => opt.MapFrom(src => IsFullSessions(src)))
                .ForMember(dest => dest.Targets, opt => opt.MapFrom(src => GetTargets(src.Seances)))
                .ForMember(dest => dest.Duration, opt => opt.MapFrom(src => EventSessionProfiles.GetDuration(src.Seances)))
                .ForMember(dest => dest.Resume, opt => opt.MapFrom(src => src.ManifestationInfos.ResumeManif))
                .ForMember(dest => dest.EventTypeId, opt => opt.MapFrom(src => src.ManifestationGroupe.TypeEvenement))
                .ForMember(dest => dest.Discipline, opt => opt.MapFrom(src => src.Discipline.Masquer == 'N' ? GetTranslationDiscipline(src.Discipline) : string.Empty))
                .ForMember(dest => dest.TraductionsForLockEvents, opt => opt.MapFrom(src => EventSessionProfiles.GetTraductionsDto(src.GPManifestation.TraductionGpManifestationsForLock)))
                .ForMember(dest => dest.TraductionsForWaitingEvents, opt => opt.MapFrom(src => EventSessionProfiles.GetTraductionsDto(src.GPManifestation.TraductionGpManifestationsForWaiting)))
                .ForMember(dest => dest.Localisations, opt => opt.MapFrom(src => GetLocalisations(src.Seances)));

            CreateMap<FormuleAbonnementEntity, AbonnementCardInfo>()
                 .ForMember(dest => dest.Name, opt => opt.MapFrom(src => src.FormAbonNom))
                 .ForMember(dest => dest.NbEvents, opt => opt.MapFrom(src => src.FormAbonManifMin))
                 .ForMember(dest => dest.FormuleId, opt => opt.MapFrom(src => src.FormAbonId))
                 .ForMember(dest => dest.PlaceNames, opt => opt.MapFrom(src => GetPlacesNames(src.FormuleGroupes.SelectMany(f => f.AbonnementManifestations.SelectMany(am => am.Manifestation.Seances)).ToList())));

            CreateMap<DisciplineEntity, DisciplineDTO>()
                 .ForMember(dest => dest.id, opt => opt.MapFrom(src => src.Id))
                 .ForMember(dest => dest.name, opt => opt.MapFrom(src => src.Nom));

            CreateMap<OfferEntity, OfferDTO>()
                .ForMember(dest => dest.OfferId, opt => opt.MapFrom(src => src.OffreId))
                .ForMember(dest => dest.OfferVente, opt => opt.MapFrom(src => src.OffreVente))
                .ForMember(dest => dest.OfferName, opt => opt.MapFrom(src => src.OffreNom))
                .ForMember(dest => dest.DateDebutValidite, opt => opt.MapFrom(src => src.DateDebValidite))
                .ForMember(dest => dest.DateFinValidite, opt => opt.MapFrom(src => src.DateFinValidite))
                .ForMember(dest => dest.DateOperation, opt => opt.MapFrom(src => src.DateOperation));

            CreateMap<FormuleAbonnementEntity, FormulaDTO>()
                .ForMember(dest => dest.FormulaId, opt => opt.MapFrom(src => src.FormAbonId))
                .ForMember(dest => dest.FormulaName, opt => opt.MapFrom(src => src.FormAbonNom))
                .ForMember(dest => dest.IsForcer, opt => opt.MapFrom(src => src.Forcer));

            CreateMap<TraductionGPManifestationEntity, TraductionDTO>()
                .ForMember(dest => dest.LangueId, opt => opt.MapFrom(src => src.LangueId))
                .ForMember(dest => dest.LangCode, opt => opt.MapFrom(src => src.LangueCode))
                .ForMember(dest => dest.TraductionId, opt => opt.MapFrom(src => src.TraductionId))
                .ForMember(dest => dest.Traduction, opt => opt.MapFrom(src => src.Traduction))
                .ForMember(dest => dest.Language, opt => opt.MapFrom(src => src.Langue));

            CreateMap<ManifestationEntity, EventWaitingDTO>()
                .ForMember(dest => dest.EventId, opt => opt.MapFrom(src => src.ManifestationId))
                .ForMember(dest => dest.EventName, opt => opt.MapFrom(src => src.ManifestationNom))
                .ForMember(dest => dest.IsEventWaiting, opt => opt.MapFrom(src => src.GPManifestation.IsListeAttente))
                .ForMember(dest => dest.TraductionGpWaitingEvents, opt => opt.MapFrom(src => EventSessionProfiles.GetTraductionsDto(src.GPManifestation.TraductionGpManifestationsForWaiting)));


            CreateMap<EventWaitingDTO, ManifestationEntity>()
                .ForMember(dest => dest.ManifestationId, opt => opt.MapFrom(src => src.EventId))
                .ForMember(dest => dest.ManifestationNom, opt => opt.MapFrom(src => src.EventName))
                .ForMember(dest => dest.GPManifestation, opt => opt.MapFrom(src => new GPManifestationEntity()
                {
                    GpManifestationListeAttenteManifCommentId = src.TraductionGpWaitingEvents.Count() == 0 ? null : src.TraductionGpWaitingEvents.FirstOrDefault().TraductionId,
                    IsListeAttente = src.IsEventWaiting,
                    TraductionGpManifestationsForWaiting = GetTraductionEntity(src.TraductionGpWaitingEvents)
                }));

            CreateMap<ManifestationEntity, EventLockDTO>()
                .ForMember(dest => dest.EventId, opt => opt.MapFrom(src => src.ManifestationId))
                .ForMember(dest => dest.EventName, opt => opt.MapFrom(src => src.ManifestationNom))
                .ForMember(dest => dest.IsEventLock, opt => opt.MapFrom(src => src.GPManifestation.IsLock))
                .ForMember(dest => dest.TraductionGpLockEvents, opt => opt.MapFrom(src => EventSessionProfiles.GetTraductionsDto(src.GPManifestation.TraductionGpManifestationsForLock)));


            CreateMap<EventLockDTO, ManifestationEntity>()
                .ForMember(dest => dest.ManifestationId, opt => opt.MapFrom(src => src.EventId))
                .ForMember(dest => dest.GPManifestation, opt => opt.MapFrom(src => new GPManifestationEntity()
                {
                    GpManifestationLockManifCommentId = src.TraductionGpLockEvents.Count() == 0 ? null : src.TraductionGpLockEvents.FirstOrDefault().TraductionId,
                    IsLock = src.IsEventLock,
                    ManifestationId = src.EventId,
                    TraductionGpManifestationsForLock = GetTraductionEntity(src.TraductionGpLockEvents)
                }));


            CreateMap<EventLockDTO, GPManifestationEntity>()
              .ForMember(dest => dest.ManifestationId, opt => opt.MapFrom(src => src.EventId))
              .ForMember(dest => dest.IsLock, opt => opt.MapFrom(src => src.IsEventLock))
              .ForMember(dest => dest.DateOperation, opt => opt.MapFrom(src => src.DateOperation))
              .ForMember(dest => dest.OperateurId, opt => opt.MapFrom(src => src.OperateurId))
              .ForMember(dest => dest.GpManifestationLockManifCommentId, opt => opt.MapFrom(src => src.GpManifestationLockManifCommentId))
              .ForMember(dest => dest.GpManifestationListeAttenteManifCommentId, opt => opt.MapFrom(src => src.GpManifestationListeAttenteManifCommentId))
              .ForMember(dest => dest.IsListeAttente, opt => opt.MapFrom(src => src.IsListeAttente));


            CreateMap<SeanceEntity, SessionLockDTO>()
               .ForMember(dest => dest.SessionId, opt => opt.MapFrom(src => src.SeanceId))
               .ForMember(dest => dest.StartDate, opt => opt.MapFrom(src => src.SeanceDateDeb))
               .ForMember(dest => dest.PlaceId, opt => opt.MapFrom(src => src.LieuId))
               .ForMember(dest => dest.PlaceName, opt => opt.MapFrom(src => src.Lieu.LieuNom))
               .ForMember(dest => dest.IsSessionLock, opt => opt.MapFrom(src => src.GPSeance.IsLock))
               .ForMember(dest => dest.TraductionGpLockSessions, opt => opt.MapFrom(src => EventSessionProfiles.GetTraductionsDto(src.GPSeance.TraductionGpManifestationsForLock)));


            CreateMap<SessionLockDTO, SeanceEntity>()
                .ForMember(dest => dest.SeanceId, opt => opt.MapFrom(src => src.SessionId))
                .ForMember(dest => dest.GPSeance, opt => opt.MapFrom(src => new GPSeanceEntity()
                {
                    GpSeanceLockSeanceCommentId = src.TraductionGpLockSessions.Count() == 0 ? null : src.TraductionGpLockSessions.FirstOrDefault().TraductionId,
                    IsLock = src.IsSessionLock,
                    TraductionGpManifestationsForLock = GetTraductionEntity(src.TraductionGpLockSessions)
                }));


            CreateMap<CatalogDataDto, ManifestationEntity>()
                .ForMember(dest => dest.ManifestationId, opt => opt.MapFrom(src => src.ManifestationId))
                .ForMember(dest => dest.ManifestationNom, opt => opt.MapFrom(src => src.Nom))
                .ForMember(dest => dest.ManifestationDescrip, opt => opt.MapFrom(src => src.ManifestationDescrip))
                .ForMember(dest => dest.ManifestationDescrip2, opt => opt.MapFrom(src => src.ManifestationDescrip2))
                .ForMember(dest => dest.ManifestationDescrip3, opt => opt.MapFrom(src => src.ManifestationDescrip3))
                .ForMember(dest => dest.ManifestationDescrip4, opt => opt.MapFrom(src => src.ManifestationDescrip4))
                .ForMember(dest => dest.ManifestationDescrip5, opt => opt.MapFrom(src => src.ManifestationDescrip5))
                .ForMember(dest => dest.ManifestationGroupeId, opt => opt.MapFrom(src => src.ManifestationGroupeId))
                .ForMember(dest => dest.IdGenre, opt => opt.MapFrom(src => src.IdGenre));


            CreateMap<CatalogDataDto, FormuleAbonnementEntity>()
                .ForMember(dest => dest.FormAbonId, opt => opt.MapFrom(src => src.FormulaId))
                .ForMember(dest => dest.FormAbonNom, opt => opt.MapFrom(src => src.Nom))
                .ForMember(dest => dest.FormAbonManifMin, opt => opt.MapFrom(src => src.NbManifMin))
                .ForMember(dest => dest.FormAbonManifMax, opt => opt.MapFrom(src => src.NbManifMax));

            #endregion

            #region Card Product

            CreateMap<ProductInfoResultDTO, CardAdhesionInfo>()
                .ForMember(dest => dest.ProductId, opt => opt.MapFrom(src => src.ProductId))
                .ForMember(dest => dest.ProductId, opt => opt.MapFrom(src => src.ProductId))
                .ForMember(dest => dest.ProductType, opt => opt.MapFrom(src => (EnumProductType)src.GroupId))
                .ForMember(dest => dest.TypeMontant, opt => opt.MapFrom(src => (EnumTypeMontant)src.TypeMontant))
                .ForMember(dest => dest.Name, opt => opt.MapFrom(src => src.Name))
                .ForMember(dest => dest.Price, opt => opt.MapFrom(src => src.Price))
                .ForMember(dest => dest.ShortDescription, opt => opt.MapFrom(src => src.ShortDescription))
                .ForMember(dest => dest.ValidityDate, opt => opt.MapFrom(src => src.ValidityDate))
                .ForMember(dest => dest.ValidityPeriod, opt => opt.MapFrom(src => src.ValidityPeriod))
                .ForMember(dest => dest.TimeUnit, opt => opt.MapFrom(src => src.TimeUnit))
                ;

            CreateMap<ProductInfoResultDTO, CardFeedbookInfo>()
                .ForMember(dest => dest.ProductId, opt => opt.MapFrom(src => src.ProductId))
                .ForMember(dest => dest.ProductType, opt => opt.MapFrom(src => (EnumProductType)src.GroupId))                
                .ForMember(dest => dest.TypeMontant, opt => opt.MapFrom(src => (EnumTypeMontant)src.TypeMontant))
                .ForMember(dest => dest.Name, opt => opt.MapFrom(src => src.Name))
                .ForMember(dest => dest.Price, opt => opt.MapFrom(src => src.Price))
                .ForMember(dest => dest.ShortDescription, opt => opt.MapFrom(src => src.ShortDescription))
                .ForMember(dest => dest.ValidityDate, opt => opt.MapFrom(src => src.ValidityDate))
                .ForMember(dest => dest.ValidityPeriod, opt => opt.MapFrom(src => src.ValidityPeriod))
                .ForMember(dest => dest.TicketsNumber, opt => opt.MapFrom(src => src.TicketsNumber))
                ;

            CreateMap<ProductInfoResultDTO, CardShopInfo>()
                .ForMember(dest => dest.ProductId, opt => opt.MapFrom(src => src.ProductId))
                .ForMember(dest => dest.ProductType, opt => opt.MapFrom(src => (EnumProductType)src.GroupId))
                .ForMember(dest => dest.TypeMontant, opt => opt.MapFrom(src => (EnumTypeMontant)src.TypeMontant))
                .ForMember(dest => dest.Name, opt => opt.MapFrom(src => src.Name))
                .ForMember(dest => dest.Price, opt => opt.MapFrom(src => src.Price))
                .ForMember(dest => dest.ShortDescription, opt => opt.MapFrom(src => src.ShortDescription))
                .ForMember(dest => dest.SizeAvailable, opt => opt.MapFrom(src => src.OthterSizesAvailble))
                .ForMember(dest => dest.ColorsAvailable, opt => opt.MapFrom(src => src.OtherColorsAvailble))
                ;

            CreateMap<ProductInfoResultDTO, CardProductInfoBase>()
                .ForMember(dest => dest.ProductId, opt => opt.MapFrom(src => src.ProductId))
                .ForMember(dest => dest.ProductType, opt => opt.MapFrom(src => (EnumProductType)src.GroupId))
                .ForMember(dest => dest.TypeMontant, opt => opt.MapFrom(src => (EnumTypeMontant)src.TypeMontant))
                .ForMember(dest => dest.Name, opt => opt.MapFrom(src => src.Name))
                .ForMember(dest => dest.Price, opt => opt.MapFrom(src => src.Price))
                .ForMember(dest => dest.ShortDescription, opt => opt.MapFrom(src => src.ShortDescription))
                ;

            #endregion

            CreateMap<CatalogEntity, CatalogDTO>()
                .ForMember(dest => dest.CatalogId, opt => opt.MapFrom(src => src.CatalogId))
                .ForMember(dest => dest.ShowImage, opt => opt.MapFrom(src => src.ShowImage))
                .ForMember(dest => dest.StructureId, opt => opt.MapFrom(src => src.StructureId))
                .ForMember(dest => dest.ItemsPerPage, opt => opt.MapFrom(src => src.ItemsPerPage))
                .ForMember(dest => dest.Name, opt => opt.MapFrom(src => src.Name))
                .ForMember(dest => dest.ImgRatio, opt => opt.MapFrom(src => src.ImageRatio))
                .ForMember(dest => dest.ViewMode, opt => opt.MapFrom(src => (EnumViewMode)src.ViewModeTypeId))
                .ForMember(dest => dest.Areas, opt => opt.MapFrom(src => src.Areas))
                .ForMember(dest => dest.DisplaySettings, opt => opt.MapFrom(src => src.DisplaySettings))
                .ForMember(dest => dest.DestinationPage, opt => opt.MapFrom(src => src.DestinationPage))
                .ForMember(dest => dest.DestinationPageType, opt => opt.MapFrom(src => src.DestinationPage != null ? (EnumDestinationPageType)src.DestinationPage.DestinationPageTypeId : EnumDestinationPageType.None))
                ;

            CreateMap<CatalogDTO, CatalogEntity>()
                .ForMember(dest => dest.CatalogId, opt => opt.MapFrom(src => src.CatalogId))
                .ForMember(dest => dest.ShowImage, opt => opt.MapFrom(src => src.ShowImage))
                .ForMember(dest => dest.StructureId, opt => opt.MapFrom(src => src.StructureId))
                .ForMember(dest => dest.ItemsPerPage, opt => opt.MapFrom(src => src.ItemsPerPage))
                .ForMember(dest => dest.Name, opt => opt.MapFrom(src => src.Name))
                .ForMember(dest => dest.ImageRatio, opt => opt.MapFrom(src => src.ImgRatio))
                .ForMember(dest => dest.ViewModeTypeId, opt => opt.MapFrom(src => (int)src.ViewMode))
                .ForMember(dest => dest.Areas, opt => opt.MapFrom(src => src.Areas))
                .ForMember(dest => dest.DisplaySettings, opt => opt.MapFrom(src => src.DisplaySettings))
                .ForMember(dest => dest.DestinationPage, opt => opt.MapFrom(src => src.DestinationPage))
                ;

            CreateMap<CatalogAreaEntity, AreaDTO>()
                .ForMember(dest => dest.AreaId, opt => opt.MapFrom(src => src.AreaId))
                .ForMember(dest => dest.CatalogId, opt => opt.MapFrom(src => src.CatalogId))
                .ForMember(dest => dest.Order, opt => opt.MapFrom(src => src.Order))
                .ForMember(dest => dest.SortByDefault, opt => opt.MapFrom(src => (EnumSortBy)src.SortByDefaultTypeId))
                .ForMember(dest => dest.SortsBy, opt => opt.MapFrom(src => src.SortSettings))
                .ForMember(dest => dest.MainMenu, opt => opt.MapFrom(src => GetMainMenuDTO(src).AsReadOnly()))
                .ForMember(dest => dest.Filters, opt => opt.MapFrom(src => src.Filters))
                .ForMember(dest => dest.GroupingsBy, opt => opt.MapFrom(src => src.Groupings))
                .ForMember(dest => dest.ToDelete, opt => opt.MapFrom(src => src.ToDelete))
                ;

            CreateMap<AreaDTO, CatalogAreaEntity>()
                .ForMember(dest => dest.AreaId, opt => opt.MapFrom(src => src.AreaId))
                .ForMember(dest => dest.CatalogId, opt => opt.MapFrom(src => src.CatalogId))
                .ForMember(dest => dest.Order, opt => opt.MapFrom(src => src.Order))
                .ForMember(dest => dest.SortByDefaultTypeId, opt => opt.MapFrom(src => (int)src.SortByDefault))
                .ForMember(dest => dest.SortSettings, opt => opt.MapFrom(src => src.SortsBy))
                .ForMember(dest => dest.MainMenu, opt => opt.MapFrom(src => src.MainMenu))
                .ForMember(dest => dest.Filters, opt => opt.MapFrom(src => src.Filters))
                .ForMember(dest => dest.Groupings, opt => opt.MapFrom(src => src.GroupingsBy))
                .ForMember(dest => dest.ToDelete, opt => opt.MapFrom(src => src.ToDelete))
                .ForMember(dest => dest.SortByDefault, opt => opt.Ignore())
                ;

            CreateMap<CatalogMainMenuItemEntity, MainMenuItemDTO>()
                .ForMember(dest => dest.MainMenuItemId, opt => opt.MapFrom(src => src.MainMenuItemId))
                .ForMember(dest => dest.AreaId, opt => opt.MapFrom(src => src.AreaId))
                .ForMember(dest => dest.MainMenuType, opt => opt.MapFrom(src => (EnumMainMenuType)src.MainMenuTypeId))
                .ForMember(dest => dest.SubMenuType, opt => opt.MapFrom(src => src.SubMenuTypeId.HasValue ? (EnumSubMenuType)src.SubMenuTypeId : EnumSubMenuType.None))
                .ForMember(dest => dest.IsVisible, opt => opt.MapFrom(src => src.IsVisible))
                .ForMember(dest => dest.IdLinked, opt => opt.MapFrom(src => src.IdLinked))
                .ForMember(dest => dest.Order, opt => opt.MapFrom(src => src.Order))
                .ForMember(dest => dest.SubMenu, opt => opt.MapFrom(src => GetSubMenuDTO(src)))
                .ForMember(dest => dest.ToDelete, opt => opt.MapFrom(src => src.ToDelete))
                ;

            CreateMap<MainMenuItemDTO, CatalogMainMenuItemEntity>()
                .ForMember(dest => dest.MainMenuItemId, opt => opt.MapFrom(src => src.MainMenuItemId))
                .ForMember(dest => dest.AreaId, opt => opt.MapFrom(src => src.AreaId))
                .ForMember(dest => dest.MainMenuTypeId, opt => opt.MapFrom(src => (int)src.MainMenuType))
                .ForMember(dest => dest.SubMenuTypeId, opt => opt.MapFrom(src => src.SubMenuType == EnumSubMenuType.None ? (int?)null : (int)src.SubMenuType))
                .ForMember(dest => dest.IsVisible, opt => opt.MapFrom(src => src.IsVisible))
                .ForMember(dest => dest.IdLinked, opt => opt.MapFrom(src => src.IdLinked))
                .ForMember(dest => dest.Order, opt => opt.MapFrom(src => src.Order))
                .ForMember(dest => dest.SubMenu, opt => opt.MapFrom(src => src.SubMenu))
                .ForMember(dest => dest.ToDelete, opt => opt.MapFrom(src => src.ToDelete))
                .ForMember(dest => dest.MainMenuType, opt => opt.Ignore())
                ;

            CreateMap<CatalogSubMenuItemEntity, SubMenuItemDTO>()
                .ForMember(dest => dest.SubMenuItemId, opt => opt.MapFrom(src => src.SubMenuItemId))
                .ForMember(dest => dest.MainMenuItemId, opt => opt.MapFrom(src => src.MainMenuItemId))
                .ForMember(dest => dest.IsVisible, opt => opt.MapFrom(src => src.IsVisible))
                .ForMember(dest => dest.IdLinked, opt => opt.MapFrom(src => src.IdLinked))
                .ForMember(dest => dest.Order, opt => opt.MapFrom(src => src.Order))
                .ForMember(dest => dest.ToDelete, opt => opt.MapFrom(src => src.ToDelete))
                ;

            CreateMap<SubMenuItemDTO, CatalogSubMenuItemEntity>()
                .ForMember(dest => dest.SubMenuItemId, opt => opt.MapFrom(src => src.SubMenuItemId))
                .ForMember(dest => dest.MainMenuItemId, opt => opt.MapFrom(src => src.MainMenuItemId))
                .ForMember(dest => dest.IsVisible, opt => opt.MapFrom(src => src.IsVisible))
                .ForMember(dest => dest.IdLinked, opt => opt.MapFrom(src => src.IdLinked))
                .ForMember(dest => dest.Order, opt => opt.MapFrom(src => src.Order))
                .ForMember(dest => dest.ToDelete, opt => opt.MapFrom(src => src.ToDelete))
                ;

            CreateMap<CatalogAreaSortSettingEntity, SortBySettingsDTO>()
                .ForMember(dest => dest.SortBySettingsId, opt => opt.MapFrom(src => src.AreaSortSettingId))
                .ForMember(dest => dest.AreaId, opt => opt.MapFrom(src => src.AreaId))
                .ForMember(dest => dest.SortGroupType, opt => opt.MapFrom(src => (EnumSortGroupType)src.SortGroupTypeId))
                .ForMember(dest => dest.Visible, opt => opt.MapFrom(src => src.IsVisible))
                .ForMember(dest => dest.SortTypes, opt => opt.MapFrom(src => src.SortGroupType!.SortByTypes!.Select(t => (EnumSortBy)t.SortByTypeId).ToList()))
                .ForMember(dest => dest.AvailableMainMenuTypes, opt => opt.MapFrom(src => src.SortGroupType!.MainMenuTypes!.Select(m => (EnumMainMenuType)m.MainMenuTypeId).ToArray()))
                ;

            CreateMap<SortBySettingsDTO, CatalogAreaSortSettingEntity>()
                .ForMember(dest => dest.AreaSortSettingId, opt => opt.MapFrom(src => src.SortBySettingsId))
                .ForMember(dest => dest.AreaId, opt => opt.MapFrom(src => src.AreaId))
                .ForMember(dest => dest.SortGroupTypeId, opt => opt.MapFrom(src => (int)src.SortGroupType))
                .ForMember(dest => dest.IsVisible, opt => opt.MapFrom(src => src.Visible))
                .ForMember(dest => dest.SortGroupType, opt => opt.Ignore())
                ;

            CreateMap<CatalogAreaFilterEntity, FilterDTO>()
                .ForMember(dest => dest.AreaId, opt => opt.MapFrom(src => src.AreaId))
                .ForMember(dest => dest.FilterType, opt => opt.MapFrom(src => (EnumFilterType)src.FilterTypeId))
                .ForMember(dest => dest.IsVisible, opt => opt.MapFrom(src => src.IsVisible))
                .ForMember(dest => dest.FilterId, opt => opt.MapFrom(src => src.AreaFilterId))
                .ForMember(dest => dest.AvailableMainMenuTypes, opt => opt.MapFrom(src => src.FilterType!.MainMenuTypes!.Select(m => (EnumMainMenuType)m.MainMenuTypeId).ToArray()))
                ;

            CreateMap<FilterDTO, CatalogAreaFilterEntity>()
                .ForMember(dest => dest.AreaId, opt => opt.MapFrom(src => src.AreaId))
                .ForMember(dest => dest.FilterTypeId, opt => opt.MapFrom(src => (int)src.FilterType))
                .ForMember(dest => dest.IsVisible, opt => opt.MapFrom(src => src.IsVisible))
                .ForMember(dest => dest.AreaFilterId, opt => opt.MapFrom(src => src.FilterId))
                .ForMember(dest => dest.FilterType, opt => opt.Ignore())
                ;

            CreateMap<CatalogAreaGroupingEntity, GroupingDTO>()
                .ForMember(dest => dest.AreaId, opt => opt.MapFrom(src => src.AreaId))
                .ForMember(dest => dest.GroupingType, opt => opt.MapFrom(src => (EnumGroupingType)src.GroupingTypeId))
                .ForMember(dest => dest.IsActif, opt => opt.MapFrom(src => src.IsActif))
                .ForMember(dest => dest.GroupingId, opt => opt.MapFrom(src => src.AreaGroupingId))
                .ForMember(dest => dest.AvailableMainMenuTypes, opt => opt.MapFrom(src => src.GroupingType!.MainMenuTypes!.Select(m => (EnumMainMenuType)m.MainMenuTypeId).ToArray()))
                ;

            CreateMap<GroupingDTO, CatalogAreaGroupingEntity>()
                .ForMember(dest => dest.AreaId, opt => opt.MapFrom(src => src.AreaId))
                .ForMember(dest => dest.GroupingTypeId, opt => opt.MapFrom(src => (int)src.GroupingType))
                .ForMember(dest => dest.IsActif, opt => opt.MapFrom(src => src.IsActif))
                .ForMember(dest => dest.AreaGroupingId, opt => opt.MapFrom(src => src.GroupingId))
                .ForMember(dest => dest.GroupingType, opt => opt.Ignore())
                ;

            CreateMap<CatalogDisplaySettingEntity, DisplaySettingsDTO>()
                .ForMember(dest => dest.CatalogId, opt => opt.MapFrom(src => src.CatalogId))
                .ForMember(dest => dest.DisplaySettingId, opt => opt.MapFrom(src => src.DisplaySettingId))
                .ForMember(dest => dest.DisplayGroupType, opt => opt.MapFrom(src => (EnumDisplayGroupType)src.DisplayGroupTypeId))
                .ForMember(dest => dest.ElementSettings, opt => opt.MapFrom(src => src.SettingsElements.OrderBy(elt => elt.DisplayElementType.Order)))
                ;

            CreateMap<DisplaySettingsDTO, CatalogDisplaySettingEntity>()
                .ForMember(dest => dest.CatalogId, opt => opt.MapFrom(src => src.CatalogId))
                .ForMember(dest => dest.DisplaySettingId, opt => opt.MapFrom(src => src.DisplaySettingId))
                .ForMember(dest => dest.DisplayGroupTypeId, opt => opt.MapFrom(src => (int)src.DisplayGroupType))
                .ForMember(dest => dest.SettingsElements, opt => opt.MapFrom(src => src.ElementSettings))
                .ForMember(dest => dest.DisplayGroupType, opt => opt.Ignore())
                ;

            CreateMap<CatalogDisplayElementSettingEntity, DisplayElementSettingDTO>()
                .ForMember(dest => dest.DisplayElementSettingId, opt => opt.MapFrom(src => src.DisplayElementSettingId))
                .ForMember(dest => dest.DisplaySettingId, opt => opt.MapFrom(src => src.DisplaySettingId))
                .ForMember(dest => dest.IsVisible, opt => opt.MapFrom(src => src.IsVisible))
                .ForMember(dest => dest.DisplayElementSettingType, opt => opt.MapFrom(src => (EnumDisplayElementType)src.DisplayElementTypeId))
                ;

            CreateMap<DisplayElementSettingDTO, CatalogDisplayElementSettingEntity>()
                .ForMember(dest => dest.DisplayElementSettingId, opt => opt.MapFrom(src => src.DisplayElementSettingId))
                .ForMember(dest => dest.DisplaySettingId, opt => opt.MapFrom(src => src.DisplaySettingId))
                .ForMember(dest => dest.IsVisible, opt => opt.MapFrom(src => src.IsVisible))
                .ForMember(dest => dest.DisplayElementTypeId, opt => opt.MapFrom(src => (int)src.DisplayElementSettingType))
                ;

            CreateMap<CatalogDestinationPageEntity, DestinationPageDTO>()
                .ForMember(dest => dest.DestinationPageId, opt => opt.MapFrom(src => src.DestinationPageId))
                .ForMember(dest => dest.DestinationPageType, opt => opt.MapFrom(src => (EnumDestinationPageType)src.DestinationPageTypeId))
                .ForMember(dest => dest.StructureId, opt => opt.MapFrom(src => src.StructureId))
                .ForMember(dest => dest.CatalogId, opt => opt.MapFrom(src => src.CatalogId))
                ;

            CreateMap<DestinationPageDTO, CatalogDestinationPageEntity>()
                .ForMember(dest => dest.DestinationPageId, opt => opt.MapFrom(src => src.DestinationPageId))
                .ForMember(dest => dest.DestinationPageTypeId, opt => opt.MapFrom(src => (int)src.DestinationPageType))
                .ForMember(dest => dest.StructureId, opt => opt.MapFrom(src => src.StructureId))
                .ForMember(dest => dest.CatalogId, opt => opt.MapFrom(src => src.CatalogId))
                ;

            CreateMap<CatalogDisplayGroupTypeEntity, DisplaySettingsDTO>()
                .ForMember(dest => dest.DisplayGroupType, opt => opt.MapFrom(src => (EnumDisplayGroupType)src.DisplayGroupTypeId))
                .ForMember(dest => dest.ElementSettings, opt => opt.MapFrom(src => src.DisplayElementTypes))
                ;

            CreateMap<CatalogDisplayElementTypeEntity, DisplayElementSettingDTO>()
                .ForMember(dest => dest.DisplayElementSettingType, opt => opt.MapFrom(src => (EnumDisplayElementType)src.DisplayElementTypeId))
                .ForMember(dest => dest.IsVisible, opt => opt.MapFrom(src => false))
                ;

            CreateMap<CatalogFilterTypeEntity, FilterDTO>()
                .ForMember(dest => dest.FilterType, opt => opt.MapFrom(src => (EnumFilterType)src.FilterTypeId))
                .ForMember(dest => dest.IsVisible, opt => opt.MapFrom(src => false))
                .ForMember(dest => dest.AvailableMainMenuTypes, opt => opt.MapFrom(src => src.MainMenuTypes.Select(m => (EnumMainMenuType)m.MainMenuTypeId).ToArray()))
                ;

            CreateMap<CatalogGroupingTypeEntity, GroupingDTO>()
                .ForMember(dest => dest.GroupingType, opt => opt.MapFrom(src => (EnumGroupingType)src.GroupingTypeId))
                .ForMember(dest => dest.IsActif, opt => opt.MapFrom(src => false))
                .ForMember(dest => dest.AvailableMainMenuTypes, opt => opt.MapFrom(src => src.MainMenuTypes.Select(m => (EnumMainMenuType)m.MainMenuTypeId).ToArray()))
                ;

            CreateMap<CatalogSortGroupTypeEntity, SortBySettingsDTO>()
                .ForMember(dest => dest.SortGroupType, opt => opt.MapFrom(src => (EnumSortGroupType)src.SortGroupTypeId))
                .ForMember(dest => dest.Visible, opt => opt.MapFrom(src => false))
                .ForMember(dest => dest.SortTypes, opt => opt.MapFrom(src => src.SortByTypes.Select(s => (EnumSortBy)s.SortByTypeId)))
                .ForMember(dest => dest.AvailableMainMenuTypes, opt => opt.MapFrom(src => src.MainMenuTypes.Select(m => (EnumMainMenuType)m.MainMenuTypeId).ToArray()))
                ;

            CreateMap<CatalogMainMenuTypeEntity, MainMenuTypeDTO>()
                .ForMember(dest => dest.MainMenuType, opt => opt.MapFrom(src => (EnumMainMenuType)src.MainMenuTypeId))
                .ForMember(dest => dest.SubMenuTypes, opt => opt.MapFrom(src => src.SubMenuTypes.Select(s => (EnumSubMenuType)s.SubMenuTypeId)))
                ;

            CreateMap<CatalogDTO, GeneralCatalogSettingsDTO>()
                .ForMember(dest => dest.ItemsPerPage, opt => opt.MapFrom(src => src.ItemsPerPage))
                .ForMember(dest => dest.ShowImage, opt => opt.MapFrom(src => src.ShowImage))
                .ForMember(dest => dest.ViewMode, opt => opt.MapFrom(src => src.ViewMode))
                .ForMember(dest => dest.ImgRatio, opt => opt.MapFrom(src => src.ImgRatio))
                .ForMember(dest => dest.DisplaySettings, opt => opt.MapFrom(src => src.DisplaySettings))
                ;

            #region Offer catalog (widget offer)

            CreateMap<CatalogEntity, OfferCatalogDTO>()
                .ForMember(dest => dest.GeneralCatalogSettings, opt => opt.MapFrom(src => src))
                .ForMember(dest => dest.OfferAreas, opt => opt.MapFrom(src => src.Areas.OrderBy(a => a.Order)))
                ;

            CreateMap<CatalogEntity, GeneralCatalogSettingsDTO>()
                .ForMember(dest => dest.ItemsPerPage, opt => opt.MapFrom(src => src.ItemsPerPage))
                .ForMember(dest => dest.ImgRatio, opt => opt.MapFrom(src => src.ImageRatio))
                .ForMember(dest => dest.ViewMode, opt => opt.MapFrom(src => (EnumViewMode)src.ViewModeTypeId))
                .ForMember(dest => dest.ShowImage, opt => opt.MapFrom(src => src.ShowImage))
                .ForMember(dest => dest.DisplaySettings, opt => opt.MapFrom(src => src.DisplaySettings))
                ;

            CreateMap<CatalogAreaEntity, OfferCatalogAreaDTO>()
                .ForMember(dest => dest.SortByTypes, opt => opt.MapFrom(src => GetSortForArea(src.SortSettings.ToList())))
                .ForMember(dest => dest.FilterTypes, opt => opt.MapFrom(src => src.Filters.Where(f => f.IsVisible)
                                                                                          .Select(f => (EnumFilterType)f.FilterTypeId)))
                .ForMember(dest => dest.SearchForm, opt => opt.MapFrom(src => src))
                ;

            CreateMap<CatalogAreaEntity, SearchForm>()
                .ForMember(dest => dest.MenuFilterItems, opt => opt.MapFrom(src => GetMenuFilterItem(src.MainMenu)))
                .ForMember(dest => dest.SortBy, opt => opt.MapFrom(src => (EnumSortBy)src.SortByDefaultTypeId))
                .ForMember(dest => dest.GroupByColor, opt => opt.MapFrom(src => src.Groupings.Any(g => g.GroupingTypeId == (int)EnumGroupingType.ByColor && g.IsActif)))
                .ForMember(dest => dest.GroupBySize, opt => opt.MapFrom(src => src.Groupings.Any(g => g.GroupingTypeId == (int)EnumGroupingType.BySize && g.IsActif)))
                .ForMember(dest => dest.OnlyAbo, opt => opt.MapFrom(src => src.MainMenu.Where(m => m.IsVisible && m.IdLinked != -1)
                                                                                       .All(m => m.MainMenuTypeId == (int)EnumMainMenuType.AboType)))
                ;


            #endregion
        }

        private List<MainMenuItemDTO> GetMainMenuDTO(CatalogAreaEntity area)
        {
            if (area.MainMenu is null)
                return [];

            return area.MainMenu.Select(m =>
            {

                bool isDisable = false;

                if (m.IdLinked == -1)
                {
                    isDisable = area.MainMenu.Count(mm => mm.IsVisible && mm.IdLinked != -1) <= 1
                            || area.MainMenu.Where(mm => mm.IsVisible).DistinctBy(mm => mm.MainMenuTypeId).Count() > 1;
                }

                return new MainMenuItemDTO()
                {
                    MainMenuItemId = m.MainMenuItemId,
                    AreaId = m.AreaId,
                    IsVisible = m.IsVisible,
                    IdLinked = m.IdLinked,
                    Order = m.Order,
                    SubMenuType = m.SubMenuTypeId.HasValue ? (EnumSubMenuType)m.SubMenuTypeId.Value : EnumSubMenuType.None,
                    MainMenuType = (EnumMainMenuType)m.MainMenuTypeId,
                    SubMenu = GetSubMenuDTO(m),
                    IsDisabled = isDisable,
                };
            })
            .ToList();
        }

        private List<SubMenuItemDTO> GetSubMenuDTO(CatalogMainMenuItemEntity mainMenuEntity)
        {
            if (mainMenuEntity.SubMenu is null)
                return new();

            return mainMenuEntity.SubMenu.Select(s => new SubMenuItemDTO()
            {
                SubMenuItemId = s.SubMenuItemId,
                MainMenuItemId = s.MainMenuItemId,
                IsVisible = s.IsVisible,
                IdLinked = s.IdLinked,
                Order = s.Order,
                SubMenuItemType = (EnumSubMenuType)mainMenuEntity.SubMenuTypeId!,
                MainMenuItemType = (EnumMainMenuType)mainMenuEntity.MainMenuTypeId
            })
            .ToList();
        }

        private bool GetIsShowDate(ICollection<SeanceEntity> seanceEntities)
        {
            return seanceEntities.Where(s => s.SeanceDateDeb >= DateTime.Now).All(s => s.NePasAfficherDate == 'O') == false;
        }

        private bool GetIsShowHour(ICollection<SeanceEntity> seanceEntities)
        {
            return seanceEntities.Where(s => s.SeanceDateDeb >= DateTime.Now).All(s => s.NePasAfficherDate == 'H') == false;
        }



        private IEnumerable<string> GetPlacesNames(ICollection<SeanceEntity> seances)
        {
            var result = seances.Select(s => s.Lieu).DistinctBy(l => l.LieuId).Select(l => l.LieuNom);
            return result;
        }

        private IEnumerable<string> GetTargets(ICollection<SeanceEntity> seances)
        {
            return seances.SelectMany(s => s.Cibles.Select(c => c)).DistinctBy(c => c.Id).Select(c => c.Nom);
        }

        private IEnumerable<string> GetLocalisations(ICollection<SeanceEntity> seances)
        {
            return seances.SelectMany(s => s.Localisations
                                .Select(c => c))
                          .DistinctBy(c => c.Id)
                          .Select(c => GetTranslationLocalisations(c));
        }

        private bool IsFullSessions(ManifestationEntity manifestation)
        {
            var result = manifestation?.Seances.ToList().All(se => se.GestionPlaces.Where(gp => gp.IsValide.Value && gp.GestionPlaceParentId != null).All(gp => gp.Dispo == 0)) == true;

            return result;
        }

        private string GetTranslationManifestaionGroup(ManifestationGroupeEntity manifestationGroupe)
        {

            if (manifestationGroupe.TraductionManifestationGroupes != null && manifestationGroupe.TraductionManifestationGroupes.Count > 0)
            {
                return manifestationGroupe.TraductionManifestationGroupes?.FirstOrDefault(tmg => tmg.ManifGroupeId == manifestationGroupe.ManifGroupeId).ManifGroupeNom;
            }

            return manifestationGroupe.ManifGroupeNom;
        }

        private string GetTranslationGenre(ManifestationGenreEntity genre)
        {

            if (genre.TraductionManifestationGenres != null && genre.TraductionManifestationGenres.Count > 0)
            {
                string trad = genre.TraductionManifestationGenres.FirstOrDefault()?.Nom;
                if (!string.IsNullOrEmpty(trad)) { return trad; }
            }

            return genre.Nom;
        }

        private string GetTranslationTarget(CibleEntity target)
        {

            if (target.TraductionCibles != null && target.TraductionCibles.Count > 0)
            {
                string trad = target.TraductionCibles.FirstOrDefault()?.Nom;
                if (!string.IsNullOrEmpty(trad)) { return trad; }
            }

            return target.Nom;
        }

        private string GetTranslationDiscipline(DisciplineEntity discipline)
        {

            if (discipline.TraductionsDiscipline != null && discipline.TraductionsDiscipline.Count > 0)
            {
                string trad = discipline.TraductionsDiscipline.FirstOrDefault()?.Nom;
                if (!string.IsNullOrEmpty(trad)) { return trad; }
            }

            return discipline.Nom;
        }

        private string GetTranslationLocalisations(LocalisationEntity localisation)
        {
            if (localisation.TraductionsLocalisation != null && localisation.TraductionsLocalisation.Count > 0)
            {
                string? trad = localisation.TraductionsLocalisation.FirstOrDefault()?.Nom;

                if (!string.IsNullOrEmpty(trad))
                    return trad;
            }

            return localisation.Nom;
        }

        private string GetTranslationSousGenre(ManifestationSousGenreEntity sgEntity)
        {

            if (sgEntity.TraductionManifestationSousGenres != null && sgEntity.TraductionManifestationSousGenres.Count > 0)
            {
                string trad = sgEntity.TraductionManifestationSousGenres.FirstOrDefault()?.Nom;
                if (!string.IsNullOrEmpty(trad)) { return trad; }
            }

            return sgEntity.Nom;
        }

        private TraductionEntity? GetTraductionEntity(List<TraductionDTO> traductions)
        {

            if (traductions.Count == 0)
                return null;

            return new TraductionEntity()
            {
                TraductionId = traductions.FirstOrDefault()?.TraductionId ?? 0,
                Langue1 = traductions.SingleOrDefault(t => t.LangueId == 1)?.Traduction ?? string.Empty,
                Langue2 = traductions.SingleOrDefault(t => t.LangueId == 2)?.Traduction ?? string.Empty,
                Langue3 = traductions.SingleOrDefault(t => t.LangueId == 3)?.Traduction ?? string.Empty,
                Langue4 = traductions.SingleOrDefault(t => t.LangueId == 4)?.Traduction ?? string.Empty,
                Langue5 = traductions.SingleOrDefault(t => t.LangueId == 5)?.Traduction ?? string.Empty,
                Langue6 = traductions.SingleOrDefault(t => t.LangueId == 6)?.Traduction ?? string.Empty,
                Langue7 = traductions.SingleOrDefault(t => t.LangueId == 7)?.Traduction ?? string.Empty,
                Langue8 = traductions.SingleOrDefault(t => t.LangueId == 8)?.Traduction ?? string.Empty,
                Langue9 = traductions.SingleOrDefault(t => t.LangueId == 9)?.Traduction ?? string.Empty,
                Langue10 = traductions.SingleOrDefault(t => t.LangueId == 10)?.Traduction ?? string.Empty,

            };


        }

        private List<OfferCatalogSortDTO> GetSortForArea(List<CatalogAreaSortSettingEntity> sortSettingEntity)
        {
            List<OfferCatalogSortDTO> sortList = [];

            foreach (var sort in sortSettingEntity)
            {
                if (sort.IsVisible)
                {
                    foreach (var sortBy in sort.SortGroupType.SortByTypes)
                    {
                        EnumMainMenuType[] availableMainMenuTypes = [.. sort.SortGroupType.MainMenuTypes.Select(m => (EnumMainMenuType)m.MainMenuTypeId)];

                        sortList.Add(new()
                        {
                            SortType = (EnumSortBy)sortBy.SortByTypeId,
                            AvailableMainMenuTypes = availableMainMenuTypes
                        });
                    }
                }
            }

            return sortList;
        }

        private List<FilterItem> GetMenuFilterItem(IEnumerable<CatalogMainMenuItemEntity> MainMenuItemEntities)
        {
            List<FilterItem> result = [];

            var entities = MainMenuItemEntities.Where(m => m.IsVisible)
                                               .OrderBy(m => m.Order)
                                               .ToList();

            entities.ForEach(m =>
            {
                result.Add(new FilterItem
                {
                    Id = m.IdLinked,
                    ItemType = GetEnumFilterItemTypeByEnumMainMenuType((EnumMainMenuType)m.MainMenuTypeId),
                    SubFiltersItemType = GetEnumMenuItemTypeByEnumSubMenuType((EnumSubMenuType?)m.SubMenuTypeId),
                    SubFilterItems = m.SubMenu?
                    .Where(s => s.IsVisible)
                    .OrderBy(s => s.IdLinked)
                    .Select(s => new FilterItem()
                    {
                        Id = s.IdLinked,
                        ItemType = GetEnumMenuItemTypeByEnumSubMenuType((EnumSubMenuType)m.SubMenuTypeId!).Value,
                    })
                    .ToList()
                });
            });

            return result;
        }

        public static EnumFilterItemType GetEnumFilterItemTypeByEnumMainMenuType(EnumMainMenuType mainMenuType)
        {
            return mainMenuType switch
            {
                EnumMainMenuType.EventType => EnumFilterItemType.EventType,
                EnumMainMenuType.AboType => EnumFilterItemType.Abo,
                EnumMainMenuType.ProductType => EnumFilterItemType.ProductType,
                EnumMainMenuType.ProductShopType => EnumFilterItemType.ProductShopType,
                EnumMainMenuType.ProductShopWithParamsType => EnumFilterItemType.ProductShopWithParamsType,
                _ => throw new NotImplementedException()
            };
        }

        public static EnumMainMenuType GetEnumMainMenuTypeByEnumFilterItemType(EnumFilterItemType filterItemType)
        {
            return filterItemType switch
            {
                EnumFilterItemType.EventType => EnumMainMenuType.EventType,
                EnumFilterItemType.Abo => EnumMainMenuType.AboType,
                EnumFilterItemType.ProductType => EnumMainMenuType.ProductType,
                EnumFilterItemType.ProductShopType => EnumMainMenuType.ProductShopType,
                EnumFilterItemType.ProductShopWithParamsType => EnumMainMenuType.ProductShopWithParamsType,
                _ => throw new NotImplementedException()
            };
        }

        private EnumFilterItemType? GetEnumMenuItemTypeByEnumSubMenuType(EnumSubMenuType? subMenuType)
        {
            return subMenuType switch
            {
                EnumSubMenuType.EventGroup => EnumFilterItemType.EventGroup,
                EnumSubMenuType.Genre => EnumFilterItemType.Genre,
                EnumSubMenuType.Target => EnumFilterItemType.Target,
                EnumSubMenuType.SubFamily => EnumFilterItemType.SubFamily,
                _ => null,
            };
        }
    }
}
