﻿@using Microsoft.AspNetCore.Components
@using Microsoft.AspNetCore.Components.Web
@inject IThemisSupportToolsManager _themisSupportToolsManager

@foreach (var sec in ConfigIniSections)
{
    <div class="accordion-item">
        <h2 class="accordion-header d-flex justify-content-between align-items-center" id="<EMAIL>">
            <button class="accordion-button" style="background-color: white; border: 1px solid #ccc;" type="button" data-bs-toggle="collapse"
                    data-bs-target="#<EMAIL>" aria-expanded="false" aria-controls="<EMAIL>">
                @sec.SectionName
            </button>
            <!-- Icône de suppression de la section -->
            @if (!sec.SectionFields.Any(f => f.IsMandatory))
            {
                <span class="text-danger" style="cursor: pointer;" @onclick="() => OnRemoveSection.InvokeAsync(sec)">
                    <i class="bi bi-x-circle"></i> <!-- Icône de croix -->
                </span>
            }
        </h2>
        <div id="<EMAIL>" class="accordion-collapse collapse" aria-labelledby="<EMAIL>"
             data-bs-parent="@AccordionId">
            <div class="accordion-body">


                @foreach (var field in sec.SectionFields)
                {
                    string borderColor = "border-success is-valid";

                    if (string.IsNullOrWhiteSpace(field.FieldValue))
                        borderColor = "border-secondary";

                    if (field.IsMandatory && string.IsNullOrWhiteSpace(field.FieldValue))
                        borderColor = "border-danger is-invalid";

                    <div class="col-12 settings_section_item border-3 border-start p-2 @borderColor" data-parent="@sec.SectionName">
                        <Tooltip Title="@GetLocalizedComment(field.Comment)" Placement="TooltipPlacement.Top">
                            <DynamicLabel Isrequired="@field.IsMandatory" Comment="@field.Comment" CssClass="@borderColor">
                                @field.FieldName
                            </DynamicLabel>
                        </Tooltip>

                        @if (field.IsMultiple.HasValue && (bool)field.IsMultiple)
                        {

                            if (!string.IsNullOrEmpty(field.ConnectedToGroup))
                            {
                                <Select2 id="@($"{sec.SectionName}_{field.FieldName}")"
                                         class="@($"form-control {borderColor}")"
                                         data-placeholder="Choisir option"
                                         data-allow-clear="true"
                                         multiple
                                         SelectOptionsItem="@SetConnectedToGroup(field.ConnectedToGroup)"
                                         Value="field.FieldMultipleValue"
                                         OnChangeValue="((string[] value) => ChangeMultipleSelectValue(value, field))">
                                </Select2>
                            }
                            else
                            {
                                <Select2 id="@($"{sec.SectionName}_{field.FieldName}")"
                                         class="form-control"
                                         data-placeholder="Choisir option"
                                         data-allow-clear="true"
                                         multiple
                                         SelectOptionsItem="field.SelectData"
                                         Value="field.FieldMultipleValue"
                                         OnChangeValue="((string[] value) => ChangeMultipleSelectValue(value, field))">
                                </Select2>
                            }

                        }
                        else
                        {
                            @if (field.Type.ToLower() == TSTFieldTypeEnum.Select.ToString().ToLower())
                            {

                               
                                    bool showValueInSelect = !string.IsNullOrWhiteSpace(field.TableFieldValue);

                                    <Select2 id="@($"{sec.SectionName}_{field.FieldName}")"
                                             class="@($"form-control {borderColor}")"
                                             data-placeholder="Choisir option"
                                             data-allow-clear="true"
                                             SelectOptionsItem="field.SelectData"
                                             Value="field.FieldValue"
                                             OnChangeValue="((string value) => ChangeSelecValue(value, field))">
                                    </Select2>
                               
                                <div class="invalid-feedback">@Localizer["message_champ_obligatoire_xml"]</div>
                            }
                            else
                            {
                                string cssClass = "form-control";
                                if (field.Type.ToLower() == TSTFieldTypeEnum.Checkbox.ToString().ToLower())
                                    cssClass = "form-check-input";

                                <input @onchange="((e) => ChangeValue(e, field))" value="@field.FieldValue" placeholder="@KeyNotUsed" class="@cssClass @borderColor" @attributes="field.HtmlAttributes" />
                                <div class="invalid-feedback ">@Localizer["message_champ_obligatoire_xml"]</div>
                            }
                        }
                    </div>
                }
            </div>
        </div>
    </div>
}

@code {
    [Parameter]
    public List<ConfigIniSectionDTO> ConfigIniSections { get; set; }
    [Parameter]
    public List<ConfigIniSectionDTO> ConfigIniAll { get; set; } = new();

    [Parameter]
    public EventCallback<ConfigIniSectionFieldDTO> OnFieldChanged { get; set; }

    [Parameter]
    public EventCallback<ConfigIniSectionDTO> OnRemoveSection { get; set; }

    [Parameter]
    public string AccordionId { get; set; }

    [Parameter]
    public IStringLocalizer Localizer { get; set; }

    [Parameter]
    public string KeyNotUsed { get; set; } = "Clé non utilisée";

    public List<SelectLookup> MandatoryItems { get; set; }
    public List<SelectLookup> NonMandatoryItems { get; set; }


    private async Task ChangeValue(ChangeEventArgs e, ConfigIniSectionFieldDTO field)
    {
        field.FieldValue = e.Value.ToString();

        if (field.SelectData.Count > 0)
            field.SelectData.First(d => d.Value == field.FieldValue).IsSelected = true;

        if (!string.IsNullOrWhiteSpace(field.Groupe))
        {
            _themisSupportToolsManager.SetSelectDatas(ConfigIniSections);

        }

        field.IsModified = true;
        await OnFieldChanged.InvokeAsync(field);
    }

    private async Task ChangeSelecValue(string value, ConfigIniSectionFieldDTO sectionFieldDTO)
    {
        if (sectionFieldDTO.SelectData.Count > 0 && !string.IsNullOrEmpty(sectionFieldDTO.FieldValue))
        {

            var fieldWithThisValue = sectionFieldDTO.SelectData.FirstOrDefault(d => d.Value == sectionFieldDTO.FieldValue);
            if (fieldWithThisValue is not null)
                fieldWithThisValue.IsSelected = true;

        }

        sectionFieldDTO.FieldValue = value;
        sectionFieldDTO.IsModified = true;

        await OnFieldChanged.InvokeAsync(sectionFieldDTO);
    }


    private async Task ChangeMultipleSelectValue(string[] value, ConfigIniSectionFieldDTO sectionFieldDTO)
    {
        sectionFieldDTO.FieldMultipleValue = value;
        sectionFieldDTO.FieldValue = string.Join(",", value).Trim();
        sectionFieldDTO.IsModified = true;

        await OnFieldChanged.InvokeAsync(sectionFieldDTO);

    }

    private List<SelectLookup> SetConnectedToGroup(string group)
    {
        if (ConfigIniAll.Count > 0)
        {
            var connectedToGroup = _themisSupportToolsManager.GetSectionGroupe(ConfigIniAll, group);
            return connectedToGroup;
        }
        return new List<SelectLookup>();
    }

    private string GetLocalizedComment(string comment)
    {
        if (string.IsNullOrWhiteSpace(comment))
        {
            return string.Empty;
        }

        try
        {
            return Localizer[comment];
        }
        catch (Exception)
        {
            // Si la clé de localisation n'existe pas, retourner le commentaire original ou une chaîne vide
            return comment;
        }
    }


}
