﻿using Core.Themis.Libraries.BLL.Extentions.ServicesBuilder;
using Core.Themis.Libraries.BLL.Services.RedisCache.Interfaces;
using Core.Themis.Libraries.BLL.Services.RedisCache;
using Microsoft.Extensions.Caching.Distributed;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using StackExchange.Redis;
using System.Diagnostics;
using System.IO;

namespace Core.Themis.Libraries.BLL.Extentions.UnitTest
{
    public static class RodrigueConfigExtentionForUnitTest
    {
        /// <summary>
        /// Extention method for add rodrigue config in <see cref="IServiceCollection"/>
        /// </summary>
        /// <param name="services"></param>
        public static void AddRodrigueConfigurationForUnitTest(this IServiceCollection services)
        {
            var enviroment = System.Environment.CurrentDirectory;
            string projectDirectory = Directory.GetParent(enviroment)!.Parent!.Parent!.Parent!.Parent!.FullName;

            var config = new ConfigurationBuilder()
                        .SetBasePath($"{projectDirectory}\\..\\Libraries\\Core.Themis.Libraries.BLL\\Core.Themis.Libraries.BLL\\Extentions\\UnitTest")
                        .AddJsonFile("appSettingsTest.json")
                        .Build();

            services.AddSingleton<IConfiguration>(config);
        }

        public static void AddRedisForUnitTest(this IServiceCollection services)
        {
            services.AddRodrigueRedis("**************:6555,password=tS4pDJfVCN3Y0j5pvrdEBCpH0gXLZsZ/QCdN2zh6lJwoi+Va4eBUwtkZEUNYCcCl", "Rodrigue_cache_Local.");

            services.AddSingleton<IRedisCacheService>(sp =>
                new RedisCacheService(
                    "Rodrigue_cache_Local",
                    sp.GetRequiredService<IDistributedCache>(),
                    sp.GetRequiredService<IConnectionMultiplexer>()
                )
            );
        }

        public static void AddRodrigueServiceForRazorViewRenderer(this IServiceCollection services)
        {
            var diagnosticListener = new DiagnosticListener("Microsoft.AspNetCore");
            services.AddSingleton(diagnosticListener);
            services.AddSingleton<DiagnosticSource>(diagnosticListener);

            services.AddLogging();
            services.AddMvc();
        }
    }
}
