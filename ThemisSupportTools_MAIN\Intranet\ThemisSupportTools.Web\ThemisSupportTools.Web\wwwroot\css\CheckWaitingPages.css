/* Check Waiting Pages - Simple and Clean Design */

/* Filters row styling */
.filters-row {
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    gap: 10px;
    padding: 15px;
    background-color: #f8f9fa;
    border-radius: 8px;
    border: 1px solid #dee2e6;
}

.filters-row .form-control,
.filters-row .form-select {
    border-radius: 6px;
    border: 1px solid #ced4da;
    transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}

.filters-row .form-control:focus,
.filters-row .form-select:focus {
    border-color: #0d6efd;
    box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25);
}

.filters-row .btn-primary {
    border-radius: 6px;
    padding: 6px 16px;
}

/* Table improvements */
.table {
    margin-top: 20px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    border-radius: 8px;
    overflow: hidden;
}

.table thead th {
    background-color: #e9ecef;
    border-bottom: 2px solid #dee2e6;
    font-weight: 600;
    color: #495057;
    padding: 12px 8px;
    font-size: 0.9rem;
}

.table tbody td {
    padding: 10px 8px;
    vertical-align: middle;
}

/* Row styling */
.table-danger {
    background-color: rgba(220, 53, 69, 0.1) !important;
    border-left: 4px solid #dc3545;
}

.table tbody tr:hover {
    background-color: rgba(0, 123, 255, 0.05);
}

/* URL links */
.table a {
    color: #0d6efd;
    text-decoration: none;
    word-break: break-all;
}

.table a:hover {
    color: #0a58ca;
    text-decoration: underline;
}

/* Status icons */
.text-success {
    color: #198754 !important;
}

.text-warning {
    color: #fd7e14 !important;
}

.text-danger {
    color: #dc3545 !important;
}

/* Responsive improvements */
@media (max-width: 768px) {
    .filters-row {
        flex-direction: column;
        align-items: stretch;
    }

    .filters-row .form-control,
    .filters-row .form-select {
        width: 100% !important;
        margin-bottom: 10px;
    }

    .table {
        font-size: 0.85rem;
    }

    .table thead th,
    .table tbody td {
        padding: 8px 4px;
    }
}

/* Loading spinner */
.spinner {
    text-align: center;
    padding: 20px;
    font-style: italic;
    color: #6c757d;
}

/* Page title improvements */
h3 {
    color: #495057;
    font-weight: 600;
    margin-bottom: 20px;
}

/* Small improvements for better readability */
.fw-bold {
    font-weight: 600 !important;
}

.table tbody td[style*="word-break"] {
    max-width: 200px;
    word-break: break-word;
}

/* Error details styling */
.text-danger.fw-bold {
    font-size: 1.2rem;
}

small.text-danger {
    display: block;
    line-height: 1.3;
    max-height: 60px;
    overflow-y: auto;
}


