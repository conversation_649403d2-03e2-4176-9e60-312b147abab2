/* Check Waiting Pages - Beautiful Design */

/* Main Container */
.check-waiting-pages-container {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    padding: 0;
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

/* Header Section */
.page-header {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-bottom: 1px solid rgba(255, 255, 255, 0.2);
    padding: 2rem 0;
    margin-bottom: 2rem;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.header-content {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.header-icon {
    width: 60px;
    height: 60px;
    background: linear-gradient(135deg, #667eea, #764ba2);
    border-radius: 15px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    color: white;
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
    transition: transform 0.3s ease;
}

.header-icon:hover {
    transform: scale(1.05);
}

.page-title {
    font-size: 2rem;
    font-weight: 700;
    color: #2d3748;
    margin: 0;
    background: linear-gradient(135deg, #667eea, #764ba2);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.page-subtitle {
    color: #718096;
    margin: 0;
    font-size: 1.1rem;
    font-weight: 500;
}

.environment-badge .badge {
    font-size: 0.9rem;
    padding: 0.5rem 1rem;
    border-radius: 25px;
    box-shadow: 0 4px 15px rgba(255, 193, 7, 0.3);
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.05); }
    100% { transform: scale(1); }
}

/* Loading Section */
.loading-section, .error-section {
    margin-bottom: 2rem;
}

.loading-section .card, .error-section .card {
    border-radius: 20px;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.loading-spinner {
    display: inline-block;
    width: 40px;
    height: 40px;
    border: 4px solid rgba(102, 126, 234, 0.3);
    border-radius: 50%;
    border-top-color: #667eea;
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

.error-icon {
    font-size: 3rem;
}

/* Filters Section */
.filters-section {
    margin-bottom: 2rem;
}

.filters-section .card {
    border-radius: 20px;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.form-label {
    color: #4a5568;
    font-weight: 600;
    margin-bottom: 0.5rem;
}

.form-control, .form-select {
    border-radius: 12px;
    border: 2px solid #e2e8f0;
    transition: all 0.3s ease;
    background-color: rgba(255, 255, 255, 0.9);
}

.form-control:focus, .form-select:focus {
    border-color: #667eea;
    box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
    background-color: white;
}

.btn-primary {
    background: linear-gradient(135deg, #667eea, #764ba2);
    border: none;
    border-radius: 12px;
    font-weight: 600;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
}

.stats-info {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 100%;
}

/* Table Section */
.table-section .card {
    border-radius: 20px;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    overflow: hidden;
    border: 1px solid rgba(255, 255, 255, 0.2);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.card-header {
    background: rgba(248, 250, 252, 0.8);
    border-bottom: 1px solid rgba(226, 232, 240, 0.5);
}

.card-title {
    color: #2d3748;
    font-weight: 600;
}

.modern-table {
    font-size: 0.9rem;
    margin-bottom: 0;
}

.table-header {
    background: linear-gradient(135deg, #f8fafc, #e2e8f0);
    border-bottom: 2px solid #e2e8f0;
}

.table-header th {
    border: none;
    padding: 1rem 0.75rem;
    font-weight: 600;
    color: #4a5568;
    font-size: 0.85rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    position: relative;
}

.th-content {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.25rem;
}

.sortable {
    cursor: pointer;
    transition: all 0.2s ease;
    position: relative;
}

.sortable:hover {
    background-color: rgba(102, 126, 234, 0.1);
}

.sortable::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 50%;
    width: 0;
    height: 2px;
    background: linear-gradient(135deg, #667eea, #764ba2);
    transition: all 0.3s ease;
    transform: translateX(-50%);
}

.sortable:hover::after {
    width: 80%;
}

/* Table Rows */
.table-row {
    transition: all 0.3s ease;
    border-bottom: 1px solid #f1f5f9;
}

.table-row:hover {
    background-color: rgba(102, 126, 234, 0.05);
    transform: scale(1.005);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.warning-row {
    background-color: rgba(255, 193, 7, 0.05);
    border-left: 4px solid #ffc107;
}

.table-row td {
    padding: 1rem 0.75rem;
    vertical-align: middle;
}

.structure-name {
    display: flex;
    align-items: center;
    font-weight: 500;
    color: #2d3748;
}

/* Status Icons */
.status-icon {
    font-size: 1.2rem;
    padding: 0.25rem;
    border-radius: 50%;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
}

.status-icon.success {
    color: #38a169;
    background-color: rgba(56, 161, 105, 0.1);
}

.status-icon.success:hover {
    background-color: rgba(56, 161, 105, 0.2);
    transform: scale(1.1);
}

.status-icon.warning {
    color: #d69e2e;
    background-color: rgba(214, 158, 46, 0.1);
    animation: warning-pulse 2s infinite;
}

@keyframes warning-pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.7; }
}

.status-icon.error {
    color: #e53e3e;
    background-color: rgba(229, 62, 62, 0.1);
}

/* URL Links */
.url-cell {
    max-width: 200px;
}

.url-link {
    color: #667eea;
    text-decoration: none;
    font-weight: 500;
    transition: all 0.2s ease;
    display: inline-flex;
    align-items: center;
    gap: 0.25rem;
    padding: 0.25rem 0.5rem;
    border-radius: 8px;
    font-size: 0.85rem;
}

.url-link:hover {
    color: #764ba2;
    background-color: rgba(102, 126, 234, 0.1);
    text-decoration: none;
    transform: translateX(2px);
}

/* Details Cell */
.details-cell {
    max-width: 250px;
}

.error-details {
    font-size: 0.8rem;
    color: #e53e3e;
    background-color: rgba(229, 62, 62, 0.1);
    padding: 0.5rem;
    border-radius: 8px;
    border-left: 3px solid #e53e3e;
    white-space: pre-line;
}

/* No Data Message */
.no-data-message {
    text-align: center;
    padding: 2rem;
}

.no-data-message i {
    font-size: 3rem;
    display: block;
    margin-bottom: 1rem;
}

/* Action Buttons */
.btn-group-sm .btn {
    border-radius: 8px;
    font-weight: 500;
    transition: all 0.3s ease;
    border: 1px solid #e2e8f0;
}

.btn-outline-primary:hover {
    background: linear-gradient(135deg, #667eea, #764ba2);
    border-color: #667eea;
    transform: translateY(-1px);
}

.btn-outline-secondary:hover {
    background-color: #718096;
    border-color: #718096;
    transform: translateY(-1px);
}

/* Footer */
.card-footer {
    background: rgba(248, 250, 252, 0.8);
    border-radius: 0 0 20px 20px;
    border-top: 1px solid rgba(226, 232, 240, 0.5);
}

/* Responsive Design */
@media (max-width: 1200px) {
    .url-cell {
        max-width: 150px;
    }
    
    .url-link {
        font-size: 0.8rem;
    }
}

@media (max-width: 768px) {
    .page-title {
        font-size: 1.5rem;
    }
    
    .header-icon {
        width: 50px;
        height: 50px;
        font-size: 1.2rem;
    }
    
    .table-responsive {
        font-size: 0.8rem;
    }
    
    .filters-section .row > div {
        margin-bottom: 1rem;
    }
    
    .btn-group {
        flex-direction: column;
        width: 100%;
    }
    
    .btn-group .btn {
        margin-bottom: 0.25rem;
    }
}

@media (max-width: 576px) {
    .page-header {
        padding: 1rem 0;
    }
    
    .header-content {
        flex-direction: column;
        text-align: center;
        gap: 0.5rem;
    }
    
    .environment-badge {
        margin-top: 1rem;
    }
    
    .card-header .row {
        flex-direction: column;
        gap: 1rem;
    }
    
    .table-responsive {
        font-size: 0.75rem;
    }
    
    .status-icon {
        font-size: 1rem;
    }
}

/* Custom Scrollbar */
.table-responsive::-webkit-scrollbar {
    height: 8px;
}

.table-responsive::-webkit-scrollbar-track {
    background: #f1f5f9;
    border-radius: 4px;
}

.table-responsive::-webkit-scrollbar-thumb {
    background: linear-gradient(135deg, #667eea, #764ba2);
    border-radius: 4px;
}

.table-responsive::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(135deg, #5a67d8, #6b46c1);
}
