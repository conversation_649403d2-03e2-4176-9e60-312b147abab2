/* 🚀 CHECK WAITING PAGES - WAW DESIGN 🚀 */

@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap');

/* Global WAW Styles */
* {
    box-sizing: border-box;
}

.waw-container {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    padding: 0;
    margin: 0;
}

/* 🎯 Hero Header */
.hero-header {
    position: relative;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 4rem 2rem;
    text-align: center;
    overflow: hidden;
}

.hero-bg {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="75" cy="75" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="50" cy="10" r="0.5" fill="rgba(255,255,255,0.05)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
    opacity: 0.3;
}

.hero-content {
    position: relative;
    z-index: 2;
}

.hero-icon {
    width: 80px;
    height: 80px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 2rem;
    font-size: 2rem;
    backdrop-filter: blur(10px);
    border: 2px solid rgba(255, 255, 255, 0.3);
    animation: heroIconFloat 3s ease-in-out infinite;
}

@keyframes heroIconFloat {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-10px); }
}

.hero-title {
    font-size: 3.5rem;
    font-weight: 800;
    margin: 0 0 1rem 0;
    background: linear-gradient(45deg, #fff, #f0f8ff);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    text-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
}

.hero-subtitle {
    font-size: 1.3rem;
    font-weight: 400;
    margin: 0 0 3rem 0;
    opacity: 0.9;
}

.hero-stats {
    display: flex;
    justify-content: center;
    gap: 3rem;
    flex-wrap: wrap;
}

.stat-item {
    text-align: center;
    background: rgba(255, 255, 255, 0.1);
    padding: 1.5rem 2rem;
    border-radius: 20px;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    transition: transform 0.3s ease;
}

.stat-item:hover {
    transform: translateY(-5px);
}

.stat-number {
    display: block;
    font-size: 2.5rem;
    font-weight: 700;
    line-height: 1;
}

.stat-label {
    display: block;
    font-size: 0.9rem;
    opacity: 0.8;
    margin-top: 0.5rem;
    text-transform: uppercase;
    letter-spacing: 1px;
}

/* 🔄 Loading WAW */
.loading-waw {
    text-align: center;
    padding: 5rem 2rem;
    background: rgba(255, 255, 255, 0.95);
    margin: 2rem;
    border-radius: 30px;
    backdrop-filter: blur(10px);
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.1);
}

.loading-animation {
    display: flex;
    justify-content: center;
    gap: 1rem;
    margin-bottom: 2rem;
}

.loading-circle {
    width: 20px;
    height: 20px;
    background: linear-gradient(135deg, #667eea, #764ba2);
    border-radius: 50%;
    animation: loadingBounce 1.4s ease-in-out infinite both;
}

.loading-circle:nth-child(1) { animation-delay: -0.32s; }
.loading-circle:nth-child(2) { animation-delay: -0.16s; }

@keyframes loadingBounce {
    0%, 80%, 100% { transform: scale(0); }
    40% { transform: scale(1); }
}

.loading-text {
    font-size: 2rem;
    font-weight: 600;
    color: #333;
    margin: 0 0 1rem 0;
}

.loading-subtext {
    color: #666;
    font-size: 1.1rem;
}

/* ⚠️ Error WAW */
.error-waw {
    text-align: center;
    padding: 5rem 2rem;
    background: rgba(255, 255, 255, 0.95);
    margin: 2rem;
    border-radius: 30px;
    backdrop-filter: blur(10px);
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.1);
}

.error-icon {
    font-size: 4rem;
    color: #ff6b6b;
    margin-bottom: 2rem;
}

/* 🎛️ Control Panel */
.control-panel {
    background: rgba(255, 255, 255, 0.95);
    margin: 2rem;
    border-radius: 30px;
    backdrop-filter: blur(10px);
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.1);
    overflow: hidden;
}

.panel-header {
    background: linear-gradient(135deg, #f8fafc, #e2e8f0);
    padding: 2rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 1px solid rgba(0, 0, 0, 0.1);
}

.panel-header h2 {
    margin: 0;
    font-size: 1.8rem;
    font-weight: 700;
    color: #2d3748;
}

.panel-actions {
    display: flex;
    gap: 1rem;
}

.filters-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
    padding: 2rem;
    align-items: end;
}

.filter-group {
    display: flex;
    flex-direction: column;
}

.filter-label {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-weight: 600;
    color: #4a5568;
    margin-bottom: 0.8rem;
    font-size: 0.9rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.filter-input,
.filter-select {
    padding: 1rem 1.5rem;
    border: 2px solid #e2e8f0;
    border-radius: 15px;
    font-size: 1rem;
    transition: all 0.3s ease;
    background: white;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
}

.filter-input:focus,
.filter-select:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
    transform: translateY(-2px);
}

.results-summary {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1.5rem 2rem;
    background: rgba(102, 126, 234, 0.05);
    border-top: 1px solid rgba(0, 0, 0, 0.1);
}

.results-count {
    font-weight: 600;
    color: #4a5568;
    font-size: 1.1rem;
}

.status-indicators {
    display: flex;
    gap: 1rem;
}

.indicator {
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-size: 0.9rem;
    font-weight: 600;
}

.indicator.success {
    background: rgba(72, 187, 120, 0.1);
    color: #38a169;
}

.indicator.warning {
    background: rgba(237, 137, 54, 0.1);
    color: #dd6b20;
}

/* 🎨 WAW Buttons */
.btn-waw {
    padding: 1rem 2rem;
    border: none;
    border-radius: 15px;
    font-weight: 600;
    font-size: 1rem;
    cursor: pointer;
    transition: all 0.3s ease;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    text-decoration: none;
    position: relative;
    overflow: hidden;
}

.btn-waw::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
}

.btn-waw:hover::before {
    left: 100%;
}

.btn-waw-primary {
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
}

.btn-waw-primary:hover {
    transform: translateY(-3px);
    box-shadow: 0 15px 35px rgba(102, 126, 234, 0.4);
}

.btn-waw-secondary {
    background: linear-gradient(135deg, #718096, #4a5568);
    color: white;
    box-shadow: 0 8px 25px rgba(113, 128, 150, 0.3);
}

.btn-waw-secondary:hover {
    transform: translateY(-3px);
    box-shadow: 0 15px 35px rgba(113, 128, 150, 0.4);
}

.btn-waw-outline {
    background: rgba(255, 255, 255, 0.9);
    color: #4a5568;
    border: 2px solid #e2e8f0;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.btn-waw-outline:hover {
    background: white;
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

/* 📊 Data Grid Container */
.data-grid-container {
    background: rgba(255, 255, 255, 0.95);
    margin: 2rem;
    border-radius: 30px;
    backdrop-filter: blur(10px);
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.1);
    overflow: hidden;
}

.grid-header {
    background: linear-gradient(135deg, #f8fafc, #e2e8f0);
    padding: 2rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 1px solid rgba(0, 0, 0, 0.1);
}

.grid-header h2 {
    margin: 0;
    font-size: 1.8rem;
    font-weight: 700;
    color: #2d3748;
}

.grid-actions {
    display: flex;
    gap: 1rem;
}

/* 🃏 Data Grid Cards */
.data-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
    gap: 2rem;
    padding: 2rem;
}

.data-card {
    background: white;
    border-radius: 20px;
    overflow: hidden;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    border: 2px solid transparent;
}

.data-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 20px 50px rgba(0, 0, 0, 0.15);
}

.success-card {
    border-color: rgba(72, 187, 120, 0.3);
}

.success-card:hover {
    border-color: rgba(72, 187, 120, 0.5);
    box-shadow: 0 20px 50px rgba(72, 187, 120, 0.2);
}

.error-card {
    border-color: rgba(245, 101, 101, 0.3);
    background: linear-gradient(135deg, rgba(255, 255, 255, 1), rgba(254, 242, 242, 0.5));
}

.error-card:hover {
    border-color: rgba(245, 101, 101, 0.5);
    box-shadow: 0 20px 50px rgba(245, 101, 101, 0.2);
}

/* 🎯 Card Header */
.card-header-waw {
    padding: 1.5rem;
    background: linear-gradient(135deg, #f8fafc, #e2e8f0);
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 1px solid rgba(0, 0, 0, 0.1);
}

.structure-info {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.structure-id {
    display: flex;
    align-items: center;
}

.id-badge {
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
    padding: 0.5rem 1rem;
    border-radius: 15px;
    font-weight: 700;
    font-size: 1.1rem;
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
}

.structure-name h3 {
    margin: 0;
    font-size: 1.2rem;
    font-weight: 600;
    color: #2d3748;
    line-height: 1.2;
}

.version-badge {
    background: rgba(102, 126, 234, 0.1);
    color: #667eea;
    padding: 0.3rem 0.8rem;
    border-radius: 10px;
    font-size: 0.8rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    margin-top: 0.3rem;
    display: inline-block;
}

.status-success {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    color: #38a169;
    font-weight: 600;
    background: rgba(72, 187, 120, 0.1);
    padding: 0.8rem 1.2rem;
    border-radius: 15px;
}

.status-error {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    color: #e53e3e;
    font-weight: 600;
    background: rgba(245, 101, 101, 0.1);
    padding: 0.8rem 1.2rem;
    border-radius: 15px;
    animation: errorPulse 2s infinite;
}

@keyframes errorPulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.7; }
}

/* 📱 Card Body */
.card-body-waw {
    padding: 1.5rem;
}

.url-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: 1rem;
    margin-bottom: 1.5rem;
}

.url-item {
    background: rgba(248, 250, 252, 0.8);
    padding: 1rem;
    border-radius: 15px;
    border: 1px solid rgba(226, 232, 240, 0.5);
    transition: all 0.3s ease;
}

.url-item:hover {
    background: rgba(248, 250, 252, 1);
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.url-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 0.8rem;
}

.url-label {
    font-size: 0.8rem;
    font-weight: 600;
    color: #4a5568;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.status-indicator {
    width: 24px;
    height: 24px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.8rem;
    font-weight: 600;
}

.status-ok {
    background: rgba(72, 187, 120, 0.2);
    color: #38a169;
}

.status-error {
    background: rgba(245, 101, 101, 0.2);
    color: #e53e3e;
}

.url-link-waw {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    color: #667eea;
    text-decoration: none;
    font-weight: 500;
    font-size: 0.9rem;
    transition: all 0.3s ease;
    padding: 0.5rem;
    border-radius: 8px;
}

.url-link-waw:hover {
    background: rgba(102, 126, 234, 0.1);
    color: #5a67d8;
    transform: translateX(3px);
}

.url-empty {
    color: #a0aec0;
    font-style: italic;
    font-size: 0.9rem;
}

/* 🐛 Error Details */
.error-details-waw {
    background: rgba(245, 101, 101, 0.05);
    border: 1px solid rgba(245, 101, 101, 0.2);
    border-radius: 15px;
    padding: 1rem;
    margin-top: 1rem;
}

.error-header {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    color: #e53e3e;
    font-weight: 600;
    margin-bottom: 0.8rem;
    font-size: 0.9rem;
}

.error-content {
    color: #c53030;
    font-size: 0.85rem;
    line-height: 1.4;
    white-space: pre-line;
    background: rgba(255, 255, 255, 0.8);
    padding: 0.8rem;
    border-radius: 8px;
    border-left: 3px solid #e53e3e;
}

/* 🎬 Card Footer */
.card-footer-waw {
    padding: 1rem 1.5rem;
    background: rgba(248, 250, 252, 0.5);
    border-top: 1px solid rgba(226, 232, 240, 0.5);
}

.action-buttons {
    display: flex;
    gap: 0.8rem;
    justify-content: center;
}

.btn-action {
    padding: 0.6rem 1.2rem;
    border: none;
    border-radius: 10px;
    font-size: 0.85rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 0.4rem;
    flex: 1;
    justify-content: center;
}

.btn-test {
    background: linear-gradient(135deg, #48bb78, #38a169);
    color: white;
    box-shadow: 0 4px 15px rgba(72, 187, 120, 0.3);
}

.btn-test:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(72, 187, 120, 0.4);
}

.btn-details {
    background: linear-gradient(135deg, #4299e1, #3182ce);
    color: white;
    box-shadow: 0 4px 15px rgba(66, 153, 225, 0.3);
}

.btn-details:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(66, 153, 225, 0.4);
}

.btn-logs {
    background: linear-gradient(135deg, #ed8936, #dd6b20);
    color: white;
    box-shadow: 0 4px 15px rgba(237, 137, 54, 0.3);
}

.btn-logs:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(237, 137, 54, 0.4);
}

/* 🔍 No Data WAW */
.no-data-waw {
    text-align: center;
    padding: 5rem 2rem;
    color: #4a5568;
}

.no-data-icon {
    font-size: 5rem;
    color: #a0aec0;
    margin-bottom: 2rem;
    opacity: 0.7;
}

.no-data-waw h3 {
    font-size: 2rem;
    font-weight: 600;
    margin: 0 0 1rem 0;
    color: #2d3748;
}

.no-data-waw p {
    font-size: 1.1rem;
    margin: 0 0 2rem 0;
    color: #718096;
}

/* 📱 Responsive WAW */
@media (max-width: 1200px) {
    .data-grid {
        grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
    }

    .hero-title {
        font-size: 2.5rem;
    }

    .hero-stats {
        gap: 2rem;
    }
}

@media (max-width: 768px) {
    .waw-container {
        padding: 0;
    }

    .hero-header {
        padding: 3rem 1rem;
    }

    .hero-title {
        font-size: 2rem;
    }

    .hero-stats {
        flex-direction: column;
        gap: 1rem;
    }

    .stat-item {
        padding: 1rem 1.5rem;
    }

    .filters-grid {
        grid-template-columns: 1fr;
        gap: 1.5rem;
        padding: 1.5rem;
    }

    .data-grid {
        grid-template-columns: 1fr;
        gap: 1.5rem;
        padding: 1.5rem;
    }

    .panel-header,
    .grid-header {
        flex-direction: column;
        gap: 1rem;
        text-align: center;
    }

    .url-grid {
        grid-template-columns: 1fr;
    }

    .action-buttons {
        flex-direction: column;
    }

    .card-header-waw {
        flex-direction: column;
        gap: 1rem;
        text-align: center;
    }
}

@media (max-width: 480px) {
    .hero-title {
        font-size: 1.8rem;
    }

    .hero-subtitle {
        font-size: 1rem;
    }

    .stat-number {
        font-size: 2rem;
    }

    .control-panel,
    .data-grid-container {
        margin: 1rem;
        border-radius: 20px;
    }
}

/* ✨ Special Effects */
@keyframes shimmer {
    0% { background-position: -200px 0; }
    100% { background-position: calc(200px + 100%) 0; }
}

.shimmer {
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200px 100%;
    animation: shimmer 1.5s infinite;
}

/* 🎯 Scroll Animations */
@media (prefers-reduced-motion: no-preference) {
    .data-card {
        opacity: 0;
        transform: translateY(20px);
        animation: fadeInUp 0.6s ease forwards;
    }

    .data-card:nth-child(1) { animation-delay: 0.1s; }
    .data-card:nth-child(2) { animation-delay: 0.2s; }
    .data-card:nth-child(3) { animation-delay: 0.3s; }
    .data-card:nth-child(4) { animation-delay: 0.4s; }
    .data-card:nth-child(5) { animation-delay: 0.5s; }
    .data-card:nth-child(6) { animation-delay: 0.6s; }
}

@keyframes fadeInUp {
    to {
        opacity: 1;
        transform: translateY(0);
    }
}


