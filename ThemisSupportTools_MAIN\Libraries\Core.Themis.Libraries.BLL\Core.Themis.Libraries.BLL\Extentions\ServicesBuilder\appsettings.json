{
  "Logging": {
    "LogLevel": {
      "Default": "Information",
      "Microsoft": "Warning",
      "Microsoft.Hosting.Lifetime": "Information"
    },
    "Console": {
      "LogLevel": {
        "Default": "Error",
        "Microsoft": "Error",
        "Core.Themis.API.Customers.Controllers": "Trace"
      }
    },
    "File": {
      "Default": "Information",
      "Microsoft": "Warning",
      "Microsoft.Hosting.Lifetime": "Information"
    },
    "RodrigueFileLogger": {
      "Options": {
        "FolderPath": "D:\\LOGS\\Webservices\\API\\OFFERS",
        "FilePath": "log_{structureid}_{date}.log"
      },
      "LogLevel": {
        "Default": "Error",
        "Microsoft": "Error",
        "Core.Themis.API.Customers.Controllers": "Information"
      }
    }

  },
  "AllowedHosts": "*",
  "ApiSecretKey": "secret401b09eab3c013d4ca54922bb802bec8fd5318192b0a75f201d8b3727429090fb337591abd3e44453b954555b7a0812e1081c39b740293f765eae731f5a65ed1",
  "ConnectionStrings": {
    "WsAdminDB": "Server=************;Database=WSAdmin;Persist Security Info=True;User ID=SphereWeb;Password=************************************************************************************************************;MultipleActiveResultSets=true;TrustServerCertificate=True;",
    "GlobalOpinionDB": "Server=************;Database=GlobalWebLibrary;Persist Security Info=True;User ID=sa;Password=************************;MultipleActiveResultSets=true;TrustServerCertificate=True;",
    "WebLibraryDB": "Server=************;Database=GlobalWebLibrary;Persist Security Info=True;User ID=sa;Password=************************;MultipleActiveResultSets=true;TrustServerCertificate=True;",
    "QueuingDB": "Server=**************;Database=WAITINGPAGES;Persist Security Info=True;User ID=ROD_WAITINGPAGES;Password=****************************************************************;MultipleActiveResultSets=true;TrustServerCertificate=True;",
    "ThemisSupportTools": "Server=************;Database=WSAdmin;Persist Security Info=True;User ID=SphereWeb;Password=************************************************************************************************************;MultipleActiveResultSets=true;TrustServerCertificate=True;"

  },
  "PathScriptSqlCommons": "\\\\**************\\webservices\\PROD\\libraries\\1.0.5\\[directory\\][filename][.structureid].sql",
  "PathForSqlScript": "\\\\**************\\webservices\\dev\\libraries\\LIBRARIES_SQLSCRIPTS\\{0}\\{1}.sql",

  "WsAdminConnectionCache": 300,
  "TypeRun": "PROD",
  "ConfigIniPath": "\\\\Srv-paiement64\\customerfiles\\PROD\\[structureId]\\CONFIGSERVER\\config.ini.xml",
  "Cache": {
    //Cache pour la liste des commentaires au tarif
    "CommentsTarifAbsoluteExpiration": 10,
    "CommentsTarifSlidingExpiration": 2,
    "StructurePrefsAbsoluteExpiration": 600
  }

}