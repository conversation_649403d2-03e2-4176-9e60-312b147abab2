﻿@page "/preparation-mise-vente-checkwaitingpages"

<link href="~/css/CheckWaitingPages.css" rel="stylesheet" />

<h3>Check waiting pages</h3>

@if (isLoading)
{
    <div class="spinner">Chargement des données...</div>
}
else if (fileAttenteInfos == null)
{
    <div>Accès refusé ou aucune donnée disponible.</div>
}
else
{
    <h3>Liste FileAttentes</h3>

    <div class="filters-row mb-3">
        <input type="text" placeholder="Filtrer StructureId"
               @bind="filterStructureId" class="form-control d-inline-block me-2" style="width: 200px;" />

        <span class="me-2">Version active :</span>
        <select @bind="filterVersionActive" class="form-select d-inline-block me-2" style="width: 120px;">
            <option value="">all</option>
            <option value="v1">v1</option>
            <option value="v2">v2</option>
            <option value="maintenance">maintenance</option>
        </select>

        <span class="me-2">erreurs :</span>
        <select @bind="filterErrors" class="form-select d-inline-block me-2" style="width: 100px;">
            <option value="">all</option>
            <option value="0">✅</option>
            <option value="1">⚠️</option>
        </select>

        <button @onclick="Reload" class="btn btn-primary">Filtrer</button>
    </div>


    <table class="table table-bordered table-sm table-hover">
        <thead class="table-light">
            <tr>
                <th>Id</th>
                <th>Nom</th>
                <th>version active</th>
                <th>urlV1</th>
                <th>urlV1 Ok</th>
                <th>urlV2</th>
                <th>urlV2 Ok</th>
                <th>url Maintenance</th>
                <th>urlMaintenance Ok</th>
                <th>pb</th>
                <th>pbs details</th>
            </tr>
        </thead>
        <tbody>
            @if (FilteredInfos != null && FilteredInfos.Any())
            {
                @foreach (var s in FilteredInfos)
                {
                    var pbs = s.listPbsFA != null && s.listPbsFA.Any();
                    var trclass = pbs ? "table-danger" : "";

                    <tr class="@trclass">
                        <td class="fw-bold">@s.StructureId</td>
                        <td style="word-break:break-all;">@s.Name</td>
                        <td>@s.versionFileAttente</td>
                        <td style="word-break:break-all;">
                            @if (!string.IsNullOrEmpty(s.urlV1))
                            {
                                <a href="@s.urlV1" target="_blank" class="text-decoration-none">@s.urlV1</a>
                            }
                        </td>
                        <td class="text-center">
                            @if (s.isUrlV1OK)
                            {
                                <span class="text-success">✅</span>
                            }
                            else
                            {
                                <span class="text-warning">⚠️</span>
                            }
                        </td>
                        <td style="word-break:break-all;">
                            @if (!string.IsNullOrEmpty(s.urlV2))
                            {
                                <a href="@s.urlV2" target="_blank" class="text-decoration-none">@s.urlV2</a>
                            }
                        </td>
                        <td class="text-center">
                            @if (s.isUrlV2OK)
                            {
                                <span class="text-success">✅</span>
                            }
                            else
                            {
                                <span class="text-warning">⚠️</span>
                            }
                        </td>
                        <td style="word-break:break-all;">
                            @if (!string.IsNullOrEmpty(s.urlMaintenance))
                            {
                                <a href="@s.urlMaintenance" target="_blank" class="text-decoration-none">@s.urlMaintenance</a>
                            }
                        </td>
                        <td class="text-center">
                            @if (s.isUrlMaintenanceOK)
                            {
                                <span class="text-success">✅</span>
                            }
                            else
                            {
                                <span class="text-warning">⚠️</span>
                            }
                        </td>
                        <td class="text-center">
                            @if (pbs)
                            {
                                <span class="text-danger fw-bold">!</span>
                            }
                        </td>
                        <td style="max-width: 200px; word-break: break-word;">
                            @if (pbs)
                            {
                                <small class="text-danger">@GetErrors(s.listPbsFA)</small>
                            }
                        </td>
                    </tr>
                }
            }
            else
            {
                <tr>
                    <td colspan="11" class="text-center"><em>Aucune donnée trouvée</em></td>
                </tr>
            }
        </tbody>
    </table>
}
