﻿@page "/preparation-mise-vente-checkwaitingpages"

<link href="~/css/CheckWaitingPages.css" rel="stylesheet" />

<div class="waw-container">
    <!-- Hero Header -->
    <div class="hero-header">
        <div class="hero-bg"></div>
        <div class="hero-content">
            <div class="hero-icon">
                <i class="fas fa-rocket"></i>
            </div>
            <h1 class="hero-title">Check Waiting Pages</h1>
            <p class="hero-subtitle">Surveillance en temps réel des files d'attente</p>
            <div class="hero-stats">
                <div class="stat-item">
                    <span class="stat-number">@(fileAttenteInfos?.Count ?? 0)</span>
                    <span class="stat-label">Structures</span>
                </div>
                <div class="stat-item">
                    <span class="stat-number">@(FilteredInfos?.Count(s => s.listPbsFA?.Any() == true) ?? 0)</span>
                    <span class="stat-label">Erreurs</span>
                </div>
                <div class="stat-item">
                    <span class="stat-number">@(FilteredInfos?.Count(s => s.isUrlV1OK && s.isUrlV2OK && s.isUrlMaintenanceOK) ?? 0)</span>
                    <span class="stat-label">OK</span>
                </div>
            </div>
        </div>
    </div>

    @if (isLoading)
    {
        <div class="loading-waw">
            <div class="loading-animation">
                <div class="loading-circle"></div>
                <div class="loading-circle"></div>
                <div class="loading-circle"></div>
            </div>
            <h3 class="loading-text">Chargement des données...</h3>
            <p class="loading-subtext">Analyse en cours des files d'attente</p>
        </div>
    }
    else if (fileAttenteInfos == null)
    {
        <div class="error-waw">
            <div class="error-icon">
                <i class="fas fa-shield-alt"></i>
            </div>
            <h3>Accès Restreint</h3>
            <p>Vous n'avez pas les permissions nécessaires pour accéder à cette section.</p>
        </div>
    }
    else
    {
        <!-- Control Panel -->
        <div class="control-panel">
            <div class="panel-header">
                <h2><i class="fas fa-sliders-h me-2"></i>Centre de Contrôle</h2>
                <div class="panel-actions">
                    <button class="btn-waw btn-waw-secondary" @onclick="Reload">
                        <i class="fas fa-sync-alt"></i>
                        Actualiser
                    </button>
                </div>
            </div>

            <div class="filters-grid">
                <div class="filter-group">
                    <label class="filter-label">
                        <i class="fas fa-search"></i>
                        Structure ID
                    </label>
                    <input type="text"
                           placeholder="Rechercher par ID..."
                           @bind="filterStructureId"
                           class="filter-input" />
                </div>

                <div class="filter-group">
                    <label class="filter-label">
                        <i class="fas fa-code-branch"></i>
                        Version
                    </label>
                    <select @bind="filterVersionActive" class="filter-select">
                        <option value="">Toutes les versions</option>
                        <option value="v1">Version 1</option>
                        <option value="v2">Version 2</option>
                        <option value="maintenance">Maintenance</option>
                    </select>
                </div>

                <div class="filter-group">
                    <label class="filter-label">
                        <i class="fas fa-exclamation-triangle"></i>
                        État
                    </label>
                    <select @bind="filterErrors" class="filter-select">
                        <option value="">Tous les états</option>
                        <option value="0">✨ Fonctionnel</option>
                        <option value="1">⚠️ Problèmes détectés</option>
                    </select>
                </div>

                <div class="filter-group">
                    <button @onclick="Reload" class="btn-waw btn-waw-primary">
                        <i class="fas fa-filter"></i>
                        Appliquer les filtres
                    </button>
                </div>
            </div>

            <div class="results-summary">
                <span class="results-count">@(FilteredInfos?.Count() ?? 0) résultats trouvés</span>
                <div class="status-indicators">
                    <span class="indicator success">@(FilteredInfos?.Count(s => !(s.listPbsFA?.Any() == true)) ?? 0) OK</span>
                    <span class="indicator warning">@(FilteredInfos?.Count(s => s.listPbsFA?.Any() == true) ?? 0) Erreurs</span>
                </div>
            </div>
        </div>


        <!-- Data Grid WAW -->
        <div class="data-grid-container">
            <div class="grid-header">
                <h2><i class="fas fa-database me-2"></i>Monitoring Dashboard</h2>
                <div class="grid-actions">
                    <button class="btn-waw btn-waw-outline">
                        <i class="fas fa-download"></i>
                        Export
                    </button>
                    <button class="btn-waw btn-waw-outline">
                        <i class="fas fa-chart-line"></i>
                        Analytics
                    </button>
                </div>
            </div>

            @if (FilteredInfos != null && FilteredInfos.Any())
            {
                <div class="data-grid">
                    @foreach (var s in FilteredInfos)
                    {
                        var pbs = s.listPbsFA != null && s.listPbsFA.Any();
                        var cardClass = pbs ? "data-card error-card" : "data-card success-card";
                        var statusClass = pbs ? "status-error" : "status-success";

                        <div class="@cardClass">
                            <div class="card-header-waw">
                                <div class="structure-info">
                                    <div class="structure-id">
                                        <span class="id-badge">@s.StructureId</span>
                                    </div>
                                    <div class="structure-name">
                                        <h3>@s.Name</h3>
                                        <span class="version-badge">@s.versionFileAttente</span>
                                    </div>
                                </div>
                                <div class="@statusClass">
                                    @if (pbs)
                                    {
                                        <i class="fas fa-exclamation-triangle"></i>
                                        <span>Erreurs détectées</span>
                                    }
                                    else
                                    {
                                        <i class="fas fa-check-circle"></i>
                                        <span>Opérationnel</span>
                                    }
                                </div>
                            </div>

                            <div class="card-body-waw">
                                <div class="url-grid">
                                    <!-- URL V1 -->
                                    <div class="url-item">
                                        <div class="url-header">
                                            <span class="url-label">Version 1</span>
                                            <div class="status-indicator @(s.isUrlV1OK ? "status-ok" : "status-error")">
                                                <i class="fas @(s.isUrlV1OK ? "fa-check" : "fa-times")"></i>
                                            </div>
                                        </div>
                                        @if (!string.IsNullOrEmpty(s.urlV1))
                                        {
                                            <a href="@s.urlV1" target="_blank" class="url-link-waw">
                                                <i class="fas fa-external-link-alt"></i>
                                                @GetShortUrl(s.urlV1)
                                            </a>
                                        }
                                        else
                                        {
                                            <span class="url-empty">Non configuré</span>
                                        }
                                    </div>

                                    <!-- URL V2 -->
                                    <div class="url-item">
                                        <div class="url-header">
                                            <span class="url-label">Version 2</span>
                                            <div class="status-indicator @(s.isUrlV2OK ? "status-ok" : "status-error")">
                                                <i class="fas @(s.isUrlV2OK ? "fa-check" : "fa-times")"></i>
                                            </div>
                                        </div>
                                        @if (!string.IsNullOrEmpty(s.urlV2))
                                        {
                                            <a href="@s.urlV2" target="_blank" class="url-link-waw">
                                                <i class="fas fa-external-link-alt"></i>
                                                @GetShortUrl(s.urlV2)
                                            </a>
                                        }
                                        else
                                        {
                                            <span class="url-empty">Non configuré</span>
                                        }
                                    </div>

                                    <!-- URL Maintenance -->
                                    <div class="url-item">
                                        <div class="url-header">
                                            <span class="url-label">Maintenance</span>
                                            <div class="status-indicator @(s.isUrlMaintenanceOK ? "status-ok" : "status-error")">
                                                <i class="fas @(s.isUrlMaintenanceOK ? "fa-check" : "fa-times")"></i>
                                            </div>
                                        </div>
                                        @if (!string.IsNullOrEmpty(s.urlMaintenance))
                                        {
                                            <a href="@s.urlMaintenance" target="_blank" class="url-link-waw">
                                                <i class="fas fa-external-link-alt"></i>
                                                @GetShortUrl(s.urlMaintenance)
                                            </a>
                                        }
                                        else
                                        {
                                            <span class="url-empty">Non configuré</span>
                                        }
                                    </div>
                                </div>

                                @if (pbs)
                                {
                                    <div class="error-details-waw">
                                        <div class="error-header">
                                            <i class="fas fa-bug"></i>
                                            <span>Détails des erreurs</span>
                                        </div>
                                        <div class="error-content">
                                            @GetErrors(s.listPbsFA)
                                        </div>
                                    </div>
                                }
                            </div>

                            <div class="card-footer-waw">
                                <div class="action-buttons">
                                    <button class="btn-action btn-test">
                                        <i class="fas fa-play"></i>
                                        Tester
                                    </button>
                                    <button class="btn-action btn-details">
                                        <i class="fas fa-info-circle"></i>
                                        Détails
                                    </button>
                                    <button class="btn-action btn-logs">
                                        <i class="fas fa-file-alt"></i>
                                        Logs
                                    </button>
                                </div>
                            </div>
                        </div>
                    }
                </div>
            }
            else
            {
                <div class="no-data-waw">
                    <div class="no-data-icon">
                        <i class="fas fa-search"></i>
                    </div>
                    <h3>Aucun résultat trouvé</h3>
                    <p>Essayez de modifier vos critères de recherche</p>
                    <button @onclick="@(() => { filterStructureId = ""; filterVersionActive = ""; filterErrors = ""; })" class="btn-waw btn-waw-primary">
                        <i class="fas fa-refresh"></i>
                        Réinitialiser les filtres
                    </button>
                </div>
            }
        </div>
    }
</div>
