﻿@page "/preparation-mise-vente-checkwaitingpages"

<link href="~/css/CheckWaitingPages.css" rel="stylesheet" />

<div class="check-waiting-pages-container">
    <!-- Header Section -->
    <div class="page-header">
        <div class="container-fluid">
            <div class="row align-items-center">
                <div class="col-md-8">
                    <div class="header-content">
                        <div class="header-icon">
                            <i class="fas fa-clock"></i>
                        </div>
                        <div class="header-text">
                            <h1 class="page-title">@Localizer["preparation-mise-vente-checkwaitingpages"]</h1>
                            <p class="page-subtitle">Liste FileAttentes</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-4 text-end">
                    <div class="environment-badge">
                        <span class="badge bg-warning text-dark">
                            <i class="fas fa-server me-1"></i>
                            Environnement: DEV
                        </span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    @if (isLoading)
    {
        <div class="loading-section">
            <div class="container-fluid">
                <div class="card shadow border-0">
                    <div class="card-body text-center py-5">
                        <div class="loading-spinner mb-3"></div>
                        <h5 class="text-muted">Chargement des données...</h5>
                        <p class="text-muted mb-0">Veuillez patienter pendant que nous récupérons les informations.</p>
                    </div>
                </div>
            </div>
        </div>
    }
    else if (fileAttenteInfos == null)
    {
        <div class="error-section">
            <div class="container-fluid">
                <div class="card shadow border-0">
                    <div class="card-body text-center py-5">
                        <div class="error-icon mb-3">
                            <i class="fas fa-exclamation-triangle text-warning"></i>
                        </div>
                        <h5 class="text-muted">Accès refusé ou aucune donnée disponible</h5>
                        <p class="text-muted mb-0">Vous n'avez pas les permissions nécessaires ou aucune donnée n'est disponible.</p>
                    </div>
                </div>
            </div>
        </div>
    }
    else
    {
        <!-- Filters Section -->
        <div class="filters-section">
            <div class="container-fluid">
                <div class="card shadow-sm border-0">
                    <div class="card-body">
                        <div class="row g-3 align-items-end">
                            <div class="col-md-3">
                                <label class="form-label fw-semibold">
                                    <i class="fas fa-filter me-1"></i>
                                    Filtre StructureId
                                </label>
                                <input type="text" class="form-control form-control-lg"
                                       placeholder="Entrez l'ID de structure..."
                                       @bind="filterStructureId">
                            </div>
                            <div class="col-md-2">
                                <label class="form-label fw-semibold">Version active</label>
                                <select class="form-select form-select-lg" @bind="filterVersionActive">
                                    <option value="">Toutes</option>
                                    <option value="v1">v1</option>
                                    <option value="v2">v2</option>
                                    <option value="maintenance">maintenance</option>
                                </select>
                            </div>
                            <div class="col-md-2">
                                <label class="form-label fw-semibold">Erreurs</label>
                                <select class="form-select form-select-lg" @bind="filterErrors">
                                    <option value="">Toutes</option>
                                    <option value="0">✅ Sans erreurs</option>
                                    <option value="1">⚠️ Avec erreurs</option>
                                </select>
                            </div>
                            <div class="col-md-2">
                                <button class="btn btn-primary btn-lg w-100" @onclick="Reload">
                                    <i class="fas fa-search me-2"></i>
                                    Filtrer
                                </button>
                            </div>
                            <div class="col-md-3">
                                <div class="stats-info">
                                    <small class="text-muted">
                                        <i class="fas fa-info-circle me-1"></i>
                                        @(FilteredInfos?.Count() ?? 0) structures trouvées
                                    </small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>


        <!-- Table Section -->
        <div class="table-section">
            <div class="container-fluid">
                <div class="card shadow border-0">
                    <div class="card-header bg-white border-bottom">
                        <div class="row align-items-center">
                            <div class="col">
                                <h5 class="card-title mb-0">
                                    <i class="fas fa-table me-2 text-primary"></i>
                                    Résultats de la vérification
                                </h5>
                            </div>
                            <div class="col-auto">
                                <div class="btn-group" role="group">
                                    <button class="btn btn-outline-secondary btn-sm">
                                        <i class="fas fa-download me-1"></i>
                                        Exporter
                                    </button>
                                    <button class="btn btn-outline-secondary btn-sm" @onclick="Reload">
                                        <i class="fas fa-sync-alt me-1"></i>
                                        Actualiser
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="card-body p-0">
                        <div class="table-responsive">
                            <table class="table table-hover mb-0 modern-table">
                                <thead class="table-header">
                                    <tr>
                                        <th class="sortable">
                                            <div class="th-content">
                                                ID
                                                <i class="fas fa-sort ms-1"></i>
                                            </div>
                                        </th>
                                        <th class="sortable">
                                            <div class="th-content">
                                                Nom
                                                <i class="fas fa-sort ms-1"></i>
                                            </div>
                                        </th>
                                        <th class="text-center">Version active</th>
                                        <th class="text-center">urlV1 OK</th>
                                        <th class="text-center">urlV1</th>
                                        <th class="text-center">urlV2 OK</th>
                                        <th class="text-center">urlV2</th>
                                        <th class="text-center">url Maintenance OK</th>
                                        <th class="text-center">url Maintenance</th>
                                        <th class="text-center">Problèmes</th>
                                        <th class="text-center">Détails</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @if (FilteredInfos != null && FilteredInfos.Any())
                                    {
                                        @foreach (var s in FilteredInfos)
                                        {
                                            var pbs = s.listPbsFA != null && s.listPbsFA.Any();
                                            var trclass = pbs ? "table-row warning-row" : "table-row";

                                            <tr class="@trclass">
                                                <td class="fw-bold text-primary">@s.StructureId</td>
                                                <td>
                                                    <div class="structure-name">
                                                        <i class="fas fa-building me-2 text-muted"></i>
                                                        @s.Name
                                                    </div>
                                                </td>
                                                <td class="text-center">
                                                    <span class="badge bg-info">@s.versionFileAttente</span>
                                                </td>
                                                <td class="text-center">
                                                    <span class="status-icon @(s.isUrlV1OK ? "success" : "warning")">
                                                        <i class="fas @(s.isUrlV1OK ? "fa-check-circle" : "fa-exclamation-triangle")"></i>
                                                    </span>
                                                </td>
                                                <td class="url-cell">
                                                    @if (s.isUrlV1OK && !string.IsNullOrEmpty(s.urlV1))
                                                    {
                                                        <a href="@s.urlV1" class="url-link" target="_blank">
                                                            <i class="fas fa-external-link-alt"></i>
                                                            @GetShortUrl(s.urlV1)
                                                        </a>
                                                    }
                                                    else
                                                    {
                                                        <span class="text-muted">@(string.IsNullOrEmpty(s.urlV1) ? "Non défini" : GetShortUrl(s.urlV1))</span>
                                                    }
                                                </td>
                                                <td class="text-center">
                                                    <span class="status-icon @(s.isUrlV2OK ? "success" : "warning")">
                                                        <i class="fas @(s.isUrlV2OK ? "fa-check-circle" : "fa-exclamation-triangle")"></i>
                                                    </span>
                                                </td>
                                                <td class="url-cell">
                                                    @if (s.isUrlV2OK && !string.IsNullOrEmpty(s.urlV2))
                                                    {
                                                        <a href="@s.urlV2" class="url-link" target="_blank">
                                                            <i class="fas fa-external-link-alt"></i>
                                                            @GetShortUrl(s.urlV2)
                                                        </a>
                                                    }
                                                    else
                                                    {
                                                        <span class="text-muted">@(string.IsNullOrEmpty(s.urlV2) ? "Non défini" : GetShortUrl(s.urlV2))</span>
                                                    }
                                                </td>
                                                <td class="text-center">
                                                    <span class="status-icon @(s.isUrlMaintenanceOK ? "success" : "warning")">
                                                        <i class="fas @(s.isUrlMaintenanceOK ? "fa-check-circle" : "fa-exclamation-triangle")"></i>
                                                    </span>
                                                </td>
                                                <td class="url-cell">
                                                    @if (s.isUrlMaintenanceOK && !string.IsNullOrEmpty(s.urlMaintenance))
                                                    {
                                                        <a href="@s.urlMaintenance" class="url-link" target="_blank">
                                                            <i class="fas fa-external-link-alt"></i>
                                                            @GetShortUrl(s.urlMaintenance)
                                                        </a>
                                                    }
                                                    else
                                                    {
                                                        <span class="text-muted">@(string.IsNullOrEmpty(s.urlMaintenance) ? "Non défini" : GetShortUrl(s.urlMaintenance))</span>
                                                    }
                                                </td>
                                                <td class="text-center">
                                                    @if (pbs)
                                                    {
                                                        <span class="badge bg-danger">
                                                            <i class="fas fa-exclamation-triangle me-1"></i>
                                                            Erreurs
                                                        </span>
                                                    }
                                                    else
                                                    {
                                                        <span class="badge bg-success">
                                                            <i class="fas fa-check me-1"></i>
                                                            OK
                                                        </span>
                                                    }
                                                </td>
                                                <td class="details-cell">
                                                    @if (pbs)
                                                    {
                                                        <div class="error-details">
                                                            @GetErrors(s.listPbsFA)
                                                        </div>
                                                    }
                                                    else
                                                    {
                                                        <span class="text-muted">Aucun problème</span>
                                                    }
                                                </td>
                                            </tr>
                                        }
                                    }
                                    else
                                    {
                                        <tr>
                                            <td colspan="11" class="text-center py-5">
                                                <div class="no-data-message">
                                                    <i class="fas fa-search text-muted mb-2"></i>
                                                    <p class="text-muted mb-0">Aucune donnée trouvée</p>
                                                    <small class="text-muted">Essayez de modifier vos filtres</small>
                                                </div>
                                            </td>
                                        </tr>
                                    }
                                </tbody>
                            </table>
                        </div>
                    </div>
                    <div class="card-footer bg-light border-top-0">
                        <div class="row align-items-center">
                            <div class="col">
                                <small class="text-muted">
                                    <i class="fas fa-info-circle me-1"></i>
                                    @(FilteredInfos?.Count() ?? 0) résultats trouvés
                                </small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    }
</div>
