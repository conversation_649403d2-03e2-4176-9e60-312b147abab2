﻿@page "/preparation-mise-vente-checkwaitingpages"

<h3>Check waiting pages</h3>




@if (isLoading)
{
    <div class="spinner">Chargement des données...</div>
}
else if (fileAttenteInfos == null)
{
    <div>Accès refusé ou aucune donnée disponible.</div>
}
else
{
    <h3>Liste FileAttentes</h3>



    <input type="text" placeholder="Filtrer StructureId"
           @bind="filterStructureId" style="margin-right:0.5em;" />

    <span>Version active :</span>

    <select @bind="filterVersionActive" style="margin-right:0.5em;">
        <option value="">all</option>
        <option value="v1">v1</option>
        <option value="v2">v2</option>
        <option value="maintenance">maintenance</option>
    </select>

    <span>erreurs  :</span>
    <select @bind="filterErrors" style="margin-right:0.5em;">
        <option value="">all</option>
        <option value="0">✅</option>
        <option value="1">⚠️</option>
    </select>

    <button @onclick="Reload">Filtrer</button>


    <table class="table table-bordered table-sm">
        <thead>
            <tr>
                <th>Id</th>
                <th>Nom</th>
                <th>version active</th>
                <th>urlV1</th>
                <th>urlV1 Ok</th>
                <th>urlV2</th>
                <th>urlV2 Ok</th>
                <th>url Maintenance</th>
                <th>urlMaintenance Ok</th>
                <th>pb</th>
                <th>pbs details</th>
            </tr>
        </thead>
        <tbody>
            @if (FilteredInfos != null && FilteredInfos.Any())
            {
                @foreach (var s in FilteredInfos)
                {
                    var pbs = s.listPbsFA != null && s.listPbsFA.Any();
                    var trclass = pbs ? "table-danger" : "";


                    <tr class="@trclass">
                        <td>@s.StructureId</td>
                        <td style="word-break:break-all;">@s.Name</td>
                        <td>@s.versionFileAttente</td>
                        <td style="word-break:break-all;">@s.urlV1</td>
                        <td>@(s.isUrlV1OK ? "✅" : "⚠️")</td>
                        <td style="word-break:break-all;">@s.urlV2</td>
                        <td>@(s.isUrlV2OK ? "✅" : "⚠️")</td>
                        <td style="word-break:break-all;">@s.urlMaintenance</td>
                        <td>@(s.isUrlMaintenanceOK ? "✅" : "⚠️")</td>
                        <td>@(pbs ? "!" : "")</td>
                        <td>@GetErrors(s.listPbsFA)</td>
                    </tr>
                }
            }
            else
            {
                <tr>
                    <td colspan="10"><em>Aucune donnée trouvée</em></td>
                </tr>
            }
        </tbody>
    </table>
}
