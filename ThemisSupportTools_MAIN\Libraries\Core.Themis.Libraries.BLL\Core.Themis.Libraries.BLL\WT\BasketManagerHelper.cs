﻿using Core.Themis.Libraries.Data.Entities.WebTracing;
using Core.Themis.Libraries.DTO;
using Core.Themis.Libraries.DTO.EventsSessions;
using Core.Themis.Libraries.DTO.Seats;
using System.Collections.Generic;
using Microsoft.Data.SqlClient;

namespace Core.Themis.Libraries.BLL.WT
{
    public static class BasketManagerHelper
    {
        private enum WhichListToAdd
        {
            unitSale,
            abo,
            reabo,
            aboV2
        }

        #region Rempli les objets avec la nouvelle méthode

        public static void AddEventUnitSales(BasketDTO baskReturn, PanierEntreeEntity panierEntree)
        {
            AddBasketEvent(baskReturn, panierEntree, WhichListToAdd.unitSale);
        }


        private static void AddBasketEvent(BasketDTO baskReturn, PanierEntreeEntity panierEntree, WhichListToAdd wichList)
        {
            EventDTO evtE = new()
            {
                EventId = panierEntree.ManifId,
                EventName = panierEntree.ManifNom,
                ListSessions = new List<SessionDTO>()

            };

            switch (wichList)
            {
                case WhichListToAdd.unitSale:
                    if (!baskReturn.ListEventsUnitSales.Exists(evte => evte.EventId == panierEntree.ManifId))
                    {

                        baskReturn.ListEventsUnitSales.Add(evtE);
                    }
                    break;

                default:
                    break;
            }

        }

        public static void AddBasketSession(ref EventDTO evt, PanierEntreeEntity panierEntree)
        {

            //if (whichTypeEvent = "eventUnitSale")

            SessionDTO sessEnt = new()
            {
                SessionId = (int)panierEntree.SeanceId,
                SessionDecription = panierEntree.SeanceDescription,
                ListZones = new()
            };


            if (!evt.ListSessions.Exists(sesse => sesse.SessionId == panierEntree.SeanceId))
            {
                evt.ListSessions.Add(sessEnt);
            }
        }


        public static void AddBasketZone(ref SessionDTO sess, PanierEntreeEntity panierEntree)
        {
            int thisZoneId = int.Parse(panierEntree.ZoneId.ToString());
            if (!sess.ListZones.Exists(sesse => sesse.ZoneId == thisZoneId))
            {
                ZoneDTO zoneEnt = new()
                {
                    ZoneId = thisZoneId,

                    ListFloors = new List<FloorDTO>()
                };
                sess.ListZones.Add(zoneEnt);
            }
        }


        public static void AddBasketFloor(ref ZoneDTO zone, PanierEntreeEntity panierEntree)
        {

            if (panierEntree.EtageId != null)
            {
                if (!zone.ListFloors.Exists(z => z.FloorId == panierEntree.EtageId))
                {
                    FloorDTO floorEnt = new()
                    {
                        FloorId = panierEntree.EtageId ?? 0,
                        ListSections = new List<SectionDTO>()
                    };
                    zone.ListFloors.Add(floorEnt);
                }
            }
        }

        public static void AddBasketSection(ref FloorDTO floor, PanierEntreeEntity panierEntree)
        {
            int thisFloorId = int.Parse(panierEntree.SectionId.ToString());
            if (!floor.ListSections.Exists(sect => sect.SectionId == thisFloorId))
            {
                SectionDTO sectionEnt = new()
                {
                    SectionId = thisFloorId,
                    ListCategories = new()
                };
                floor.ListSections.Add(sectionEnt);
            }
        }

        public static void AddBasketCategorie(ref SectionDTO section, PanierEntreeEntity panierEntree)
        {
            int thisCategId = (int)panierEntree?.CategId;
            if (!section.ListCategories.Exists(cat => cat.CategId == thisCategId))
            {
                CategoryDTO categEnt = new()
                {
                    CategId = thisCategId,
                    CategoryName = panierEntree?.CategNom!,
                    //ListGestionPlace = new List<GestionPlaceEntity>()
                    ListPrices = new List<PriceDTO>()
                };
                section.ListCategories.Add(categEnt);
            }
        }

        public static void AddBasketPrice(ref CategoryDTO categ, PanierEntreeEntity panierEntree)
        {
            int thisPriceId = int.Parse(panierEntree.TypeTarifId.ToString());
            if (!categ.ListPrices.Exists(pric => pric.PriceId == thisPriceId))
            {
                PriceDTO priceEnt = new()
                {
                    UnitTTCAmount = int.Parse(panierEntree.Montant.ToString()),
                    UnitFeeAmount = int.Parse(panierEntree.Frais.ToString()),
                    UnitValue = panierEntree.Valeur == null ? 0 : int.Parse(panierEntree.Valeur.ToString()),

                    PriceId = thisPriceId,
                    PriceName = panierEntree.TypeTarifNom.ToString(),
                    VtsId = int.Parse(panierEntree.VtsId.ToString()),
                    RuleId = int.Parse(panierEntree.GestionPlaceId.ToString()),
                    ListSeats = new List<SeatDTO>(),

                    ListAboMultiSeats = new List<SeatAboMultiDTO>(),
                    ListAboSeats = new List<SeatAboDTO>(),
                };
                categ.ListPrices.Add(priceEnt);
            }
        }


        #endregion


        public static void AddEventUnitSales(BasketDTO baskReturn, SqlDataReader reader)
        {
            AddEvent(baskReturn, reader, WhichListToAdd.unitSale);
        }

        private static void AddEvent(BasketDTO baskReturn, SqlDataReader reader, WhichListToAdd wichList)
        {
            int thisEventId = int.Parse(reader["manif_id"].ToString());
            EventDTO evtE = new()
            {
                EventId = thisEventId,
                EventName = reader["manif_nom"].ToString(),
                ListSessions = new List<SessionDTO>()

            };

            switch (wichList)
            {
                case WhichListToAdd.unitSale:
                    if (!baskReturn.ListEventsUnitSales.Exists(evte => evte.EventId == thisEventId))
                    {

                        baskReturn.ListEventsUnitSales.Add(evtE);
                    }
                    break;

                default:
                    break;
            }
        }

        /// <summary>
        /// add seance à l'event
        /// </summary>
        /// <param name="evt"></param>
        /// <param name="reader"></param>
        public static void AddSession(ref EventDTO evt, SqlDataReader reader)
        {
            int thisSessionId = int.Parse(reader["seance_id"].ToString());

            //if (whichTypeEvent = "eventUnitSale")

            SessionDTO sessEnt = new SessionDTO
            {
                SessionId = thisSessionId,
                SessionDecription = reader["seance_description"].ToString(),

                ListZones = new List<ZoneDTO>()
            };


            if (!evt.ListSessions.Exists(sesse => sesse.SessionId == thisSessionId))
            {
                evt.ListSessions.Add(sessEnt);
            }
        }
        /// <summary>
        /// add zone à la session
        /// </summary>
        /// <param name="sess"></param>
        /// <param name="reader"></param>
        public static void AddZone(ref SessionDTO sess, SqlDataReader reader)
        {
            int thisZoneId = int.Parse(reader["zone_id"].ToString());
            if (!sess.ListZones.Exists(sesse => sesse.ZoneId == thisZoneId))
            {
                ZoneDTO zoneEnt = new ZoneDTO
                {
                    ZoneId = thisZoneId,

                    ListFloors = new List<FloorDTO>()
                };
                sess.ListZones.Add(zoneEnt);
            }
        }

        /// <summary>
        /// add floor a la zone
        /// </summary>
        /// <param name="zone"></param>
        /// <param name="reader"></param>
        public static void AddFloor(ref ZoneDTO zone, SqlDataReader reader)
        {
            int thisFloorId = int.Parse(reader["etage_id"].ToString());

            if (!zone.ListFloors.Exists(z => z.FloorId == thisFloorId))
            {
                FloorDTO floorEnt = new()
                {
                    FloorId = thisFloorId,
                    ListSections = new List<SectionDTO>()
                };
                zone.ListFloors.Add(floorEnt);
            }
        }

        /// <summary>
        /// add section au floor
        /// </summary>
        /// <param name="floor"></param>
        /// <param name="reader"></param>
        public static void AddSection(ref FloorDTO floor, SqlDataReader reader)
        {
            int thisFloorId = int.Parse(reader["section_id"].ToString());
            if (!floor.ListSections.Exists(sect => sect.SectionId == thisFloorId))
            {
                SectionDTO sectionEnt = new SectionDTO
                {
                    SectionId = thisFloorId,
                    ListCategories = new List<CategoryDTO>()
                };
                floor.ListSections.Add(sectionEnt);
            }
        }

        /// <summary>
        /// add categorie à la section
        /// </summary>
        /// <param name="section"></param>
        /// <param name="reader"></param>
        public static void AddCategorie(ref SectionDTO section, SqlDataReader reader)
        {
            int thisCategId = int.Parse(reader["categ_id"].ToString());
            if (!section.ListCategories.Exists(cat => cat.CategId == thisCategId))
            {
                CategoryDTO categEnt = new CategoryDTO
                {
                    CategId = thisCategId,
                    CategoryName = reader["categ_nom"].ToString(),
                    //ListGestionPlace = new List<GestionPlaceEntity>()
                    ListPrices = new List<PriceDTO>()
                };
                section.ListCategories.Add(categEnt);
            }
        }

        /// <summary>
        /// add price à la categorie 
        /// </summary>
        /// <param name="categ"></param>
        /// <param name="reader"></param>
        public static void AddPrice(ref CategoryDTO categ, SqlDataReader reader)
        {
            int thisPriceId = int.Parse(reader["type_tarif_id"].ToString());
            if (!categ.ListPrices.Exists(pric => pric.PriceId == thisPriceId))
            {
                PriceDTO priceEnt = new PriceDTO
                {
                    UnitTTCAmount = int.Parse(reader["montant"].ToString()),
                    UnitFeeAmount = int.Parse(reader["frais"].ToString()),
                    UnitValue = int.Parse(reader["valeur"].ToString()),

                    PriceId = thisPriceId,
                    PriceName = reader["type_tarif_nom"].ToString(),
                    VtsId = int.Parse(reader["vts_id"].ToString()),
                    RuleId = int.Parse(reader["gestion_place_id"].ToString()),
                    ListSeats = new List<SeatDTO>(),

                    ListAboMultiSeats = new List<DTO.Seats.SeatAboMultiDTO>(),
                    ListAboSeats = new List<DTO.Seats.SeatAboDTO>(),
                };
                categ.ListPrices.Add(priceEnt);
            }
        }



    }


}
