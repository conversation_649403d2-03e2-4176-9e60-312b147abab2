﻿using Microsoft.AspNetCore.Components;
using Microsoft.AspNetCore.Components.Web;
using Microsoft.EntityFrameworkCore;
using MiseEnVentes.Models;
using MiseEnVentes.Shared;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Xml.Linq;

namespace MiseEnVentes.Pages
{
    public partial class IndexBase : ComponentBase
    {

        public List<MiseEnVenteNew> misesEnVente = new();
        protected List<Intervenant>? intervenants;
        public List<Plateforme>? plateformees;
        public List<MiseEnVentePlateforme>? MiseEnVentePlateformes;

        public bool showPopup = false;
        public FormModel formModel = new();

        public MiseEnVenteNew? selectedVente;
        public EditFormModel editModel = new();
        public bool showEditModal = false;

        public bool showDeleteModal = false;
        public MiseEnVenteNew? venteToDelete;

        public int selectedYear = 0;

        public int? selectedIntervenantId = null;

        public int? selectedPlateformeId = null;

        protected string? selectedStructureId;

        [Inject]
        public ApplicationDbContext Db { get; set; } = default!;

        [Inject]
        public DynamicsDbContext DynamicsDb { get; set; } = default!;
        protected Dictionary<Guid, string?> AccountBases { get; set; } = new();
        protected Dictionary<Guid, AccountBase> AccountBasesNum { get; set; } = new();

        protected bool showToast = false;
        protected bool showEditToast = false;
        protected bool showDeleteToast = false;
        protected string message = string.Empty;
        protected string structureSearch = string.Empty;
        protected string selectedStructure = string.Empty;
        protected string? selectedStructureName;

        protected int? lastModifiedId = null;



        //Filtrer l'affichage du tableaux 
        protected override async Task OnInitializedAsync()
        {
            var today = DateOnly.FromDateTime(DateTime.Today);

            misesEnVente = await Db.MisesEnVenteNew
              .Where(m => m.Date >= today)
              .Include(m => m.MiseEnVenteIntervenants)
                .ThenInclude(mi => mi.Intervenant)
              .Include(m => m.MiseEnVentePlateformes)
                .ThenInclude(mp => mp.Plateforme)
              .OrderBy(m => m.Date)
              .ToListAsync();

            intervenants = await Db.Intervenants
                .OrderBy(i => i.IntervenantName)
                .ToListAsync();

            plateformees = await Db.Plateformes
                .OrderBy(i => i.PlateformeName)
                .ToListAsync();

            AccountBases = await DynamicsDb.AccountBases
               .Where(a => a.Name != null)
               .ToDictionaryAsync(a => a.AccountId, a => a.Name);

            AccountBasesNum = await DynamicsDb.AccountBases
                .Where(a => a.Name != null && a.jav_IDStructure != null)
                .ToDictionaryAsync(a => a.AccountId, a => a);

        }


        // pour afficher le modal d'ajout d'une mise en vente
        protected void TogglePopup()
        {
            showPopup = !showPopup;
        }


        // Ajout d'une mise en vente 
        public async Task HandleValidSubmit()
        {

            // Nettoyer et extraire le nom + ID structure à partir de formModel.Structure
            var selectedStructureName = formModel.Structure?.Trim();
            Guid? selectedStructureId = null;


            if (!string.IsNullOrWhiteSpace(selectedStructureName))
            {
                //// ex nom - suite du nom - 0123 => "nom - suite du nom" / "0123"
                string separator = " - ";

                int lastSep = selectedStructureName.LastIndexOf(separator);
                var namePart = selectedStructureName.Substring(0, lastSep);

                string idPart = selectedStructureName.Substring(lastSep + separator.Length);
       
                var structure = AccountBasesNum.FirstOrDefault(s =>
                    string.Equals(s.Value.Name, namePart, StringComparison.OrdinalIgnoreCase) &&
                    string.Equals(s.Value.jav_IDStructure, idPart, StringComparison.OrdinalIgnoreCase));

                if (!structure.Equals(default(KeyValuePair<Guid, AccountBase>)))
                {
                    selectedStructureId = structure.Key;
                }
            }
            var nouvelleVente = new MiseEnVenteNew
            {
                Date = DateOnly.FromDateTime(formModel.Date),
                Hour = TimeOnly.Parse(formModel.Heure!),
                StructureId = selectedStructureId?.ToString(), // Utilisation de l'ID récupéré
                NotifMail = formModel.Mail,
                NotifTicket = formModel.Ticket,
                Commentaire = formModel.Commentaire,
                Importance = (int?)formModel.Importance
            };

            Db.MisesEnVenteNew.Add(nouvelleVente);
            await Db.SaveChangesAsync(); // pour générer l'ID

            // Ajout de la plateforme (si Id fourni)
            if (formModel.Plateformes != null)
            {
                foreach (var plateformeId in formModel.Plateformes)
                {
                    Db.MiseEnVentePlateformes.Add(new MiseEnVentePlateforme
                    {
                        IdMiseEnVente = nouvelleVente.IdMiseEnVente,
                        IdPlateforme = plateformeId
                    });
                }
            }

            // Ajout de l'intervenant (si Id fourni)
            if (formModel.Intervenants != null)
            {
                foreach (var intervenantId in formModel.Intervenants)
                {
                    Db.MiseEnVenteIntervenants.Add(new MiseEnVenteIntervenant
                    {
                        IdMiseEnVente = nouvelleVente.IdMiseEnVente,
                        IdIntervenant = intervenantId
                    });
                }
            }

            await Db.SaveChangesAsync(); // pour enregistrer les liaisons
            lastModifiedId = nouvelleVente?.IdMiseEnVente;

            misesEnVente.Add(nouvelleVente);

            misesEnVente = misesEnVente
                .OrderBy(m => m.Date)
                .ToList();

            showPopup = false;
            formModel = new();

            await ShowToast();
            await Task.Delay(2000); // 2 secondes
            lastModifiedId = null;
            StateHasChanged();
        }



        // Ouvre le modal pour modifier une mise en vente 
        protected void OpenEditModal(MiseEnVenteNew vente)
        {
            selectedVente = vente;
            // Rechercher l'ID à partir du nom
            var structure = AccountBasesNum.FirstOrDefault(x => x.Key.ToString().Equals(vente.StructureId, StringComparison.OrdinalIgnoreCase));

            editModel = new EditFormModel
            {
                Date = vente.Date?.ToDateTime(TimeOnly.MinValue) ?? DateTime.Today,
                Heure = vente.Hour?.ToString("HH\\:mm") ?? "",
                Plateformes = vente.MiseEnVentePlateformes.Select(x => x.IdPlateforme).ToList(),
                Structure = structure.Value != null ? $"{structure.Value.Name} - {structure.Value.jav_IDStructure}" : "",
                Intervenants = vente.MiseEnVenteIntervenants.Select(i => i.IdIntervenant).ToList(),
                Mail = vente.NotifMail ?? false,
                Ticket = vente.NotifTicket ?? false,
                Commentaire = vente.Commentaire ?? null,
                Importance = (ImportanceNiveau?)vente.Importance
            };

            showEditModal = true;
        }

        protected void CloseEditModal()
        {
            showEditModal = false;
            selectedVente = null;
        }




        // Methode pour modifier une mise en vente 
        protected async Task SubmitEdit()

        {
            Guid? structureId = null;

            if (!string.IsNullOrWhiteSpace(editModel.Structure))
            {
                var parts = editModel.Structure.Split(" - ", StringSplitOptions.RemoveEmptyEntries);

                if (parts.Length == 2)
                {
                    var namePart = parts[0].Trim();
                    var idPart = parts[1].Trim();

                    var structure = AccountBasesNum.FirstOrDefault(s =>
                        string.Equals(s.Value.Name, namePart, StringComparison.OrdinalIgnoreCase) &&
                        string.Equals(s.Value.jav_IDStructure, idPart, StringComparison.OrdinalIgnoreCase));

                    if (!structure.Equals(default(KeyValuePair<Guid, AccountBase>)))
                    {
                        structureId = structure.Key;
                    }
                }
            }

            if (selectedVente is not null)
            {
                selectedVente.Date = DateOnly.FromDateTime(editModel.Date);
                if (TimeOnly.TryParse(editModel.Heure, out var heureParsed))
                {
                    selectedVente.Hour = heureParsed;
                }

                selectedVente.StructureId = structureId?.ToString();
                selectedVente.NotifMail = editModel.Mail;
                selectedVente.NotifTicket = editModel.Ticket;
                selectedVente.Commentaire = string.IsNullOrWhiteSpace(editModel.Commentaire) ? null : editModel.Commentaire;
                selectedVente.Importance = (int?)editModel.Importance;


                // Mise à jour Plateforme
                var currentPlateformes = await Db.MiseEnVentePlateformes
                    .Where(p => p.IdMiseEnVente == selectedVente.IdMiseEnVente)
                    .ToListAsync();
                Db.MiseEnVentePlateformes.RemoveRange(currentPlateformes);

                if (editModel.Plateformes != null && editModel.Plateformes.Any())
                {
                    foreach (var newPlatId in editModel.Plateformes)
                    {
                        Db.MiseEnVentePlateformes.Add(new MiseEnVentePlateforme
                        {
                            IdMiseEnVente = selectedVente.IdMiseEnVente,
                            IdPlateforme = newPlatId
                        });
                    }
                }

                // Mise à jour Intervenant
                var currentIntervenants = await Db.MiseEnVenteIntervenants
                    .Where(i => i.IdMiseEnVente == selectedVente.IdMiseEnVente)
                    .ToListAsync();
                Db.MiseEnVenteIntervenants.RemoveRange(currentIntervenants);

                if (editModel.Intervenants != null && editModel.Intervenants.Any())
                {
                    foreach (var interId in editModel.Intervenants)
                    {
                        Db.MiseEnVenteIntervenants.Add(new MiseEnVenteIntervenant
                        {
                            IdMiseEnVente = selectedVente.IdMiseEnVente,
                            IdIntervenant = interId
                        });
                    }
                }

                await Db.SaveChangesAsync();
                lastModifiedId = selectedVente?.IdMiseEnVente;

            }
            showEditModal = false;
            selectedVente = null;
            await ShowEditToast();
            await Task.Delay(2000); // 2 secondes
            lastModifiedId = null;
            StateHasChanged();
        }


        // Methode pour supprimer une mise en vente 
        protected async Task DeleteVente()
        {
            if (venteToDelete is not null)
            {
                var lien = await Db.MiseEnVentePlateformes
                  .Where(p => p.IdMiseEnVente == venteToDelete.IdMiseEnVente)
                  .ToListAsync();

                Db.MiseEnVentePlateformes.RemoveRange(lien);

                Db.MisesEnVenteNew.Remove(venteToDelete);
                await Db.SaveChangesAsync();
                misesEnVente.Remove(venteToDelete);
                venteToDelete = null;
                showDeleteModal = false;
                await ShowDeleteToast();
            }
        }

        protected void ConfirmDelete(MiseEnVenteNew vente)
        {
            venteToDelete = vente;
            showDeleteModal = true;
        }

        protected void CancelDelete()
        {
            showDeleteModal = false;
            venteToDelete = null;
        }


        // Methode pour filtrer le tableaux des mises en ventes par année 
        protected async Task OnDateFilterChanged(ChangeEventArgs e)
        {
            if (int.TryParse(e.Value?.ToString(), out int year))
            {
                selectedYear = year;
            }
            else
            {
                selectedYear = 0;
            }

            await LoadFilteredMisesEnVente();
        }



        // Methode pour filtrer le tableaux des mises en ventes par Intervenant 
        public async Task OnIntervenantChanged(ChangeEventArgs e)
        {
            if (int.TryParse(e.Value?.ToString(), out var id))
                selectedIntervenantId = id;
            else
                selectedIntervenantId = null;

            await LoadFilteredMisesEnVente();
        }


        // Methode pour filtrer le tableaux des mises en ventes par Plateformes 
        protected async Task OnPlateformeChanged(ChangeEventArgs e)
        {
            if (int.TryParse(e.Value?.ToString(), out var id))
                selectedPlateformeId = id;
            else
                selectedPlateformeId = null;

            await LoadFilteredMisesEnVente();
        }



        // Methode pour filtrer le tableaux des mises en ventes par Structures
        protected async Task OnStructureChanged(ChangeEventArgs e)
        {
            selectedStructureName = e.Value?.ToString()?.Trim();

            if (string.IsNullOrWhiteSpace(selectedStructureName))
            {
                selectedStructureId = null;
            }
            else
            {
                // Essayer d'extraire le nom et l'ID structure à partir du format "Nom - ID"
                var parts = selectedStructureName.Split(" - ", StringSplitOptions.RemoveEmptyEntries);

                if (parts.Length == 2)
                {
                    var namePart = parts[0].Trim();
                    var idPart = parts[1].Trim();

                    var structure = AccountBasesNum.FirstOrDefault(s =>
                        string.Equals(s.Value.Name, namePart, StringComparison.OrdinalIgnoreCase) &&
                        string.Equals(s.Value.jav_IDStructure, idPart, StringComparison.OrdinalIgnoreCase));

                    if (!structure.Equals(default(KeyValuePair<Guid, AccountBase>)))
                    {
                        selectedStructureId = structure.Key.ToString();
                    }
                    else
                    {
                        selectedStructureId = null;
                    }
                }
                else
                {
                    selectedStructureId = null; // Format invalide
                }
            }

            await LoadFilteredMisesEnVente();
        }



        protected async Task LoadFilteredMisesEnVente()
        {
            IQueryable<MiseEnVenteNew> query = Db.MisesEnVenteNew
                .Include(m => m.MiseEnVenteIntervenants).ThenInclude(mi => mi.Intervenant)
                .Include(m => m.MiseEnVentePlateformes).ThenInclude(mp => mp.Plateforme);

            var today = DateOnly.FromDateTime(DateTime.Today);

            // Date / Année
            if (selectedYear == 0)
                query = query.Where(m => m.Date >= today);
            else
                query = query.Where(m => m.Date.HasValue && m.Date.Value.Year == selectedYear);

            // Intervenant
            if (selectedIntervenantId.HasValue)
            {
                query = query.Where(m =>
                    m.MiseEnVenteIntervenants.Any(mi => mi.IdIntervenant == selectedIntervenantId.Value));
            }

            // Plateforme
            if (selectedPlateformeId.HasValue)
            {
                query = query.Where(m =>
                    m.MiseEnVentePlateformes.Any(mp => mp.IdPlateforme == selectedPlateformeId.Value));
            }

            // Structure
            if (!string.IsNullOrWhiteSpace(selectedStructureId))
            {
                query = query.Where(m => m.StructureId == selectedStructureId);
            }

            misesEnVente = await query.OrderBy(m => m.Date).ToListAsync();
        }



        // Pour sélectionner plusieur intervenant a la fois
        protected void OnIntervenantCheckedChanged(int intervenantId, object checkedValue)
        {
            var isChecked = (bool)checkedValue;

            if (isChecked)
            {
                if (!formModel.Intervenants.Contains(intervenantId))
                    formModel.Intervenants.Add(intervenantId);
            }
            else
            {
                if (formModel.Intervenants.Contains(intervenantId))
                    formModel.Intervenants.Remove(intervenantId);
            }
        }

        protected void OnEditIntervenantCheckedChanged(int intervenantId, object checkedValue)
        {
            var isChecked = (bool)checkedValue;

            if (isChecked)
            {
                if (!editModel.Intervenants.Contains(intervenantId))
                    editModel.Intervenants.Add(intervenantId);
            }
            else
            {
                if (editModel.Intervenants.Contains(intervenantId))
                    editModel.Intervenants.Remove(intervenantId);
            }
        }

        // Pour sélectionner plusieur plateformes a la fois
        protected void OnPlateformeCheckedChanged(int idPlateforme, object checkedValue)
        {
            bool isChecked = (bool)checkedValue;

            if (isChecked)
            {
                if (!formModel.Plateformes.Contains(idPlateforme))
                    formModel.Plateformes.Add(idPlateforme);
            }
            else
            {
                formModel.Plateformes.Remove(idPlateforme);
            }
        }

        protected void OnEditPlateformeCheckedChanged(int idPlateforme, object checkedValue)
        {
            bool isChecked = (bool)checkedValue;

            if (isChecked)
            {
                if (!editModel.Plateformes.Contains(idPlateforme))
                    editModel.Plateformes.Add(idPlateforme);
            }
            else
            {
                editModel.Plateformes.Remove(idPlateforme);
            }
        }



        // Toast pour prévenir de l'action effectuer
        public async Task ShowToast()
        {
            showToast = true;
            StateHasChanged(); // Affiche immédiatement

            await Task.Delay(5000);
            showToast = false;
            StateHasChanged(); // Cache le toast après 5s
        }

        protected void HideToast()
        {
            showToast = false;
            showEditToast = false;
            showDeleteToast = false;
        }

        protected async Task ShowEditToast()
        {
            showEditToast = true;
            StateHasChanged();

            await Task.Delay(5000);
            showEditToast = false;
            StateHasChanged();
        }

        protected async Task ShowDeleteToast()
        {
            showDeleteToast = true;
            StateHasChanged();

            await Task.Delay(5000);
            showDeleteToast = false;
            StateHasChanged();
        }


        //Pour les intervenants minimums 1
        /*public class AtLeastOneRequiredAttribute : ValidationAttribute
        {
            public override bool IsValid(object? value)
            {
                if (value is List<int> list)
                {
                    return list.Any();
                }
                return false;
            }

            public override string FormatErrorMessage(string name)
            {
                return $"Au moins un intervenant doit être sélectionné.";
            }
        }*/



        // Pour le filtre des structures pourvoir ecrire et a la fois faire une recherche 

        protected IEnumerable<KeyValuePair<string, string>> FilteredStructures =>
        (IEnumerable<KeyValuePair<string, string>>)(string.IsNullOrWhiteSpace(structureSearch)
            ? AccountBases
            : AccountBases.Where(s => s.Value.Contains(structureSearch, StringComparison.OrdinalIgnoreCase)));




    }
}
