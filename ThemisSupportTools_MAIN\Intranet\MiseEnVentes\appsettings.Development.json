{"ConnectionStrings": {"Connection_RODRIGUE_MISESENVENTES": "Server=*************;Database=RODRIGUE_MISESENVENTES_TEST;User ID=SphereWebTest;Password=********************************************************************************************************************************;TrustServerCertificate=True;", "Connection_for_Structure": "Data Source=*************;Initial Catalog=RODRIGUE_MSCRM;User ID=sa;Password=****************************************************************;TrustServerCertificate=True;"}, "ConfigIniPath": "\\\\*************\\customerfiles\\TEST\\[idstructure]\\CONFIGSERVER\\config.ini.xml", "MiseEnVenteConfig": {"DelaiExpirationHeures": 3}, "TimerSettings": {"InitialDelaySeconds": 0, "IntervalSeconds": 30}, "Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning"}}, "AllowedHosts": "*"}