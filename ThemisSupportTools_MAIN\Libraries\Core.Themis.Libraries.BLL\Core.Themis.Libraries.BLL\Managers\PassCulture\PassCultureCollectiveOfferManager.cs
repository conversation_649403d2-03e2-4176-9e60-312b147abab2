﻿using AutoMapper;
using Core.Themis.Libraries.BLL.Managers.PassCulture.Interfaces;
using Core.Themis.Libraries.BLL.Services.PassCulture.Interfaces;
using Core.Themis.Libraries.Data.Entities.Open.Dossier;
using Core.Themis.Libraries.Data.Entities.Open.EventsSessions;
using Core.Themis.Libraries.Data.Entities.Open.Identite;
using Core.Themis.Libraries.Data.Entities.Open.Order;
using Core.Themis.Libraries.Data.Entities.Open.Partner;
using Core.Themis.Libraries.Data.Repositories.Open.Identities.Interfaces;
using Core.Themis.Libraries.Data.Repositories.Open.Orders.Interfaces;
using Core.Themis.Libraries.Data.Repositories.Open.Partner.Interfaces;
using Core.Themis.Libraries.DTO.Enums;
using Core.Themis.Libraries.DTO.Enums.PassCulture;
using Core.Themis.Libraries.DTO.Lookup;
using Core.Themis.Libraries.DTO.PassCulture;
using Core.Themis.Libraries.DTO.PassCulture.ApiExchange.Response;
using Core.Themis.Libraries.DTO.PassCulture.Forms;
using Core.Themis.Libraries.Utilities.Logging;
using Microsoft.Extensions.Caching.Memory;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Core.Themis.Libraries.BLL.Managers.PassCulture
{
    public class PassCultureCollectiveOfferManager : IPassCultureCollectiveOfferManager
    {
        private static readonly RodrigueNLogger RodrigueLogger = new();

        private readonly IMapper _mapper;
        private readonly IMemoryCache _memoryCache;
        private readonly IPassCultureService _passCultureService;
        private readonly IOrderRepository _orderRepository;
        private readonly IIdentiteImmatriculationRepository _identiteImmatriculationRepository;
        private readonly IPartnerIdsProviderRepository _partnerIdsProviderRepository;

        public PassCultureCollectiveOfferManager(
            IMapper mapper,
            IMemoryCache memoryCache,
            IPassCultureService passCultureService,
            IOrderRepository orderRepository,
            IIdentiteImmatriculationRepository identiteImmatriculationRepository,
            IPartnerIdsProviderRepository partnerIdsProviderRepository)
        {
            _mapper = mapper;
            _memoryCache = memoryCache;
            _passCultureService = passCultureService;
            _orderRepository = orderRepository;
            _identiteImmatriculationRepository = identiteImmatriculationRepository;
            _partnerIdsProviderRepository = partnerIdsProviderRepository;
        }

        public async Task<PassCultureResponse<CollectiveOfferConsultation>> CreateCollectiveOfferAsync(int structureId, CollectiveOfferFormModel model)
        {
            try
            {
                CollectiveOfferCreation collectiveOfferPassCulture = _mapper.Map<CollectiveOfferCreation>(model);

                CommandeEntity commandeEntity = GetCommandeEntityForCollectiveOffer(structureId, model.OrderId);

                SetReservationInfos(collectiveOfferPassCulture, commandeEntity);

                SaveUAICode(structureId, model.EducationalInstitution, commandeEntity.IdentiteId, commandeEntity.Buyer!.Immatriculation);

                var result = await _passCultureService.CreateCollectiveOfferAsync(structureId, collectiveOfferPassCulture)
                                                      .ConfigureAwait(false);

                if (result.Success)
                    SaveIdsProvideByPassCultureForACollectiveOffer(structureId, result.Result!.Id.ToString(), model.OrderId);

                return result;
            }
            catch (Exception ex)
            {
                RodrigueLogger.Error(structureId, ex.Message);
                return new()
                {
                    Success = false,
                    ErrorMessage = ex.Message
                };
            }
        }

        public async Task<PassCultureResponse<CollectiveOfferConsultation>> UpdateCollectiveOfferAsync(int structureId, CollectiveOfferFormModel model)
        {
            try
            {
                if (model.ToArchive)
                {
                    CollectiveOfferConsultation archiveCollectiveOffer = await ArchivePassCultureCollectiveOfferByOrderIdAsync(structureId, model.OrderId);
                    return new()
                    {
                        Success = true,
                        Result = archiveCollectiveOffer
                    };
                }

                CollectiveOfferConsultation collectiveOfferInPassCulture = await GetPassCultureCollectiveOfferByOrderIdAsync(structureId, model.OrderId)
                                                                                    .ConfigureAwait(false);

                CollectiveOfferCreation collectiveOfferPassCulture = _mapper.Map<CollectiveOfferCreation>(collectiveOfferInPassCulture);

                CollectiveOfferCreation newCollectiveOffer = _mapper.Map<CollectiveOfferCreation>(model);

                CommandeEntity commandeEntity = GetCommandeEntityForCollectiveOffer(structureId, model.OrderId);

                SetReservationInfos(newCollectiveOffer, commandeEntity);

                SaveUAICode(structureId, model.EducationalInstitution, commandeEntity.IdentiteId, commandeEntity.Buyer!.Immatriculation);

                var updatedCollectiveOffer = _mapper.Map(newCollectiveOffer, collectiveOfferPassCulture);

                return await _passCultureService.UpdateCollectiveOfferAsync(structureId, collectiveOfferInPassCulture.Id, updatedCollectiveOffer)
                                                .ConfigureAwait(false);
            }
            catch (Exception ex)
            {
                RodrigueLogger.Error(structureId, ex.Message);
                return new()
                {
                    Success = false,
                    ErrorMessage = ex.Message
                };
            }
        }

        public async Task<CollectiveOfferFormModel> GetCollectiveOfferFormModelByOrderIdAsync(int structureId, int orderId)
        {
            try
            {
                var collectiveOffer = await GetPassCultureCollectiveOfferByOrderIdAsync(structureId, orderId).ConfigureAwait(false);

                var result = _mapper.Map<CollectiveOfferFormModel>(collectiveOffer);
                result.OrderId = orderId;

                if (result.State == PassCultureBookingStates.Archived)
                    return result;

                CommandeEntity commandeEntity = GetCommandeEntityForCollectiveOffer(structureId, orderId);

                bool toArchive = commandeEntity.Lignes?.Where(l => l.TypeLigne == "DOS" && l.Dossier is not null && l.Dossier.State == TypeStateEnum.Reserver)
                                                       .Select(l => l.Dossier)
                                                       .Count() == 0;

                if (toArchive)
                {
                    result.ToArchive = toArchive;
                    return result;
                }

                result.OrderHasChange = ReservationHasChange(collectiveOffer, commandeEntity);

                return result;
            }
            catch (Exception ex)
            {
                RodrigueLogger.Error(structureId, $"{ex.Message} \n {ex.StackTrace}");
                throw;
            }
        }

        public ReservationInfosForPassCulture GetReservationInfosForPassCulture(int structureId, int orderId)
        {
            CommandeEntity commandeEntity = GetOrderWithDependanceForPassCultureById(structureId, orderId);

            List<SeanceEntity> seanceEntities = commandeEntity.Lignes?.Where(l => l.TypeLigne == "DOS" && l.Dossier is not null && l.Dossier.State == TypeStateEnum.Reserver)
                                                                      .Select(l => l.Dossier!.Seance)
                                                                      .ToList()!;

            if (seanceEntities.Count == 0)
                return new() { OrderId = orderId };

            return new()
            {
                OrderId = orderId,
                FirstSessionStartDate = seanceEntities.MinBy(s => s.SeanceDateDeb)!.SeanceDateDeb,
                IdentityId = commandeEntity.IdentiteId,
                IdentityName = commandeEntity.Buyer!.IdentiteNom,
                ImmatriculationValue = commandeEntity.Buyer.Immatriculation?.ImmatriculationValue,
                OffreDescription = GenerateAutoDescriptionForPassCulture(structureId, orderId),
                PriceDetail = GenerateAutoPriceDetailForPassCulture(structureId, orderId),
                NbTickets = commandeEntity.Lignes?
                                .Where(l => l.TypeLigne == "DOS" && l.Dossier is not null && l.Dossier.State == TypeStateEnum.Reserver)
                                .Sum(l => l.Dossier!.DossierNbPlace) ?? 0,
                TotalPrice = commandeEntity.Lignes?
                                .Where(l => l.TypeLigne == "DOS" && l.Dossier is not null && l.Dossier.State == TypeStateEnum.Reserver)
                                .Sum(l => l.Dossier!.GetDossierGlobalAmountTTC()) ?? 0,
            };
        }

        public string GenerateAutoDescriptionForPassCulture(int structureId, int orderId)
        {
            CommandeEntity commandeEntity = GetOrderWithDependanceForPassCultureById(structureId, orderId);

            List<SeanceEntity> seanceEntities = commandeEntity.Lignes?.Where(l => l.TypeLigne == "DOS" && l.Dossier is not null && l.Dossier.State == TypeStateEnum.Reserver)
                                                                      .Select(l => l.Dossier!.Seance)
                                                                      .ToList()!;
            string description = string.Empty;

            var groupManif = seanceEntities.GroupBy(s => s.ManifestationId);

            foreach (var group in groupManif)
            {
                ManifestationEntity manifestation = group.First().Manifestation!;
                List<SeanceEntity> seances = group.Select(s => s).ToList();

                description += $"{manifestation.ManifestationNom}: \n";

                if (!string.IsNullOrWhiteSpace(manifestation.ManifestationInfos?.ResumeManif))
                    description += $"{manifestation.ManifestationInfos?.ResumeManif} ";

                description += $"\nSéances: \n";

                foreach (var seance in seances)
                {
                    description += $"du {seance.SeanceDateDeb}\n";
                }

                description += $"\n";
            }

            return description;
        }

        public string GenerateAutoPriceDetailForPassCulture(int structureId, int orderId)
        {
            CommandeEntity commandeEntity = GetOrderWithDependanceForPassCultureById(structureId, orderId);

            List<DossierEntity> dossierEntities = commandeEntity.Lignes?.Where(l => l.TypeLigne == "DOS" && l.Dossier is not null && l.Dossier.State == TypeStateEnum.Reserver)
                                                                      .Select(l => l.Dossier!)
                                                                      .ToList()!;

            string priceDetails = string.Empty;
            decimal totalPrice = 0;

            var groupDossier = dossierEntities.GroupBy(s => s.ManifestationId);

            foreach (var group in groupDossier)
            {
                ManifestationEntity manifestation = group.First().Seance!.Manifestation!;
                List<DossierEntity> dossiers = group.Select(d => d).ToList();

                priceDetails += $"\n{manifestation.ManifestationNom}: \n";

                priceDetails += $"\nSéances: \n";

                foreach (var dossier in dossiers)
                {
                    decimal amountDossier = dossier.GetDossierGlobalAmountTTC();
                    totalPrice += amountDossier;
                    priceDetails += $"du {dossier.Seance!.SeanceDateDeb} : {dossier.DossierNbPlace} billet(s) x {amountDossier / dossier.DossierNbPlace:0.00} € TTC = {amountDossier:0.00} € TTC \n";
                }
            }

            priceDetails += $"\nMontant total de l'offre collective: {totalPrice:0.00} € TTC";

            return priceDetails;
        }

        #region SelectLookup

        public async Task<List<SelectLookup>> SelectLookupReservationOffreCollectivePassCultureAsync(int structureId, bool onlyForUpdate = false)
        {
            List<ReservationInfosForPassCulture> reservations = [];

            if (onlyForUpdate)
            {
                reservations = await _orderRepository.GetAllReservationUpdatablePassCultureCollectiveOfferAsync(structureId)
                                                     .ConfigureAwait(false);
            }
            else
            {
                reservations = await _orderRepository.GetAllReservationOrderAvailableForPassCultureCollectiveOfferAsync(structureId)
                                                     .ConfigureAwait(false);
            }

            return reservations.Select(o => new SelectLookup()
            {
                Value = o.OrderId.ToString(),
                Libelle = $"{o.OrderId} - {o.IdentityName} ({o.IdentityId})",
            })
            .DistinctBy(l => l.Value)
            .ToList();
        }

        #endregion

        #region Private

        private CommandeEntity GetCommandeEntityForCollectiveOffer(int structureId, int orderId)
        {
            var commandeEntity = _orderRepository.GetCustomEntityById(structureId, orderId)
                                         .Include(c => c.Buyer)
                                         .Include(c => c.Buyer!.Immatriculation)
                                         .Include(c => c.Lignes)
                                         .Include(c => c.Lignes!.Select(l => l.Dossier))
                                         .Include(c => c.Lignes!.Select(l => l.Dossier!.Seance))
                                         .Include(c => c.Lignes!.Select(l => l.Dossier!.Seance!.Manifestation))
                                         .Include(c => c.Lignes!.Select(l => l.Dossier!.Seance!.Manifestation!.Structure))
                                         .ToEntity();

            if (commandeEntity is null)
                throw new NullReferenceException($"commande with id {orderId} from structure {structureId} not found");

            return commandeEntity;
        }

        private async Task<CollectiveOfferConsultation> GetPassCultureCollectiveOfferByOrderIdAsync(int structureId, int orderId)
        {
            var idProvider = _partnerIdsProviderRepository.FindFirstOrDefault(p => p.RodrigueId == orderId && p.PartnerIdsProviderTypeId == (int)PartnerIdsProviderType.PassCulture_OffreCollective, structureId)
               ?? throw new NullReferenceException($"orderId: {orderId} from structure {structureId}, don't linked to pass culture collective offer id");

            var collectiveOffer = await _passCultureService.GetCollectiveOfferByIdAsync(structureId, int.Parse(idProvider.IdProviderValue))
                                                           .ConfigureAwait(false);

            if (collectiveOffer == null)
                throw new NullReferenceException($"pass culture collective offer with id {idProvider.IdProviderValue} not found");

            return collectiveOffer;
        }

        private async Task<CollectiveOfferConsultation> ArchivePassCultureCollectiveOfferByOrderIdAsync(int structureId, int orderId)
        {
            var idProvider = _partnerIdsProviderRepository.FindFirstOrDefault(p => p.RodrigueId == orderId && p.PartnerIdsProviderTypeId == (int)PartnerIdsProviderType.PassCulture_OffreCollective, structureId)
               ?? throw new NullReferenceException($"orderId: {orderId} from structure {structureId}, don't linked to pass culture collective offer id");

            int collectiveOfferId = int.Parse(idProvider.IdProviderValue);

            bool isArchive = await _passCultureService.ArchiveCollectiveOffersAsync(structureId, [collectiveOfferId]);

            if (!isArchive)
                throw new Exception($"collectiveOffer with id {collectiveOfferId} not archive in pass culture");

            var collectiveOffer = await _passCultureService.GetCollectiveOfferByIdAsync(structureId, collectiveOfferId)
                ?? throw new NullReferenceException($"pass culture collective offer with id {idProvider.IdProviderValue} not found");

            return collectiveOffer;
        }

        private void SaveIdsProvideByPassCultureForACollectiveOffer(int structureId, string idProvideByPassCulture, int orderId)
        {
            PartnerIdsProviderEntity idsProviderEntity = new()
            {
                IdProviderValue = idProvideByPassCulture,
                PartnerIdsProviderTypeId = (int)PartnerIdsProviderType.PassCulture_OffreCollective,
                RodrigueId = orderId,
                PartnerTableRodrigueTypeId = (int)TableRodrigueType.Commande
            };

            _partnerIdsProviderRepository.Insert(structureId, idsProviderEntity);
        }

        private void SaveUAICode(int structureId, string uaiCode, int identiteId, IdentiteImmatriculationEntity? immatriculation)
        {
            if (immatriculation is null)
            {
                IdentiteImmatriculationEntity entity = new()
                {
                    IdentiteId = identiteId,
                    IdentiteImmatriculationTypeId = (int)ImmatriculationType.UAI,
                    ImmatriculationValue = uaiCode
                };

                _identiteImmatriculationRepository.Insert(structureId, entity);
            }
            else if (immatriculation!.ImmatriculationValue != uaiCode)
            {
                IdentiteImmatriculationEntity entity = immatriculation;
                entity.ImmatriculationValue = uaiCode;
                _identiteImmatriculationRepository.Update(structureId, entity);
            }
        }

        private void SetReservationInfos(CollectiveOfferCreation collectiveOfferPassCulture, CommandeEntity commandeEntity)
        {
            List<SeanceEntity> seanceEntities = commandeEntity.Lignes?.Where(l => l.TypeLigne == "DOS" && l.Dossier is not null && l.Dossier.State == TypeStateEnum.Reserver)
                                                                      .Select(l => l.Dossier!.Seance)
                                                                      .ToList()!;

            collectiveOfferPassCulture.StartDatetime = seanceEntities.MinBy(s => s.SeanceDateDeb)!.SeanceDateDeb.ToUniversalTime();
            collectiveOfferPassCulture.EndDatetime = collectiveOfferPassCulture.StartDatetime;
            collectiveOfferPassCulture.NumberOfTickets = commandeEntity.Lignes?
                                                                       .Where(l => l.TypeLigne == "DOS" && l.Dossier is not null && l.Dossier.State == TypeStateEnum.Reserver)
                                                                       .Sum(l => l.Dossier!.DossierNbPlace) ?? 0;
            collectiveOfferPassCulture.TotalPrice = commandeEntity.Lignes?
                                                                  .Where(l => l.TypeLigne == "DOS" && l.Dossier is not null && l.Dossier.State == TypeStateEnum.Reserver)
                                                                  .Sum(l => l.Dossier!.GetDossierGlobalAmountTTC()) ?? 0;

            if (seanceEntities.Count == 1 && seanceEntities[0].SeanceDateDeb != seanceEntities[0].SeanceDateFin)
                collectiveOfferPassCulture.DurationMinutes = (int)(seanceEntities[0].SeanceDateFin - seanceEntities[0].SeanceDateDeb).TotalMinutes;

        }

        private bool ReservationHasChange(CollectiveOfferConsultation collectiveOfferPassCulture, CommandeEntity commandeEntity)
        {
            List<SeanceEntity> seanceEntities = commandeEntity.Lignes?.Where(l => l.TypeLigne == "DOS" && l.Dossier is not null && l.Dossier.State == TypeStateEnum.Reserver)
                                                          .Select(l => l.Dossier!.Seance)
                                                          .ToList()!;

            if (collectiveOfferPassCulture.StartDatetime != seanceEntities.MinBy(s => s.SeanceDateDeb)!.SeanceDateDeb.ToUniversalTime())
                return true;

            int nbTicketInOrder = commandeEntity.Lignes?
                                                .Where(l => l.TypeLigne == "DOS" && l.Dossier is not null && l.Dossier.State == TypeStateEnum.Reserver)
                                                .Sum(l => l.Dossier!.DossierNbPlace) ?? 0;

            if (collectiveOfferPassCulture.NumberOfTickets != nbTicketInOrder)
                return true;

            decimal amountOrder = commandeEntity.Lignes?
                                                .Where(l => l.TypeLigne == "DOS" && l.Dossier is not null && l.Dossier.State == TypeStateEnum.Reserver)
                                                .Sum(l => l.Dossier!.GetDossierGlobalAmountTTC()) ?? 0;

            if (collectiveOfferPassCulture.TotalPrice != amountOrder)
                return true;

            return false;
        }

        private CommandeEntity GetOrderWithDependanceForPassCultureById(int structureId, int orderId)
        {
            var cacheKey = $"GetOrderWithDependanceForPassCultureById_{structureId}_{orderId}";

            var cacheResult = _memoryCache.Get<CommandeEntity>(cacheKey);

            if (cacheResult is not null)
                return cacheResult;

            var commandeEntity = _orderRepository.GetCustomEntityById(structureId, orderId)
                                                 .Include(c => c.Buyer)
                                                 .Include(c => c.Buyer!.Immatriculation)
                                                 .Include(c => c.Lignes)
                                                 .Include(c => c.Lignes!.Select(l => l.Dossier))
                                                 .Include(c => c.Lignes!.Select(l => l.Dossier!.Seance))
                                                 .Include(c => c.Lignes!.Select(l => l.Dossier!.Seance!.Manifestation))
                                                 .Include(c => c.Lignes!.Select(l => l.Dossier!.Seance!.Manifestation!.ManifestationInfos))
                                                 .Include(c => c.Lignes!.Select(l => l.Dossier!.Seance!.Manifestation!.Structure))
                                                 .ToEntity()
                         ?? throw new NullReferenceException($"Order '{orderId}' not found in db from structure '{structureId}'");

            _memoryCache.Set(cacheKey, commandeEntity, TimeSpan.FromSeconds(1));

            return commandeEntity;
        }


        #endregion
    }
}
