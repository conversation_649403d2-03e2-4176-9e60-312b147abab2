﻿using AutoMapper;
using Core.Themis.Libraries.BLL.Interfaces;
using Core.Themis.Libraries.Data.Repositories.Open.BuyerProfil.Interfaces;
using Core.Themis.Libraries.DTO;
using Core.Themis.Libraries.DTO.Lookup;
using Core.Themis.Libraries.DTO.Transactionnals;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.Configuration;
using System.Collections.Generic;
using System.Data;
using System.Linq;

namespace Core.Themis.Libraries.BLL
{
    public class BuyerProfilManager : IBuyerProfilManager
    {


        private readonly IBuyerProfilRepository _buyerProfilRepository;
        private readonly IMapper _mapper;
        private readonly IMemoryCache _memoryCache;
        private readonly IConfiguration _configuration;

        public BuyerProfilManager(IBuyerProfilRepository buyerProfilRepository, IMapper mapper, IMemoryCache memoryCache, IConfiguration configuration)
        {
            _buyerProfilRepository = buyerProfilRepository;
            _mapper = mapper;
            _memoryCache = memoryCache;
            _configuration = configuration;
        }

        public IEnumerable<BuyerProfilDTO> GetBuyerProfils(int structureId)
        {
            var listBPEntities = _buyerProfilRepository.GetBuyerProfils(structureId);

            var listModesPOfBPEntities = _buyerProfilRepository.GetPaymentMethodOfBuyerProfils(structureId);
            var paymentMethodsByProfilId = listModesPOfBPEntities
                    .GroupBy(pm => pm.ProfilAcheteurId)
                    .ToDictionary(
                        g => g.Key,
                        g => g.ToList()
            );

            var listBP = _mapper.Map<IEnumerable<BuyerProfilDTO>>(listBPEntities);

            foreach (var bp in listBP)
            {
                if (paymentMethodsByProfilId.TryGetValue(bp.Id, out var paymentMethods))
                {
                    bp.ListPaymentMethods = _mapper.Map<List<PaymentMethodDTO>>(paymentMethods);
                }
                else
                {
                    bp.ListPaymentMethods = new List<PaymentMethodDTO>();
                }
            }

            return listBP;

        }

        public BuyerProfilDTO? GetBuyerProfilById(int structureId, int buyerProfilId)
        {
            var buyerProfil = GetBuyerProfils(structureId).Where(bp => bp.Id == buyerProfilId).SingleOrDefault();

            return _mapper.Map<BuyerProfilDTO?>(buyerProfil);
        }

        public BuyerProfilDTO? GetBuyerProfilByLoginPassword(int structureId, string buyerProfilLogin, string buyerProfilPassword)
        {
            var buyerProfil = GetBuyerProfils(structureId).Where(bp => bp.Password.ToLower() == buyerProfilPassword.ToLower() && bp.Libelle.ToLower() == buyerProfilLogin.ToLower()).SingleOrDefault();

            return buyerProfil;
        }

        public BuyerProfilDTO? GetBuyerProfilByIdORLoginPassword(int structureId, int buyerProfilId, string login = null, string password = null)
        {

            if (!string.IsNullOrEmpty(login) && !string.IsNullOrEmpty(password))
            {
                return GetBuyerProfilByLoginPassword(structureId, login, password);
            }
            else
            {
                return GetBuyerProfilById(structureId, buyerProfilId);
            }
        }

        public static bool BuyerProfil_IsCorrect(BuyerProfilDTO bp)
        {
            bool result = true;
            if (bp.IsReseller)
            {
                if (bp.OperatorPaymentId == 0)
                {
                    result = false;
                }
                if (bp.IdentityPaiement == 0)
                {
                    result = false;
                }
                if (bp.ListPaymentMethods.Count == 0)
                {
                    result = false;
                }
            }
            return result;


        }

        #region SelectLookup

        public List<SelectLookup> SelectLookupBuyerProfils(int structureId)
        {
            return _buyerProfilRepository.GetAll(structureId)?
                                         .Select(b => new SelectLookup()
                                         {
                                             Libelle = $"{b.Id} - {b.Libelle}",
                                             Value = b.Id.ToString(),
                                         })
                                         .ToList() ?? [];
        }

        #endregion

    }
}
