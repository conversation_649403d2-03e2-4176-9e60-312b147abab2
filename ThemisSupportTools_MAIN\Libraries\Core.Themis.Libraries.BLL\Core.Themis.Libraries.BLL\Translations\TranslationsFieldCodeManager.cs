﻿using Core.Themis.Libraries.DTO.Translations;
using Core.Themis.Libraries.Utilities.ThemisSql;
using System;
using System.Collections.Generic;
using System.Data;
using Microsoft.Data.SqlClient;

namespace Core.Themis.Libraries.BLL.Translations
{
    public class TranslationsFieldCodeManager
    {
        /// <summary>
        /// Liste des traductions fields (champs par defaut)
        /// </summary>
        /// <param name="structureId"></param>
        /// <returns></returns>
        public static List<TranslationFieldsCode> GetTranslationsFieldsCodeList(int structureId, SqlConnection cnxWebLibrary, string scriptPathSqlCommons)
        {

            List<TranslationFieldsCode> lstAFieldsCode = new List<TranslationFieldsCode>();
            try
            {
                if (cnxWebLibrary.State == ConnectionState.Closed)
                    cnxWebLibrary.Open();
                //liste contenant le nom des sections
                string translationsAreasListSql = FilesForSqlRequestsManager.GetScript(structureId, "Translations\\Rodrigue\\", "getTranslationsFieldCodeList", scriptPathSqlCommons);

                using (SqlCommand cmd = new SqlCommand())
                {
                    cmd.Connection = cnxWebLibrary;
                    #region Rempli la liste des sections
                    cmd.CommandText = translationsAreasListSql;

                    using (SqlDataReader reader = cmd.ExecuteReader())
                    {
                        while (reader.Read())
                        {




                            /*
                            List<string> lstLangCodeColumns = reader.GetColumnNames().Where(g => g.StartsWith("txt_")).ToList();



                            foreach (string langCode in lstLangCodeColumns)
                            {
                                string thisLangCode = langCode.Split('_')[1].ToUpper();
                                //si la langue existe dans la base de Rodrigue
                                if (lstLanguages.Any(l => l.LanguageCode.ToUpper().Trim() == thisLangCode))
                                {

                                    TranslationsEntity trad = new TranslationsEntity
                                    {
                                        LangCode = thisLangCode,
                                        TranslationValue = drFieldCodeList[langCode].ToString()
                                    };

                                    translationDefault.ListTranslations.Add(trad);
                                }

                            }
                           
                            List<string> lstLangCodeColumns = (from x in reader.Table.Columns.Cast<DataColumn>()
                                                               where x.ColumnName.StartsWith("txt_")
                                                               select x.ColumnName).ToList();
                            */



                            TranslationFieldsCode fieldCode = new TranslationFieldsCode()
                            {
                                FieldCodeId = reader.GetInt32(reader.GetOrdinal("id")),
                                FieldCode = reader.GetString(reader.GetOrdinal("fieldSpecificCode")),
                                FieldDescription = reader.GetString(reader.GetOrdinal("description")),
                                AreaId = reader.GetInt32(reader.GetOrdinal("area_id")),
                                TranslationArea = new TranslationAreaDTO()
                                {
                                    TranslationAreaId = reader.GetInt32(reader.GetOrdinal("area_id")),
                                    TranslationAreaName = reader.GetString(reader.GetOrdinal("name"))
                                },
                                TranslationField = new TranslationFields()
                                {
                                    ListTranslations = new List<TranslationsDTO>()
                                  {

                                      new TranslationsDTO()
                                      {
                                          LangCode =  "FR",
                                          TranslationValue = reader.GetString(reader.GetOrdinal("txt_fr")),
                                      },
                                       new TranslationsDTO()
                                      {
                                          LangCode =  "EN",
                                          TranslationValue = reader.GetString(reader.GetOrdinal("txt_en")),
                                      },
                                        new TranslationsDTO()
                                      {
                                          LangCode =  "DE",
                                          TranslationValue = reader.GetString(reader.GetOrdinal("txt_de")),
                                      },
                                         new TranslationsDTO()
                                      {
                                          LangCode =  "NL",
                                          TranslationValue = reader.GetString(reader.GetOrdinal("txt_nl")),
                                      }
                                  }

                                },


                                GlobalFieldCodeId = reader.GetInt32(reader.GetOrdinal("global_field_id")),
                            };

                            lstAFieldsCode.Add(fieldCode);
                        }
                    }
                    #endregion
                }


                cnxWebLibrary.Close();
            }
            catch (Exception ex)
            {
                throw new Exception(ex.Message);
            }
            finally
            {

                if (cnxWebLibrary.State == ConnectionState.Open)
                    cnxWebLibrary.Close();
            }
            return lstAFieldsCode;

        }

        public static bool InsertTranslationsFieldsCodeList(int structureId, int globalFieldCodeId, TranslationFieldsCode translationFieldCode, SqlConnection cnxWebLibrary, string scriptPathSqlCommons)
        {
            bool result = false;
            try
            {
                if (cnxWebLibrary.State == ConnectionState.Closed)
                    cnxWebLibrary.Open();


                //liste contenant les traductions par défault dans Rodrigue
                string translationsSpecificsListSql = FilesForSqlRequestsManager.GetScript(structureId, "Translations\\Rodrigue\\", "insertTranslationsFieldCode", scriptPathSqlCommons);

                using (SqlCommand cmd = new SqlCommand())
                {
                    cmd.Parameters.Add(new SqlParameter("@pFieldSpecificCode", translationFieldCode.FieldCode.Trim().Replace(" ", "_")));
                    cmd.Parameters.Add(new SqlParameter("@pDescription", translationFieldCode.FieldDescription));
                    cmd.Parameters.Add(new SqlParameter("@pAreaId", translationFieldCode.AreaId));
                    cmd.Parameters.Add(new SqlParameter("@pGlobalFieldId", globalFieldCodeId));

                    cmd.Connection = cnxWebLibrary;
                    cmd.CommandText = translationsSpecificsListSql;

                    //return (int)cmd.ExecuteScalar();
                    result = cmd.ExecuteNonQuery() == 1;
                }


                cnxWebLibrary.Close();

            }
            catch (Exception ex)
            {
                throw new Exception(ex.Message);
            }
            finally
            {
                if (cnxWebLibrary.State == ConnectionState.Open)
                    cnxWebLibrary.Close();

            }


            return result;

        }


        public static bool UpdateTranslationsFieldsCodeList(int structureId, int globalFieldCodeId, TranslationFieldsCode translationFieldCode, SqlConnection cnxWebLibrary, string scriptPathSqlCommons)
        {
            bool result = false;
            try
            {
                if (cnxWebLibrary.State == ConnectionState.Closed)
                    cnxWebLibrary.Open();


                //liste contenant les traductions par défault dans Rodrigue
                string translationsSpecificsListSql = FilesForSqlRequestsManager.GetScript(structureId, "Translations\\Rodrigue\\", "updateTranslationsFieldCode", scriptPathSqlCommons);

                using (SqlCommand cmd = new SqlCommand())
                {
                    cmd.Parameters.Add(new SqlParameter("@pFieldSpecificCode", translationFieldCode.FieldCode.Trim().Replace(" ", "_")));
                    cmd.Parameters.Add(new SqlParameter("@pDescription", translationFieldCode.FieldDescription));
                    cmd.Parameters.Add(new SqlParameter("@pAreaId", translationFieldCode.AreaId));
                    cmd.Parameters.Add(new SqlParameter("@pGlobalFieldId", globalFieldCodeId));
                    cmd.Parameters.Add(new SqlParameter("@pFieldCodeId", translationFieldCode.FieldCodeId));

                    cmd.Connection = cnxWebLibrary;
                    cmd.CommandText = translationsSpecificsListSql;

                    //return (int)cmd.ExecuteScalar();
                    result = cmd.ExecuteNonQuery() == 1;
                }


                cnxWebLibrary.Close();

            }
            catch (Exception ex)
            {
                throw new Exception(ex.Message);
            }
            finally
            {
                if (cnxWebLibrary.State == ConnectionState.Open)
                    cnxWebLibrary.Close();

            }


            return result;

        }


    }
}
