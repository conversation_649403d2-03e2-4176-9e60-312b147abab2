/* big One v2 : one recoit les gestion places, retourne : zone, etage, section, dispo calculée sur les entree_xx */

 
/*
declare @peventid int =[EVENTID]
declare @psessionid int = [SESSIONID]
declare @pidentityId int = 0
declare @pbuyerprofilId int = 0
declare @plangCode varchar(2) = 'fR'
declare @plisteZonesId varchar(max) = '' 
declare @plisteEtagesId varchar(max) = ''
declare @plisteSectionsId varchar(max) = ''
declare @plisteCategsId varchar(max) = ''
declare @pmyPanier varchar(max) = '' 
declare @isrevendeur bit = 0
*/


    DECLARE @myBeneficiaire VARCHAR(6) 

	    /******************************** commenter/decommenter la ligne ci dessous suivant ce que l'on	veut : */

          SET @myBeneficiaire ='PAYEUR' 

CREATE TABLE #mygp 
( 
    gestion_place_id INT,
    isforpa int
) 


DECLARE @langue_Id int = 0
SELECT @langue_Id = langue_id FROM langue WHERE  upper(langue_code) = upper(@plangcode);
IF @langue_Id is null
	SET @langue_Id = 0
	
INSERT INTO #mygp
    SELECT gestion_place_id , isContrainteIdentite from gestion_place gp where gestion_place_id in ({gpAsked}) and isvalide =1

INSERT INTO #mygp
      SELECT DISTINCT gestion_place_parent_id, isContrainteIdentite FROM gestion_place -- les parents
     WHERE gestion_place_id in (select gestion_place_id from #mygp)
 
CREATE TABLE #Gpres (Gestion_Place_id int ,reserve_id int)
INSERT INTO #GPres 
SELECT gpin.gestion_place_id, reserve_id  from #mygp my 
		 INNER JOIN gestion_place gpin on gpin.gestion_place_id = my.gestion_place_id
		 INNER JOIN gestion_place_reserve gpr on gpr.gestion_place_id=my.gestion_place_id
		 WHERE gpin.isvalide = 1		 
		 UNION		 
		 SELECT gpin.gestion_place_id, 0 as  reserve_id from #mygp my 
		 INNER JOIN gestion_place gpin on gpin.gestion_place_id = my.gestion_place_id		 
		 WHERE gpin.isvalide = 1 AND gpin.aucune_reserve=1 
CREATE CLUSTERED INDEX IX_Gestion_place_id ON #GPres (gestion_place_id)
CREATE INDEX IX_reserve_id ON #GPres (reserve_id)

CREATE TABLE #GpLight (Gestion_Place_id int ,reserve_id int,Manif_id int ,Seance_id int, categ_id int,Type_tarif_id int,Nb_Min int,Nb_Max int,Prise_Place int, Aucune_Reserve int)
INSERT INTO #GpLight 
SELECT gp.Gestion_place_id,reserve_id,manif_id ,Seance_id,Categ_id ,type_tarif_id,gp.nb_min,gp.nb_max,gp.prise_place,gp.Aucune_Reserve   from gestion_place gp inner join #Gpres gpr on gpr.Gestion_Place_id = gp.gestion_place_id
	  
      DECLARE @sqlreq VARCHAR(max) 
      DECLARE @const_placementlibre INT; 
      DECLARE @const_priseauto INT;
      DECLARE @const_vueplacement INT ;
      DECLARE @const_choixsurplan INT; 

      SET @const_placementlibre =32; 
      SET @const_priseauto =16; 
      SET @const_vueplacement =8; 
      SET @const_choixsurplan =1; 

                      DECLARE @listgp TABLE 
                  ( 
                     manif_id         INT, 
                     gestion_place_id INT, 
                     isforpa          INT 
                  ) ;


      CREATE TABLE #tableresultgrilletarif 
        ( 
           event_Id         INT, 
           session_id       INT, 
           railingpriceid  INT, 
           amountexcepttax DECIMAL(18, 10), 
           charge          DECIMAL(18, 10), 
           tax             DECIMAL(18, 10), 
           discount        DECIMAL(18, 10), 
           commission      DECIMAL(18, 10), 
           totaltax        DECIMAL(18, 10), 
           totalamount     DECIMAL(18, 10), 
           ticketamount    DECIMAL(18, 10), 
           zoneid          INT, zonedispSequence int,
           zonename        VARCHAR(50), 
           sectionid       INT, sectiondispSequence int,
           sectionname     VARCHAR(50), 
           floorname       VARCHAR(50), 
           floorid         INT, floordispSequence int,
           categ_Id      INT, 
           categoryname    VARCHAR(50), 
           categorycode    VARCHAR(50), 
		   categorycolor    INT, 
		   categdispSequence int,
           priceid         INT, 
           pricename       VARCHAR(100), 
           nbseats          INT, 
           nbseatsmin       INT, 
           nbseatsmax       INT, 
		   nbhisto          INT,
           bookingtype      INT, 
           notnumbered      INT, 
           priseauto        INT,
           surplan          INT,
           voirplace        INT,
           gpid             INT, 
           PriceDispSequence INT,
           zapper_ZES       INT,
           zapper_ZE        INT 
        ); 

      CREATE TABLE #tableresultsession 
        ( 
           gestion_place_id INT, 
           event_Id         INT, 
           session_id       INT, 
           nbseatmin        INT, 
           nbseatmax        INT 
        ); 

      /* multiplicateur : multiplie le nombre de places max grÃ¢ce aux formules et ou cartes de l'historique */
      CREATE TABLE #mymultiplicator 
        ( 
           type_tarif_id INT, 
           mult          INT 
        ) 

      /* insert le nombre de formules prises */ 
      DECLARE @peventIdHisto_encours INT 

      CREATE TABLE #myhisto 
        ( 
           manifid     INT, 
           seanceid    INT, 
           categid     INT, 
           typetarifid INT, 
           nbr         INT 
        )
        IF (@isrevendeur != 1) -- si revendeur, pas besoin de calculer l'histo, panier, etc
        BEGIN
        
          IF (@myBeneficiaire = 'CONSOM' ) AND @pidentityId > 0 -- ************* 
            BEGIN 
                INSERT #mymultiplicator 
                       (type_tarif_id, 
                        mult) 
                SELECT mult.type_tarif_id, 
                       Count(DISTINCT abo_id) AS mult 
                FROM   [gestion_place_multiplicateur_nbmax] mult, 
                       commande_ligne_comp clc, 
                       commande_ligne cl 
                WHERE  cl.commande_ligne_id = clc.commande_ligne_id 
                       AND identite_id = @pidentityId 
                       AND mult.formule_id = cl.formule_id   AND etat IN ( 'R', 'P', 'B' )               
                GROUP  BY mult.type_tarif_id, 
                          mult.produit_id, 
                          mult.formule_id 

                /* insert le nombre de cartes d'abo prises */ 
                INSERT #mymultiplicator 
                       (type_tarif_id, 
                        mult) 
                SELECT mult.type_tarif_id, 
                       Max(nb_produit) 
                FROM   [gestion_place_multiplicateur_nbmax] mult, 
                       dossier_produit dp 
                WHERE  identite_id = @pidentityId 
                       AND dp.produit_id = mult.produit_id 
                -- les dossiers produit à mon nom 
                GROUP  BY mult.type_tarif_id, 
                          mult.produit_id, 
                          mult.formule_id 
            END 

          IF ( @myBeneficiaire = 'PAYEUR' ) AND @pidentityId > 0  -- ************* 
            BEGIN 
                INSERT #mymultiplicator 
                       (type_tarif_id, 
                        mult) 
                SELECT mult.type_tarif_id, 
                       Count(DISTINCT abo_id) AS mult 
                FROM   [gestion_place_multiplicateur_nbmax] mult, 
                       commande_ligne_comp clc, 
                       commande_ligne cl 
                       INNER JOIN commande c  ON c.commande_id = cl.commande_id 
                WHERE  cl.commande_ligne_id = clc.commande_ligne_id 
                       AND abo_id <> 0 
                       AND c.identite_id = @pidentityId 
                       AND mult.formule_id = cl.formule_id  AND etat IN ( 'R', 'P', 'B' )
                -- les abos que j'ai acheté 
                GROUP  BY mult.type_tarif_id, 
                          mult.produit_id, 
                          mult.formule_id 

                /* insert le nombre de cartes d'abo prises */ 
                INSERT #mymultiplicator 
                       (type_tarif_id, 
                        mult) 
                SELECT mult.type_tarif_id, 
                       Count(*) 
                FROM   [gestion_place_multiplicateur_nbmax] mult, 
                       dossier_produit dp 
                       INNER JOIN commande c 
                               ON c.commande_id = dp.commande_id 
                WHERE  c.identite_id = @pidentityId 
                       AND dp.produit_id = mult.produit_id 
                -- les cartes que j'ai acheté 
                GROUP  BY mult.type_tarif_id, 
                          mult.produit_id, 
                          mult.formule_id 
            END 

            INSERT INTO #myhisto SELECT @peventid, e.seance_id, e.categorie_id, e.type_tarif_id, count(*) as nbr
                from dossier_[EVENTID] d 
                INNER JOIN entree_[EVENTID] e on e.dossier_id=d.dossier_id 
                LEFT JOIN coupons_promo cp ON cp.commande_id = d.commande_id 
                WHERE identite_id=@pidentityId AND cp.commande_id IS NULL
                GROUP by e.seance_id,  e.categorie_id, e.type_tarif_id, e.seance_id

          -- select * from #myhisto 

          IF (@pmyPanier <> '' ) 
            BEGIN 
                DECLARE @gpInBasket TABLE (gpid INT ) 
                DECLARE @Counter INT 

                SET @Counter = 1 

                WHILE ( Charindex(',', @pmyPanier) > 0 ) 
                  BEGIN 
                      INSERT INTO @gpInBasket (gpid) 
                      SELECT gpId = Ltrim(Rtrim(Substring(@pmyPanier, 1, 
                                                Charindex(',', @pmyPanier)-1)  )) 

                      SET @pmyPanier = Substring(@pmyPanier, Charindex(',', @pmyPanier) + 1, Len( @pmyPanier)) 
                      SET @Counter = @Counter + 1 
                  END 

                INSERT INTO @gpInBasket 
                            (gpid) 
                SELECT gpId = Ltrim(Rtrim(@pmyPanier)) 
            END 

          INSERT INTO #myhisto 
          SELECT manif_id, 
                 seance_id, 
                 categ_id, 
                 type_tarif_id, 
                 1 
          FROM   gestion_place gp 
                 INNER JOIN @gpInBasket bask 
                         ON bask.gpid = gp.gestion_place_id 

              --select * from #myhisto

          UPDATE #myhisto 
          SET    typetarifid = (SELECT  type_tarif_id_maitre 
                                FROM   gestion_place_correspondance_tarifs 
                                WHERE  #myhisto.typetarifid = type_tarif_id_eleve
                  and type_tarif_id_maitre in (select type_tarif_id from @listgp gp inner join gestion_place gp2 on gp2.gestion_place_id = gp.gestion_place_id)
              
                  ) WHERE typetarifid in (select type_tarif_id_eleve FROM gestion_place_correspondance_tarifs)

           /******************************************** FIN calcul de l'histo */ 
      END 
      
            DECLARE @zapperEtage INT 

            SELECT @zapperEtage = Count(*) 
            FROM   proprietes_of_manifs pm, 
                   proprietes_references_of_manifs pref 
            WHERE  pref.propriete_ref_id = pm.propriete_ref_id 
                   AND valeur = '1' 
                   AND pm.manifestation_id = @peventId 
                   AND pref.code = 'ZapperEtag' 

            DECLARE @zapperZEC INT 

            SELECT @zapperZEC = Count(*) 
            FROM   proprietes_of_manifs pm, 
                   proprietes_references_of_manifs pref 
            WHERE  pref.propriete_ref_id = pm.propriete_ref_id 
                   AND valeur = '1 '
                   AND pm.manifestation_id = @peventId 
                   AND pref.code = 'ZapperZES' 

            PRINT 'zapperZEC=' + CONVERT(VARCHAR, @zapperZEC) 

            PRINT 'zapperEtage=' + CONVERT(VARCHAR, @zapperEtage) 

            SELECT type_tarif_id, SUM(mult) as total_mult
INTO #mymult_total
FROM #mymultiplicator
GROUP BY type_tarif_id

SELECT typetarifid, manifid, SUM(nbr) as total_nbhisto
INTO #myhisto_total
FROM #myhisto
GROUP BY typetarifid, manifid


;WITH VTS_MAX AS (
    SELECT
        *,
        ROW_NUMBER() OVER (
            PARTITION BY
                tarif_logique_id,
                seance_id,
                categ_id,
                type_tarif_id
            ORDER BY vts_v DESC
        ) as rn
    FROM valeur_tarif_stock[EVENTID]
    --WHERE vts_grille1 >= 0
)
, VTS_GROUP_OK AS (
    -- On repère les groupes dont la ligne max (rn = 1) a vts_grille1 >= 0
    SELECT
        tarif_logique_id,
        seance_id,
        categ_id,
        type_tarif_id
    FROM VTS_MAX
    WHERE rn = 1 AND vts_grille1 >= 0
)

 INSERT INTO #tableResultGrilleTarif
SELECT 
    event_Id,
    session_id,
    RailingPriceID,
    AmountExceptTax,
    Charge,
    Tax,
    discount,
    Commission,
    TotalTax,
    TotalAmount,
    TicketAmount,
    ZoneId,
    zonedispsequence,
    ZoneName,
    SectionId,
    sectiondispSequence,
    SectionName,
    FloorName,
    floorId,
    floordispSequence,
    categ_Id,
    CategoryName,
    CategoryCode,
    categColor,
    CategdispSequence,
    PriceID,
    PriceName,
    sum(nbSeats) as nbSeats,
    NbSeatMin,
    NbSeatMax,
    NbHisto,
    BookingType,
    NotNumbered,
    priseauto,
    surplan,
    voirplace,
    GpId,
    PriceDispSequence,
    0 as zapper_ZES,
    1 as zapper_ze 
FROM (
    SELECT 
        vgp.manif_id as event_Id,
        vgp.seance_id as session_id,
        vts.vts_id as RailingPriceID,
        vts.vts_grille1 as AmountExceptTax,
        vts.vts_grille2 as Charge,
        Tax = 
            case when modecol4 = 'TAXE' then vts.vts_grille4 else 0 end +
            case when modecol5 = 'TAXE' then vts.vts_grille5 else 0 end +
            case when modecol6 = 'TAXE' then vts.vts_grille6 else 0 end +
            case when modecol7 = 'TAXE' then vts.vts_grille7 else 0 end +
            case when modecol8 = 'TAXE' then vts.vts_grille8 else 0 end +
            case when modecol9 = 'TAXE' then vts.vts_grille9 else 0 end +
            case when modecol10 = 'TAXE' then vts.vts_grille10 else 0 end,
        Discount =
            case when modecol4 = 'REMISE' then vts.vts_grille4 else 0 end +
            case when modecol5 = 'REMISE' then vts.vts_grille5 else 0 end +
            case when modecol6 = 'REMISE' then vts.vts_grille6 else 0 end +
            case when modecol7 = 'REMISE' then vts.vts_grille7 else 0 end +
            case when modecol8 = 'REMISE' then vts.vts_grille8 else 0 end +
            case when modecol9 = 'REMISE' then vts.vts_grille9 else 0 end +
            case when modecol10 = 'REMISE' then vts.vts_grille10 else 0 end,
        Commission =
            case when modecol4 = 'COMMISSION' then vts.vts_grille4 else 0 end +
            case when modecol5 = 'COMMISSION' then vts.vts_grille5 else 0 end +
            case when modecol6 = 'COMMISSION' then vts.vts_grille6 else 0 end +
            case when modecol7 = 'COMMISSION' then vts.vts_grille7 else 0 end +
            case when modecol8 = 'COMMISSION' then vts.vts_grille8 else 0 end +
            case when modecol9 = 'COMMISSION' then vts.vts_grille9 else 0 end +
            case when modecol10 = 'COMMISSION' then vts.vts_grille10 else 0 end,
        TotalTax =
            case when modecol4 = 'REMISE' then -vts.vts_grille4 when modecol4 = 'TAXE' then vts.vts_grille4 else 0 end +
            case when modecol5 = 'REMISE' then -vts.vts_grille5 when modecol5 = 'TAXE' then vts.vts_grille5 else 0 end +
            case when modecol6 = 'REMISE' then -vts.vts_grille6 when modecol6 = 'TAXE' then vts.vts_grille6 else 0 end +
            case when modecol7 = 'REMISE' then -vts.vts_grille7 when modecol7 = 'TAXE' then vts.vts_grille7 else 0 end +
            case when modecol8 = 'REMISE' then -vts.vts_grille8 when modecol8 = 'TAXE' then vts.vts_grille8 else 0 end +
            case when modecol9 = 'REMISE' then -vts.vts_grille9 when modecol9 = 'TAXE' then vts.vts_grille9 else 0 end +
            case when modecol10 = 'REMISE' then -vts.vts_grille10 when modecol10 = 'TAXE' then vts.vts_grille10 else 0 end,
        TotalAmount =
            vts.vts_grille1 + vts.vts_grille2 +
            case when modecol4 = 'REMISE' then -vts.vts_grille4 when modecol4 = 'TAXE' then vts.vts_grille4 else 0 end +
            case when modecol5 = 'REMISE' then -vts.vts_grille5 when modecol5 = 'TAXE' then vts.vts_grille5 else 0 end +
            case when modecol6 = 'REMISE' then -vts.vts_grille6 when modecol6 = 'TAXE' then vts.vts_grille6 else 0 end +
            case when modecol7 = 'REMISE' then -vts.vts_grille7 when modecol7 = 'TAXE' then vts.vts_grille7 else 0 end +
            case when modecol8 = 'REMISE' then -vts.vts_grille8 when modecol8 = 'TAXE' then vts.vts_grille8 else 0 end +
            case when modecol9 = 'REMISE' then -vts.vts_grille9 when modecol9 = 'TAXE' then vts.vts_grille9 else 0 end +
            case when modecol10 = 'REMISE' then -vts.vts_grille10 when modecol10 = 'TAXE' then vts.vts_grille10 else 0 end,
        TicketAmount = vts.vts_grille3,
        vgp.zone_id as ZoneID,
        z.pref_affichage as zonedispSequence,
        isnull(ztrad.zone_nom, z.zone_nom) as ZoneName,
        vgp.section_id as Sectionid,
        sc.pref_affichage as sectiondispSequence,
        isnull(sctrad.section_nom, sc.section_nom) as SectionName,
        'Uni' as FloorName,
        0 as FloorId,
        0 as floordispSequence,
        vts.categ_id as categ_Id,
        isnull(ctrad.[categ_nom], c.categ_nom) as CategoryName,
        c.categ_code as CategoryCode,
        c.categ_couleur_id as categColor,
        c.pref_affichage as categdispSequence,
        vts.type_tarif_id as PriceID,
        isnull(tttrad.type_tarif_nom, tt.type_tarif_nom) as PriceName,
        NbSeats as NbSeats,
        vgp.nb_min as NbSeatMin,
        vgp.nb_max * ISNULL(mt.total_mult, 1) as NbSeatMax,
        ISNULL(mh.total_nbhisto, 0) as NbHisto,
        BookingType = CASE WHEN vgp.prise_place is null THEN 0 ELSE vgp.prise_place END,
        NotNumbered = CASE WHEN (vgp.prise_place & 32) = 0 THEN 0 ELSE 1 END,
        priseauto = CASE WHEN (vgp.prise_place & 16) = 0 THEN 0 ELSE 1 END,
        voirplace = CASE WHEN (vgp.prise_place & 8) = 0 THEN 0 ELSE 1 END,
        surplan = CASE WHEN (vgp.prise_place & 1) = 0 THEN 0 ELSE 1 END,
        vgp.gestion_place_id as GpId,
        tt.pref_affichage as PriceDispSequence  
    FROM VTS_MAX  vts
    JOIN VTS_GROUP_OK ok
        ON vts.tarif_logique_id = ok.tarif_logique_id
        AND vts.seance_id = ok.seance_id
        AND vts.categ_id = ok.categ_id
        AND vts.type_tarif_id = ok.type_tarif_id
    INNER JOIN (
        SELECT 
            gp.manif_id,
            gp.gestion_place_id,
            gp.seance_id,
            gp.type_tarif_id,
            gp.categ_id,
            e.reserve_id,
            gp.nb_min,
            gp.nb_max,
            gp.prise_place,
            rlp.zone_id,
            rlp.section_id,
            rlp.etage_id,
            isnull(sum(
                case when entree_etat = 'L' then 
                    case when gp.aucune_reserve = 1 then 
                        case when e.reserve_id > 0 then 
                            case when gpreserve.reserve_id > 0 then 1 else 0 end
                        else 1 end
                    when entree_etat <> 'L' then 0 
                    else 1 end
                end                  
            ), 0) as NbSeats
        FROM entree_[EVENTID] e 
        INNER JOIN #GpLight gp  
            ON gp.seance_id = e.seance_id
            and gp.categ_id = e.categorie_id
            and gp.reserve_id = e.reserve_id
        INNER JOIN VTS_MAX vts
            ON vts.type_tarif_id = gp.type_tarif_id
            AND vts.categ_id = gp.categ_id
            AND vts.seance_id = e.seance_id
        INNER JOIN #mygp ON #mygp.gestion_place_id = gp.gestion_place_id 
        INNER JOIN #Gpres gpreserve
            ON gpreserve.gestion_place_id = gp.gestion_place_id 
            AND e.reserve_id = gpreserve.reserve_id 
        INNER JOIN reference_lieu_physique rlp
            ON rlp.ref_uniq_phy_id = e.reference_unique_physique_id  
        INNER JOIN traduction_categorie c on c.categ_id = vts.categ_id 
        INNER JOIN traduction_zone z on z.zone_id = rlp.zone_id 
        INNER JOIN traduction_section sc on sc.section_id = rlp.section_id 
        INNER JOIN traduction_etage et on et.etage_id = rlp.etage_id 
        WHERE
            e.entree_etat in ('L','P','B','R')
            AND (flag_selection = '' OR flag_selection IS NULL)
            AND alotissement_id = 0
            AND contingent_id = 0 
            AND et.langue_id = @langue_id
            AND z.langue_id = @langue_id
            AND sc.langue_id = @langue_id
            AND c.langue_id = @langue_id
             AND vts.rn = 1
        GROUP BY
            gp.manif_id, gp.gestion_place_id, gp.seance_id, gp.type_tarif_id,
            gp.categ_id, gp.nb_min, gp.nb_max, gp.prise_place, e.reserve_id,
            rlp.zone_id, rlp.section_id, rlp.etage_id
    ) vgp ON vts.type_tarif_id = vgp.type_tarif_id
        and vts.categ_id = vgp.categ_id 
        AND vts.seance_id = vgp.seance_id
    INNER JOIN structure st ON st.structure_id > 0
    INNER JOIN categorie c ON c.categ_id = vts.categ_id
    LEFT OUTER JOIN traduction_categorie ctrad
        ON ctrad.categ_id = c.categ_id and ctrad.langue_id = @langue_id
    INNER JOIN type_tarif tt ON tt.type_tarif_id = vts.type_tarif_id
    LEFT OUTER JOIN traduction_type_tarif tttrad
        ON tttrad.type_tarif_id = tt.type_tarif_id and tttrad.langue_id =@langue_id

	LEFT JOIN #mymult_total mt ON mt.type_tarif_id = tt.type_tarif_id
	LEFT JOIN #myhisto_total mh ON mh.typetarifid = vts.type_tarif_id AND mh.manifid = @peventId

    INNER JOIN zone z on z.zone_id = vgp.zone_id
    LEFT OUTER JOIN traduction_zone ztrad on ztrad.zone_id = z.zone_id and ztrad.langue_Id = @langue_id
    INNER JOIN section sc on sc.section_id = vgp.section_id
    LEFT OUTER JOIN traduction_section sctrad on sctrad.section_id = sc.section_id and sctrad.langue_Id = @langue_id
    WHERE 
        vts.rn = 1
) ssr
WHERE 1 = 1 
    --AND session_id = @psessionId
GROUP BY
    event_Id, session_id, RailingPriceID, AmountExceptTax, Charge, Tax,
    discount, Commission, TotalTax, TotalAmount, TicketAmount, ZoneId,
    zonedispsequence, ZoneName, SectionId, sectiondispSequence, SectionName,
    FloorName, floorId, floordispSequence, categ_Id, CategoryName, CategoryCode,
    categColor, CategdispSequence, PriceID, PriceName, NbSeatMin, NbSeatMax,
    NbHisto, BookingType, NotNumbered, priseauto, surplan, voirplace, GpId, PriceDispSequence



    /* regle generale sur la seance : */ 
	DECLARE @sumNbr INT;
    SELECT @sumNbr = ISNULL(SUM(nbr), 0) FROM #myhisto WHERE manifid = @peventId;

	    INSERT INTO #tableResultSession (gestion_place_id, event_Id, session_id, NbSeatMin, NbSeatMax)
    SELECT 
        gp.gestion_place_id,
        gp.manif_id as event_Id,
        gp.seance_id as session_id, 
        gp.nb_min as NbSeatMin,
        CASE 
            WHEN gp.dispo < (gp.nb_max - @sumNbr) THEN gp.dispo 
            ELSE (gp.nb_max - @sumNbr) 
        END AS NbSeatMax
    FROM 
        gestion_place gp
        INNER JOIN #mygp ON #mygp.gestion_place_id = gp.gestion_place_id 
    WHERE 
        gp.manif_id = @peventId 
        AND gp.type_tarif_id IS NULL 
        AND gp.categ_id IS NULL 
        AND gp.isvalide = 1 
        --AND gp.seance_id = @psessionId


DROP TABLE #mymultiplicator 
DROP TABLE #mymult_total
DROP TABLE #myhisto_total


;WITH AdhProps AS (
   SELECT Adhesion_Catalog_ID, Propriete_Code, Propriete_Valeur_Int1, Propriete_Valeur_Int2, Propriete_Valeur_Int3
   FROM   Adhesion_Catalog_Propriete
   WHERE  Propriete_Code IN ('TARIF_MAITRE','TARIF_ELEVE')
)

SELECT DISTINCT event_Id, 
					m.manifestation_nom as event_name,
					m.manifestation_code as event_code,	
					l.lieu_id as place_id, l.lieu_nom as place_name,								
                    session_id, 
					sea.seance_date_deb as date_start, 
					sea.seance_date_fin as date_end,
                    railingpriceid, 
                    convert(int, 100 * amountexcepttax) as amountexcepttax , 
                    convert(int, 100 * charge) as charge, 
                    convert(int, 100 * tax) as tax, 
                    convert(int, 100 * discount) as discount, 
                    convert(int, 100 * commission) as commission, 
                    convert(int, 100 * totaltax) as totaltax, 
                    convert(int, 100 * totalamount) as totalamount, 
                    convert(int, 100 * ticketamount) as ticketamount, 
                    zoneid, 
                    zonename, 
					zonedispSequence,
                    sectionid, 
                    sectionname, 
					sectiondispSequence,
                    floorname, 
                    floorid, 
					floordispSequence,
                    gt.categ_Id, 
                    rtrim(ltrim(categoryname)) as categoryname, 
                    categorycode, 
					categorycolor,
					categdispSequence,
                    priceid, 
                    rtrim(ltrim(pricename)) as pricename, 
                    nbseats, 
                    nbseatsmin, 
                    case when adh.Adhesion_Catalog_ID is null then nbseatsmax - nbhisto else nbseatsmax end as nbseatsmax, 
                    bookingtype, 
                    notnumbered,
                    priseauto,
                    surplan,
                    voirplace,
					case when nepasafficherdate like 'o' then 0 else 1 end as isshowsessiondate,
					case when nepasafficherdate like 'h' then 0 when nepasafficherdate like 'o' then 0 else 1 end as isshowsessionhour,
                    Max(gpid) AS GpId,
					gp.gestion_place_parent_id as GpParentId,
					o.offre_id as offerId,  o.offre_nom as offerName,
					adh.Adhesion_Catalog_ID as adhCatalogId,
					adh.Catalog_Libelle as adhCatalogName,
					adh.Mise_En_Avant as miseenavant,
                    PriceDispSequence,

                    ISNULL(adh.Pref_Affichage,  sum( 9999 +gt.PriceDispSequence)) as prefAffichageAdhesion, -- permet de mettre les tarifs hors adhésion à la fin
                    ISNULL(adh.Pref_Affichage, 99999) as prefAffichageAdhesion2, -- permet de mettre les tarifs hors adhésion à la fin
               
                    CASE WHEN propMaitres.Propriete_Valeur_Int1 IS NULL THEN 0 ELSE 1 END as isTarifMaitre,
                    propMaitres.Propriete_Valeur_Int2 as rodMinMaitre,
                    propMaitres.Propriete_Valeur_Int3 as rodMaxMaitre,
                    CASE WHEN propEleves.Propriete_Valeur_Int1 IS NULL THEN 0 ELSE 1 END as isTarifEleve,
                    propEleves.Propriete_Valeur_Int2 as rodMinEleve,
                    propEleves.Propriete_Valeur_Int3 as rodMaxEleve


                    ,zapper_ZES, zapper_ze,
                    (select islock from GP_MANIFESTATION gpm where gpm.manifestation_id = event_Id) as is_lock
INTO #tmpTableGrilleTarif

FROM #tableresultgrilletarif gt
INNER JOIN manifestation m on m.manifestation_id = gt.event_Id
INNER JOIN seance sea on sea.seance_Id = gt.session_id
INNER JOIN lieu_configuration liconf on liconf.lieu_config_id = sea.lieu_config_id
INNER JOIN lieu l on l.lieu_id = liconf.lieu_ID

INNER JOIN gestion_place gp on gp.gestion_place_id = gt.gpid

LEFT JOIN offre_gestion_place ogp on ogp.gestion_place_id = gt.gpid
LEFT JOIN offre o on o.offre_id = ogp.offre_id
LEFT JOIN adhesion_catalog_offresliees aco on aco.offre_id = o.offre_id
LEFT JOIN Adhesion_Catalog adh on adh.Adhesion_Catalog_ID = aco.adhesion_catalog_id
LEFT JOIN AdhProps propMaitres
   ON propMaitres.Adhesion_Catalog_ID = adh.Adhesion_Catalog_ID 
   AND propMaitres.Propriete_Valeur_Int1 = gt.priceid
   AND propMaitres.Propriete_Code = 'TARIF_MAITRE'
LEFT JOIN AdhProps propEleves
   ON propEleves.Adhesion_Catalog_ID = adh.Adhesion_Catalog_ID 
   AND propEleves.Propriete_Valeur_Int1 = gt.priceid
   AND propEleves.Propriete_Code = 'TARIF_ELEVE'

WHERE event_Id not in(select manifestation_id from GP_manifestation where islock =1)
AND session_id not in(select seance_id from GP_seance where islock =1)

GROUP BY event_Id, m.manifestation_code, m.manifestation_nom,
		nepasafficherdate,
		l.lieu_id, l.lieu_nom,
		session_id, 
		sea.seance_date_deb, sea.seance_date_fin,		
		railingpriceid, 
		amountexcepttax, 
		charge, 
		tax, 
		discount, 
		commission, 
		totaltax,  
		totalamount, 
		ticketamount , 
		zoneid, 
		zonename, 
		sectionid, 
		sectionname, 
		floorname, 
		floorid, 
		gt.categ_Id, 
		categoryname, 
		categorycode, categorycolor, categdispSequence,
		priceid, 
		pricename, 
		nbseats, 
		nbseatsmin, 
		nbseatsmax, 
		nbhisto,
		bookingtype, 
		notnumbered, 
		priseauto,
		surplan,
		voirplace,
		adh.Mise_En_Avant,
		adh.Pref_Affichage,
		PriceDispSequence,
		o.offre_id, o.offre_nom,
		adh.Adhesion_Catalog_ID,
		adh.Catalog_Libelle,
        propMaitres.Propriete_Valeur_Int1,
        propMaitres.Propriete_Valeur_Int2,
        propMaitres.Propriete_Valeur_Int3,
                propEleves.Propriete_Valeur_Int1,
        propEleves.Propriete_Valeur_Int2,
        propEleves.Propriete_Valeur_Int3,

		zapper_ZES, zapper_ze	,
		zonedispSequence, floordispSequence,
		sectiondispSequence,
		gestion_place_parent_id

    ORDER  BY event_Id, 
              session_id, 
			  categdispSequence,
              categoryname, 
			  zonedispSequence,
			  floordispSequence, 
			  sectiondispSequence,
              prefAffichageAdhesion,
              priceDispSequence,
              isTarifMaitre,
              isTarifEleve

SELECT * FROM #tmpTableGrilleTarif WHERE nbseats > 0
ORDER  BY event_Id, 
              session_id, 
			  categdispSequence,
              categoryname, 
			  zonedispSequence,
			  floordispSequence, 
			  sectiondispSequence,
              prefAffichageAdhesion,
              PriceDispSequence,
              isTarifMaitre,
              isTarifEleve,
              pricename,
			  offerId 

/******* update max des gp parents des tarifs multipliés CAS-107985-W8H1F3  */ 			  
UPDATE t2
SET t2.nbseatmax = t1.min_nbseatmax
FROM #tableresultsession t2
JOIN (
    SELECT gpparentid, MIN(nbseatsmax) AS min_nbseatmax
    FROM #tmpTableGrilleTarif 
	inner join [gestion_place_multiplicateur_nbmax] m on m.type_tarif_id =#tmpTableGrilleTarif.priceid 
    GROUP BY gpparentid
) t1 ON t2.gestion_place_id = t1.gpparentid;	

              
SELECT distinct * 
FROM #tableresultsession 
ORDER by nbseatmax asc

DROP TABLE #myhisto 
DROP TABLE #Gpres
DROP TABLE #GpLight
DROP TABLE #mygp
DROP TABLE #tableresultgrilletarif 
DROP TABLE #tableresultsession 
DROP TABLE #tmpTableGrilleTarif 
