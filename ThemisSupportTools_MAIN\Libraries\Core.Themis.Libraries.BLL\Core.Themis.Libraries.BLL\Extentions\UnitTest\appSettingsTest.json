{
  "AllowedHosts": "*",
  "ApiSecretKey": "secret401b09eab3c013d4ca54922bb802bec8fd5318192b0a75f201d8b3727429090fb337591abd3e44453b954555b7a0812e1081c39b740293f765eae731f5a65ed1",
  "ConnectionStrings": {
    "WsAdminDB": "Server=*************;Database=WSAdmin_dev;Persist Security Info=True;User ID=SupportTest;Password=****************************************************************************************;MultipleActiveResultSets=true;TrustServerCertificate=True;",
    //  "WsAdminDB": "Server=************;Database=WSAdmin;Persist Security Info=True;User ID=SphereWeb;Password=************************************************************************************************************;MultipleActiveResultSets=true;TrustServerCertificate=True;",
    // "WsAdminDBTest": "Server=*************;Database=WSAdmin_test;Persist Security Info=True;User ID=SupportTest;Password=****************************************************************************************;MultipleActiveResultSets=true;TrustServerCertificate=True;",
    "GlobalOpinionDB": "Server=*************;Database=GlobalOpinions_DEV;Persist Security Info=True;User ID=SupportTest;Password=****************************************************************************************;MultipleActiveResultSets=true;TrustServerCertificate=True;",
    "WebLibraryDB": "Server=*************;Database=GlobalWebLibrary_TU;Persist Security Info=True;User ID=SupportTest;Password=****************************************************************************************;MultipleActiveResultSets=true;TrustServerCertificate=True;",
    "QueuingDB": "Server=*************;Database=WSAdmin_DEV;Persist Security Info=True;User ID=SupportTest;Password=****************************************************************************************;MultipleActiveResultSets=true;TrustServerCertificate=True;",
    "ThemisSupportTools": "Server=*************;Database=WSAdmin_TU;Persist Security Info=True;User ID=SupportTest;Password=****************************************************************************************;MultipleActiveResultSets=true;TrustServerCertificate=True;",
    "DEV": {
      "WsAdminDB": "Server=*************;Database=WSAdmin_test;Persist Security Info=True;User ID=SupportTest;Password=****************************************************************************************;MultipleActiveResultSets=true;TrustServerCertificate=True;"
    },
    "TEST": {
      "WsAdminDB": "Server=*************;Database=WSAdmin_test;Persist Security Info=True;User ID=SupportTest;Password=****************************************************************************************;MultipleActiveResultSets=true;TrustServerCertificate=True;"
    },
    "PROD": {
      "WsAdminDB": "Server=************;Database=WSAdmin;Persist Security Info=True;User ID=SphereWeb;Password=************************************************************************************************************;MultipleActiveResultSets=true;TrustServerCertificate=True;"
    },
    "Redis": "**************:6555,password=tS4pDJfVCN3Y0j5pvrdEBCpH0gXLZsZ/QCdN2zh6lJwoi+Va4eBUwtkZEUNYCcCl"
  },
  "physicalPathOfInfosCompJSON": "D:\\customerfiles\\DEV\\{structureId}\\infoComps.json",
  "physicalPathOfCountries": "d:\\customerfiles\\DEV\\{structureId}\\customer\\filesinfos\\[fileName]",

  "physicalPathOfSettingsCustomerJSON": "d:\\customerfiles\\DEV\\{structureId}\\customer\\APPSETINGS\\appsettings.json",
  "PathForSqlScript": "..\\..\\..\\..\\..\\..\\Libraries\\Core.Themis.Libraries.Data\\Core.Themis.Librairies.Data\\LIBRARIES_SQLSCRIPTS\\{0}\\{1}.sql",
  "jsonSettingsPath": "D:\\customerfiles\\DEV\\{0}\\appsettings.json",

  "configFileTransfers": "configTransfers.json",

  "ApiAuthenticationUrl": "http://**************/dev/api_authentication/v1/api/",

  "WsAdminConnectionCache": 300,
  "TypeRun": "TEST",
  "CryptoKey": "RodWebShop95",
  "CryptoKeyExterne": "Rodr1gue95sann",

  "ConfigIniPath": "\\\\Srv-paiement64\\customerfiles\\TEST\\[structureId]\\CONFIGSERVER\\config.ini.xml",
  "TSTConfigIniPath": "\\\\Srv-paiement64\\customerfiles\\{environment}\\{structureId}\\CONFIGSERVER\\config.ini.xml",
  "Cache": {
    //Cache pour la liste des sièges en secondes
    "SeatsAbsoluteExpiration": 120,
    "SeatsSlidingExpiration": 2,
    "SeatsTextAbsoluteExpiration": 600,
    "SeatsTextSlidingExpiration": 2,
    "AdhesionsAbsoluteExpiration": 120,
    "AdhesionsSlidingExpiration": 2
  },
  "CancelOrderCKeyIsNotMandatory": "0",
  "CGVFilesInfosPhysicalPath": "D:\\customerfiles\\DEV\\{0}\\customer\\filesinfos\\cgv{1}.htm",
  "CGVFilesInfosUrlPath": "https://dev.themisweb.fr/files/{0}/customer/filesinfos/cgv{1}.htm",
  "CustomerfilesIndivPhysicalPath": "d:\\customerfiles\\DEV\\{structureId}\\INDIV\\",
  "CustomerfilesUrlPath": "https://dev.themisweb.fr/files/{structureId}/INDIV/",
  "Images": {
    "PanoUrlPath": "https://dev.themisweb.fr/files/{structureId}/INDIV/images/pano/",
    "BaseImagesPhysicalPath": "D:\\CUSTOMERFILES\\DEV\\{structureId}\\INDIV\\IMAGES\\",
    "EventsImagesPhysicalPath": "D:\\CUSTOMERFILES\\DEV\\{structureId}\\INDIV\\IMAGES\\manifestations\\",
    "EventsImagesUrlPath": "https://dev.themisweb.fr/files/{structureId}/INDIV/images/manifestations/",
    "ProductsImagesPhysicalPath": "D:\\CUSTOMERFILES\\DEV\\{structureId}\\INDIV\\IMAGES\\produits\\",
    "ProductsImagesUrlPath": "https://dev.themisweb.fr/files/{structureId}/INDIV/images/produits/",
    "BaseImagesUrlPath": "https://dev.themisweb.fr/files/{structureId}/INDIV/images/",
    "AssetsImagesUrlPath": "https://dev.themisweb.fr/assets/IMAGES/indiv/"
  },
  "Logging": {
    "LogLevel": {
      "Default": "Information",
      "Microsoft": "Warning",
      "Microsoft.Hosting.Lifetime": "Information"
    }
  },
  "MTicket": {
    "Google": {
      "AccountFile": "\\\\**************\\d\\CERTIFICATS\\GoogleWallet\\Local\\wallet-369412-463ff125805b.json",
      "IssuerId": 3388000000022257621,
      "UrlPay": "https://pay.google.com/gp/v/save",
      "ListUrlOrigins": [
        "https://dev.themisweb.fr",
        "https://dev2.themisweb.fr",
        "https://development.rodrigue-ws.com"
      ]
    },
    "Apple": {
      "AccountFile": "\\\\**************\\d\\CERTIFICATS\\AppleWallet\\Local\\AppleCredentialParam.json",
      "IconPath": "\\\\**************\\sites\\assets\\IMAGES\\customer\\mticket\\apple\\Icons"
    },
    "Samsung": {
      "AccountFile": "\\\\**************\\d\\CERTIFICATS\\SamsungWallet\\Local\\SamsungCredentialParam.json",
      "PartnerId": *******************,
      "CardId": "3gl7tatkio500"
    }
  },
  "PathImagesLogos": "\\\\*************\\customerfiles\\TEST\\[structureId]\\paiement\\images\\logosMaquettes\\",
  "PathPdf": "\\\\*************\\emails\\TEST\\[structureId]\\pdf\\",
  "PathPdfFonts": "\\\\172.30.200.120\\customerfiles\\dev\\resources\\fonts\\",
  "PathPdfForViewMaquette": "D:\\Generation_test\\viewMaquette\\",
  "pdfFonts": [
    "Arial",
    "arialb",
    "ariali",
    "arialbi",
    "Arial Italic",
    "Arial Bold",
    "Arial Bold Italic",
    "Bell",
    "Verdana",
    "Verdanab",
    "Verdanai",
    "Verdanabi",
    "Verdana Bold",
    "Verdana Italic",
    "Verdana Bold Italic",
    "Comic sans MS",
    "Broadway Normal",
    "insatiable display condensed",
    "insatiable display compressed"
    //"Segoe UI"
  ],
  "physicalPathOfSettingsJSON": "D:\\customerfiles\\DEV\\{structureId}\\appsettings.json",
  "Sponsor": {
    "SponsorAdvantageCardSalt": "7f5a54f374604d362034ba883d9ac38dc4a8f00f8dc708138135d242457a4e49"
  },
  "TranslationsAreas": {
    "CrossSelling": [ "Global", "CrossSelling" ],
    "Session": [ "Global", "Session" ],
    "Basket": [ "Global", "Basket" ],
    "Product": [ "Global", "Product" ],
    "HomeModular": [ "Global", "HomeModular" ],
    "Insurance": [ "Global", "Insurance" ]
  },
  "CustomerFilesPath": "\\\\Srv-paiement64\\customerfiles\\{environment}",
  "ResourcesFilesOfPlatformsPath": "D:\\customerfiles\\{environment}\\Default\\{platform}\\RESOURCES",
  "PassCulture": {
    "ApiPath": "https://backend.integration.passculture.pro/",
    "ApiKey": "integration_a69103c22ef4_7f671668f2932be85a53830a0ad8f871d41e57b87543893acb0a083cc9fa462c"
  },
  "ApiExternalPath": "https://development.rodrigue-ws.com/v1/external/api/"
}
