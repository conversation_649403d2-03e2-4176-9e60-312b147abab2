﻿DECLARE @TYPEPLAN INT
SELECT @typePlan = lieu_physique_type_plan from seance s
INNER JOIN lieu_configuration lc on lc.lieu_config_id = s.lieu_config_id
INNER JOIN lieu_physique lp on lp.lieu_physique_id = lc.lieu_physique_id
WHERE seance_id = [sessionID]

SELECT type_siege, 
case when @typePlan=2 and type_siege='L£' then convert(int, pos_x * 19.0/18.0) else pos_x end as pos_x,
case when @typePlan=2 and type_siege='L£' then convert(int,pos_y * 19.0/18.0) else pos_y end as pos_y,
case when @typePlan=2 and type_siege='L£' then convert(int,decal_x * 19.0/18.0) else decal_x end as decal_x,
case when @typePlan=2 and type_siege='L£' then convert(int,decal_y * 19.0/18.0) else decal_y end as decal_y,
texte,
CASE when len(type_siege)=2 then
	case when type_siege = 'L¤' 
		then 'isPoteau' 
		else
			case when type_siege='L£' then 'isTrait' + convert(varchar(1), @typePlan) else 'isTexte'
		end
	end 
else
	case when SUBSTRING(type_siege,1,1)='£' then 'TexteLong' else '??' end
end	
as typeTEXTE,
case when len(type_siege)=2 then
	case when type_siege = 'L¤' 
	then '°°' 
	when type_siege='L£' then
		texte
	else
		SUBSTRING(type_siege,2,1)
	end 
else
	case when SUBSTRING(type_siege,1,1)='£' then 
		texte + '|' + type_siege else '' end 
end	as valTEXTE
,@typePlan as type_plan

FROM seance s 
INNER JOIN lieu_configuration lc ON lc.lieu_config_id = s.lieu_config_id
INNER JOIN lieu_physique lp ON lp.lieu_physique_id = lc.lieu_physique_id -- and s.lieu_id=rlp.lieu_id
INNER JOIN reference_lieu_physique rlp ON rlp.lieu_physique_id = lp.lieu_physique_id and s.lieu_id=rlp.lieu_id

AND seance_id= [sessionID]
AND type_siege<>'F' AND type_siege NOT LIKE 'S%' 
--and type_siege<>'L£'


ORDER BY iindex