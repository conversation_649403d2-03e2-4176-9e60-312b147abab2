﻿using Core.Themis.Libraries.DTO.Transactionnals;
using System.Collections.Generic;
using Microsoft.Data.SqlClient;

namespace Core.Themis.Libraries.BLL.Transactionnals.Interfaces
{
    public interface ICompteClientManager
    {
        /// <summary>
        /// INSERT INTO Compte_Client
        /// </summary>
        /// <param name="structureId"></param>
        /// <param name="cc"></param>
        /// <returns></returns>
        int Create(SqlConnection cnxOpen, int structureId, CompteClientLineDTO cc);

        int CreateCompteClient2(SqlConnection cnxOpen, int structureId, int posteId, string poste, long cc_operationId);

        /// <summary>
        /// INSERT INTO compte_transaction
        /// </summary>
        /// <returns></returns>
        int CreateCompteTransaction(SqlConnection cnxOpen, int structureId, int OperatorId, long operation1_id, long operation2_id, string typeOperation);

        List<CompteClientLineDTO> Get(int structureId, int identiteId, int orderId);

        public int GetNumPaiement(int structureId);

        public long GetCompteurOperationId(int structureId);
    }
}
