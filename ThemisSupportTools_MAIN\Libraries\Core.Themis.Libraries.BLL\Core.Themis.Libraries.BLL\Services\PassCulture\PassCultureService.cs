﻿using Core.Themis.Libraries.BLL.Interfaces;
using Core.Themis.Libraries.BLL.Services.Interfaces;
using Core.Themis.Libraries.BLL.Services.PassCulture.Interfaces;
using Core.Themis.Libraries.Data.Entities.Open.Partner;
using Core.Themis.Libraries.Data.Entities.WebTracing;
using Core.Themis.Libraries.Data.Repositories.Open.GestionPlaces.Interfaces;
using Core.Themis.Libraries.Data.Repositories.Open.Lieu.Interfaces;
using Core.Themis.Libraries.Data.Repositories.Open.Orders.Interfaces;
using Core.Themis.Libraries.Data.Repositories.Open.Partner.Interfaces;
using Core.Themis.Libraries.Data.Repositories.WebTracing.Interfaces;
using Core.Themis.Libraries.DTO.Enums;
using Core.Themis.Libraries.DTO.Identity;
using Core.Themis.Libraries.DTO.Lookup;
using Core.Themis.Libraries.DTO.Orders;
using Core.Themis.Libraries.DTO.PassCulture;
using Core.Themis.Libraries.DTO.PassCulture.ApiExchange;
using Core.Themis.Libraries.DTO.PassCulture.ApiExchange.Response;
using Core.Themis.Libraries.DTO.PassCulture.Error;
using Core.Themis.Libraries.DTO.PassCulture.Forms;
using Core.Themis.Libraries.DTO.WTObjects;
using Core.Themis.Libraries.Utilities.API.Interfaces;
using Core.Themis.Libraries.Utilities.Extensions;
using Core.Themis.Libraries.Utilities.Logging;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.Configuration;
using Newtonsoft.Json;
using RestSharp;
using RestSharp.Authenticators.OAuth2;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Core.Themis.Libraries.BLL.Services.PassCulture
{
    public class PassCultureService : IPassCultureService
    {
        private readonly string _passCultureApiPath;
        private readonly string _apiKey;
        private readonly string _apiExternalPath;

        private readonly IMemoryCache _memoryCache;
        private readonly IRevenderOrderManagementService _revenderOrderManagementService;
        private readonly IOpenPartnerRepository _openPartnerRepository;
        private readonly IPartnerIdsProviderRepository _partnerIdsProviderRepository;
        private readonly IGestionPlaceRepository _gestionPlaceRepository;
        private readonly IOrderRepository _orderRepository;
        private readonly IBasketRepository _basketRepository;
        private readonly IOrderManager _orderManager;
        private readonly ILieuRepository _lieuRepository;

        private static readonly RodrigueNLogger Logger = new();
        private readonly IApiRateLimiter<PassCultureService> _rateLimiter;


        public PassCultureService(
            IConfiguration config, 
            IMemoryCache memoryCache,
            IRevenderOrderManagementService revenderOrderManagementService,
            IBasketRepository basketRepository,
            IOrderManager orderManager,
            IOpenPartnerRepository openPartnerRepository,
            IPartnerIdsProviderRepository partnerIdsProviderRepository,
            IGestionPlaceRepository gestionPlaceRepository,
            IOrderRepository orderRepository,
            ILieuRepository lieuRepository,
            IApiRateLimiter<PassCultureService> rateLimiter)
        {
            _apiKey = config["PassCulture:ApiKey"]!;
            _apiExternalPath = config["ApiExternalPath"]!;
            _memoryCache = memoryCache;
            _revenderOrderManagementService = revenderOrderManagementService;
            _basketRepository = basketRepository;
            _orderManager = orderManager;
            _openPartnerRepository = openPartnerRepository;
            _partnerIdsProviderRepository = partnerIdsProviderRepository;
            _gestionPlaceRepository = gestionPlaceRepository;
            _orderRepository = orderRepository;
            _lieuRepository = lieuRepository;
            _rateLimiter = rateLimiter;
        }

        public int Limit { get; } = 50;

        #region Settings

        public async Task<PassCultureResponse<bool>> SaveSettingsAsync(int structureId, int buyerProfilId, int passCultureVenueId)
        {
            try
            {
                SavePassCultureVenueIdsProvider(structureId, passCultureVenueId.ToString());

                await SetPartnerBuyerProfilAsync(structureId, buyerProfilId);

                await SetStructureProviderAsync(structureId, passCultureVenueId);

                await SetDefaultRodrigueAddressInPassCulture(structureId);

                return new()
                {
                    Success = true,
                    Result = true,
                };
            }
            catch (Exception ex) 
            {
                return new()
                {
                    Success = false,
                    ErrorMessage = ex.Message,
                };
            }
        }

        public async Task<PassCultureSettingsForm> GetSettingsFormAsync(int structureId)
        {
            Venue? venue = null;

            int? venueId = GetVenueIdByStructureId(structureId);

            if (venueId is not null)
                venue = await GetVenueByIdAsync(structureId, venueId.Value);

            int buyerProfilId = _openPartnerRepository.GetById(structureId, (int)PartnerType.PassCulture)?.ProfilAcheteurId ?? 0;

            return new PassCultureSettingsForm() 
            { 
                Siret = venue?.Siret,
                VenueId = venue?.Id ?? 0,
                BuyerProfilId = buyerProfilId,
                BookingUrl = venue?.BookingUrl,
                CancellingUrl = venue?.CancelUrl
            };
        }

        public async Task<SettingsErrorsDetail> GetSettingsDetails(int structureId)
        {
            PassCultureSettingsForm settings = await GetSettingsFormAsync(structureId);

            SettingsErrorsDetail details = new()
            {
                BuyerProfilIsSet = !(settings.BuyerProfilId == 0),
                VenueIdIsSet = !(settings.VenueId == 0),
                ProviderUrlsIsSet = !(string.IsNullOrWhiteSpace(settings.BookingUrl) || string.IsNullOrWhiteSpace(settings.CancellingUrl))
            };

            return details;
        }

        private async Task SetStructureProviderAsync(int structureId, int venueId)
        {
            Provider provider = new()
            {
                BookingUrl = $"{_apiExternalPath}PassCulture/{structureId}/Booking",
                CancelUrl = $"{_apiExternalPath}PassCulture/{structureId}/Cancellation"
            };

            await UpdateVenueExternalUrlsAsync(structureId, venueId, provider);
        }

        private async Task SetPartnerBuyerProfilAsync(int structureId, int buyerProfilId)
        {
            OpenPartnerEntity partnerEntity = await _openPartnerRepository.GetByIdAsync(structureId, (int)PartnerType.PassCulture)
                ?? throw new NullReferenceException($"Pass culture partner not fount in bdd of the structure '{structureId}'");

            partnerEntity.ProfilAcheteurId = buyerProfilId;

            await _openPartnerRepository.UpdateAsync(structureId, partnerEntity);
        }

        private async Task SetDefaultRodrigueAddressInPassCulture(int structureId)
        {
            var venueAddressId = await GetVenueAddressIdAsync(structureId);

            if (venueAddressId is not null)
                SavePassCultureAddressIdsProvider(structureId, venueAddressId.Value.ToString());

            await SetAddressPassCultureByLieuRodrigue(structureId);
        }

        private async Task SetAddressPassCultureByLieuRodrigue(int structureId)
        {
            var listLieu = await _lieuRepository.GetAllAsync(structureId) ?? [];

            foreach (var lieu in listLieu)
            {
                List<string> addresses = [lieu.lieuRue1, lieu.lieuRue2, lieu.lieuRue3, lieu.lieuRue4];

                string? street = addresses.FirstOrDefault(a => !string.IsNullOrWhiteSpace(a));

                if (street is null) continue;

                var city = lieu.lieuVille;

                if (string.IsNullOrWhiteSpace(city)) continue;

                var postalCode = lieu.lieuCp;

                if (string.IsNullOrWhiteSpace(postalCode) || !postalCode.IsValidPostalCodeFr()) continue;

                await TrySetAddressAsync(structureId, street, city, postalCode);
            }
        }

        private void SavePassCultureVenueIdsProvider(int structureId, string venueId)
        {
            PartnerIdsProviderEntity? venueIdsProvider = _partnerIdsProviderRepository.FindFirstOrDefault(p => p.IdProviderValue == venueId
                                                                                                            && p.RodrigueId == structureId
                                                                                                            && p.PartnerIdsProviderTypeId == (int)PartnerIdsProviderType.PassCulture_Venue, structureId);

            if(venueIdsProvider is not null)
            {
                venueIdsProvider.IdProviderValue = venueId;

                _partnerIdsProviderRepository.Update(structureId, venueIdsProvider);
            }
            else
            {
                venueIdsProvider = new()
                {
                    IdProviderValue = venueId,
                    PartnerIdsProviderTypeId = (int)PartnerIdsProviderType.PassCulture_Venue,
                    PartnerTableRodrigueTypeId = (int)TableRodrigueType.Structure,
                    RodrigueId = structureId,
                };

                _partnerIdsProviderRepository.Insert(structureId, venueIdsProvider);
            }
        }

        #endregion

        #region Providers

        public async Task<Provider?> GetProviderAsync(int structureId)
        {
            try
            {
                var request = new RestRequest("public/providers/v1/provider", Method.Get)
                {
                    Authenticator = new OAuth2AuthorizationRequestHeaderAuthenticator(_apiKey, "Bearer")
                };

                RestResponse reponse = await _rateLimiter.CallApiWithRateLimitAsync(request);

                if (!reponse.IsSuccessful)
                    throw new Exception($"{reponse.ErrorMessage} {reponse.Content}");

                return JsonConvert.DeserializeObject<Provider>(reponse.Content!);
            }
            catch (Exception ex)
            {
                throw new Exception(ex.Message);
            }
        }

        public async Task<Provider?> UpdateProviderAsync(int structureId, Provider provider)
        {
            try
            {
                var request = new RestRequest("public/providers/v1/provider", Method.Patch)
                {
                    RequestFormat = DataFormat.Json,
                    Authenticator = new OAuth2AuthorizationRequestHeaderAuthenticator(_apiKey, "Bearer")
                };

                string json = JsonConvert.SerializeObject(provider);

                request.AddBody(json);

                RestResponse reponse = await _rateLimiter.CallApiWithRateLimitAsync(request);

                if (!reponse.IsSuccessful)
                    throw new Exception($"{reponse.ErrorMessage} {reponse.Content}");

                return JsonConvert.DeserializeObject<Provider>(reponse.Content!);
            }
            catch (Exception ex)
            {
                throw new Exception(ex.Message);
            }
        }

        public async Task<bool> UpdateVenueExternalUrlsAsync(int structureId, int venueId, Provider provider)
        {
            try
            {
                var request = new RestRequest($"public/providers/v1/venues/{venueId}", Method.Patch)
                {
                    RequestFormat = DataFormat.Json,
                    Authenticator = new OAuth2AuthorizationRequestHeaderAuthenticator(_apiKey, "Bearer")
                };

                string json = JsonConvert.SerializeObject(provider);

                request.AddBody(json);

                RestResponse reponse = await _rateLimiter.CallApiWithRateLimitAsync(request);

                return reponse.IsSuccessful;
            }
            catch (Exception ex)
            {
                throw new Exception(ex.Message);
            }
        }

        #endregion

        #region Venues

        public async Task<List<VenuesInfo>> GetVenuesAsync(int structureId, string? siren = null)
        {
            try
            {
                var cacheKey = $"PassCulture_GetVenuesAsync_{structureId}_{siren}";

                var cacheResult = _memoryCache.Get<List<VenuesInfo>>(cacheKey);

                if(cacheResult is not null )
                    return cacheResult;

                var request = new RestRequest("public/offers/v1/offerer_venues", Method.Get)
                {
                    Authenticator = new OAuth2AuthorizationRequestHeaderAuthenticator(_apiKey, "Bearer")
                };

                if (siren is not null)
                    request.AddParameter("siren", siren);

                RestResponse reponse = await _rateLimiter.CallApiWithRateLimitAsync(request);

                if (!reponse.IsSuccessful)
                    throw new Exception($"{reponse.ErrorMessage} {reponse.Content}");

                var venuesInfos = JsonConvert.DeserializeObject<List<VenuesInfo>>(reponse.Content!);

                if(venuesInfos is not null )
                    _memoryCache.Set(cacheKey, venuesInfos);

                return venuesInfos ?? [];
            }
            catch (Exception ex)
            {
                throw new Exception(ex.Message);
            }
        }

        public async Task<Venue?> GetVenueBySiretAsync(int structureId, string siret)
        {
            try
            {
                var cacheKey = $"PassCulture_GetVenueBySiretAsync_{structureId}_{siret}";

                var cacheResult = _memoryCache.Get<Venue>(cacheKey);

                if (cacheResult is not null)
                    return cacheResult;

                var request = new RestRequest($"public/offers/v1/venues/{siret}", Method.Get)
                {
                    Authenticator = new OAuth2AuthorizationRequestHeaderAuthenticator(_apiKey, "Bearer")
                };

                RestResponse reponse = await _rateLimiter.CallApiWithRateLimitAsync(request);

                if (!reponse.IsSuccessful)
                    return null;

                var venue = JsonConvert.DeserializeObject<Venue>(reponse.Content!);

                if (venue is not null)
                    _memoryCache.Set(cacheKey, venue);

                return venue;
            }
            catch (Exception ex)
            {
                throw new Exception(ex.Message);
            }
        }

        public async Task<Venue?> GetVenueByIdAsync(int structureId, int venueId)
        {
            try
            {
                var cacheKey = $"PassCulture_GetVenueByIdAsync_{structureId}_{venueId}";

                var cacheResult = _memoryCache.Get<Venue>(cacheKey);

                if (cacheResult is not null)
                    return cacheResult;

                var venuesList = await GetVenuesAsync(structureId);

                var venues = venuesList.SelectMany(l => l.Venues);

                var venue = venues.FirstOrDefault(v => v.Id == venueId);

                if (venue is not null)
                    _memoryCache.Set(cacheKey, venue);

                return venue;
            }
            catch (Exception ex)
            {
                throw new Exception(ex.Message);
            }
        }

        public async Task<List<SelectLookup>> SelectLookupVenuesAsync(int structureId, string? siren = null)
        {
            var venuesInfo = await GetVenuesAsync(structureId, siren);

            return venuesInfo.SelectMany(i => i.Venues)
                             .Select(v => new SelectLookup()
                             {
                                 Value = v.Id.ToString(),
                                 Libelle = string.IsNullOrWhiteSpace(v.PublicName) ? v.LegalName : v.PublicName,
                             })
                             .ToList();
        }

        public int? GetVenueIdByStructureId(int structureId)
        {
            string? providerId = _partnerIdsProviderRepository.GetProviderIdByIdRodrigue(structureId, structureId, PartnerIdsProviderType.PassCulture_Venue);

            if (!string.IsNullOrWhiteSpace(providerId))
                return int.Parse(providerId);

            return null;
        }

        #endregion

        #region Addresses

        public async Task<AddressConsultation> GetAddressAsync(int structureId, int addressId)
        {
            try
            {
                string cacheKey = $"PassCulture_GetAddressAsync_{structureId}_{addressId}";

                var cacheResult = _memoryCache.Get<AddressConsultation>(cacheKey);

                if (cacheResult is not null)
                    return cacheResult;

                var request = new RestRequest($"public/offers/v1/addresses/{addressId}", Method.Get)
                {
                    Authenticator = new OAuth2AuthorizationRequestHeaderAuthenticator(_apiKey, "Bearer"),
                };

                RestResponse reponse = await _rateLimiter.CallApiWithRateLimitAsync(request);

                if (!reponse.IsSuccessful)
                    throw new Exception($"{reponse.ErrorMessage} {reponse.Content}");

                AddressConsultation address = JsonConvert.DeserializeObject<AddressConsultation>(reponse.Content!)!;

                _memoryCache.Set(cacheKey, address);

                return address;
            }
            catch (Exception ex)
            {
                throw new Exception(ex.Message);
            }
        }

        public async Task<PassCultureResponse<AddressConsultation>> SearchAddressesAsync(int structureId, string street, string city, string postalCode, double? latitude = null, double? longitude = null)
        {
            try
            {
                string cacheKey = $"PassCulture_SearchAddressesAsync_{structureId}_{street}_{city}_{postalCode}_{latitude}_{longitude}";

                var cacheResult = _memoryCache.Get<PassCultureResponse<AddressConsultation>>(cacheKey);

                if (cacheResult is not null)
                    return cacheResult;

                var request = new RestRequest($"public/offers/v1/addresses/search", Method.Get)
                {
                    Authenticator = new OAuth2AuthorizationRequestHeaderAuthenticator(_apiKey, "Bearer"),
                };

                request.AddQueryParameter("street", street);
                request.AddQueryParameter("city", city);
                request.AddQueryParameter("postalCode", postalCode);

                if (latitude is not null)
                    request.AddQueryParameter("latitude", latitude.ToString());

                if (longitude is not null)
                    request.AddQueryParameter("longitude", longitude.ToString());

                RestResponse reponse = await _rateLimiter.CallApiWithRateLimitAsync(request);

                AddressConsultation? content = null;

                if (reponse.IsSuccessful)
                    content = JsonConvert.DeserializeObject<AddressesList>(reponse.Content!).Addresses.FirstOrDefault();

                var result = new PassCultureResponse<AddressConsultation>()
                {
                    Success = reponse.IsSuccessful,
                    ErrorMessage = !reponse.IsSuccessful ? reponse.Content : null,
                    Result = reponse.IsSuccessful ? content : null,
                };

                _memoryCache.Set(cacheKey, result);

                return result;
            }
            catch (Exception ex)
            {
                throw new Exception(ex.Message);
            }
        }

        public async Task<PassCultureResponse<AddressConsultation>> CreateAddressAsync(int structureId, AddressCreation address)
        {
            try
            {
                var request = new RestRequest("public/offers/v1/addresses", Method.Post)
                {
                    RequestFormat = DataFormat.Json,
                    Authenticator = new OAuth2AuthorizationRequestHeaderAuthenticator(_apiKey, "Bearer")
                };

                string json = JsonConvert.SerializeObject(address);

                request.AddBody(json);

                RestResponse reponse = await _rateLimiter.CallApiWithRateLimitAsync(request);

                AddressConsultation? resultAddress = null;

                if (reponse.IsSuccessful)
                {
                    resultAddress = JsonConvert.DeserializeObject<AddressConsultation>(reponse.Content!);

                    if (resultAddress is not null)
                        SavePassCultureAddressIdsProvider(structureId, resultAddress.Id.ToString());

                    _memoryCache.RemoveKeysContainPartOfKey($"PassCulture_SearchAddressesAsync_{structureId}");
                }

                return new()
                {
                    Success = reponse.IsSuccessful,
                    ErrorMessage = !reponse.IsSuccessful ? reponse.Content : null,
                    Result = resultAddress,
                };
            }
            catch (Exception ex)
            {
                throw new Exception(ex.Message);
            }
        }

        public async Task<PassCultureResponse<AddressConsultation>> TrySetAddressAsync(int structureId, string street, string city, string postalCode)
        {
            var addressResult = await SearchAddressesAsync(structureId, street, city, postalCode);

            if (addressResult.Result is null)
            {
                return await CreateAddressAsync(structureId, new()
                {
                    City = city,
                    PostalCode = postalCode,
                    Street = street,
                });
            }
            else
            {
                SavePassCultureAddressIdsProvider(structureId, addressResult.Result.Id.ToString());
                return addressResult;
            }
        }

        public async Task<int?> GetVenueAddressIdAsync(int structureId)
        {
            var venueId = GetVenueIdByStructureId(structureId);

            if (venueId is not null)
            {
                var venue = await GetVenueByIdAsync(structureId, venueId.Value);

                if (venue is not null)
                {
                    var venueAdress = await SearchAddressesAsync(structureId, venue.Location.Address, venue.Location.City, venue.Location.PostalCode);
                    return venueAdress?.Result?.Id;
                }
            }

            return null;
        }

        public async Task<List<SelectLookup>> SelectLookupAddressAsync(int structureId)
        {
            List<SelectLookup> selectLookups = [];

            var idProviders = await _partnerIdsProviderRepository.FindByAsync(ip => ip.PartnerIdsProviderTypeId == (int)PartnerIdsProviderType.PassCulture_Address, structureId) ?? [];

            foreach (var idProvider in idProviders) 
            {
                int addressId = int.Parse(idProvider.IdProviderValue);
                var address = await GetAddressAsync(structureId, addressId);

                if (address is not null)
                    selectLookups.Add(new()
                    {
                        Value = idProvider.IdProviderValue,
                        Libelle = $"{address.Id} - {address.Street}, {address.PostalCode} {address.City}",
                    });
            }

            return selectLookups;
        }

        private void SavePassCultureAddressIdsProvider(int structureId, string addressId)
        {
            PartnerIdsProviderEntity? addressIdsProvider = _partnerIdsProviderRepository.FindFirstOrDefault(p => p.IdProviderValue == addressId
                                                                                                            && p.PartnerIdsProviderTypeId == (int)PartnerIdsProviderType.PassCulture_Address, structureId);

            if(addressIdsProvider == null)
            {
                _partnerIdsProviderRepository.Insert(structureId, new()
                {
                    IdProviderValue = addressId,
                    PartnerIdsProviderTypeId = (int)PartnerIdsProviderType.PassCulture_Address,
                });
            }
        }

        #endregion

        #region Event offer

        public async Task<List<EventOffer>> GetEventsAsync(int structureId, int venueId, string? idsAtProvider = null, int? limit = null, int? firstIndex = null)
        {
            try
            {
                string cacheKey = $"PassCulture_GetEventsAsync_{structureId}_{venueId}_{idsAtProvider}_{limit}_{firstIndex}";

                var cacheResult = _memoryCache.Get<List<EventOffer>>(cacheKey);

                if (cacheResult is not null)
                    return cacheResult;

                var request = new RestRequest($"public/offers/v1/events", Method.Get)
                {
                    Authenticator = new OAuth2AuthorizationRequestHeaderAuthenticator(_apiKey, "Bearer"),
                };

                request.AddQueryParameter("venueId", venueId);

                if (idsAtProvider is not null)
                    request.AddQueryParameter("idsAtProvider", idsAtProvider);

                if (limit is not null)
                    request.AddQueryParameter("limit", limit.ToString());

                if (firstIndex is not null)
                    request.AddQueryParameter("firstIndex", firstIndex.ToString());

                RestResponse reponse = await _rateLimiter.CallApiWithRateLimitAsync(request);

                if (!reponse.IsSuccessful)
                    throw new Exception($"{reponse.ErrorMessage} {reponse.Content}");

                var datas = JsonConvert.DeserializeObject<EventsList>(reponse.Content!);

                _memoryCache.Set(cacheKey, datas.Events);

                return datas.Events;
            }
            catch (Exception ex)
            {
                throw new Exception(ex.Message);
            }
        }

        public async Task<EventOffer?> GetEventByIdAsync(int structureId, int passCultureEventId)
        {
            try
            {
                string cacheKey = $"PassCulture_GetEventByIdAsync_{structureId}_{passCultureEventId}";

                var cacheResult = _memoryCache.Get<EventOffer>(cacheKey);

                if(cacheResult is not null)
                    return cacheResult;

                var request = new RestRequest($"public/offers/v1/events/{passCultureEventId}", Method.Get)
                {
                    Authenticator = new OAuth2AuthorizationRequestHeaderAuthenticator(_apiKey, "Bearer"),
                };

                RestResponse reponse = await _rateLimiter.CallApiWithRateLimitAsync(request);

                if (!reponse.IsSuccessful)
                    throw new Exception($"{reponse.ErrorMessage} {reponse.Content}");

                var eventOffer = JsonConvert.DeserializeObject<EventOffer>(reponse.Content!);

                if(eventOffer is not null) 
                    _memoryCache.Set(cacheKey, eventOffer);

                return eventOffer;
            }
            catch (Exception ex)
            {
                throw new Exception(ex.Message);
            }
        }

        public async Task<EventOffer?> CreateEventOfferAsync(int structureId, EventOffer eventOffer)
        {
            try
            {
                var request = new RestRequest("public/offers/v1/events", Method.Post)
                {
                    RequestFormat = DataFormat.Json,
                    Authenticator = new OAuth2AuthorizationRequestHeaderAuthenticator(_apiKey, "Bearer")
                };

                string json = JsonConvert.SerializeObject(eventOffer);

                request.AddBody(json);

                RestResponse reponse = await _rateLimiter.CallApiWithRateLimitAsync(request);

                if (!reponse.IsSuccessful)
                    throw new Exception($"{reponse.ErrorMessage} {reponse.Content}");

                return JsonConvert.DeserializeObject<EventOffer>(reponse.Content!);
            }
            catch (Exception ex)
            {
                throw new Exception(ex.Message);
            }
        }

        public async Task<EventOffer?> UpdateEventOfferAsync(int structureId, int passCultureEventId, EventOfferUpdate eventOffer)
        {
            try
            {
                var request = new RestRequest($"public/offers/v1/events/{passCultureEventId}", Method.Patch)
                {
                    RequestFormat = DataFormat.Json,
                    Authenticator = new OAuth2AuthorizationRequestHeaderAuthenticator(_apiKey, "Bearer")
                };

                string json = JsonConvert.SerializeObject(eventOffer);

                request.AddBody(json);

                var reponse = await _rateLimiter.CallApiWithRateLimitAsync(request);

                if (!reponse.IsSuccessful)
                    throw new Exception($"{reponse.ErrorMessage} {reponse.Content}");

                RemoveIndividualOfferCache(structureId, passCultureEventId);

                return JsonConvert.DeserializeObject<EventOffer>(reponse.Content!);
            }
            catch (Exception ex)
            {
                throw new Exception(ex.Message);
            }
        }

        #endregion

        #region Event offer Price 

        public async Task<List<PriceCategory>> CreatePriceCategoriesAsync(int structureId, int passCultureEventId, List<PriceCategory> priceCategories)
        {
            try
            {
                var request = new RestRequest($"public/offers/v1/events/{passCultureEventId}/price_categories", Method.Post)
                {
                    RequestFormat = DataFormat.Json,
                    Authenticator = new OAuth2AuthorizationRequestHeaderAuthenticator(_apiKey, "Bearer")
                };

                string json = JsonConvert.SerializeObject(new PriceCategoriesList() { PriceCategories = priceCategories });

                request.AddBody(json);

                RestResponse reponse = await _rateLimiter.CallApiWithRateLimitAsync(request);

                if (!reponse.IsSuccessful)
                    throw new Exception($"{reponse.ErrorMessage} {reponse.Content}");

                var datas = JsonConvert.DeserializeObject<PriceCategoriesList>(reponse.Content!);

                return datas.PriceCategories;
            }
            catch (Exception ex)
            {
                throw new Exception(ex.Message);
            }
        }

        public async Task<PriceCategory?> UpdatePriceCategoryAsync(int structureId, int passCultureEventId, int passCulturePriceId, PriceCategory priceCategory)
        {
            try
            {
                var request = new RestRequest($"public/offers/v1/events/{passCultureEventId}/price_categories/{passCulturePriceId}", Method.Patch)
                {
                    RequestFormat = DataFormat.Json,
                    Authenticator = new OAuth2AuthorizationRequestHeaderAuthenticator(_apiKey, "Bearer")
                };

                string json = JsonConvert.SerializeObject(priceCategory);

                request.AddBody(json);

                var reponse = await _rateLimiter.CallApiWithRateLimitAsync(request);

                if (!reponse.IsSuccessful)
                    throw new Exception($"{reponse.ErrorMessage} {reponse.Content}");

                RemoveIndividualOfferCache(structureId, passCultureEventId);

                return JsonConvert.DeserializeObject<PriceCategory>(reponse.Content!); ;
            }
            catch (Exception ex)
            {
                throw new Exception(ex.Message);
            }
        }

        #endregion

        #region Event offer Stocks

        public async Task<List<Date>> GetEventStocksByIdEventAsync(int structureId, int passCultureEventId, int? limit = null, int? firstIndex = null, string? idsAtProvider = null)
        {
            try
            {
                string cacheKey = $"PassCulture_GetEventStocksByIdEventAsync_{structureId}_{passCultureEventId}_{limit}_{firstIndex}_{idsAtProvider}";

                var cacheResult = _memoryCache.Get<List<Date>>(cacheKey);

                if (cacheResult is not null)
                    return cacheResult;

                var request = new RestRequest($"public/offers/v1/events/{passCultureEventId}/dates", Method.Get)
                {
                    Authenticator = new OAuth2AuthorizationRequestHeaderAuthenticator(_apiKey, "Bearer"),
                };

                if (limit is not null)
                    request.AddQueryParameter("limit", limit.ToString());

                if (firstIndex is not null)
                    request.AddQueryParameter("firstIndex", firstIndex.ToString());

                if (!string.IsNullOrWhiteSpace(idsAtProvider))
                    request.AddQueryParameter("idsAtProvider", idsAtProvider);

                RestResponse reponse = await _rateLimiter.CallApiWithRateLimitAsync(request);

                if (!reponse.IsSuccessful)
                    throw new Exception($"{reponse.ErrorMessage} {reponse.Content}");

                var datas = JsonConvert.DeserializeObject<DatesList>(reponse.Content!);

                _memoryCache.Set(cacheKey, datas.Dates);

                return datas.Dates;
            }
            catch (Exception ex)
            {
                throw new Exception(ex.Message);
            }
        }

        public async Task<List<Date>> AddStocksToAnEventAsync(int structureId, int passCultureEventId, List<Date> dates)
        {
            try
            {
                var request = new RestRequest($"public/offers/v1/events/{passCultureEventId}/dates", Method.Post)
                {
                    RequestFormat = DataFormat.Json,
                    Authenticator = new OAuth2AuthorizationRequestHeaderAuthenticator(_apiKey, "Bearer")
                };

                string json = JsonConvert.SerializeObject(new DatesList() { Dates = dates });

                request.AddBody(json);

                RestResponse reponse = await _rateLimiter.CallApiWithRateLimitAsync(request);

                if (!reponse.IsSuccessful)
                    throw new Exception($"{reponse.ErrorMessage} {reponse.Content}");

                RemoveIndividualOfferCache(structureId, passCultureEventId);

                var datas = JsonConvert.DeserializeObject<DatesList>(reponse.Content!);

                return datas.Dates;
            }
            catch (Exception ex)
            {
                throw new Exception(ex.Message);
            }
        }

        public async Task<Date?> UpdateEventStockAsync(int structureId, int passCultureEventId, int passCultureStockId, DateForUpdate date)
        {
            try
            {
                var request = new RestRequest($"public/offers/v1/events/{passCultureEventId}/dates/{passCultureStockId}", Method.Patch)
                {
                    RequestFormat = DataFormat.Json,
                    Authenticator = new OAuth2AuthorizationRequestHeaderAuthenticator(_apiKey, "Bearer")
                };

                string json = JsonConvert.SerializeObject(date);

                request.AddBody(json);

                var reponse = await _rateLimiter.CallApiWithRateLimitAsync(request);

                if (!reponse.IsSuccessful)
                    throw new Exception($"{reponse.ErrorMessage} {reponse.Content}");

                RemoveIndividualOfferCache(structureId, passCultureEventId);

                return JsonConvert.DeserializeObject<Date>(reponse.Content!);
            }
            catch (Exception ex)
            {
                throw new Exception(ex.Message);
            }
        }

        public async Task<bool> DeleteEventStockAsync(int structureId, int passCultureEventId, int passCultureStockId)
        {
            try
            {
                var request = new RestRequest($"public/offers/v1/events/{passCultureEventId}/dates/{passCultureStockId}", Method.Delete)
                {
                    RequestFormat = DataFormat.Json,
                    Authenticator = new OAuth2AuthorizationRequestHeaderAuthenticator(_apiKey, "Bearer")
                };

                var reponse = await _rateLimiter.CallApiWithRateLimitAsync(request);

                if (!reponse.IsSuccessful)
                    throw new Exception($"{reponse.ErrorMessage} {reponse.Content}");

                RemoveIndividualOfferCache(structureId, passCultureEventId);

                return reponse.IsSuccessful;
            }
            catch (Exception ex)
            {
                throw new Exception(ex.Message);
            }
        }

        #endregion

        #region Offer attributes

        public async Task<List<EventCategory>> GetEventCategoriesAsync(int structureId)
        {
            try
            {
                string cacheKey = $"PassCulture_GetEventCategoriesAsync";

                var cacheResult = _memoryCache.Get<List<EventCategory>>(cacheKey);

                if (cacheResult is not null)
                    return cacheResult;

                var request = new RestRequest("public/offers/v1/events/categories", Method.Get)
                {
                    Authenticator = new OAuth2AuthorizationRequestHeaderAuthenticator(_apiKey, "Bearer")
                };

                RestResponse reponse = await _rateLimiter.CallApiWithRateLimitAsync(request);

                if (!reponse.IsSuccessful)
                    throw new Exception($"{reponse.ErrorMessage} {reponse.Content}");

                var categories = JsonConvert.DeserializeObject<List<EventCategory>>(reponse.Content!);

                if (categories is not null)
                    _memoryCache.Set(cacheKey, categories, TimeSpan.FromMinutes(2));

                return categories ?? new();
            }
            catch (Exception ex)
            {
                throw new Exception(ex.Message);
            }
        }

        public async Task<List<PassCultureType>> GetAllMusicTypesAsync(int structureId)
        {
            try
            {
                string cacheKey = "PassCulture_GetAllMusicTypesAsync";

                var cacheResult = _memoryCache.Get<List<PassCultureType>>(cacheKey);

                if (cacheResult is not null)
                    return cacheResult;

                var request = new RestRequest("public/offers/v1/music_types/all", Method.Get)
                {
                    Authenticator = new OAuth2AuthorizationRequestHeaderAuthenticator(_apiKey, "Bearer")
                };

                RestResponse reponse = await _rateLimiter.CallApiWithRateLimitAsync(request);

                if (!reponse.IsSuccessful)
                    throw new Exception($"{reponse.ErrorMessage} {reponse.Content}");

                var types = JsonConvert.DeserializeObject<List<PassCultureType>>(reponse.Content!);

                if(types is not null)
                    _memoryCache.Set(cacheKey, types, TimeSpan.FromMinutes(2));

                return types ?? new();
            }
            catch (Exception ex)
            {
                throw new Exception(ex.Message);
            }
        }

        public async Task<List<PassCultureType>> GetEventsMusicTypesAsync(int structureId)
        {
            try
            {
                string cacheKey = "PassCulture_GetEventsMusicTypesAsync";

                var cacheResult = _memoryCache.Get<List<PassCultureType>>(cacheKey);

                if (cacheResult is not null)
                    return cacheResult;

                var request = new RestRequest("public/offers/v1/music_types/event", Method.Get)
                {
                    Authenticator = new OAuth2AuthorizationRequestHeaderAuthenticator(_apiKey, "Bearer")
                };

                RestResponse reponse = await _rateLimiter.CallApiWithRateLimitAsync(request);

                if (!reponse.IsSuccessful)
                    throw new Exception($"{reponse.ErrorMessage} {reponse.Content}");

                var types = JsonConvert.DeserializeObject<List<PassCultureType>>(reponse.Content!);

                if (types is not null)
                    _memoryCache.Set(cacheKey, types, TimeSpan.FromMinutes(2));

                return types ?? new();
            }
            catch (Exception ex)
            {
                throw new Exception(ex.Message);
            }
        }

        public async Task<List<PassCultureType>> GetShowTypesAsync(int structureId)
        {
            try
            {
                string cacheKey = "PassCulture_GetShowTypesAsync";

                var cacheResult = _memoryCache.Get<List<PassCultureType>>(cacheKey);

                if (cacheResult is not null)
                    return cacheResult;

                var request = new RestRequest("public/offers/v1/show_types", Method.Get)
                {
                    Authenticator = new OAuth2AuthorizationRequestHeaderAuthenticator(_apiKey, "Bearer")
                };

                RestResponse reponse = await _rateLimiter.CallApiWithRateLimitAsync(request);

                if (!reponse.IsSuccessful)
                    throw new Exception($"{reponse.ErrorMessage} {reponse.Content}");

                var types = JsonConvert.DeserializeObject<List<PassCultureType>>(reponse.Content!);

                if (types is not null)
                    _memoryCache.Set(cacheKey, types, TimeSpan.FromMinutes(2));

                return types ?? new();
            }
            catch (Exception ex)
            {
                throw new Exception(ex.Message);
            }
        }

        #endregion

        #region Booking/Cancellation

        public ResponseBase CreateBookingInRodrigue(int structureId, BookingPayload bookingInfos, WebUser webUser)
        {
            try
            {
                int offreId = _partnerIdsProviderRepository.GetRodrigueIdByPartnerIdProvide(structureId, bookingInfos.PassCultureOfferId, PartnerIdsProviderType.PassCulture_OffreIndividuel);

                int seanceId = _partnerIdsProviderRepository.GetRodrigueIdByPartnerIdProvide(structureId, bookingInfos.PassCultureStockId, PartnerIdsProviderType.PassCulture_Dates);

                int categoryId = _partnerIdsProviderRepository.GetRodrigueIdByPartnerIdProvide(structureId, bookingInfos.PassCulturePriceCategoryId, PartnerIdsProviderType.PassCulture_PriceCategory);

                IdentityDTO identityConsomer = new()
                {
                    DateOfBirthday = bookingInfos.UserBirthDate,
                    Email = bookingInfos.UserEmail,
                    FirstName = bookingInfos.UserFirstName,
                    SurName = bookingInfos.UserLastName,
                    PhoneNumber = bookingInfos.UserPhone
                };

                RevenderOrderCreationInfos revenderOrderCreationInfos = new()
                {
                    CategoryId = categoryId,
                    IdentityConsomer = identityConsomer,
                    OfferId = offreId,
                    PartnerType = PartnerType.PassCulture,
                    Price = bookingInfos.OfferPrice,
                    TicketsQuantity = bookingInfos.BookingQuantity,
                    SeanceId = seanceId,
                    WebUser = webUser,
                };

                OrderDTO order = _revenderOrderManagementService.CreateOrderWithFlagAuto(structureId, revenderOrderCreationInfos);

                var commande = _orderRepository.GetCustomEntityById(structureId, order.OrderId)
                                                .Include(o => o.Lignes)
                                                .Where(o => o.Lignes.Any(l => l.TypeLigne == "DOS"))
                                                .Include(o => o.Lignes.Select(l => l.Dossier))
                                                .Include(o => o.Lignes.Select(l => l.Dossier.Entrees))
                                                .Include(o => o.Lignes.Select(l => l.Dossier.Entrees.Select(e => e.Recette)))
                                                .Include(o => o.Lignes.Select(l => l.Dossier.Entrees.Select(e => e.ReferenceLieuPhysique)))
                                                .ToEntity();

                int dispos = _gestionPlaceRepository.GetDispoFromGestionPlacesWithOffer(structureId, offreId, seanceId, categoryId);

                return new BookingSuccess() 
                { 
                    RemainingQuantity = dispos - bookingInfos.BookingQuantity,
                    Tickets = commande!.Lignes!
                        .SelectMany(l => l.Dossier!.Entrees!)
                        .Select(e => {

                            string seatLabel = string.IsNullOrWhiteSpace(e.ReferenceLieuPhysique!.Rang) ? e.ReferenceLieuPhysique!.Siege : $"{e.ReferenceLieuPhysique!.Rang}-{e.ReferenceLieuPhysique!.Siege}";

                            var gestionPlace = _gestionPlaceRepository.GetGestionPlaceWithOfferBySessionIdAndCategoryId(structureId, offreId, seanceId, categoryId);

                            if (gestionPlace is not null && gestionPlace.IsPlacementLibre)
                                seatLabel = "Placement libre";

                            return new TicketResponseInfo()
                            {
                                Barcode = e.Recette!.GetBarcode(),
                                Seat = seatLabel 
                            };
                        }).ToList(),
                };
            }
            catch (Exception ex) 
            {
                return new BookingError() 
                { 
                    ErrorMessage = ex.Message,
                };
            }
        }


        /// <summary>
        /// Annule une réservation dans Rodrigue à partir des informations de payload d'annulation PassCulture.
        /// Récupère l'orderId à partir des codes-barres, annule la commande, puis recalcule la quantité restante disponible.
        /// </summary>
        /// <param name="structureId">Identifiant de la structure</param>
        /// <param name="cancellationInfos">Payload contenant les informations d'annulation (barcodes, etc.)</param>
        /// <returns>Un objet CancellationSuccess avec la quantité restante disponible après annulation</returns>
        public CancellationSuccess CancelBookingInRodrigue(int structureId, CancellationPayload cancellationInfos)
        {
            Logger.Info(991, $@"{JsonConvert.SerializeObject(cancellationInfos)}");

            try
            {
                int dispos = 0;
                int orderId = _orderRepository.GetOrderIdsByBarcodes(structureId, [.. cancellationInfos.Barcodes])
                                              .First();

                bool result = _orderManager.Cancel(structureId, orderId);

                PanierEntity panier = _basketRepository.FindCustomEntityBy(p => p.CommandeId == orderId, structureId)
                                              .Include(p => p.Entries)
                                              .ToEntity()!;


                int[] gestionPlacesIds = panier.Entries!.Select(e => e.GestionPlaceId ?? 0).ToArray();

                foreach (int gestionPlaceId in gestionPlacesIds)
                    dispos += _gestionPlaceRepository.GetById(structureId, gestionPlaceId)?.Dispo ?? 0;

                return new CancellationSuccess()
                {
                    RemainingQuantity = dispos + cancellationInfos.Barcodes.Count,
                };
            }
            catch
            {
                throw;
            }
        }

        public async Task CancelOrdersByDateIdInRodrigueAsync(int  structureId, int dateId)
        {
            int sessionId = _partnerIdsProviderRepository.GetRodrigueIdByPartnerIdProvide(structureId, dateId, PartnerIdsProviderType.PassCulture_Dates);

            OpenPartnerEntity partner = await _openPartnerRepository.GetByIdAsync(structureId, (int)PartnerType.PassCulture)
                ?? throw new NullReferenceException($"Open partner with id {(int)PartnerType.PassCulture} not found in db {structureId}");

            int[] orderIds = await _basketRepository.GetAllOrderIdByBuyerProfilIdAndSessionIdAsync(structureId, partner.ProfilAcheteurId!.Value, sessionId);

            if(orderIds.Length > 0)
                orderIds = await GetOrderIdsWithBuyStatusAsync(structureId, orderIds);

            foreach (int orderId in orderIds)
                _orderManager.Cancel(structureId, orderId);
        }

        private async Task<int[]> GetOrderIdsWithBuyStatusAsync(int structureId, int[] orderIds)
        {
            var query = _orderRepository.FindCustomEntityBy(o => orderIds.Contains(o.CommandeId), structureId);

            await query.IncludeAsync(o => o.Lignes);
            await query.IncludeAsync(o => o.Lignes.Select(l => l.Dossier));

            var orders = query.ToEntityList();

            return orderIds.Where(id => orders.First(o => o.CommandeId == id).Lignes.All(l => l.Dossier.DossierEtat.Trim() == "B")).ToArray();
        }

        #endregion

        #region Collective offers

        public async Task<List<CollectiveOfferConsultation>> GetCollectiveOffersAsync(int structureId, int? venueId = null, string? status = null, string? periodBeginningDate = null, string? periodEndingDate = null)
        {
            try
            {
                string cacheKey = $"PassCulture_GetCollectiveOffersAsync_{structureId}_{venueId}_{status}_{periodBeginningDate}_{periodEndingDate}";

                var cacheResult = _memoryCache.Get<List<CollectiveOfferConsultation>>(cacheKey);

                if (cacheResult is not null)
                    return cacheResult;

                var request = new RestRequest($"v2/collective/offers/", Method.Get)
                {
                    Authenticator = new OAuth2AuthorizationRequestHeaderAuthenticator(_apiKey, "Bearer"),
                };

                if (venueId is not null)
                    request.AddQueryParameter("venueId", venueId.Value);

                if (status is not null)
                    request.AddQueryParameter("status", status);

                if (periodBeginningDate is not null)
                    request.AddQueryParameter("periodBeginningDate", periodBeginningDate);

                if (periodEndingDate is not null)
                    request.AddQueryParameter("firstIndex", periodEndingDate);

                RestResponse reponse = await _rateLimiter.CallApiWithRateLimitAsync(request);

                if (!reponse.IsSuccessful)
                    throw new Exception($"{reponse.ErrorMessage} {reponse.Content}");

                var collectiveOffers = JsonConvert.DeserializeObject<List<CollectiveOfferConsultation>>(reponse.Content!);

                _memoryCache.Set(cacheKey, collectiveOffers);

                return collectiveOffers ?? [];
            }
            catch (Exception ex)
            {
                throw new Exception(ex.Message);
            }
        }

        public async Task<CollectiveOfferConsultation?> GetCollectiveOfferByIdAsync(int structureId, int passCultureOfferId)
        {
            try
            {
                string cacheKey = $"PassCulture_GetCollectiveOfferByIdAsync_{structureId}_{passCultureOfferId}";

                var cacheResult = _memoryCache.Get<CollectiveOfferConsultation>(cacheKey);

                if (cacheResult is not null)
                    return cacheResult;

                var request = new RestRequest($"v2/collective/offers/{passCultureOfferId}", Method.Get)
                {
                    Authenticator = new OAuth2AuthorizationRequestHeaderAuthenticator(_apiKey, "Bearer"),
                };

                RestResponse reponse = await _rateLimiter.CallApiWithRateLimitAsync(request);

                if (!reponse.IsSuccessful)
                    throw new Exception($"{reponse.ErrorMessage} {reponse.Content}");

                var collectiveOffer = JsonConvert.DeserializeObject<CollectiveOfferConsultation>(reponse.Content!);

                if (collectiveOffer is not null)
                    _memoryCache.Set(cacheKey, collectiveOffer);

                return collectiveOffer;
            }
            catch (Exception ex)
            {
                throw new Exception(ex.Message);
            }
        }

        public async Task<PassCultureResponse<CollectiveOfferConsultation>> CreateCollectiveOfferAsync(int structureId, CollectiveOfferCreation collectiveOffer)
        {
            try
            {
                collectiveOffer.VenueId = GetVenueIdByStructureId(structureId) ??
                    throw new NullReferenceException($"Venue id for structure {structureId} doesn't exist, check pass culture settings");

                var request = new RestRequest("v2/collective/offers/", Method.Post)
                {
                    RequestFormat = DataFormat.Json,
                    Authenticator = new OAuth2AuthorizationRequestHeaderAuthenticator(_apiKey, "Bearer")
                };

                string json = JsonConvert.SerializeObject(collectiveOffer);

                request.AddBody(json);

                RestResponse reponse = await _rateLimiter.CallApiWithRateLimitAsync(request);

                return new()
                {
                    Success = reponse.IsSuccessful,
                    ErrorMessage = !reponse.IsSuccessful ? reponse.Content : null,
                    Result = reponse.IsSuccessful ? JsonConvert.DeserializeObject<CollectiveOfferConsultation>(reponse.Content!) : null,
                };
            }
            catch (Exception ex)
            {
                throw new Exception(ex.Message);
            }
        }

        public async Task<PassCultureResponse<CollectiveOfferConsultation>> UpdateCollectiveOfferAsync(int structureId, int passCultureOfferId, CollectiveOfferCreation collectiveOffer)
        {
            try
            {
                var request = new RestRequest($"v2/collective/offers/{passCultureOfferId}", Method.Patch)
                {
                    RequestFormat = DataFormat.Json,
                    Authenticator = new OAuth2AuthorizationRequestHeaderAuthenticator(_apiKey, "Bearer")
                };

                string json = JsonConvert.SerializeObject(collectiveOffer);

                request.AddBody(json);

                var reponse = await _rateLimiter.CallApiWithRateLimitAsync(request);

                if(reponse.IsSuccessful)
                    RemoveCollectiveOfferCache(structureId, passCultureOfferId);

                return new()
                {
                    Success = reponse.IsSuccessful,
                    ErrorMessage = !reponse.IsSuccessful ? reponse.Content : null,
                    Result = reponse.IsSuccessful ? JsonConvert.DeserializeObject<CollectiveOfferConsultation>(reponse.Content!) : null,
                };
            }
            catch (Exception ex)
            {
                throw new Exception(ex.Message);
            }
        }

        public async Task<bool> ArchiveCollectiveOffersAsync(int structureId, int[] collectiveOfferIds)
        {
            try
            {
                var request = new RestRequest("v2/collective/offers/archive", Method.Post)
                {
                    RequestFormat = DataFormat.Json,
                    Authenticator = new OAuth2AuthorizationRequestHeaderAuthenticator(_apiKey, "Bearer")
                };

                string json = JsonConvert.SerializeObject(new ArchiveList()
                {
                    IdsToArchive = collectiveOfferIds
                });

                request.AddBody(json);

                RestResponse reponse = await _rateLimiter.CallApiWithRateLimitAsync(request);

                if (reponse.IsSuccessful)
                    foreach(int id in collectiveOfferIds) 
                        RemoveCollectiveOfferCache(structureId, id);

                return reponse.IsSuccessful;
            }
            catch (Exception ex)
            {
                throw new Exception(ex.Message);
            }
        }

        #endregion

        #region Collective offer attributes

        public async Task<List<EducationalDomain>> GetEducationalDomainsAsync(int structureId)
        {
            try
            {
                string cacheKey = $"PassCulture_GetEducationalDomainsAsync";

                var cacheResult = _memoryCache.Get<List<EducationalDomain>>(cacheKey);

                if (cacheResult is not null)
                    return cacheResult;

                var request = new RestRequest("v2/collective/educational-domains", Method.Get)
                {
                    Authenticator = new OAuth2AuthorizationRequestHeaderAuthenticator(_apiKey, "Bearer")
                };

                RestResponse reponse = await _rateLimiter.CallApiWithRateLimitAsync(request);

                if (!reponse.IsSuccessful)
                    throw new Exception($"{reponse.ErrorMessage} {reponse.Content}");

                var educationalDomains = JsonConvert.DeserializeObject<List<EducationalDomain>>(reponse.Content!);

                if (educationalDomains is not null)
                    _memoryCache.Set(cacheKey, educationalDomains, TimeSpan.FromMinutes(2));

                return educationalDomains ?? [];
            }
            catch (Exception ex)
            {
                throw new Exception(ex.Message);
            }
        }

        public async Task<List<EducationalInstitution>> GetAllEducationalInstitutionAsync(int structureId, EducationalInstitutionSearchForm search)
        {
            try
            {
                string cacheKey = $"PassCulture_GetAllEducationalInstitutionAsync_{JsonConvert.SerializeObject(search)}";

                var cacheResult = _memoryCache.Get<List<EducationalInstitution>>(cacheKey);

                if (cacheResult is not null)
                    return cacheResult;

                var request = new RestRequest($"v2/collective/educational-institutions/", Method.Get)
                {
                    Authenticator = new OAuth2AuthorizationRequestHeaderAuthenticator(_apiKey, "Bearer"),
                };

                if (search.Id is not null)
                    request.AddQueryParameter("id", search.Id.Value);

                if (!string.IsNullOrWhiteSpace(search.Name))
                    request.AddQueryParameter("name", search.Name);

                if (!string.IsNullOrWhiteSpace(search.InstitutionType))
                    request.AddQueryParameter("institutionType", search.InstitutionType);

                if (!string.IsNullOrWhiteSpace(search.City))
                    request.AddQueryParameter("city", search.City);

                if (!string.IsNullOrWhiteSpace(search.PostalCode))
                    request.AddQueryParameter("postalCode", search.PostalCode);

                if (!string.IsNullOrWhiteSpace(search.Uai))
                    request.AddQueryParameter("uai", search.Uai);

                RestResponse reponse = await _rateLimiter.CallApiWithRateLimitAsync(request);

                if (!reponse.IsSuccessful)
                    throw new Exception($"{reponse.ErrorMessage} {reponse.Content}");

                var educationalInstitutions = JsonConvert.DeserializeObject<List<EducationalInstitution>>(reponse.Content!);

                _memoryCache.Set(cacheKey, educationalInstitutions);

                return educationalInstitutions ?? [];
            }
            catch (Exception ex)
            {
                throw new Exception(ex.Message);
            }
        }

        public async Task<List<NationalProgram>> GetAllKnownNationalProgramsAsync(int structureId)
        {
            try
            {
                string cacheKey = $"PassCulture_GetAllKnownNationalProgramsAsync";

                var cacheResult = _memoryCache.Get<List<NationalProgram>>(cacheKey);

                if (cacheResult is not null)
                    return cacheResult;

                var request = new RestRequest("v2/collective/national-programs/", Method.Get)
                {
                    Authenticator = new OAuth2AuthorizationRequestHeaderAuthenticator(_apiKey, "Bearer")
                };

                RestResponse reponse = await _rateLimiter.CallApiWithRateLimitAsync(request);

                if (!reponse.IsSuccessful)
                    throw new Exception($"{reponse.ErrorMessage} {reponse.Content}");

                var nationalPrograms = JsonConvert.DeserializeObject<List<NationalProgram>>(reponse.Content!);

                if (nationalPrograms is not null)
                    _memoryCache.Set(cacheKey, nationalPrograms, TimeSpan.FromMinutes(2));

                return nationalPrograms ?? [];
            }
            catch (Exception ex)
            {
                throw new Exception(ex.Message);
            }
        }

        public async Task<List<StudentLevel>> GetStudentLevelsEligibleAsync(int structureId)
        {
            try
            {
                string cacheKey = $"PassCulture_GetStudentLevelsEligibleAsync";

                var cacheResult = _memoryCache.Get<List<StudentLevel>>(cacheKey);

                if (cacheResult is not null)
                    return cacheResult;

                var request = new RestRequest("v2/collective/student-levels", Method.Get)
                {
                    Authenticator = new OAuth2AuthorizationRequestHeaderAuthenticator(_apiKey, "Bearer")
                };

                RestResponse reponse = await _rateLimiter.CallApiWithRateLimitAsync(request);

                if (!reponse.IsSuccessful)
                    throw new Exception($"{reponse.ErrorMessage} {reponse.Content}");

                var studentLevels = JsonConvert.DeserializeObject<List<StudentLevel>>(reponse.Content!);

                if (studentLevels is not null)
                    _memoryCache.Set(cacheKey, studentLevels, TimeSpan.FromMinutes(2));

                return studentLevels ?? [];
            }
            catch (Exception ex)
            {
                throw new Exception(ex.Message);
            }
        }

        public async Task<List<SelectLookup>> SelectLookupEducationalDomainsAsync(int structureId)
        {
            var eductionalDomains = await GetEducationalDomainsAsync(structureId).ConfigureAwait(false);

            return eductionalDomains.Select(d => new SelectLookup()
            {
                Value = d.Id.ToString(),
                Libelle = d.Name,
            })
            .ToList();
        }

        public async Task<List<SelectLookup>> SelectLookupEducationalInstitutionAsync(int structureId, EducationalInstitutionSearchForm search)
        {
            var educationalInstitution = await GetAllEducationalInstitutionAsync(structureId, search).ConfigureAwait(false);

            return educationalInstitution.Select(i => new SelectLookup()
            {
                Value = i.Uai,
                Libelle =$"{i.Uai} - {i.Name}"
            })
            .ToList();
        }

        public async Task<List<SelectLookup>> SelectLookupNationalProgramsAsync(int structureId)
        {
            var nationalPrograms = await GetAllKnownNationalProgramsAsync(structureId).ConfigureAwait(false);

            return nationalPrograms.Select(d => new SelectLookup()
            {
                Value = d.Id.ToString(),
                Libelle = d.Name,
            })
            .ToList();
        }

        public async Task<List<SelectLookup>> SelectLookupStudentLevelsAsync(int structureId)
        {
            var studentLevels = await GetStudentLevelsEligibleAsync(structureId).ConfigureAwait(false);

            return studentLevels.Select(d => new SelectLookup()
            {
                Value = d.Id,
                Libelle = d.Name,
            })
            .ToList();
        }

        #endregion

        #region Adage Mock

        public async Task<bool> AdageMockCollectiveOfferBookingAsync(int structureId, int offerId)
        {
            try
            {
                var client = new RestClient(_passCultureApiPath);
                var request = new RestRequest($"v2/collective/adage_mock/offer/{offerId}/book", Method.Post)
                {
                    RequestFormat = DataFormat.Json,
                    Authenticator = new OAuth2AuthorizationRequestHeaderAuthenticator(_apiKey, "Bearer")
                };

                RestResponse reponse = await client.ExecuteAsync(request)
                                                   .ConfigureAwait(false);

                if(reponse.IsSuccessful)
                    _memoryCache.RemoveKeysContainPartOfKey($"PassCulture_GetCollectiveOfferByIdAsync_{structureId}");

                return reponse.IsSuccessful;

            }
            catch (Exception ex)
            {
                throw new Exception(ex.Message);
            }
        }

        public async Task<bool> AdageMockCollectiveOfferBookingCancellationAsync(int structureId, int bookingId)
        {
            try
            {
                var client = new RestClient(_passCultureApiPath);
                var request = new RestRequest($"v2/collective/adage_mock/bookings/{bookingId}/cancel", Method.Post)
                {
                    RequestFormat = DataFormat.Json,
                    Authenticator = new OAuth2AuthorizationRequestHeaderAuthenticator(_apiKey, "Bearer")
                };

                RestResponse reponse = await client.ExecuteAsync(request)
                                                   .ConfigureAwait(false);

                if (reponse.IsSuccessful)
                    _memoryCache.RemoveKeysContainPartOfKey($"PassCulture_GetCollectiveOfferByIdAsync_{structureId}");

                return reponse.IsSuccessful;
            }
            catch (Exception ex)
            {
                throw new Exception(ex.Message);
            }
        }

        public async Task<bool> AdageMockCollectiveOfferBookingConfirmationAsync(int structureId, int bookingId)
        {
            try
            {
                var client = new RestClient(_passCultureApiPath);
                var request = new RestRequest($"v2/collective/adage_mock/bookings/{bookingId}/confirm", Method.Post)
                {
                    RequestFormat = DataFormat.Json,
                    Authenticator = new OAuth2AuthorizationRequestHeaderAuthenticator(_apiKey, "Bearer")
                };

                RestResponse reponse = await client.ExecuteAsync(request)
                                                   .ConfigureAwait(false);

                if (reponse.IsSuccessful)
                    _memoryCache.RemoveKeysContainPartOfKey($"PassCulture_GetCollectiveOfferByIdAsync_{structureId}");

                return reponse.IsSuccessful;
            }
            catch (Exception ex)
            {
                throw new Exception(ex.Message);
            }
        }

        public async Task<bool> AdageMockCollectiveOfferBookingReimbursementAsync(int structureId, int bookingId)
        {
            try
            {
                var client = new RestClient(_passCultureApiPath);
                var request = new RestRequest($"v2/collective/adage_mock/bookings/{bookingId}/reimburse", Method.Post)
                {
                    RequestFormat = DataFormat.Json,
                    Authenticator = new OAuth2AuthorizationRequestHeaderAuthenticator(_apiKey, "Bearer")
                };

                RestResponse reponse = await client.ExecuteAsync(request)
                                                   .ConfigureAwait(false);

                if (reponse.IsSuccessful)
                    _memoryCache.RemoveKeysContainPartOfKey($"PassCulture_GetCollectiveOfferByIdAsync_{structureId}");

                return reponse.IsSuccessful;
            }
            catch (Exception ex)
            {
                throw new Exception(ex.Message);
            }
        }

        public async Task<bool> AdageMockCollectiveOfferBookingUseAsync(int structureId, int bookingId)
        {
            try
            {
                var client = new RestClient(_passCultureApiPath);
                var request = new RestRequest($"v2/collective/bookings/{bookingId}/use", Method.Post)
                {
                    RequestFormat = DataFormat.Json,
                    Authenticator = new OAuth2AuthorizationRequestHeaderAuthenticator(_apiKey, "Bearer")
                };

                RestResponse reponse = await client.ExecuteAsync(request)
                                                   .ConfigureAwait(false);

                if (reponse.IsSuccessful)
                    _memoryCache.RemoveKeysContainPartOfKey($"PassCulture_GetCollectiveOfferByIdAsync_{structureId}");

                return reponse.IsSuccessful;
            }
            catch (Exception ex)
            {
                throw new Exception(ex.Message);
            }
        }

        public async Task<bool> AdageMockCollectiveOfferBookingPendingAsync(int structureId, int bookingId)
        {
            try
            {
                var client = new RestClient(_passCultureApiPath);
                var request = new RestRequest($"v2/collective/adage_mock/bookings/{bookingId}/pending", Method.Post)
                {
                    RequestFormat = DataFormat.Json,
                    Authenticator = new OAuth2AuthorizationRequestHeaderAuthenticator(_apiKey, "Bearer")
                };

                RestResponse reponse = await client.ExecuteAsync(request)
                                                   .ConfigureAwait(false);

                if (reponse.IsSuccessful)
                    _memoryCache.RemoveKeysContainPartOfKey($"PassCulture_GetCollectiveOfferByIdAsync_{structureId}");

                return reponse.IsSuccessful;
            }
            catch (Exception ex)
            {
                throw new Exception(ex.Message);
            }
        }

        #endregion

        #region Privates

        private void RemoveIndividualOfferCache(int structureId, int passCultureEventId)
        {
            _memoryCache.Remove($"PassCulture_GetEventByIdAsync_{structureId}_{passCultureEventId}");
            _memoryCache.RemoveKeysContainPartOfKey($"PassCulture_GetEventsAsync_{structureId}");
            _memoryCache.RemoveKeysContainPartOfKey($"PassCulture_GetEventStocksByIdEventAsync_{structureId}_{passCultureEventId}");
        }

        private void RemoveCollectiveOfferCache(int structureId, int passCultureOfferId)
        {
            _memoryCache.RemoveKeysContainPartOfKey($"PassCulture_GetCollectiveOffersAsync_{structureId}");
            _memoryCache.Remove($"PassCulture_GetCollectiveOfferByIdAsync_{structureId}_{passCultureOfferId}");
        }

        #endregion

    }


    #region Structs

    public struct VenuesInfo
    {
        /// <summary>
        /// Offerer to which the venues belong. Entity linked to the api key used.
        /// </summary>
        [JsonProperty("offerer")]
        public Offerer Offerer { get; set; }

        [JsonProperty("venues")]
        public List<Venue> Venues { get; set; }
    }

    public struct EventsList
    {
        public List<EventOffer> Events { get; set; }
    }

    public struct PriceCategoriesList
    {
        [JsonProperty("priceCategories")]
        public List<PriceCategory> PriceCategories { get; set; }
    }

    public struct DatesList
    {
        [JsonProperty("dates")]
        public List<Date> Dates { get; set; }
    }

    public struct AddressesList
    {
        [JsonProperty("addresses")]
        public List<AddressConsultation> Addresses { get; set; }
    }

    public struct ArchiveList
    {
        [JsonProperty("ids")]
        public int[] IdsToArchive { get; set; }
    }

    #endregion
}

