{
  "Logging": {
    "LogLevel": {
      "Default": "Information",
      "Microsoft.Hosting.Lifetime": "Information"
    }
  },
  "WorkerConfig": {
    "DelaySeconds": 20,

    "pathReflagApp": "D:\\APPLIS\\servicesRattrapeP\\prod\\reflag\\servicereflag.exe",
    "pathDoCommandeApp": "D:\\APPLIS\\servicesRattrapeP\\preprod\\createCmd\\serviceCreateCmd.exe",
    "pathDoPaiementApp": "D:\\APPLIS\\servicesRattrapeP\\preprod\\doPaiement\\serviceDoPaiement.exe",
    "pathEditionApp": "D:\\APPLIS\\servicesRattrapeP\\preprod\\doEdition\\serviceEdition.exe",
    "pathEmailtApp": "D:\\APPLIS\\servicesRattrapeP\\preprod\\envoiemail\\serviceEmail.exe"
  },
  "ConnectionStrings": {
    "WsAdminDB": "Server=************;Database=WSAdmin;Persist Security Info=True;User ID=SphereWeb;Password=************************************************************************************************************;MultipleActiveResultSets=true",

    //"GlobalOpinionDB": "Server=*************;Database=WSAdmin_test;Persist Security Info=True;User ID=SupportTest;Password=****************************************************************************************;MultipleActiveResultSets=true",
    "WebLibraryDB": "Server=*************;Database=GlobalWebLibrary_DEV;Persist Security Info=True;User ID=SphereWebTest;Password=************************************************************************************************************;MultipleActiveResultSets=true",
    "Redis": "**************:6555,password=tS4pDJfVCN3Y0j5pvrdEBCpH0gXLZsZ/QCdN2zh6lJwoi+Va4eBUwtkZEUNYCcCl"
  },
  "TypeRun": "PROD"

}
