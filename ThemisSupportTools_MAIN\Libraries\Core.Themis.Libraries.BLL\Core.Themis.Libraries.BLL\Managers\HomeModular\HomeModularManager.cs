﻿using AutoMapper;
using Core.Themis.Libraries.BLL.Managers.HomeModular.Interfaces;
using Core.Themis.Libraries.BLL.Translations.Interfaces;
using Core.Themis.Libraries.Data.Entities.Open.EventsSessions;
using Core.Themis.Libraries.Data.Entities.Open.HomeModular;
using Core.Themis.Libraries.Data.Repositories.Open.EventsSessions.Interfaces;
using Core.Themis.Libraries.Data.Repositories.Open.HomeModular.Interfaces;
using Core.Themis.Libraries.Data.Repositories.Open.Produit.Boutique.Interfaces;
using Core.Themis.Libraries.Data.Repositories.Open.Produit.Interfaces;
using Core.Themis.Libraries.DTO.Enums;
using Core.Themis.Libraries.DTO.Enums.HomeModular;
using Core.Themis.Libraries.DTO.EventsSessions;
using Core.Themis.Libraries.DTO.HomeModular;
using Core.Themis.Libraries.DTO.HomeModular.Clients;
using Core.Themis.Libraries.DTO.HomeModular.Clients.ViewModels;
using Core.Themis.Libraries.DTO.Products;
using Core.Themis.Libraries.Utilities.Extensions;
using Core.Themis.Libraries.Utilities.Logging;
//using Microsoft.Extensions.Caching.Distributed;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.Configuration;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;

namespace Core.Themis.Libraries.BLL.HomeModular
{
    public class HomeModularManager : IHomeModularManager
    {

        private static readonly RodrigueNLogger RodrigueLogger = new RodrigueNLogger();

        private readonly IProprietesOfManifsRepository _proprietesOfManifsRepository;
        private readonly IProprietesReferencesOfManifsRepository _proprietesReferencesOfManifsRepository;
        private readonly IHomeModularRepository _homeModularRepository;
        private readonly IEventGroupRepository _eventGroupRepository;
        private readonly IEventGenreRepository _eventGenreRepository; //Genres
        private readonly IEventSousGenreRepository _eventSousGenreRepository; //sous genres
        private readonly IProduitRepository _productRepository;
        private readonly IProduitStockRepository _produitStockRepository;
        private readonly IProduitInternetRepository _produitInternetRepository;
        private readonly IBoutiqueRepository _boutiqueRepository;
        private readonly IEventRepository _eventRepository;
        private readonly ITranslateManager _translateManager;
        private readonly IHomeModularBlockEmplacementRepository _homeModularBlockEmplacementRepository;
        private readonly IHomeModularBlockUserConfigRepository _homeModularBlockUserConfigRepository;
        private readonly IGPManifestationRepository _gPManifestationRepository;
        private readonly IGPSeanceRepository _gPSeanceRepository;
        private readonly IMemoryCache _memoryCache;
        //private readonly IDistributedCache _distributedCache;
        private readonly IConfiguration _configuration;
        private readonly IMapper _mapper;

        public HomeModularManager(IProprietesOfManifsRepository proprietesOfManifsRepository,
            IProprietesReferencesOfManifsRepository proprietesReferencesOfManifsRepository,
            IHomeModularRepository homeModularRepository,
            IEventGroupRepository eventGroupRepository,
            IEventGenreRepository eventGenreRepository,
            IEventSousGenreRepository eventSousGenreRepository,
            IProduitRepository produitRepository,
            IProduitStockRepository produitStockRepository,
            IProduitInternetRepository produitInternetRepository,
            IBoutiqueRepository boutiqueRepository,
            IEventRepository eventRepository,
            ITranslateManager translateManager,
            IHomeModularBlockEmplacementRepository homeModularBlockEmplacementRepository,
            IHomeModularBlockUserConfigRepository homeModularBlockUserConfigRepository,
            IGPManifestationRepository gPManifestationRepository,
            IGPSeanceRepository gPSeanceRepository,
            IMemoryCache memoryCache,
            //IDistributedCache distributedCache,
            IConfiguration configuration,
            IMapper mapper)
        {
            _proprietesOfManifsRepository = proprietesOfManifsRepository;
            _proprietesReferencesOfManifsRepository = proprietesReferencesOfManifsRepository;
            _homeModularRepository = homeModularRepository;
            _eventGroupRepository = eventGroupRepository;
            _eventGenreRepository = eventGenreRepository;
            _eventSousGenreRepository = eventSousGenreRepository;
            _productRepository = produitRepository;
            _produitStockRepository = produitStockRepository;
            _produitInternetRepository = produitInternetRepository;
            _boutiqueRepository = boutiqueRepository;
            _eventRepository = eventRepository;
            _translateManager = translateManager;
            _homeModularBlockEmplacementRepository = homeModularBlockEmplacementRepository;
            _homeModularBlockUserConfigRepository = homeModularBlockUserConfigRepository;
            _gPManifestationRepository = gPManifestationRepository;
            _gPSeanceRepository = gPSeanceRepository;
            _memoryCache = memoryCache;
            //_distributedCache = distributedCache;
            _configuration = configuration;
            _mapper = mapper;
        }

        public IEnumerable<HomeModularEmplacementGroupDTO>? GetAllEmplacementGroups(int structureId)
        {
            var emplacementGroups = _homeModularRepository.GetAllEmplacementGroups(structureId);
            return _mapper.Map<IEnumerable<HomeModularEmplacementGroupDTO>>(emplacementGroups);
        }

        public IEnumerable<HomeModularBlockFunctionDTO>? GetBlockFunctionsByBlockTypeIdWithDependencies(int structureId, int blockTypeId)
        {
            var blockFunctionsOfThisBlockType = _homeModularRepository.GetBlockFunctionsByBlockTypeIdWithDependencies(structureId, blockTypeId);
            return _mapper.Map<IEnumerable<HomeModularBlockFunctionDTO>>(blockFunctionsOfThisBlockType);

        }
        public HomeModularBlockFunctionDTO? GetBlockFunctionByIdWithDependencies(int structureId, int blockFunctionId)
        {
            var blockFunctions = _homeModularRepository.GetBlockFunctionByIdWithDependencies(structureId, blockFunctionId);
            return _mapper.Map<HomeModularBlockFunctionDTO>(blockFunctions);
        }

        public HomeModularEmplacementGroupDTO GetHomeModularEmplacementGroupById(int structureId, int emplacementGroupId, string langCode, bool isTeasing = false, bool clearCache = false)
        {
            /*
            string blocksCacheKey = $"blocks_{structureId}_{emplacementGroupId}_{isTeasing}_{langCode}";

            var homeModularBlocksCache = _distributedCache.GetRecord<HomeModularEmplacementGroupDTO>(blocksCacheKey);

            if (homeModularBlocksCache != null)
            {
                return homeModularBlocksCache;
            }
           
            string blocksCacheKey = $"blocks_{structureId}_{emplacementGroupId}_{isTeasing}_{langCode}";
            var homeModularBlocksCache = GetCache(blocksCacheKey);
            if (homeModularBlocksCache != null)
                return (HomeModularEmplacementGroupDTO)homeModularBlocksCache;
            */

            string blocksCacheKey = $"blocks_{structureId}_{isTeasing}_{langCode}";

            if (!_memoryCache.TryGetValue(blocksCacheKey, out HomeModularEmplacementGroupDTO homeModularBlocksCache) || clearCache)
            {

                var homeEmplacementGroup = _homeModularRepository.GetEmplacementGroupByWithTwoDatabasesDepedencies(structureId, emplacementGroupId);
                homeModularBlocksCache = _mapper.Map<HomeModularEmplacementGroupDTO>(homeEmplacementGroup);

                if (homeModularBlocksCache.HomeModularBlockEmplacements?.Count > 0)
                {
                    homeModularBlocksCache.HomeModularBlockEmplacements = homeModularBlocksCache.HomeModularBlockEmplacements.OrderBy(be => be.EmplacementId).OrderBy(be => be.BlockOrder).ToList();
                    var blockUserConfigs = homeModularBlocksCache.HomeModularBlockEmplacements.SelectMany(x => x.HomeModularBlockUserConfigs);


                    SetConfigValue(structureId, ref blockUserConfigs, langCode, isTeasing);

                    var blocksEventsOne = homeModularBlocksCache.HomeModularBlockEmplacements.Where(be => be.BlockTypeId == (int)HomeModularBlockTypeEnum.EventOne).ToList();

                    blocksEventsOne.ForEach(x =>
                    {
                        bool blockIsLock = false;
                        var blockUserValueEventId = x.HomeModularBlockUserConfigs.FirstOrDefault(buc => buc.BlockFunction.FunctionsId == (int)HomeModularFunctionsEnum.SelectEventsList)?.BlockUserConfigValue;
                        if (!string.IsNullOrWhiteSpace(blockUserValueEventId) && int.TryParse(blockUserValueEventId, out _))
                        {
                            var manifId = int.Parse(blockUserValueEventId);
                            var thisManif = _gPManifestationRepository.FindFirstOrDefault(gps => gps.ManifestationId == manifId, structureId);
                            if (thisManif == null)
                            {
                                RodrigueLogger.Error(structureId, $"event {manifId} doesn't exist in gp_manifestation table ?!?!");
                                blockIsLock = true; // n'existe pas dans gp_manifestation ???????!!!
                            }
                            else
                            {
                                blockIsLock = thisManif.IsLock;
                            }
                        }

                        var blockUserValueSessiontId = x.HomeModularBlockUserConfigs.FirstOrDefault(buc => buc.BlockFunction.FunctionsId == (int)HomeModularFunctionsEnum.SelectSessionsList)?.BlockUserConfigValue;
                        if (!string.IsNullOrWhiteSpace(blockUserValueSessiontId) && int.TryParse(blockUserValueSessiontId, out _))
                        {
                            var sessionId = int.Parse(blockUserValueSessiontId);
                            var thisSession = _gPSeanceRepository.FindFirstOrDefault(gps => gps.SeanceId == sessionId, structureId);
                            if (thisSession == null)
                            {
                                RodrigueLogger.Error(structureId, $"session {sessionId} doesn't exist in gp_seance table ?!?!");
                                blockIsLock = false; // n'existe pas dans gp_seance ???????!!!

                            }
                            else
                            {
                                blockIsLock = thisSession.IsLock;
                            }
                        }

                        x.IsLock = blockIsLock;

                    });



                }


                int durationCache = 100;
                if (_configuration["Cache:HomeModularAbsoluteExpiration"] != null)
                {
                    durationCache = int.Parse(_configuration["Cache:HomeModularAbsoluteExpiration"]!);
                }

                //mise en cache pendant 10 minutes ou pendant la durée définie dans le fichier apsettings.json
                var cacheEntryOptions = new MemoryCacheEntryOptions()
                   .SetPriority(CacheItemPriority.Normal)
                   .SetAbsoluteExpiration(TimeSpan.FromSeconds(durationCache));

                _memoryCache.Set(blocksCacheKey, homeModularBlocksCache, cacheEntryOptions);
            }

            // _distributedCache.SetRecord(blocksCacheKey, result); // Set cache
            return homeModularBlocksCache;
        }


        public IEnumerable<HomeModularBlockTypeDTO> GetHomeModularBlockTypes(int structureId)
        {
            var homeBlockTypes = _homeModularRepository.GetAllBlockTypes(structureId);
            return _mapper.Map<IEnumerable<HomeModularBlockTypeDTO>>(homeBlockTypes);
        }

        public HomeModularBlockEmplacementDTO? GetHomeModularBlockEmplacementById(int structureId, int blockEmplacementId)
        {
            var homeBlockTypes = _homeModularRepository.GetHomeModularBlockEmplacementById(structureId, blockEmplacementId);
            return _mapper.Map<HomeModularBlockEmplacementDTO>(homeBlockTypes);
        }

        public HomeModularViewModel GetHomeFunctionsList(int structureId, int blockEmplacementId, int blockTypeId, string langCode)
        {
            var blockTypes = GetHomeModularBlockTypes(structureId);

            List<HomeModularFunctionsDTO> functionsOfThisBlockTypeSelect = new List<HomeModularFunctionsDTO>();

            functionsOfThisBlockTypeSelect = blockTypes.Where(bt => bt.Id == blockTypeId)
                                                    .SelectMany(bf => bf.HomeModularBlockFunctions
                                                        .Select(f => f.HomeModularFunction))
                                                    .ToList();


            HomeModularViewModel vm = new HomeModularViewModel();


            vm.HomeModularBlockType = blockTypes.FirstOrDefault(bt => bt.Id == blockTypeId);

            if (vm.HomeModularBlockType is not null)
            {
                vm.HomeModularBlockType.HomeModularBlockFunctions = blockTypes.Where(bt => bt.Id == blockTypeId)
                                                                            .SelectMany(bf => bf.HomeModularBlockFunctions).ToList();
            }


            var homeModularBlockEmplacement = GetHomeModularBlockEmplacementById(structureId, blockEmplacementId);
            vm.HomeModularBlockEmplacement = homeModularBlockEmplacement;

            List<int> excludeProducts = new List<int>() { (int)EnumProductType.ObtainingMode, (int)EnumProductType.FraisEnvoi, (int)EnumProductType.Reservation, (int)EnumProductType.Assurance }; //"MO", "FRAISENVOI", "RESERVATION", "ASS" };
            foreach (var item in functionsOfThisBlockTypeSelect)
            {
                switch (item.Name)
                {
                    //case "Input_Display_Date_Picker":

                    //  //  var gg =homeModularBlockEmplacement.HomeModularBlockUserConfigs.FirstOrDefault(buc => buc.BlockFunctionId.Equals(item.Id));


                    //    //if (int.TryParse(item., out int eventIf))
                    //    //{

                    //    //}
                    //    //    //On va chercher la date de disponibilité de l'évênement dans la table proprietes_of_manifs
                    //    //    _proprietesOfManifsRepository.GetPropertieOfManifsByManifIdAndRefCode(structureId)

                    //    break;

                    case "Select_Products_Family_List" or "Select_Products_Subfamily_List" or "Select_Products_Family_List_Multiple" or "Select_Products_Subfamily_List_Multiple":
                        //rempli la liste des famille et sous famille ainsi que les produits
                        var familiesProducts = _boutiqueRepository.GetAllFamilliesProducts(structureId, langCode);

                        var resultProductsO = _mapper.Map<List<ProductFamilyDTO>>(familiesProducts);
                        vm.ProductFamilies = resultProductsO;

                        break;

                    case "Select_Products_List" or "Select_Products_List_Multiple":
                        //rempli la liste des produits
                        var products = _productRepository.GetAllProductsInternet(structureId, langCode);

                        products.ToList().ForEach(p =>
                        {
                            p.ProduitsStocks = _produitStockRepository.FindBy(ps => ps.ProduitId == p.ProduitId, structureId).ToList();
                            p.ProduitInternet = _produitInternetRepository.FindFirstOrDefault(pi => pi.ProduitId == p.ProduitId, structureId);
                        });

                        products = products.Where(p => p.ProduitsStocks.Any(ps => ps.ManifestationId == 0 || ps.SeanceId == 0) && (p.ProduitInternet != null && p.ProduitInternet.AccesAutonome));

                        var resultProducts = _mapper.Map<List<ProductDTO>>(products);
                        resultProducts = resultProducts.Where(s => !excludeProducts.Contains(s.GroupId)).ToList();
                        vm.Products = resultProducts;


                        break;

                    case "Select_Events_Genre_List" or "Select_Events_Genre_List_Multiple" or "Select_Events_Subgenre_List" or "Select_Events_Subgenre_List_Multiple":
                        //rempli la liste events
                        var genres = _eventGenreRepository.GetAll(structureId, langCode);
                        var resultGenres = _mapper.Map<List<EventGenreDTO>>(genres);
                        vm.Genres = resultGenres;

                        var eventsFuturs = _eventRepository.GetAllEventsAfterNow(structureId, langCode);
                        var resultEvents = _mapper.Map<List<EventDTO>>(eventsFuturs);
                        vm.EventsList = resultEvents;

                        break;

                    case "Select_Events_Group_List" or "Select_Events_Group_List_Multiple":
                        //rempli la liste des groupes de manfestations
                        var eventsGroups = _eventGroupRepository.GetAllWithTranslations(structureId, langCode);
                        var resultEventsGroups = _mapper.Map<List<EventsGroupsDTO>>(eventsGroups);
                        vm.EventsGroups = resultEventsGroups;
                        break;

                    case "Select_Events_List" or "Select_Events_List_Multiple":
                        var eventsGroupWithEvents = _eventGroupRepository.GetAllEventsGroupWithEventsForSelect(structureId, langCode);
                        var resultEventsGroupsWithEvents = _mapper.Map<List<EventsGroupsDTO>>(eventsGroupWithEvents);
                        vm.EventsGroups = resultEventsGroupsWithEvents;

                        break;

                    case "Select_Front_Title":
                        if (blockTypeId == (int)HomeModularBlockTypeEnum.EventsFeatured)
                        {
                            vm.TranslateFrontTitle = _translateManager.GetTranslationLikeKeyWord(structureId, "Widget_HomeModular_Block_EventsFeatured_Title_%", langCode);
                        }

                        if (blockTypeId == 15)
                        {
                            vm.TranslateFrontTitle = _translateManager.GetTranslationLikeKeyWord(structureId, "Widget_HomeModular_Block_ProductsFeatured_Title_%", langCode);
                        }

                        break;


                    case "Select_Front_MsgIntro":
                        if (blockTypeId == (int)HomeModularBlockTypeEnum.EventsFeatured)
                        {
                            vm.TranslateFrontMessageIntro = _translateManager.GetTranslationLikeKeyWord(structureId, "Widget_HomeModular_Block_EventsFeatured_MsgIntro_%", langCode);
                        }

                        if (blockTypeId == (int)HomeModularBlockTypeEnum.ProductsFeatured)
                        {
                            vm.TranslateFrontMessageIntro = _translateManager.GetTranslationLikeKeyWord(structureId, "Widget_HomeModular_Block_ProductsFeatured_MsgIntro_%", langCode);
                        }
                        break;

                    default:
                        break;
                }

            }
            return vm;
        }

        private void SetConfigValue(int structureId, ref IEnumerable<HomeModularBlockUserConfigDTO> homeModularEmplacementGroup, string langCode, bool isTeasing)
        {
            //Nom du groupe de manifestation
            homeModularEmplacementGroup
                .Where(buc => buc.BlockFunction.FunctionsId == (int)HomeModularFunctionsEnum.SelectEventsGroupList
                            && buc.BlockFunction.BlockTypeId == (int)HomeModularBlockTypeEnum.EventsGroup)
                .ToList()
                .ForEach(buctf =>
                {
                    if (int.TryParse(buctf.BlockUserConfigValue, out int eventGroupId))
                    {
                        var eventGroup = _eventGroupRepository.GetByIdWithTranslations(structureId, eventGroupId, langCode);
                        if (eventGroup != null)
                        {
                            buctf.BlockUserConfigValueText = eventGroup.TraductionManifestationGroupes?.FirstOrDefault(tmg => tmg.ManifGroupeId == eventGroupId)?.ManifGroupeNom ?? eventGroup.ManifGroupeNom;
                        }
                    }
                });

            //genres
            homeModularEmplacementGroup
           .Where(buc => buc.BlockFunction.FunctionsId == (int)HomeModularFunctionsEnum.SelectEventsGenreList
                            && buc.BlockFunction.BlockTypeId == (int)HomeModularBlockTypeEnum.EventsGenre)
           .ToList()
           .ForEach(buctf =>
           {
               if (int.TryParse(buctf.BlockUserConfigValue, out int genreId))
               {
                   var eventGenre = _eventGenreRepository.GetById(structureId, genreId, langCode);
                   if (eventGenre != null)
                   {
                       if (eventGenre.TraductionManifestationGenres?.FirstOrDefault() is not null)
                       {
                           buctf.BlockUserConfigValueText = eventGenre.TraductionManifestationGenres?.FirstOrDefault(tmg => tmg.Id == genreId)?.Nom;
                       }
                       else
                       {
                           buctf.BlockUserConfigValueText = eventGenre.Nom;
                       }
                   }
               }
           });


            //sous genres
            homeModularEmplacementGroup
              .Where(buc => buc.BlockFunction.FunctionsId == (int)HomeModularFunctionsEnum.SelectEventsSubgenreList
                            && buc.BlockFunction.BlockTypeId == (int)HomeModularBlockTypeEnum.EventsSubgenre)
              .ToList()
              .ForEach(buctf =>
              {
                  if (int.TryParse(buctf.BlockUserConfigValue, out int sousGenreId))
                  {
                      var eventSousGenre = _eventSousGenreRepository.GetById(structureId, sousGenreId, langCode);
                      if (eventSousGenre != null)
                      {
                          buctf.BlockUserConfigValueText = eventSousGenre.TraductionManifestationSousGenres?.FirstOrDefault(tmg => tmg.Id == sousGenreId)?.Nom ?? eventSousGenre.Nom;
                      }
                  }

              });


            //Produits
            homeModularEmplacementGroup
             .Where(buc => buc.BlockFunction.FunctionsId == (int)HomeModularFunctionsEnum.SelectProductsList
                            && buc.BlockFunction.BlockTypeId == (int)HomeModularBlockTypeEnum.ProductOne)
             .ToList()
             .ForEach(buctf =>
             {
                 if (int.TryParse(buctf.BlockUserConfigValue, out int productId))
                 {
                     var product = _productRepository.GetById(structureId, productId, langCode);
                     if (product != null)
                     {
                         buctf.BlockUserConfigValueText = product.TraductionProduits?.FirstOrDefault()?.ProduitNom ?? product.ProduitNom;
                     }
                 }
             });


            //Famille
            homeModularEmplacementGroup
            .Where(buc => buc.BlockFunction.FunctionsId == (int)HomeModularFunctionsEnum.SelectProductsFamilyList
                            && buc.BlockFunction.BlockTypeId == (int)HomeModularBlockTypeEnum.ProductsFamily)
            .ToList()
            .ForEach(buctf =>
            {
                if (int.TryParse(buctf.BlockUserConfigValue, out int familleId))
                {
                    var produitFamille = _boutiqueRepository.GetFamilleById(structureId, familleId);
                    buctf.BlockUserConfigValueText = produitFamille?.ProduitFamilleNom ?? "";
                }
            });

            //Sous Famille
            homeModularEmplacementGroup
            .Where(buc => buc.BlockFunction.FunctionsId == (int)HomeModularFunctionsEnum.SelectProductsSubfamilyList
                                && buc.BlockFunction.BlockTypeId == (int)HomeModularBlockTypeEnum.ProductsSubfamily)
            .ToList()
            .ForEach(buctf =>
            {
                if (int.TryParse(buctf.BlockUserConfigValue, out int sousFamilleId))
                {
                    var produitSousFamille = _boutiqueRepository.GetSousFamilleById(structureId, sousFamilleId);
                    buctf.BlockUserConfigValueText = produitSousFamille?.ProduitSousFamilleNom ?? "";
                }
            });

            //Manifestation
            homeModularEmplacementGroup
             .Where(buc => buc.BlockFunction.FunctionsId == (int)HomeModularFunctionsEnum.SelectEventsList
                                && buc.BlockFunction.BlockTypeId == (int)HomeModularBlockTypeEnum.EventOne)
             .ToList()
             .ForEach(buctf =>
             {
                 if (int.TryParse(buctf.BlockUserConfigValue, out int eventId))
                 {
                     var manifestation = _eventRepository.GetEventByIdAndLangCodeWithDependencies(structureId, eventId, langCode);
                     if (manifestation != null)
                     {
                         buctf.BlockUserConfigValueText = manifestation.TraductionManifestations?.FirstOrDefault()?.ManifestationNom ?? manifestation.ManifestationNom;

                         if (manifestation.Seances.All(s => s.SeanceDateFin < DateTime.Now))
                         {
                             buctf.ToDelete = true;
                         }
                         else
                         {
                             CheckAndInsertOpenDate(structureId);



                             //On regarde la date d'ouverture
                             var openDateOfThisEvent = _proprietesOfManifsRepository.GetPropertieOfManifsByManifIdAndRefCode(structureId, eventId, ProprietesOfManifsEnum.OpenDate.ToString());

                             //Date renseignée
                             if (openDateOfThisEvent is not null && !string.IsNullOrEmpty(openDateOfThisEvent.Valeur))
                             {
                                 if (isTeasing)
                                 {
                                     if (openDateOfThisEvent.Valeur.UnixTimeToDateTime() > DateTime.Now)
                                         buctf.IsShowComingSoon = true;
                                 }
                                 else
                                 {
                                     if (openDateOfThisEvent.Valeur.UnixTimeToDateTime() > DateTime.Now)
                                         buctf.ToDelete = true;
                                 }
                             }
                             // else //Date pas renseignée
                             // buctf.ToDelete = true;
                         }
                     }
                 }

             });

            //Seance
            homeModularEmplacementGroup
               .Where(buc => buc.BlockFunction.FunctionsId == (int)HomeModularFunctionsEnum.SelectSessionsList
                                    && buc.BlockFunction.BlockTypeId == (int)HomeModularBlockTypeEnum.EventOne)
               .ToList()
               .ForEach(buctf =>
               {
                   if (int.TryParse(buctf.BlockUserConfigValue, out int sessionId))
                   {
                       var seance = _eventRepository.GetInfosEventForHomeModular(structureId, sessionId, langCode);
                       if (seance != null)
                       {
                           buctf.BlockUserConfigValueText = seance.seance_date_deb.ToString("dd/MM/yyyy HH:mm");
                           if (seance.seance_date_fin < DateTime.Now)
                           {
                               buctf.ToDelete = true;
                           }
                       }
                   }
               });

            //Date 
            homeModularEmplacementGroup
               .Where(buc => buc.BlockFunction.FunctionsId == (int)HomeModularFunctionsEnum.InputEventDisplayDatePicker
                                        && buc.BlockFunction.BlockTypeId == (int)HomeModularBlockTypeEnum.EventOne)
               .ToList()
               .ForEach(buctf =>
               {
                   if (int.TryParse(buctf.BlockUserConfigValue, out int eventId))
                   {
                       buctf.BlockUserConfigValueText = string.Empty;
                       buctf.BlockUserConfigValue = string.Empty;
                       var result = _proprietesOfManifsRepository.GetPropertieOfManifsByManifIdAndRefCode(structureId, eventId, ProprietesOfManifsEnum.OpenDate.ToString());

                       if (result is not null)
                           buctf.BlockUserConfigValue = result.Valeur.UnixTimeToDateTime().ToString("dd/MM/yyyy HH:mm");

                       //buctf.BlockUserConfigValue = buctf.BlockUserConfigValue.UnixTimeToDateTime().ToString("dd/MM/yyyyy HH:mm");
                   }
               });
        }


        private void CheckAndInsertOpenDate(int structureId)
        {

            var checkOpenDateExist = _proprietesReferencesOfManifsRepository.GetAll(structureId).Any(v => v.Code.ToLower() == "opendate");

            if (!checkOpenDateExist)
            {


                ProprietesReferencesOfManifsEntity proprietesReferencesOfManifs = new ProprietesReferencesOfManifsEntity
                {
                    Code = "OpenDate",
                    Libelle = "Date d'ouverture"
                };

                _proprietesReferencesOfManifsRepository.Insert(structureId, proprietesReferencesOfManifs);
            }
        }


        /// <summary>
        /// Remplace la valeur du blockuserconfig sur le blocktye Input_Event_Display_Date_Picker
        /// enregistre la date dans la table proprietes_of_manifs
        /// </summary>
        /// <param name="structureId"></param>
        /// <param name="homeModularBlockEmplacements"></param>
        /// <returns></returns>
        public void SetDateOuvertureToPropertiesOfEvents(int structureId, ref List<HomeModularBlockEmplacementDTO> homeModularBlockEmplacements)
        {
            CheckAndInsertOpenDate(structureId);



            ////recupere la date de disponibilité pour un évenement
            IEnumerable<HomeModularBlockFunctionDTO> blockFunctionForEventDateBlockType = GetBlockFunctionsByBlockTypeIdWithDependencies(structureId, (int)HomeModularBlockTypeEnum.EventOne);


            //On select la fonction des events 
            HomeModularBlockFunctionDTO blockFunctionEventId = blockFunctionForEventDateBlockType.FirstOrDefault(bf => bf.FunctionsId.Equals((int)HomeModularFunctionsEnum.SelectEventsList));


            // On select la function de date de disponibilité pour une manifestation
            HomeModularBlockFunctionDTO blockFunctionEventDate = blockFunctionForEventDateBlockType.FirstOrDefault(bf => bf.FunctionsId.Equals((int)HomeModularFunctionsEnum.InputEventDisplayDatePicker));


            //On récupère les blocks de type eventOne
            var homeModularBlockEmplacementsOneEvents = homeModularBlockEmplacements.Where(bt => bt.BlockTypeId == (int)HomeModularBlockTypeEnum.EventOne).ToList();

           

            //On select le blockUserConfig en prenant blockFunctionEventDate (tous les blocs dont une date de dispo est configuré)
            var blocksFunctionsOfEventDateDispo = homeModularBlockEmplacements.Where(bt => bt.BlockTypeId == (int)HomeModularBlockTypeEnum.EventOne)
                                                .Select(bt => bt.HomeModularBlockUserConfigs.Where(buc => buc.BlockFunctionId == blockFunctionEventDate.BlockFunctionId)).ToList();


            var blockFunctionOfEvents = homeModularBlockEmplacements.Where(bt => bt.BlockTypeId == (int)HomeModularBlockTypeEnum.EventOne)
                                                   .Select(bt => bt.HomeModularBlockUserConfigs.Where(buc => buc.BlockFunctionId == blockFunctionEventId.BlockFunctionId));



            foreach (var homeModularBlockEmplacement in homeModularBlockEmplacementsOneEvents)
            {
                var eventId = blockFunctionOfEvents.SelectMany(buc => buc.Where(be => be.BlockEmplacementId == homeModularBlockEmplacement.Id)).FirstOrDefault().BlockUserConfigValue;

                var dateDispo = blocksFunctionsOfEventDateDispo.SelectMany(buc => buc.Where(be => be.BlockEmplacementId == homeModularBlockEmplacement.Id)).FirstOrDefault().BlockUserConfigValue;


                var propertieRef = _proprietesOfManifsRepository.GetPropertieOfManifsByManifIdAndRefCode(structureId, int.Parse(eventId), ProprietesOfManifsEnum.OpenDate.ToString());

                if (propertieRef is not null)
                {
                    //Si la propriete des manifestations existe 
                    //on regarde si la date est null alors on supprime la propriete  
                    if (dateDispo is null)
                    {
                        if (int.TryParse(eventId, out int ieventId))
                        {
                            if (ieventId > 0)
                                _proprietesOfManifsRepository.DeletePropertiesOfManif(structureId, ieventId, propertieRef.ProprieteRefId);
                        }
                    }
                    else
                    {

                        //sinon on met à jour
                        ProprietesOfManifsEntity proprietesOfManifs = new ProprietesOfManifsEntity()
                        {

                            ManifestationId = int.Parse(eventId),
                            Valeur = dateDispo.DateTimeToUnix().ToString(),
                            ProprieteRefId = propertieRef.ProprieteRefId

                        };
                        _proprietesOfManifsRepository.UpdatePropertiesOfManif(structureId, proprietesOfManifs);
                    }
                }
                else
                {
                    var proprietesReferencesOfManifs = _proprietesReferencesOfManifsRepository.GetPropertieReferenceOfManifsByRefCode(structureId, ProprietesOfManifsEnum.OpenDate.ToString());
                    if (proprietesReferencesOfManifs is not null && !string.IsNullOrEmpty(dateDispo) && homeModularBlockEmplacement.Id > 0)
                    {
                        ProprietesOfManifsEntity proprietesOfManifs = new ProprietesOfManifsEntity()
                        {

                            ManifestationId = int.Parse(eventId),
                            Valeur = dateDispo.DateTimeToUnix().ToString(),
                            ProprieteRefId = proprietesReferencesOfManifs.ProprieteRefId
                        };
                        _proprietesOfManifsRepository.InsertPropertiesOfManif(structureId, proprietesOfManifs);
                    }
                }

                homeModularBlockEmplacement.HomeModularBlockUserConfigs.Where(buc => buc.BlockFunctionId == blockFunctionEventDate.BlockFunctionId).FirstOrDefault().BlockUserConfigValue = eventId;


            }
        }
        #region Block Emplacement

        /// <summary>
        /// Liste des blocks type 
        /// </summary>
        /// <param name="structureId"></param>
        /// <returns></returns>
        public List<HomeModularBlockEmplacementDTO> GetHomeModularEmplacementGroupList(int structureId)
        {
            try
            {
                return _homeModularBlockEmplacementRepository.GetHomeModularEmplacementGroupList(structureId);
            }
            catch
            {
                throw;
            }
        }

        public bool DeleteHomeModularBlockEmplacement(int structureId, List<int> blockEmplacementIds)
        {
            try
            {

                foreach (var blockEmplacementId in blockEmplacementIds)
                {
                    _homeModularBlockEmplacementRepository.DeleteHomeModularBlockEmplacement(structureId, blockEmplacementId);
                }

                return true;

            }
            catch
            {
                return false;
            }
        }

        public bool InsertOrUpdateHomeModularBlockEmplacement(int structureId, List<HomeModularBlockEmplacementDTO> homeModularBlockEmplacements, bool isTeasing, string langCode)
        {
            try
            {

                var blockEmplacementsEntities = _mapper.Map<List<HomeModularBlockEmplacementEntity>>(homeModularBlockEmplacements);

                foreach (var homeModularBlockEmplacement in blockEmplacementsEntities)
                {
                    int blockEmplacementId = _homeModularBlockEmplacementRepository.InsertOrUpdateHomeModularBlockEmplacement(structureId, homeModularBlockEmplacement);

                    if (blockEmplacementId > 0)
                    {

                        foreach (var blockUserConfig in homeModularBlockEmplacement.HomeModularBlockUserConfigs)
                        {
                            _homeModularBlockUserConfigRepository.InsertBlockUserConfig(structureId, blockUserConfig, blockEmplacementId, homeModularBlockEmplacement.EmplacementId);
                        }
                    }
                }

                return true;
            }
            catch
            {
                return false;
            }
        }

        #endregion


        #region Block user config

        /// <summary>
        /// Check s'il y a au moins une config des blocs pour la structure
        /// </summary>
        /// <param name="structureId"></param>
        /// <param name="checkForAdmin"></param>
        /// <returns></returns>
        /// <exception cref="Exception"></exception>
        public bool CheckHomeModularBlockUserConfig(int structureId, int checkForAdmin = 0)
        {
            try
            {
                return _homeModularBlockUserConfigRepository.CheckHomeModularBlockUserConfig(structureId, checkForAdmin);
            }
            catch
            {
                throw;
            }
        }

        public bool InsertHomeModularBlockUserConfig(int structureId, List<HomeModularBlockUserConfigDTO> homeModularBlockUserConfigs, int blockEmplacementId, int emplacementId)
        {
            try
            {

                var blockEmplacementsEntities = _mapper.Map<List<HomeModularBlockUserConfigEntity>>(homeModularBlockUserConfigs);

                foreach (var blockUserConfig in blockEmplacementsEntities)
                {
                    _homeModularBlockUserConfigRepository.InsertBlockUserConfig(structureId, blockUserConfig, blockEmplacementId, emplacementId);

                }
                return true;
            }
            catch
            {
                return false;

            }
        }

        public List<HomeModularBlockUserConfigDTO> GetHomeModularBlockUserConfigList(int structureId)
        {
            try
            {
                return _homeModularBlockUserConfigRepository.GetHomeModularBlockUserConfigList(structureId);
            }
            catch
            {
                throw;
            }
        }



        public void UpdateHomeModular(int structureId, string langCode, List<HomeModularBlockEmplacementDTO> homeModularBlockEmplacements, HomeModularSettingsJson homeModularSetting, bool clearCache)
        {
            try
            {
                SetDateOuvertureToPropertiesOfEvents(structureId, ref homeModularBlockEmplacements);

                #region Carousel de manifestations
                var blockFunctionsForEventsFeaturesBlockType = GetBlockFunctionsByBlockTypeIdWithDependencies(structureId, (int)HomeModularBlockTypeEnum.EventsFeatured);

                var blockFunctions = blockFunctionsForEventsFeaturesBlockType.Where(bf => bf.FunctionsId.Equals((int)HomeModularFunctionsEnum.SelectEventsGenreListMultiple)
                                                                || bf.FunctionsId.Equals((int)HomeModularFunctionsEnum.SelectEventsSubgenreListMultiple)
                                                                || bf.FunctionsId.Equals((int)HomeModularFunctionsEnum.SelectEventsListMultiple)
                                                                ).ToList();



                var blockUserConfigs = homeModularBlockEmplacements.Where(be => be.BlockTypeId == (int)HomeModularBlockTypeEnum.EventsFeatured).SelectMany(be => be.HomeModularBlockUserConfigs);

                blockUserConfigs.ToList().ForEach(be =>
                {
                    var blockFunction = blockFunctions.Where(bf => bf.BlockFunctionId == be.BlockFunctionId).FirstOrDefault();
                    if (blockFunction is not null)
                    {
                        if (string.IsNullOrEmpty(be.BlockUserConfigValue))
                        {
                            be.BlockUserConfigValue = "all";
                        }
                    }
                });
                #endregion

                //#region Carousel de produits



                //var blockFunctionsForProductsFeaturesBlockType = GetBlockFunctionsByBlockTypeIdWithDependencies(structureId, (int)HomeModularBlockTypeEnum.ProductsFeatured);

                //var blockFunctionsOfProducts = blockFunctionsForProductsFeaturesBlockType.Where(bf => bf.FunctionsId.Equals((int)HomeModularFunctionsEnum.SelectProductsFamilyListMultiple)
                //                                                || bf.FunctionsId.Equals((int)HomeModularFunctionsEnum.SelectProductsSubfamilyListMultiple)
                //                                                || bf.FunctionsId.Equals((int)HomeModularFunctionsEnum.SelectProductsListMultiple)
                //                                                ).ToList();


                //var blockUserConfigsProducts = homeModularBlockEmplacements.Where(be => be.BlockTypeId == (int)HomeModularBlockTypeEnum.ProductsFeatured).SelectMany(be => be.HomeModularBlockUserConfigs);

                //blockUserConfigsProducts.ToList().ForEach(be =>
                //{
                //    var blockFunction = blockFunctionsOfProducts.Where(bf => bf.BlockFunctionId == be.BlockFunctionId).FirstOrDefault();
                //    if (blockFunction is not null)
                //    {
                //        if (string.IsNullOrEmpty(be.BlockUserConfigValue))
                //        {
                //            be.BlockUserConfigValue = "all";
                //        }
                //    }
                //});
                //#endregion


                var emplacementGroups = GetAllEmplacementGroups(structureId);

                if (emplacementGroups.Count() == 0)
                {
                    throw new ArgumentNullException("Aucune ligne dans la table emplacemetGroup chez le client");
                }


                var homeModularEmplacementGroups = GetHomeModularEmplacementGroupById(structureId, emplacementGroups.FirstOrDefault().Id, langCode, homeModularSetting.Teasing, clearCache);

                for (int i = 1; i <= homeModularEmplacementGroups.EmplacementCount; i++)
                {
                    //Liste des blocks emplacement dans la base de données pour cet emplacement
                    var homeModularOfThisEmplacementInDB = homeModularEmplacementGroups.HomeModularBlockEmplacements.Where(emp => emp.EmplacementId == i).ToList();

                    //Liste des blocks emplacement reçu dans le paramètre pour cet emplacement
                    var homeModularOfThisEmplacement = homeModularBlockEmplacements.Where(emp => emp.EmplacementId == i).ToList();

                    //Difference entre les 2 lsites de cet emplacement
                    List<int> blockEmplacementsIdToDelete = homeModularOfThisEmplacementInDB.Select(x => x.Id).Except(homeModularOfThisEmplacement.Select(y => y.Id)).ToList();

                    //Si un des blockemplacement à supprimer ne  se retrouve pas dans un autre blockemplacement alors on le supprime
                    if (blockEmplacementsIdToDelete.Count > 0 && homeModularBlockEmplacements.Where(x => blockEmplacementsIdToDelete.Any(y => y == x.Id)).FirstOrDefault() == null)
                    {
                        //Supprime les blocks emplacements qui ne sont plus configurés
                        DeleteHomeModularBlockEmplacement(structureId, blockEmplacementsIdToDelete);
                    }

                    if (homeModularOfThisEmplacement.Count() > 0)
                    {
                        //Ajoute ou modifie les blocks emplacements
                        InsertOrUpdateHomeModularBlockEmplacement(structureId, homeModularOfThisEmplacement, homeModularSetting.Teasing, langCode);
                    }

                }

                //string blocksCacheKey = $"blocks_{structureId}_{emplacementGroups.FirstOrDefault().Id}_{homeModularSetting.Teasing}_{langCode}";
                //ClearCache(blocksCacheKey);
            }
            catch (Exception ex)
            {
                throw;
            }
        }

        #endregion

        public bool ClearUploadedFile(int structureId, int clearAll)
        {

            var emplacementGroups = GetAllEmplacementGroups(structureId);
            int emplacementGroupId = emplacementGroups.FirstOrDefault().Id;

            //repertoire home/
            var homeImagesPath = Path.Combine(_configuration["Images:BaseImagesPhysicalPath"], "home");
            homeImagesPath = homeImagesPath.Replace("[structureId]", structureId.ToString("0000")).Replace("{structureId}", structureId.ToString("0000")).Replace("[\\plateformCode]", "\\INDIV");

            //repertoire home/emplacementid/
            var homeEmplacementImagesPath = Path.Combine(homeImagesPath, (emplacementGroupId > 0) ? emplacementGroupId.ToString() : string.Empty);

            //si le dossier home/ existe
            if (Directory.Exists(homeImagesPath))
            {
                //si le dossier home/emplacementid/ existe
                if (Directory.Exists(homeEmplacementImagesPath))
                {
                    List<string> imagesFilesInDirectory = Directory.GetFiles(homeEmplacementImagesPath).ToList();

                    if (clearAll == -1)
                    {
                        imagesFilesInDirectory = new List<string>();
                        var directories = Directory.EnumerateFileSystemEntries(homeImagesPath).ToList();
                        foreach (var directory in directories)
                        {
                            imagesFilesInDirectory.AddRange(Directory.GetFiles(Path.Combine(homeEmplacementImagesPath, directory)).ToList());
                        }
                    }

                    var homeEmplacementGroup = _homeModularRepository.GetEmplacementGroupByWithTwoDatabasesDepedencies(structureId, emplacementGroupId);

                    var homeModularBlocks = _mapper.Map<HomeModularEmplacementGroupDTO>(homeEmplacementGroup);

                    IEnumerable<HomeModularBlockUserConfigDTO> blockUserConfigOfUploadFunction = null;
                    IEnumerable<HomeModularBlockUserConfigDTO> blockUserConfigs = new List<HomeModularBlockUserConfigDTO>();


                    if (homeModularBlocks.HomeModularBlockEmplacements?.Count > 0)
                    {

                        blockUserConfigs = homeModularBlocks.HomeModularBlockEmplacements.SelectMany(x => x.HomeModularBlockUserConfigs);
                        //Liste des blocks user config sur la fonction 1 ==> upload 
                        blockUserConfigOfUploadFunction = blockUserConfigs
                                .Where(buc => buc.BlockFunction.FunctionsId == (int)HomeModularFunctionsEnum.UploadBackgroundImg)
                                .ToList();

                    }

                    //Liste des groupes emplacement
                    var homeModularBlockEmplacements = GetHomeModularEmplacementGroupList(structureId);


                    /*var homeBlockUserConfigsInDatabase = GetHomeModularBlockUserConfigList(structureId)
                                                                            .Where(f => f.BlockFunctionId == 1)
                                                                            .ToList();
                    */
                    if (clearAll > 0)
                    {
                        homeModularBlockEmplacements = homeModularBlockEmplacements.Where(eg => eg.HomeModularEmplacementGroupId == emplacementGroupId).ToList();
                        //var blockUserConfigOfUploadFunction = homeBlockUserConfigsInDatabase.Where(a => homeModularBlockEmplacements.Any(b => b.Id == a.BlockEmplacementId)).ToList();


                        if (blockUserConfigOfUploadFunction?.Count() >= 0)
                        {
                            foreach (var filePath in imagesFilesInDirectory)
                            {
                                if (blockUserConfigOfUploadFunction.Where(aa => aa.BlockUserConfigValue.Contains(Path.GetFileName(filePath))).Count() == 0)
                                {
                                    System.IO.File.Delete(filePath);
                                }

                                if (IsDirectoryEmpty(Path.GetDirectoryName(filePath)))
                                {
                                    Directory.Delete(Path.GetDirectoryName(filePath), true);
                                }
                            }
                        }
                    }
                    else
                    {
                        //var blockUserConfigOfUploadFunction = homeBlockUserConfigsInDatabase.Where(a => homeModularBlockEmplacements.Any(b => b.Id == a.BlockEmplacementId)).ToList();
                        if (blockUserConfigs.Count() > 0)
                        {
                            if (blockUserConfigOfUploadFunction?.Count() >= 0)
                            {
                                foreach (var filePath in imagesFilesInDirectory)
                                {
                                    //Récupère l'emplacement Id grâce au nom du répertoire
                                    var directoryName = Path.GetDirectoryName(filePath);
                                    DirectoryInfo dirInfo = new DirectoryInfo(directoryName);
                                    var directoryEmplacementGroupId = dirInfo.Name;

                                    if (int.TryParse(directoryEmplacementGroupId, out var dirEmplacementGroupId))
                                    {
                                        var homeModularBlockUsers = blockUserConfigOfUploadFunction.Where(fun => fun.BlockUserConfigValue.Contains(Path.GetFileName(filePath))).ToList();
                                        var homeModularBlockUsersOfEmplacementId = homeModularBlockUsers.Where(buc => homeModularBlockEmplacements.Any(be => be.Id == buc.BlockEmplacementId && be.HomeModularEmplacementGroupId == dirEmplacementGroupId)).FirstOrDefault();

                                        if (homeModularBlockUsersOfEmplacementId == null)
                                        {
                                            System.IO.File.Delete(filePath);
                                        }
                                    }

                                    if (IsDirectoryEmpty(Path.GetDirectoryName(filePath)))
                                    {
                                        Directory.Delete(Path.GetDirectoryName(filePath), true);
                                    }
                                }
                            }
                        }
                    }
                }

                //Check si le dossier home est vide 
                if (IsDirectoryEmpty(homeImagesPath))
                {
                    Directory.Delete(homeImagesPath, true);
                }
            }

            return true;
        }

        private bool IsDirectoryEmpty(string path)
        {
            return !Directory.EnumerateFileSystemEntries(path).Any();
        }

        //private void ClearCache(string cacheKey)
        //{

        //    var homeModularBlocksCache = _distributedCache.GetRecord<HomeModularEmplacementGroupDTO>(cacheKey);

        //    //var homeModularBlocksCache = _distributedCache.GetViaExterne(blocksCacheKey);
        //    if (homeModularBlocksCache != null)
        //    {
        //        _distributedCache.Remove(cacheKey);
        //    }
        //}

        //private object GetCache(string blocksCacheKey)
        //{
        //    //string blocksCacheKey = $"blocks_{structureId}_{emplacementGroupId}_{isTeasing}_{langCode}";
        //    return _distributedCache.GetRecord<object>(blocksCacheKey);

        //    /*
        //    var homeModularBlocksCache = _distributedCache.GetRecord<object>(blocksCacheKey);

        //    if (homeModularBlocksCache != null)
        //    {
        //        return homeModularBlocksCache;
        //    }
        //    return null;*/
        //}

    }
}
