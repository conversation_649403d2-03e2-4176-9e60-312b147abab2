﻿using AutoMapper;
using Core.Themis.Libraries.BLL.Interfaces;
using Core.Themis.Libraries.BLL.Managers;
using Core.Themis.Libraries.BLL.Products.Interfaces;
using Core.Themis.Libraries.BLL.WT.Interfaces;
using Core.Themis.Libraries.Data.DBContext;
using Core.Themis.Libraries.Data.DBContext.Interfaces;
using Core.Themis.Libraries.Data.Entities.WebTracing;
using Core.Themis.Libraries.Data.Repositories.Open.EventsSessions.Interfaces;
using Core.Themis.Libraries.Data.Repositories.Open.GestionPlaces.Interfaces;
using Core.Themis.Libraries.Data.Repositories.Open.Identities.Interfaces;
using Core.Themis.Libraries.Data.Repositories.Open.Orders.Interfaces;
using Core.Themis.Libraries.Data.Repositories.Open.PlaceObjects.Interfaces;
using Core.Themis.Libraries.Data.Repositories.Open.Produit;
using Core.Themis.Libraries.Data.Repositories.Open.Produit.Interfaces;
using Core.Themis.Libraries.Data.Repositories.Open.Sponsors.Interfaces;
using Core.Themis.Libraries.Data.Repositories.WebTracing.Interfaces;
using Core.Themis.Libraries.Data.Repositories.WebTracing.Panier.Interfaces;
using Core.Themis.Libraries.DTO;
using Core.Themis.Libraries.DTO.Enums;
using Core.Themis.Libraries.DTO.EventsSessions;
using Core.Themis.Libraries.DTO.Identity;
using Core.Themis.Libraries.DTO.Products;
using Core.Themis.Libraries.DTO.Seats;
using Core.Themis.Libraries.DTO.WTObjects;
using Core.Themis.Libraries.Utilities.Extensions;
using Core.Themis.Libraries.Utilities.Helpers;
using Core.Themis.Libraries.Utilities.Helpers.Interfaces;
using Core.Themis.Libraries.Utilities.Logging;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Data;
using Microsoft.Data.SqlClient;
using System.Linq;
using System.Threading.Tasks;

namespace Core.Themis.Libraries.BLL.WT
{
    public class BasketManager : IBasketManager
    {
        private readonly IMemoryCache _memoryCache;

        private readonly IMapper _mapper;
        private readonly IDbContext _dbContext;
        private readonly IRodrigueConfigIniDictionnary _configIniDictionnary;
        private readonly IConfiguration _config;

        private readonly ISessionManager _sessionManager;
        private readonly ISeatManager _seatManager;
        private readonly IGestionPlaceRepository _gestionPlaceRepository;
        private readonly IBasketRepository _basketRepository;
        private readonly ISponsorPanierEntreesRepository _sponsorPanierEntreesRepository;
        private readonly ISponsorRepository _sponsorRepository;
        private readonly IZoneRepository _zoneRepository;
        private readonly ISectionRepository _sectionRepository;
        private readonly IFloorRepository _floorRepository;
        private readonly IProduitRepository _produitRepository;
        private readonly IEventRepository _eventRepository;
        private readonly ISeatRepository _seatRepository;
        private readonly IIdentityRepository _identityRepository;
        private readonly IOrderRepository _orderRepository;
        private readonly IPanierProduitRepository _panierProduitRepository;
        private readonly IPanierEntreeRepository _panierEntreeRepository;
        private readonly IPanierEntreeAboRepository _panierEntreeAboRepository;
        private readonly IPanierProduitResaPropsRepository _panierProduitResaPropsRepository;

        public BasketManager(
            IRodrigueConfigIniDictionnary configIniDictionnary,
            IConfiguration config,
            IBasketRepository basketRepository,
            IMemoryCache memoryCache,

            IDbContext dbContext,
            ISessionManager sessionManager,
            ISeatManager seatManager,
            IMapper mapper,
            ISponsorPanierEntreesRepository sponsorPanierEntreesRepository,
            ISponsorRepository sponsorRepository,
            IGestionPlaceRepository gestionPlaceRepository,
            IZoneRepository zoneRepository,
            IFloorRepository floorRepository,
            ISectionRepository sectionRepository,
            IProduitRepository produitRepository,
            IEventRepository eventRepository,
            ISeatRepository seatRepository,
            IIdentityRepository identityRepository,
            IOrderRepository orderRepository,
            IPanierProduitRepository panierProduitRepository,
            IPanierEntreeRepository panierEntreeRepository,
            IPanierEntreeAboRepository panierEntreeAboRepository,
            IPanierProduitResaPropsRepository panierProduitResaPropsRepository)
        {
            _configIniDictionnary = configIniDictionnary;
            _config = config;
            _dbContext = dbContext;

            _basketRepository = basketRepository;
            _sponsorPanierEntreesRepository = sponsorPanierEntreesRepository;
            _sponsorRepository = sponsorRepository;
            _gestionPlaceRepository = gestionPlaceRepository;
            _memoryCache = memoryCache;
            _sessionManager = sessionManager;
            _seatManager = seatManager;
            _mapper = mapper;
            _zoneRepository = zoneRepository;
            _floorRepository = floorRepository;
            _sectionRepository = sectionRepository;
            _produitRepository = produitRepository;
            _eventRepository = eventRepository;
            _seatRepository = seatRepository;
            _identityRepository = identityRepository;
            _orderRepository = orderRepository;
            _panierProduitRepository = panierProduitRepository;
            _panierEntreeRepository = panierEntreeRepository;
            _panierEntreeAboRepository = panierEntreeAboRepository;
            _panierProduitResaPropsRepository = panierProduitResaPropsRepository;
        }

        private static readonly RodrigueNLogger RodrigueLogger = new();

        public enum etapeCreationCmd
        {
            MessageAttente = 0 // message en attente
            , PrisEnCompte = 1// debut
            , Termine = 2 // fini
            , RienAFaire = 3 // n'a rien fait (par exemple reflag des places sur un panier de réabonnement)
            , BloqueProcessSansErreur = 4 // le process bloque le process sans gererer d'erreur (par ex, l'étape paiement si Sharegroop & clé config.ini, le paiement est mis en attente d'un push de Sharegroop vers chez nous)
            , Echoue = 99 // en erreur
        }
        //TODO: mix avec GetBasketById => FillBasketInformations 
        /// <summary>
        /// ??????? mix avec GetBasketById => FillBasketInformations 
        /// </summary>
        /// <param name="structureId"></param>
        /// <param name="basket"></param>
        /// <param name="basketId"></param>
        private void SetChildrenBasket(int structureId, ref BasketDTO basket, int basketId)
        {
            List<PanierEntreeEntity> listPanierEntreesEntity = _basketRepository.GetPanierEntrees(structureId, basketId).ToList();

            List<SponsorPanierEntreesEntity> sponsorPanierEntrees = new();
            if (_sponsorPanierEntreesRepository.CheckSponsorPanierEntreesExist(structureId))
            {
                sponsorPanierEntrees = _sponsorPanierEntreesRepository.GetSponsorReferenceOfMyBasket(structureId, basketId).ToList();
            }

            foreach (var panierEntreeEntity in listPanierEntreesEntity)
            {
                BasketManagerHelper.AddEventUnitSales(basket, panierEntreeEntity);

                EventDTO evt = basket.ListEventsUnitSales.Where(evte => evte.EventId == panierEntreeEntity.ManifId).FirstOrDefault();

                BasketManagerHelper.AddBasketSession(ref evt, panierEntreeEntity);

                SessionDTO sessE = evt.ListSessions.Where(sesse => sesse.SessionId == panierEntreeEntity.SeanceId).FirstOrDefault();

                BasketManagerHelper.AddBasketZone(ref sessE, panierEntreeEntity);

                ZoneDTO zoneE = sessE.ListZones.Where(sesse => sesse.ZoneId == panierEntreeEntity.ZoneId).FirstOrDefault();

                BasketManagerHelper.AddBasketFloor(ref zoneE, panierEntreeEntity);

                FloorDTO floorE = zoneE.ListFloors.Where(floor => floor.FloorId == panierEntreeEntity.EtageId).FirstOrDefault();

                BasketManagerHelper.AddBasketSection(ref floorE, panierEntreeEntity);

                SectionDTO sectionE = floorE.ListSections.Where(floor => floor.SectionId == panierEntreeEntity.SectionId).FirstOrDefault();

                BasketManagerHelper.AddBasketCategorie(ref sectionE, panierEntreeEntity);

                CategoryDTO cateE = sectionE.ListCategories.Where(categ => categ.CategId == panierEntreeEntity.CategId).FirstOrDefault();

                BasketManagerHelper.AddBasketPrice(ref cateE, panierEntreeEntity);

                PriceDTO priceE = cateE.ListPrices.Where(categ => categ.PriceId == panierEntreeEntity.TypeTarifId).FirstOrDefault();

                if (priceE.ListGestionPlace == null)
                    priceE.ListGestionPlace = new List<GestionPlaceDTO>();

                priceE.ListSeats.Add(new SeatUnitSalesDTO()
                {
                    BasketLineId = int.Parse(panierEntreeEntity.PanierEntreeId.ToString()),

                    AmountTTCCents = int.Parse(panierEntreeEntity.Montant.ToString()),
                    FeesCents = int.Parse(panierEntreeEntity.Frais.ToString()),

                    EventId = panierEntreeEntity.ManifId,
                    EventName = panierEntreeEntity.ManifNom.ToString(),
                    SessionId = panierEntreeEntity.SeanceId,
                    SessionDescription = panierEntreeEntity.SeanceDescription.ToString(),
                    CategoryId = panierEntreeEntity.CategId,
                    CategoryName = panierEntreeEntity?.CategNom?.ToString(),

                    ZoneId = panierEntreeEntity.ZoneId == null ? 0 : (int)panierEntreeEntity.ZoneId,
                    FloorId = panierEntreeEntity.EtageId == null ? 0 : (int)panierEntreeEntity.EtageId,
                    SectionId = panierEntreeEntity.SectionId == null ? 0 : (int)panierEntreeEntity.SectionId,

                    SeatId = panierEntreeEntity.EntreeId,

                    SponsorReference = sponsorPanierEntrees.SingleOrDefault(sp => sp.PanierEntreeId == panierEntreeEntity.PanierEntreeId)?.ReferenceSponsor,

                    DeliveryTypeId = panierEntreeEntity.TypeEnvoiId == null ? 0 : (int)panierEntreeEntity.TypeEnvoiId,
                    DeliveryTypeName = panierEntreeEntity.TypeEnvoi,

                    RuleId = panierEntreeEntity.GestionPlaceId ?? 0,

                    Price = new PriceDTO()
                    {
                        PriceId = panierEntreeEntity.TypeTarifId,
                        PriceName = panierEntreeEntity.TypeTarifNom,
                        VtsId = panierEntreeEntity.VtsId ?? 0,
                        RuleId = panierEntreeEntity.GestionPlaceId == null ? 0 : (int)panierEntreeEntity.GestionPlaceId
                    }
                      ,
                    IIndex = panierEntreeEntity.Iindex == null ? 0 : (int)panierEntreeEntity.Iindex,
                    IsFreePlacement = (panierEntreeEntity.Rang == "LIBRE" && panierEntreeEntity.Siege == "LIBRE") ? true : false,
                    Rank = panierEntreeEntity.Rang,
                    Seat = panierEntreeEntity.Siege,
                    TypeSeat = panierEntreeEntity.TypeSiege,
                    MaquetteId = panierEntreeEntity.MaquetteId == null ? 0 : (int)panierEntreeEntity.MaquetteId,
                    ConsommateurIdentiteId = panierEntreeEntity.ConsumerId == null ? 0 : (int)panierEntreeEntity.ConsumerId
                });
            }

            IEnumerable<PanierProduitEntity> listPanierProduitsEntity = _basketRepository.GetPanierProducts(structureId, basketId);

            #region mode d'obtention

            if (basket.ListModeObtentionWT == null)
                basket.ListModeObtentionWT = new List<ProductMO>();

            IEnumerable<PanierProduitEntity> listPanierProduitsEntityMO = listPanierProduitsEntity.Where(p => p.TypeLigne == "MO");

            foreach (var panierProduitEntityMO in listPanierProduitsEntityMO)
            {
                ProductMO prodMO = new()
                {
                    ProductId = panierProduitEntityMO.ProduitId,
                    ProductName = panierProduitEntityMO.ProduitNom,

                    Count = panierProduitEntityMO.Nombre ?? 0,
                    AmountTTCCents = (int)panierProduitEntityMO.Montant,

                    BasketLineId = panierProduitEntityMO.PanierProduitId,
                };

                prodMO.EventId = panierProduitEntityMO.ManifId ?? 0;
                prodMO.SessionId = panierProduitEntityMO.SeanceId ?? 0;
                prodMO.DeliveryTypeId = panierProduitEntityMO.TypeEnvoiId ?? 0;
                prodMO.DeliveryTypeName = panierProduitEntityMO.TypeEnvoi ?? "";
                prodMO.MaquetteId = panierProduitEntityMO.MaquetteId ?? 0;
                prodMO.TypeLigne = panierProduitEntityMO.TypeLigne;
                prodMO.TypeInsertion = panierProduitEntityMO.TypeInsertion;

                basket.ListModeObtentionWT.Add(prodMO);
            }

            #endregion mode d'obtention

        }

        /// <summary>
        /// Check si les tarifs du paniers sont en mode CARTE AVANTAGE et dans ce cas on check si la ligne à bien été insérée dans dans la table 'sponsors_entrees'
        /// </summary>
        /// <param name="structureId"></param>
        /// <param name="basket"></param>
        /// <returns></returns>
        public bool CheckPriceAdhesions(int structureId, int basketId)
        {

            var basket = _basketRepository.GetBasketById(structureId, basketId);
            if (basket == null)
                return false;

            var sponsors = _sponsorRepository.GetSponsors(structureId).ToList();
            var lstPanierEntries = basket.Entries.Where(pe => sponsors.Any(sp => sp.TypeTarifs.Any(spt => spt.TypeTarifId == pe.TypeTarifId))).ToList();

            var sponsorPanierEntrees = _sponsorPanierEntreesRepository.GetAll(structureId);

            return lstPanierEntries.Select(pe => pe.PanierEntreeId).All(id => sponsorPanierEntrees.Any(spt => spt.PanierEntreeId == id));

        }

        public List<BasketLineDTO> GetWrongBasketLinesForAdhesion(int structureId, int basketId)
        {
            var basket = _basketRepository.GetBasketById(structureId, basketId);
            if (basket == null)
                throw new ArgumentNullException(nameof(basket), $"Le panier dont l'id {basketId} est null");

            var sponsors = _sponsorRepository.GetSponsors(structureId).ToList();
            var lstSeatUnitsWithSponsor = basket.Entries.Where(pe => sponsors.Any(sp => sp.TypeTarifs.Any(spt => spt.TypeTarifId == pe.TypeTarifId))).ToList();
            var lstSponsorOFMyBasket = _sponsorPanierEntreesRepository.GetSponsorReferenceOfMyBasket(structureId, basketId);

            if (lstSeatUnitsWithSponsor.Count() != lstSponsorOFMyBasket.Count())
                lstSeatUnitsWithSponsor = lstSeatUnitsWithSponsor.Where(p => !lstSponsorOFMyBasket.Any(l => p.PanierEntreeId == l.PanierEntreeId)).ToList();

            return _mapper.Map<List<BasketLineDTO>>(lstSeatUnitsWithSponsor);
        }

        public bool DeleteWrongBasketLineForAdhesion(int structureId, int basketId)
        {
            var wrongBasketLines = GetWrongBasketLinesForAdhesion(structureId, basketId);

            var result = true;
            foreach (var basketLineId in wrongBasketLines.Select(pe => pe.BasketLineId))
            {
                result = _basketRepository.DeleteSeatInBasketLine(structureId, basketLineId);

                if (!result)
                    return false;
            }
            return true;
        }

        /// <summary>
        /// Check du nombre de places prises dans le panier avec le nombre max de la règle de vente
        /// </summary>
        /// <param name="structureId"></param>
        /// <param name="basket"></param>
        /// <returns></returns>
        public bool CheckWebUserCanContinue(int structureId, BasketDTO basket)
        {
            if (basket == null) return false;

            //var grp = basket.ListSeatsUnitSales.GroupBy(gp => gp.RuleId).ToList();


            //var gg = GetBasketById(structureId, basket.BasketId);



            var unitSalesGroupedByRules = basket.ListAllSeatsUnitSales().GroupBy(sg => new { sg.RuleId }, (key, group) => new { GestionPlaceId = key.RuleId, Count = group.Count() });
            /*
            basket.ListEventsUnitSales
                .SelectMany(s => s.ListSessions
                    .SelectMany(seat => seat.ListSeats
                        .GroupBy(sg => new { sg.RuleId }, (key, group) => new { GestionPlaceId = key.RuleId, Count = group.Count() })
                        )
                    ).ToList();

            var unitSalesGroupedByRules = basket.ListSeatsUnitSales.GroupBy(seat => new { seat.RuleId}, (key, group) => new
            {
                GestionPlaceId = key.RuleId,
                Count = group.Count()
            }).ToList();
            */


            bool resultCheck = true;
            foreach (var item in unitSalesGroupedByRules)
            {
                var gestionPlace = _gestionPlaceRepository.GetById(structureId, item.GestionPlaceId);

                if (gestionPlace == null)
                    return false;

                if (item.Count > gestionPlace.NbMax)
                    resultCheck = false;
            }

            return resultCheck;


            /*
            var basket = GetBasketByWebUserIdAndBasketState(structureId, webUserId, "C");

            //Aucun panier on peut flagguer
            if (basket == null)
                return true;

            //on vient chercher la règle de vente pour le récupérer le nbmax
            var gestionPlace = _gestionPlaceRepository.GetGestionPlaceById(structureId, getionPlaceId);

            //Si aucune règle de vente => on ne flaggue pas
            if (gestionPlace == null)
                return false;

            //On ajoute les places à prendre à celle déjà dans le panier et on regarde si c'est inférieur ou égale au nb_max de la règle de vente 
            return (nbSeatsToTake + basket.ListSeatsUnitSales.Count) <= gestionPlace.NbMax;
            */
        }

        public BasketDTO? GetBasketByWebUserIdAndBasketState(int structureId, int webUserId, string basketState)
        {
            PanierEntity? panierEntity = _basketRepository.GetBasketByWebUserIdAndBasketState(structureId, webUserId, basketState);
            BasketDTO basket = _mapper.Map<BasketDTO>(panierEntity);

            if (basket == null)
                return null;

            SetChildrenBasket(structureId, ref basket, basket.BasketId);

            return basket;
        }

        public BasketDTO GetBasketById(int structureId, int basketId)
        {
            PanierEntity? panierEntity = _basketRepository.GetBasketById(structureId, basketId);


            //var bb = _basketRepository.GetAllBasketInfo(structureId, basketId, 0);

            BasketDTO basket = _mapper.Map<BasketDTO>(panierEntity);
            if (basket != null)
                SetChildrenBasket(structureId, ref basket, basketId);
            return basket;
        }

        /// <summary>
        /// Ajout une ligne panier_entree pour un sponsor
        /// </summary>
        /// <param name="structureId"></param>
        /// <param name="basketLineId"></param>
        /// <param name="sponsorReference"></param>
        /// <param name="sponsorId"></param>
        /// <returns></returns>
        public int AddSponsorSeatLine(int structureId, int basketLineId, string sponsorReference, int sponsorId)
        {
            return _sponsorPanierEntreesRepository.AddSponsorSeatLine(structureId, basketLineId, sponsorReference, sponsorId);
        }

        public int AddSeatInBasketLine(int structureId, int basketId, EventDTO evt, SessionDTO sess, SeatDTO s, GestionPlaceDTO gp, int identiteConsommateurId, string feedBookTokenIdentifiant = null)
        {

            PanierEntreeEntity basketLine = new PanierEntreeEntity()
            {
                PanierId = basketId,
                ManifId = evt.EventId,
                ManifNom = evt.EventName,
                SeanceId = sess.SessionId,
                SeanceDescription = sess.SessionDecription,
                EntreeId = s.SeatId,
                CategId = gp.CategoryId,
                CategNom = gp.CategoryName,
                ZoneId = s.ZoneId,
                EtageId = s.FloorId,
                SectionId = s.SectionId,
                TypeTarifId = gp.PriceId,
                TypeTarifNom = gp.Price.PriceName,
                GestionPlaceId = gp.GestionPlaceId,
                VtsId = gp.Price.VtsId,
                ConsumerId = identiteConsommateurId,
                Montant = gp.Price.UnitTTCAmount,
                Frais = gp.Price.UnitFeeAmount,
                Valeur = gp.Price.UnitValue,
                Iindex = s.IIndex,
                Rang = s.Rank,
                Siege = s.Seat,
                TypeSiege = s.TypeSeat is null ? "" : s.TypeSeat
            };

            return _basketRepository.InsertSeatInBasketLine(structureId, basketLine);

        }

        public bool UpdateConsumerPanierEntree(int structureId, int basketId, int consumId)
        {
            try

            {
                return _basketRepository.UpdateConsumerPanierEntree(structureId, basketId, consumId);
            }
            catch
            {
                throw;
            }
        }

        /// <summary>
        /// get panier WT à partir d'"un panier id et/ou web user id
        /// </summary>
        /// <param name="structureId"></param>
        /// <param name="orderId"></param>
        /// <param name="langCode"></param>
        /// <returns></returns>
        public BasketDTO GetBasketInfo(int structureId, int orderId)
        {
            try
            {
                return _basketRepository.GetBasketInfo(structureId, orderId);
            }
            catch
            {
                throw;
            }
        }

        public List<BasketDTO> GetAllBasketInfo(int structureId, int basketId, int webUserId)
        {
            try
            {
                var basketList = _basketRepository.GetAllBasketInfo(structureId, basketId, webUserId).ToList();

                //r actionPayementList = _basketRepository.GetActionsPayment(structureId, new List<int> { basketId });

                //var sss = _mapper.Map<List<ActionPaymentDTO>>(actionPayementList);

                //            basketList.Where(p => p.BasketId == basketId).ToList()[0].ListActionPayement = sss;



                //              var etapes = _basketRepository.GetLogsEtapesCreationCmds(structureId, new List<int> { basketId });

                //                var steps = _mapper.Map<List<StepPayment>>(etapes);



                //basketList.Where(p => p.BasketId == basketId).ToList()[0].ListActionPayement[0].ListStepsPayment = steps;

                return basketList;



            }
            catch
            {
                throw;
            }
        }


        /// <summary>
        /// retourne la liste des adhesions en cours (panier C)
        /// </summary>
        /// <param name="structureId"></param>
        /// <param name="listCustomerIds"></param>
        /// <returns></returns>
        public Dictionary<int, List<int>> CheckBasketAdhesions(int structureId, List<int> listCustomerIds)
        {
            try
            {
                return _basketRepository.CheckBasketAdhesions(structureId, listCustomerIds);
            }
            catch
            {
                throw;
            }
        }

        /// <summary>
        /// renseigne les infos depuis bdd Open
        /// </summary>
        /// <param name="structureId"></param>
        /// <param name="bask"></param>
        /// <param name="langCode"></param>
        public void FillFromOpen(int structureId, BasketDTO bask, string langCode)
        {
            try
            {
                List<ProductDTO> ListAllProducts = _produitRepository.LoadProducts(structureId, langCode);

                foreach (ProductFraisDossier prodF in bask.ListFrais)
                {
                    ProductDTO thisProd = ListAllProducts.Where(p => p.ProductId == prodF.ProductId).ToList().FirstOrDefault();
                    if (thisProd != null)
                    {
                        prodF.TvaName = thisProd.TvaName.Trim();
                        prodF.TvaTaux = thisProd.TvaTaux;
                    }
                }

                // renseigner les manifs :
                IEnumerable<int> uniqEvenId = from seat in bask.ListAllSeats() group seat by new { seat.EventId } into mygroup select mygroup.FirstOrDefault().EventId;
                if (uniqEvenId.Count() > 0)
                {
                    List<EventDTO> listEventsLoaded = _eventRepository.Load(structureId, uniqEvenId.ToList(), langCode);
                    EventManager.Fill(bask.ListEventsUnitSales, listEventsLoaded);
                }

                // renseigner les seances :
                IEnumerable<int> uniqSessionId = from seat in bask.ListAllSeats() group seat by new { seat.SessionId } into mygroup select mygroup.FirstOrDefault().SessionId;
                if (uniqSessionId.Count() > 0)
                {
                    string cachListZoneThesesSessionsIds = $"uniqSessionId[{structureId}.{string.Join("/", uniqSessionId.ToList())}.{langCode}]";
                    List<SessionDTO> listSession = new List<SessionDTO>();

                    if (!_memoryCache.TryGetValue(cachListZoneThesesSessionsIds, out listSession))// Look for cache key.
                    {
                        listSession = _sessionManager.Load(structureId, uniqSessionId.ToList(), langCode);

                        if (listSession != null && listSession.Count > 0)
                        {
                            var cacheEntryOptions = new MemoryCacheEntryOptions()
                               .SetPriority(CacheItemPriority.Normal)
                               .SetAbsoluteExpiration(TimeSpan.FromSeconds(120));
                            _memoryCache.Set(cachListZoneThesesSessionsIds, listSession, cacheEntryOptions);
                        }
                    }
                    foreach (EventDTO evt in bask.ListEventsUnitSales)
                    {
                        _sessionManager.Fill(evt.ListSessions, listSession);
                    }
                    // todo fill sessions abo, reabo, etc
                }

                IEnumerable<int> uniqZoneId = from seat in bask.ListAllSeats() group seat by new { seat.FloorId } into mygroup select mygroup.FirstOrDefault().FloorId;

                if (uniqZoneId.ToList().Count == 1 && uniqZoneId.ToList()[0] == 0)
                {

                }
                else
                {
                    if (uniqZoneId.Count() > 0)
                    {
                        string cachListZoneThesesZonesIds = $"uniqZoneId[{structureId}.{string.Join("/", uniqZoneId.ToList())}.{langCode}]";
                        List<ZoneDTO> listZones = new List<ZoneDTO>();
                        if (!_memoryCache.TryGetValue(cachListZoneThesesZonesIds, out listZones))// Look for cache key.
                        {
                            listZones = _zoneRepository.GetZonesByZonesList(structureId, uniqZoneId.ToList(), langCode);

                            if (listZones != null && listZones.Count > 0)
                            {
                                var cacheEntryOptions = new MemoryCacheEntryOptions()
                                   .SetPriority(CacheItemPriority.Normal)
                                   .SetAbsoluteExpiration(TimeSpan.FromSeconds(120));
                                _memoryCache.Set(cachListZoneThesesZonesIds, listZones, cacheEntryOptions);
                            }
                        }
                        foreach (EventDTO evt in bask.ListEventsUnitSales)
                        {
                            PlaceObjectManager.FillZones(evt.ListSessions, listZones);
                        }
                    }
                }

                IEnumerable<int> uniqFloorId = from seat in bask.ListAllSeats() group seat by new { seat.FloorId } into mygroup select mygroup.FirstOrDefault().FloorId;

                if (uniqFloorId.ToList().Count == 1 && uniqFloorId.ToList()[0] == 0)
                {

                }
                else
                {
                    if (uniqFloorId.Count() > 0)
                    {
                        List<FloorDTO> listFloors = _floorRepository.GetFloorsByFloorsList(structureId, uniqFloorId.ToList(), langCode);
                        foreach (EventDTO evt in bask.ListEventsUnitSales)
                        {
                            PlaceObjectManager.FillFloors(evt.ListSessions, listFloors);
                        }
                    }
                }
                IEnumerable<int> uniqSectionId = from seat in bask.ListAllSeats() group seat by new { seat.SectionId } into mygroup select mygroup.FirstOrDefault().SectionId;
                if (uniqSectionId.ToList().Count == 1 && uniqSectionId.ToList()[0] == 0)
                {

                }
                else
                {
                    if (uniqSectionId.Count() > 0)
                    {
                        List<SectionDTO> listSections = _sectionRepository.GetSectionsBySectionsList(structureId, uniqSectionId.ToList(), langCode);
                        foreach (EventDTO evt in bask.ListEventsUnitSales)
                        {
                            PlaceObjectManager.FillSections(evt.ListSessions, listSections);
                        }
                    }
                }

                // renseigner infos seats :
                IEnumerable<int> uniqManifId = from seat in bask.ListAllSeats() group seat by new { seat.EventId } into mygroup select mygroup.FirstOrDefault().EventId;

                foreach (int manifId in uniqManifId)
                {
                    List<SeatDTO> seatsThisManif = (from st in bask.ListAllSeats() where st.EventId == manifId select st).ToList();
                    List<int> seatsIdThisManif = (from st in bask.ListAllSeats() where st.EventId == manifId select st.SeatId).ToList();

                    List<SeatDTO> listEntreesrenseignees = _seatRepository.Get(structureId, manifId, seatsIdThisManif, langCode);

                    EventDTO evt = bask.ListEventsUnitSales.Where(e => e.EventId == manifId).FirstOrDefault();
                    if (evt != null)
                    {
                        SeatManager.Fill(evt.ListSessions, listEntreesrenseignees);
                    }
                }

                // renseigner les gestion place pour remonter les offres, adhesion, etc eventuellement liées :
                IEnumerable<int> uniqGpIds = from seat in bask.ListAllSeats() group seat by new { seat.RuleId } into mygroup select mygroup.FirstOrDefault().RuleId;
                if (uniqGpIds.Count() > 0)
                {
                    string cachListZoneThesesGpsIds = $"uniqGpIds[{structureId}.{string.Join("/", uniqGpIds.ToList())}]";
                    List<GestionPlaceDTO> listGps = new List<GestionPlaceDTO>();

                    if (!_memoryCache.TryGetValue(cachListZoneThesesGpsIds, out listGps))// Look for cache key.
                    {
                        listGps = _gestionPlaceRepository.Load(structureId, uniqGpIds.ToList());

                        if (listGps != null && listGps.Count > 0)
                        {
                            var cacheEntryOptions = new MemoryCacheEntryOptions()
                               .SetPriority(CacheItemPriority.Normal)
                               .SetAbsoluteExpiration(TimeSpan.FromSeconds(120));
                            _memoryCache.Set(cachListZoneThesesGpsIds, listGps, cacheEntryOptions);
                        }
                    }
                    foreach (EventDTO evt in bask.ListEventsUnitSales)
                    {
                        GestionPlaceManager.Fill(evt.ListSessions, listGps);
                    }
                    // todo fill sessions abo, reabo, etc
                }


                IEnumerable<int> uniqIdentiteIdsOfSeats = from seat in bask.ListAllSeats() group seat by new { seat.ConsommateurIdentiteId } into myidentites select myidentites.FirstOrDefault().ConsommateurIdentiteId;
                if (uniqIdentiteIdsOfSeats.Count() > 0)
                {
                    string cachListuniqThesesIdentiteIds = $"uniqIdentitesIds[{structureId}.{string.Join("/", uniqIdentiteIdsOfSeats.ToList())}]";
                    List<IdentityDTO> listIdentitesCardAdhesion = new List<IdentityDTO>();

                    if (!_memoryCache.TryGetValue(cachListuniqThesesIdentiteIds, out listIdentitesCardAdhesion))// Look for cache key.
                    {
                        listIdentitesCardAdhesion = _identityRepository.Load(structureId, uniqIdentiteIdsOfSeats.ToList(), "0", 0, langCode, "");

                        if (listIdentitesCardAdhesion != null && listIdentitesCardAdhesion.Count > 0)
                        {
                            var cacheEntryOptions = new MemoryCacheEntryOptions()
                               .SetPriority(CacheItemPriority.Normal)
                               .SetAbsoluteExpiration(TimeSpan.FromSeconds(120));
                            _memoryCache.Set(cachListuniqThesesIdentiteIds, listIdentitesCardAdhesion, cacheEntryOptions);
                        }
                    }

                    foreach (int manifId in uniqManifId)
                    {
                        EventDTO evt = bask.ListEventsUnitSales.Where(e => e.EventId == manifId).FirstOrDefault();
                        if (evt != null)
                        {
                            SeatManager.Fill(evt.ListSessions, listIdentitesCardAdhesion);

                        }
                    }
                }

                var identitiesListCartesAdh = bask.ListProductCartesAdhesion.Select(ca => ca.ConsommateurIdentiteId).ToList().Distinct();

                string cachUniqAdhesionCardIdentitesIds = $"uniqAdhesionCardIdentitesIds[{structureId}.{string.Join("/", identitiesListCartesAdh.ToList())}]";
                List<IdentityDTO> listIdentites = new List<IdentityDTO>();

                if (uniqIdentiteIdsOfSeats.Count() > 0) // des identites liées aux places 
                {
                    if (!_memoryCache.TryGetValue(cachUniqAdhesionCardIdentitesIds, out listIdentites))// Look for cache key.
                    {
                        listIdentites = _identityRepository.Load(structureId, uniqIdentiteIdsOfSeats.ToList(), "0", 0, langCode, "");

                        if (listIdentites != null && listIdentites.Count > 0)
                        {
                            var cacheEntryOptions = new MemoryCacheEntryOptions()
                               .SetPriority(CacheItemPriority.Normal)
                               .SetAbsoluteExpiration(TimeSpan.FromSeconds(120));
                            _memoryCache.Set(cachUniqAdhesionCardIdentitesIds, listIdentites, cacheEntryOptions);
                        }
                    }
                }

                List<IdentityDTO> listIdentitesOfCartes = _identityRepository.Load(structureId, identitiesListCartesAdh.ToList(), "0", 0, langCode, "");

                foreach (var cartAdh in bask.ListProductCartesAdhesion)
                {
                    var identiteOfThisAdhesionCard = listIdentitesOfCartes.Where(id => id.IdentiteId == cartAdh.ConsommateurIdentiteId).FirstOrDefault();
                    if (identiteOfThisAdhesionCard != null)
                    {
                        cartAdh.Identity = identiteOfThisAdhesionCard;
                    }
                }
            }
            catch (Exception ex)
            {
                RodrigueLogger.Error(structureId, $"BasketManager.FillFromOpen({structureId}, {bask.BasketId}, {langCode}) {ex.Message}\n{ex.StackTrace}");
                throw ex;
            }
        }

        /// <summary>
        /// call sp panier_transformator + sql panier_transform adhesion
        /// </summary>
        /// <param name="structureId"></param>
        /// <param name="basketId"></param>
        /// <returns></returns>
        public BasketDTO BasketTransformator(int structureId, int basketId, string langCode)
        {
            try
            {

                BasketDTO bask = new BasketDTO();
                List<BasketDTO> listasketR = GetAllBasketInfo(structureId, basketId, 0).ToList();
                if (listasketR != null && listasketR.Count == 1)
                {
                    bask = listasketR[0];
                }
                else
                    return null;

                if (_basketRepository.SP_PanierTransformatorExist(structureId))
                    bask = _basketRepository.TransformatorClassique(structureId, bask);

                bask = _basketRepository.TransformatorAddSupp(structureId, bask, langCode);

                return _basketRepository.TransformatorAdhesion(structureId, bask);
            }
            catch
            {
                throw;
            }
        }


        public int? GetCalculProductByStoredProcedure(int structureId, int basketId, int identiteId, int productId)
        {
            var basket = GetBasketById(structureId, basketId);
            if (basket == null)
                return null;

            var dataTableEntrees = GetEntriesDataTable(basket.ListAllSeats());
            var dataTableProduits = GetProductsDataTable(basket);


            var resultQuery = _basketRepository.GetProduitMontantCalculs(structureId, dataTableEntrees, dataTableProduits, identiteId, productId);
            if (resultQuery != null)
                return (int)resultQuery.montant;

            return null;
        }



        /// <summary>
        /// Créer un panier avec l'etat voulu ou le recupere ('C','R',...)
        /// </summary>
        /// <param name="structureId"></param>
        /// <param name="webUserId"></param>
        /// <param name="identiteId"></param>
        /// <param name="basketEtat"></param>
        /// <param name="orderId"></param>
        /// <returns></returns>
        public BasketDTO CreateBasketIfNotExists(int structureId, int webUserId, int identiteId, string basketEtat, int? orderId, string langCode = null)
        {
            try
            {
                var entity = _basketRepository.CreateBasketIfNotExists(structureId, webUserId, identiteId, basketEtat, orderId, langCode);
                return _mapper.Map<BasketDTO>(entity);
            }
            catch
            {
                throw;
            }
        }

        public BasketDTO GetActiveBasketByUserAndIdentity(int structureId, int webUserId, int identiteId)
        {
            try
            {
                var entity = _basketRepository.FindFirstOrDefault(b => b.WebUserId == webUserId && b.IdentiteId == identiteId && b.Etat == "C", structureId);

                return _mapper.Map<BasketDTO>(entity);
            }
            catch
            {
                throw;
            }
        }

        public int AddSeat(int structureId, int basketId, EventDTO evt, SessionDTO sess, SeatDTO s, GestionPlaceDTO gp, int identiteConsommateurId)
        {
            try
            {
                return _basketRepository.AddSeat(structureId, basketId, evt, sess, s, gp, identiteConsommateurId);
            }
            catch
            {
                throw;
            }
        }

        /// <summary>
        /// update l'identite id de [panier]
        /// </summary>
        /// <param name="structureId"></param>
        /// <param name="basketId"></param>
        /// <param name="identiteId"></param>
        /// <returns></returns>
        public int UpdateIdentiteId(int structureId, int basketId, int identiteId)
        {
            try
            {
                return _basketRepository.UpdateIdentiteId(structureId, basketId, identiteId);
            }
            catch
            {
                throw;
            }
        }

        public int UpdateEtatPanier(int structureId, int basketId, string etatBasket)
        {
            try
            {
                return _basketRepository.UpdateEtatPanier_IC_to_newState(structureId, basketId, etatBasket);
            }
            catch
            {
                throw;
            }
        }

        public int UpdateEtatPanier(int structureId, int basketId, List<string> etatsBasketInit, string etatBasket)
        {
            try
            {
                var ret = _basketRepository.UpdateEtatPanier(structureId, basketId, etatsBasketInit, etatBasket);
                if (etatBasket == "P" && ret > 0)
                {
                    return _basketRepository.UpdateDatePaiementPanier(structureId, basketId);
                }
                return ret;
            }
            catch
            {
                throw;
            }
        }

        public bool SetEtapeCreateCommande(int structureId, BasketDTO bask, string stepCode, EtapeCreationCmd debutOuFin, string comment)
        {
            try
            {
                return _basketRepository.SetEtapeCreateCommande(structureId, bask, stepCode, debutOuFin, comment);
            }
            catch
            {
                throw;
            }
        }

        public bool RestartStepPanier(int structureId, int basketId)
        {
            try
            {
                return _basketRepository.RestartStepPanier(structureId, basketId);
            }
            catch
            {
                throw;
            }
        }

        public int UpdateOrderId(int structureId, int basketId, int orderId)
        {
            try
            {
                return _basketRepository.UpdateOrderId(structureId, basketId, orderId);
            }
            catch
            {
                throw;
            }

        }

        #region Datatables

        public void GetAboEntriesDataTable(BasketDTO basket, ref DataTable entriesDataTable)
        {

            foreach (var formule in basket.ListAbonnementsMulti)
            {
                int priceId = formule.PriceId;
                foreach (var seat in formule.ListSeats)
                {

                    var row = entriesDataTable.NewRow();
                    row["entree_id"] = seat.SeatId;
                    row["manif_id"] = seat.EventId;
                    row["session_id"] = seat.SessionId;
                    row["type_tarif_id"] = priceId;
                    row["categorie_id"] = seat.CategoryId;
                    row["formule_id"] = seat.FormulaId;
                    row["maquette_id"] = seat.MaquetteId;
                    row["gestion_place_id"] = seat.RuleId;
                    entriesDataTable.Rows.Add(row);
                }
            }

        }

        public DataTable GetEntriesDataTable(List<SeatDTO> seats)
        {
            var seatsTable = new DataTable();

            seatsTable.Columns.Add("entree_id", typeof(int));
            seatsTable.Columns.Add("manif_id", typeof(int));
            seatsTable.Columns.Add("session_id", typeof(int));
            seatsTable.Columns.Add("type_tarif_id", typeof(int));
            seatsTable.Columns.Add("categorie_id", typeof(int));
            seatsTable.Columns.Add("formule_id", typeof(int));
            seatsTable.Columns.Add("maquette_id", typeof(int));
            seatsTable.Columns.Add("gestion_place_id", typeof(int));

            foreach (var seat in seats)
            {

                var row = seatsTable.NewRow();
                row["entree_id"] = seat.SeatId;
                row["manif_id"] = seat.EventId;
                row["session_id"] = seat.SessionId;
                row["type_tarif_id"] = seat.Price.PriceId;
                row["categorie_id"] = seat.CategoryId;
                row["formule_id"] = seat.FormulaId;
                row["maquette_id"] = seat.MaquetteId;
                row["gestion_place_id"] = seat.RuleId;
                seatsTable.Rows.Add(row);
            }

            return seatsTable;
        }

        public DataTable GetProductsDataTable(BasketDTO basket)
        {
            var productsTable = new DataTable();

            productsTable.Columns.Add("panier_produit_id", typeof(int));
            productsTable.Columns.Add("manif_id", typeof(int));
            productsTable.Columns.Add("session_id", typeof(int));
            productsTable.Columns.Add("produit_id", typeof(int));
            productsTable.Columns.Add("produit_nom", typeof(string));
            productsTable.Columns.Add("nombre", typeof(int));
            productsTable.Columns.Add("montant", typeof(int));
            productsTable.Columns.Add("frais", typeof(int));
            productsTable.Columns.Add("type_ligne", typeof(string));
            productsTable.Columns.Add("type_envoi", typeof(string));
            productsTable.Columns.Add("maquette_id", typeof(int));
            productsTable.Columns.Add("type_envoi_id", typeof(int));

            if (basket.ListProduitsWT != null && basket.ListProduitsWT.Count > 0)
            {
                foreach (ProductPur produit in basket.ListProduitsWT)
                {

                    var row = productsTable.NewRow();
                    row["panier_produit_id"] = produit.BasketLineId;
                    row["manif_id"] = produit.EventId;
                    row["session_id"] = produit.SessionId;
                    row["produit_id"] = produit.ProductId;
                    row["nombre"] = produit.Count;
                    row["montant"] = produit.AmountTTCCents;
                    row["frais"] = produit.FeesCents;
                    row["type_ligne"] = produit.TypeLigne;
                    row["type_envoi"] = produit.DeliveryTypeName;
                    row["maquette_id"] = produit.MaquetteId;
                    row["type_envoi_id"] = produit.DeliveryTypeId;
                    productsTable.Rows.Add(row);
                }


            }

            if (basket.ListModeObtentionWT != null && basket.ListModeObtentionWT.Count > 0)
            {
                foreach (ProductMO produit in basket.ListModeObtentionWT)
                {

                    var row = productsTable.NewRow();
                    row["panier_produit_id"] = produit.BasketLineId;
                    row["manif_id"] = produit.EventId;
                    row["session_id"] = produit.SessionId;
                    row["produit_id"] = produit.ProductId;
                    row["nombre"] = produit.Count;
                    row["montant"] = produit.AmountTTCCents;
                    row["frais"] = produit.FeesCents;
                    row["type_ligne"] = produit.TypeLigne;
                    row["type_envoi"] = produit.DeliveryTypeName;
                    row["maquette_id"] = produit.MaquetteId;
                    row["type_envoi_id"] = produit.DeliveryTypeId;
                    productsTable.Rows.Add(row);
                }
            }


            return productsTable;
        }

        #endregion

        public bool DelFraisDossier(int structureId, int basketId)
        {
            try
            {
                return _basketRepository.DelFraisDossier(structureId, basketId);
            }
            catch
            {
                throw;
            }
        }

        /// <summary>
        /// Supprime une liste de panier_produits_id pour un panier donné !! 
        /// </summary>
        /// <param name="structureId"></param>
        /// <param name="basketId"></param>
        /// <param name="listToBasketProductsId">panier produit id, les panier_produit_id NON les produit_id !!!</param>
        /// <returns></returns>
        public bool DeleteProduct(int structureId, int basketId, List<int> listToBasketProductsId)
        {
            try
            {
                return _basketRepository.DeleteProduct(structureId, basketId, listToBasketProductsId);
            }
            catch
            {
                throw;
            }
        }

        /// <summary>
        /// Supprime une liste de panier_produits_id pour un panier donné !! 
        /// </summary>
        /// <param name="structureId"></param>
        /// <param name="basketId"></param>
        /// <param name="listProductsId">produit_id</param>
        /// <returns></returns>
        public bool DeleteProductId(int structureId, int basketId, List<int> listProductsId)
        {
            try
            {
                return _basketRepository.DeleteProductId(structureId, basketId, listProductsId);
            }
            catch
            {
                throw;
            }
        }

        public bool AddFraisDossier(int structureId, int basketId, List<ProductDTO> listToAdd)
        {
            try
            {
                return _basketRepository.AddFraisDossier(structureId, basketId, listToAdd);
            }
            catch
            {
                throw;
            }
        }

        /// <summary>
        /// update le tarif, montant, etc des places indiv (call by panier transformator)
        /// </summary>
        /// <param name="cnxWT"></param>
        /// <param name="structureId"></param>
        /// <param name="basketId"></param>
        /// <param name="eventId"></param>
        /// <param name="sessionId"></param>
        /// <param name="seatId"></param>
        /// <param name="newPriceId"></param>
        /// <param name="newPriceName"></param>
        /// <param name="newMontantPayer"></param>
        /// <param name="newFrais"></param>
        /// <param name="newValeur"></param>
        /// <param name="newVtsId"></param>
        /// <returns></returns>
        public bool UpdateTarif(int structureId, int basketId, int eventId, int sessionId, int seatId, int newPriceId, string newPriceName, int newMontantPayer, int newFrais, int newValeur, int newVtsId)
        {
            try
            {
                return _basketRepository.UpdateTarif(structureId, basketId, eventId, sessionId, seatId, newPriceId, newPriceName, newMontantPayer, newFrais, newValeur, newVtsId);
            }
            catch
            {
                throw;
            }
        }

        public bool UpdatePanierStraightPriseEnCompte(int structureId, BasketDTO bask)
        {
            try
            {
                return _basketRepository.UpdatePanierStraightPriseEnCompte(structureId, bask);
            }
            catch
            {
                throw;
            }
        }

        /// <summary>
        /// delete panier_produit, les produits manifs / séance / globaux qui ne sont plus liés
        /// </summary>
        /// <param name="structureId"></param>
        /// <param name="basketId"></param>
        /// <returns></returns>
        public bool DeleteProduitsSeancesEventsGlobaux(int structureId, int basketId)
        {
            try
            {
                return _basketRepository.DeleteProduitsSeancesEventsGlobaux(structureId, basketId);
            }
            catch
            {
                throw;
            }
        }

        /// <summary>
        /// delete panier_produit les cartes d'adhesion qui ne sont plus liés 
        /// </summary>
        /// <param name="structureId"></param>
        /// <param name="basketId"></param>
        /// <returns></returns>
        public bool DeleteAllCartesAdhesion(int structureId, int basketId)
        {
            try
            {
                return _basketRepository.DeleteAllCartesAdhesion(structureId, basketId);
            }
            catch
            {
                throw;
            }
        }

        public bool DeleteAllObtentionModes(int structureId, int basketId)
        {
            try
            {
                return _basketRepository.DeleteAllObtentionModes(structureId, basketId);
            }
            catch
            {
                throw;
            }
        }

        /// <summary>
        /// s'il n'y a qu'un mode d'obtention possible pour ce panier / on le force 
        /// </summary>
        /// <param name="strucutreId"></param>
        /// <param name="basketId"></param>
        /// <returns></returns>
        public bool ForceModeObtention(int structureId, int basketId)
        {
            try
            {
                var deletedObtainingMode = DeleteAllObtentionModes(structureId, basketId);

                BasketDTO bask = GetBasketById(structureId, basketId);

                if (bask.ListModeObtentionWT.Any())
                    return false; // Règle déjà satisfaite, aucun traitement


                // =======> 
                List<int> lstGestionPlaceId = bask.ListAllSeats().Select(b => b.Price).Select(p => p.RuleId).ToList();

                // Récupérer les modes d'obtention communs pour toutes les regles de ventes du panier
                List<ProductDTO> lp = _produitRepository.LoadCommonsMOsForSeats(structureId, lstGestionPlaceId, new List<int> { }, "fr");

                if (lp.Count != 1) // un seul mode d'obtention => ok
                    return false; // Il faut qu'il n'y en ait qu'un


                var p = lp[0];

                p.TypeLigne = "MO";
                p.Count = 1;

                var lstGestionPlaceTypeEnvoi = _gestionPlaceRepository.LoadGpTypeEnvoi_byProductId(structureId, lstGestionPlaceId, p.ProductId);

                int insertResult = InsertPanierProduit(structureId, basketId, p);

                foreach (var gpte in lstGestionPlaceTypeEnvoi)
                {
                    int maquetteId = gpte.MaquetteBilletId ?? 0;
                    UpdateTypeEnvoiAndMaquette(structureId, basketId, new List<int> { gpte.GestionPlaceId }, maquetteId, gpte.ProductId);
                }

                return true;

            }
            catch
            {
                throw;
            }
        }


        /// <summary>
        /// update typeenvoi & maquette de chaque gp id
        /// </summary>
        /// <param name="structureId"></param>
        /// <param name="basketId"></param>
        /// <param name="listGestionPlaceId"></param>
        /// <param name="maquetteId"></param>
        /// <param name="typeEnvoiId"></param>
        /// <returns></returns>
        public bool UpdateTypeEnvoiAndMaquette(int structureId, int basketId, List<int> listGestionPlaceId, int maquetteId, int typeEnvoiId)
        {
            try
            {
                return _basketRepository.UpdateTypeEnvoiAndMaquette(structureId, basketId, listGestionPlaceId, maquetteId, typeEnvoiId);
            }
            catch
            {
                throw;
            }
        }

        public bool UpdateTypeEnvoiAndMaquetteProducts(int structureId, int basketId, List<int> ProductIds, int maquetteId, int typeEnvoiId)
        {
            try
            {
                return _basketRepository.UpdateTypeEnvoiAndMaquetteProducts(structureId, basketId, ProductIds, maquetteId, typeEnvoiId);
            }
            catch
            {
                throw;
            }
        }

        /// <summary>
        /// Insert dans Webtracing plusieurs lignes pour le même produit (cas : un billet par produit)
        /// </summary>
        /// <param name="structureId"></param>
        /// <param name="basketId"></param>
        /// <param name="oproduct"></param>
        /// <returns></returns>
        public void InsertMultipleLignesForTheSameProduct(int structureId, int basketId, ProductDTO oproduct)
        {
            int initialCount = oproduct.Count;

            for (int i = 1; i <= initialCount; i++)
            {
                oproduct.Count = 1;
                InsertPanierProduit(structureId, basketId, oproduct);
            }
        }

        public int InsertPanierProduit(int structureId, int basketId, ProductDTO oproduct)
        {
            try
            {
                return _basketRepository.InsertPanierProduit(structureId, basketId, oproduct);
            }
            catch
            {
                throw;
            }
        }

        public async Task<List<int>> GetProductIdsWithoutInsertionAuto(int structureId, int basketId, List<int> productsId)
        {

            try
            {
                var productsFromBasket = _basketRepository.GetPanierProducts(structureId, basketId);

                //select les produits AUTO (Type_insertion == AUTO) ==> qui ont été insérés depuis la procdure stockée PanierTransformator
                IEnumerable<int>? productsIdAutoFromBasket = productsFromBasket.Where(p => p.TypeInsertion?.ToUpper() == "AUTO").Select(p => p.ProduitId);

                //Supprime les produits de type AUTO de la liste des produits
                return productsId.Where(id => !productsIdAutoFromBasket.Contains(id)).ToList();

            }
            catch
            {

                throw;
            }

        }

        /// <summary>
        /// Supprime toutes les panier_entree where entree_id = xxx and seance_id = xxx
        /// </summary>
        /// <param name="structureId">structure id du client</param>
        /// <param name="sessionId">séance id</param>
        /// <param name="basketId">panier id</param>
        /// <returns></returns>
        public bool DeleteSeatsOfSessionBasket(int structureId, int sessionId, int basketId, List<int> seatsId)
        {
            try
            {
                return _basketRepository.DeleteEntreesIdOfSeance(structureId, sessionId, basketId, seatsId);

            }
            catch (Exception ex)
            {
                RodrigueLogger.Error(structureId, $"DeleteAllSessionsPanierEntree : {ex.Message} \n {ex.StackTrace}");
                throw;
            }
        }

        /// <summary>
        /// creation du panier à partir d'une commande Rodrigue
        /// </summary>
        /// <param name="structureId"></param>
        /// <param name="orderId"></param>
        /// <returns></returns>
        public async Task<string> InitUrlBasketForReservationPaymentAsync(int structureId, int orderId, int webUserId, int identiteId, string langCode)
        {
            var newBasketEntity = await GetNewBasketByOrderIdAsync(structureId, orderId).ConfigureAwait(false);

            if (newBasketEntity is null)
                throw new ArgumentNullException(nameof(newBasketEntity));

            newBasketEntity.WebUserId = webUserId;

            PanierEntity? initialBasketEntity = _basketRepository.FindCustomEntityBy(b => b.CommandeId == orderId, structureId)
                                                                 .Where(b => b.Etat == "VP")
                                                                 .Include(b => b.Entries)
                                                                 .Include(b => b.EntriesAbo)
                                                                 .Include(b => b.Products)
                                                                 .ToEntity();

            int? basketId = null;

            if (initialBasketEntity is null)
            {
                newBasketEntity.Etat = "VP";
                basketId = AddBasket(structureId, newBasketEntity);
            }
            else
            {
                bool isUpdated = UpdateBasket(structureId, initialBasketEntity, newBasketEntity);

                if (isUpdated)
                    basketId = initialBasketEntity.PanierId;
            }

            if (basketId is null)
                throw new ArgumentNullException(nameof(basketId));

            return UrlHelper.GetPaymentUrl(structureId, basketId.Value, identiteId, webUserId, langCode, "RESAWITHPROD");
        }

        private async Task<PanierEntity?> GetNewBasketByOrderIdAsync(int structureId, int orderId)
        {
            var orderEntity = _orderRepository.GetCustomEntityById(structureId, orderId)
                                            .Include(c => c.Lignes)
                                            .Include(c => c.CommandeInfos)
                                            .Include(c => c.DossiersProduits)
                                            .Include(c => c.Lignes.Select(l => l.Dossier))
                                            .Include(c => c.DossiersProduits.Select(dp => dp.Produit))
                                            .Include(c => c.DossiersProduits.Select(dp => dp.DossierProduitReservation))
                                            .Include(c => c.Lignes.Select(l => l.Dossier.Entrees))
                                            .Include(c => c.Lignes.Select(l => l.Dossier.Entrees.Select(e => e.Seance)))
                                            .Include(c => c.Lignes.Select(l => l.Dossier.Entrees.Select(e => e.Categorie)))
                                            .Include(c => c.Lignes.Select(l => l.Dossier.Entrees.Select(e => e.ReferenceLieuPhysique)))
                                            .Include(c => c.Lignes.Select(l => l.Dossier.Entrees.Select(e => e.Seance.Lieu)))
                                            .Include(c => c.Lignes.Select(l => l.Dossier.Entrees.Select(e => e.Seance.Manifestation)))
                                            .Include(c => c.Lignes.Select(l => l.Dossier.Entrees.Select(e => e.Seance.Manifestation.Structure)))
                                            .ToEntity();

            var newBasketEntity = _mapper.Map<PanierEntity>(orderEntity);

            if (newBasketEntity is not null)
            {
                newBasketEntity.StructureId = structureId;
                SetTypeEnvoiInfo(newBasketEntity);
            }

            return newBasketEntity;
        }


        private int AddBasket(int structureId, PanierEntity panierEntity)
        {
            using IDbContext dbContextTransactionScope = _dbContext.BeginTransaction(ContextType.WebTracing, structureId);

            try
            {
                int newBasketId = (int)_basketRepository.Insert(structureId, panierEntity);

                if (panierEntity.Entries is not null)
                {
                    foreach (var entree in panierEntity.Entries)
                    {
                        entree.PanierId = newBasketId;
                        bool entreeIsInserted = _panierEntreeRepository.InsertWithoutIdReturn(structureId, entree);

                        if (!entreeIsInserted)
                            throw new Exception("Entrie not insert in bdd");
                    }
                }

                if (panierEntity.EntriesAbo is not null)
                {
                    foreach (var entreeAbo in panierEntity.EntriesAbo)
                    {
                        entreeAbo.PanierId = newBasketId;
                        bool entreeAboIsInserted = _panierEntreeAboRepository.InsertWithoutIdReturn(structureId, entreeAbo);

                        if (!entreeAboIsInserted)
                            throw new Exception("Entrie not insert in bdd");
                    }
                }


                if (panierEntity.Products is not null)
                {
                    foreach (var product in panierEntity.Products)
                    {
                        product.PanierId = newBasketId;
                        int newProductId = (int)_panierProduitRepository.Insert(structureId, product);

                        product.PanierProduitId = newProductId;

                        if (newProductId == 0)
                            throw new Exception("Product not insert in bdd");

                        var produitResa = product.ResaProps;

                        if (product.TypeLigne == "PRESA" && produitResa is null)
                        {
                            PanierProduitResaPropsEntity newResaProps = new()
                            {
                                PanierProduitId = product.PanierProduitId,
                                DateOperation = DateTime.Now,
                                DateLimit = DateTime.Now.AddDays(1),
                            };

                            bool resaIsInserted = _panierProduitResaPropsRepository.InsertWithoutIdReturn(structureId, newResaProps);

                            if (!resaIsInserted)
                                throw new Exception("Product resa not insert in bdd");
                        }
                        else if (produitResa is not null)
                        {
                            produitResa.PanierProduitId = newProductId;

                            bool produitResaIsInserted = _panierProduitResaPropsRepository.InsertWithoutIdReturn(structureId, produitResa);

                            if (!produitResaIsInserted)
                                throw new Exception("Produit resa not insert in bdd");
                        }

                    }
                }

                dbContextTransactionScope.Commit();
                return newBasketId;
            }
            catch
            {
                dbContextTransactionScope.Rollback();
                throw;
            }
        }

        private bool UpdateBasket(int structureId, PanierEntity initialPanierEntity, PanierEntity newPanierEntity)
        {
            using IDbContext dbContextTransactionScope = _dbContext.BeginTransaction(ContextType.WebTracing, structureId);

            try
            {
                newPanierEntity.PanierId = initialPanierEntity.PanierId;
                bool basketIsUpdated = _basketRepository.Update(structureId, newPanierEntity);

                if (newPanierEntity.Entries is not null)
                {
                    if (initialPanierEntity.Entries is not null)
                    {
                        var entriesToDelete = initialPanierEntity.Entries.Where(e => !newPanierEntity.Entries.Any(ne => ne.EntreeId == e.EntreeId));

                        foreach (var entree in entriesToDelete)
                        {
                            bool entreeIsDeleted = _panierEntreeRepository.Delete(structureId, entree.PanierEntreeId);

                            if (!entreeIsDeleted)
                                throw new Exception("Entrie not delete in bdd");
                        }
                    }

                    foreach (var entree in newPanierEntity.Entries)
                    {
                        entree.PanierId = initialPanierEntity.PanierId;
                        var initialEntree = initialPanierEntity.Entries?.FirstOrDefault(e => e.EntreeId == entree.EntreeId);

                        if (initialEntree is not null)
                        {
                            entree.PanierEntreeId = initialEntree.PanierEntreeId;
                            bool entreeIsUpdated = _panierEntreeRepository.Update(structureId, entree);

                            if (!entreeIsUpdated)
                                throw new Exception("Entrie not update in bdd");
                        }
                        else
                        {
                            bool entreeIsInserted = _panierEntreeRepository.InsertWithoutIdReturn(structureId, entree);

                            if (!entreeIsInserted)
                                throw new Exception("Entrie not insert in bdd");
                        }
                    }
                }

                if (newPanierEntity.EntriesAbo is not null)
                {
                    if (initialPanierEntity.EntriesAbo is not null)
                    {
                        var entriesAboToDelete = initialPanierEntity.EntriesAbo.Where(e => !newPanierEntity.EntriesAbo.Any(ne => ne.EntreeId == e.EntreeId));

                        foreach (var entreeAbo in entriesAboToDelete)
                        {
                            bool entreeAboIsDeleted = _panierEntreeAboRepository.Delete(structureId, entreeAbo.PanierEntreeId);

                            if (!entreeAboIsDeleted)
                                throw new Exception("Entrie abo not delete in bdd");
                        }
                    }

                    foreach (var entreeAbo in newPanierEntity.EntriesAbo)
                    {
                        entreeAbo.PanierId = initialPanierEntity.PanierId;
                        var initialEntreeAbo = initialPanierEntity.EntriesAbo?.FirstOrDefault(e => e.EntreeId == entreeAbo.EntreeId);

                        if (initialEntreeAbo is not null)
                        {
                            entreeAbo.PanierEntreeId = initialEntreeAbo.PanierEntreeId;
                            bool entreeAboIsUpdated = _panierEntreeAboRepository.Update(structureId, entreeAbo);

                            if (!entreeAboIsUpdated)
                                throw new Exception("Entrie abo not update in bdd");
                        }
                        else
                        {
                            bool entreeAboIsInserted = _panierEntreeAboRepository.InsertWithoutIdReturn(structureId, entreeAbo);

                            if (!entreeAboIsInserted)
                                throw new Exception("Entrie abo not insert in bdd");
                        }
                    }
                }

                if (newPanierEntity.Products is not null)
                {
                    if (initialPanierEntity.Products is not null)
                    {
                        var productsToDelete = initialPanierEntity.Products.Where(p => !newPanierEntity.Products.Any(np => np.ProduitId == p.ProduitId));

                        foreach (var product in productsToDelete)
                        {
                            bool productIsDeleted = _panierProduitRepository.Delete(structureId, product.PanierProduitId);

                            if (!productIsDeleted)
                                throw new Exception("Product not delete in bdd");
                        }
                    }

                    foreach (var product in newPanierEntity.Products)
                    {
                        product.PanierId = initialPanierEntity.PanierId;
                        var initialProduct = initialPanierEntity.Products?.FirstOrDefault(p => p.ProduitId == product.ProduitId);

                        if (initialProduct is not null)
                        {
                            product.PanierProduitId = initialProduct.PanierProduitId;
                            bool productIsUpdated = _panierProduitRepository.Update(structureId, product);

                            if (!productIsUpdated)
                                throw new Exception("Product not update in bdd");
                        }
                        else
                        {
                            int newProductId = (int)_panierProduitRepository.Insert(structureId, product);

                            product.PanierProduitId = newProductId;

                            if (newProductId == 0)
                                throw new Exception("Product not insert in bdd");

                        }

                        if (product.TypeLigne == "PRESA" && (initialProduct is null || initialProduct.ResaProps is null))
                        {
                            PanierProduitResaPropsEntity newResaProps = new()
                            {
                                PanierProduitId = product.PanierProduitId,
                                DateOperation = DateTime.Now,
                                DateLimit = DateTime.Now.AddDays(1),
                            };

                            bool resaIsInserted = _panierProduitResaPropsRepository.InsertWithoutIdReturn(structureId, newResaProps);

                            if (!resaIsInserted)
                                throw new Exception("Product resa not insert in bdd");
                        }
                    }
                }

                dbContextTransactionScope.Commit();
                return true;
            }
            catch
            {
                dbContextTransactionScope.Rollback();
                throw;
            }
        }

        private static void SetTypeEnvoiInfo(PanierEntity panier)
        {
            var obtentionMode = panier.Products?.FirstOrDefault(p => p.TypeLigne == "MO");

            if (obtentionMode is not null && panier.Entries is not null)
            {
                foreach (var entree in panier.Entries)
                {
                    entree.TypeEnvoiId = obtentionMode.ProduitId;
                    entree.TypeEnvoi = obtentionMode.ProduitNom;
                }
            }
        }

        ICollection<BasketDTO> IBasketManager.GetBasketsToCreateCmd(int structureId, string status, string etape, int sincesecondes, int tosecondes, string? before)
        {

            var basketsInDB = _basketRepository.GetBasketsToCreateCmd(structureId, status, etape, sincesecondes, tosecondes, before);

            //var basketsInDBIds = basketsInDB.Select(ba => ba.PanierId).ToList();
            //var listActionsPayInDB = _basketRepository.GetActionsPayment(structureId, basketsInDBIds).Join(basketsInDB,actionPay =>actionPay.Panier_id,
            //    ba =>ba.PanierId,ba => ba.



            //,f;lsdnfs;ldf,ns


            return _mapper.Map<ICollection<BasketDTO>>(basketsInDB);
        }

        public void GetIsMontantCorrect(int structureId, BasketDTO bask)
        {
            _basketRepository.GetIsMontantCorrect(structureId, bask);
        }


    }
}