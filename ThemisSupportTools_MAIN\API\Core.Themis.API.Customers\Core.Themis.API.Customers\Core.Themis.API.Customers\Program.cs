
using AspNetCoreRateLimit;

using Core.Themis.API.Customers.Handlers;

using Core.Themis.Libraries.BLL.Extentions.ServicesBuilder;
using Core.Themis.Libraries.BLL.Services.RedisCache.Interfaces;
using Core.Themis.Libraries.BLL.Services.RedisCache;
using Core.Themis.Libraries.Utilities.Helpers;
using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Controllers;
using Microsoft.Extensions.Caching.Distributed;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Microsoft.IdentityModel.Tokens;
using Microsoft.OpenApi.Any;
using Microsoft.OpenApi.Interfaces;
using Microsoft.OpenApi.Models;

using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.IO;
using System.Linq;
using System.Reflection;
using System.Text;
using StackExchange.Redis;



var builder = WebApplication.CreateBuilder(args);

// Add services to the container.

builder.Services.AddCors(options =>
{
    options.AddDefaultPolicy(
        policy =>
        {

            policy.WithOrigins("https://dev.themisweb.fr", "https://externedemo.rodrigue-solutions.com"
                ).AllowAnyHeader();
        });
});

//builder.Services.AddControllers();
builder.Services.AddControllers().AddJsonOptions(options =>
{
    options.JsonSerializerOptions.PropertyNameCaseInsensitive = true;
});

// Learn more about configuring Swagger/OpenAPI at https://aka.ms/aspnetcore/swashbuckle
builder.Services.AddEndpointsApiExplorer();
builder.Services.AddSwaggerGen();

builder.Services.AddScoped<IAuthorizationHandler, RolesAuthorizationHandler>();

// Rodrigue injection
builder.Services.AddRodrigueDataServices();
builder.Services.AddRodrigueManager();
builder.Services.AddRodrigueMapper();


builder.Services.AddSingleton<IHttpContextAccessor, HttpContextAccessor>();

builder.Services.AddRazorPages();


builder.Services.AddAuthentication(options =>
{
    options.DefaultScheme = JwtBearerDefaults.AuthenticationScheme;
})
.AddJwtBearer(options =>
{
    options.SaveToken = true;
    options.TokenValidationParameters = new TokenValidationParameters()
    {
        ValidateIssuer = true,
        ValidateAudience = true,
        ClockSkew = Debugger.IsAttached ? TimeSpan.Zero : TimeSpan.FromMinutes(10),
        ValidateLifetime = true,
        ValidateIssuerSigningKey = true,
        ValidIssuer = "rodrigue",
        ValidAudience = "ThemisAPI",
        IssuerSigningKey = new SymmetricSecurityKey(Encoding.UTF8.GetBytes(builder.Configuration["ApiSecretKey"]!))
    };
});




ConfigurationHelper.Initialize(builder.Configuration);

builder.Services.AddResponseCaching();

builder.Services.AddSingleton<IIpPolicyStore, MemoryCacheIpPolicyStore>();
builder.Services.AddSingleton<IRateLimitCounterStore, MemoryCacheRateLimitCounterStore>();
builder.Services.AddSingleton<IRateLimitConfiguration, RateLimitConfiguration>();
builder.Services.AddSingleton<IProcessingStrategy, AsyncKeyLockProcessingStrategy>();
builder.Services.AddInMemoryRateLimiting();


#region Redis cache
string instanceName = "Rodrigue_cache_" + Environment.GetEnvironmentVariable("ASPNETCORE_ENVIRONMENT") + ".";
builder.Services.AddRodrigueRedis(builder.Configuration.GetConnectionString("Redis")!, instanceName);

builder.Services.AddSingleton<IRedisCacheService>(sp =>
    new RedisCacheService(
        instanceName,
        sp.GetRequiredService<IDistributedCache>(),
        sp.GetRequiredService<IConnectionMultiplexer>()
    )
);
#endregion

// In-Memory Caching
builder.Services.AddMemoryCache();
builder.Services.AddResponseCaching();

builder.Services.InitHelpers();
builder.Services.ConfigureHelpers();

//builder.Services.AddAutoMapper(AppDomain.CurrentDomain.GetAssemblies());
builder.Services.AddSwaggerGen(opts => opts.EnableAnnotations());
builder.Services.AddSwaggerGen(options =>
{
    options.SwaggerDoc("v1", new OpenApiInfo
    {
        Version = "v1",
        Title = "Themis Consumers API",
        Description = "An API to consume Customers datas",
        //TermsOfService = new Uri("https://example.com/terms"),
        Contact = new OpenApiContact
        {

            Url = new Uri("https://www.rodrigue-solutions.com/")
        },



        //License = new OpenApiLicense
        //{
        //    Name = "Example License",
        //    Url = new Uri("https://example.com/license")
        //}
    });


    //options.SwaggerDoc("internal", new OpenApiInfo
    //{
    //    Version = "interne",
    //    Title = "Themis Integration API interne",
    //    Description = "An API to consume Themis datas",
    //    //TermsOfService = new Uri("https://example.com/terms"),
    //    Contact = new OpenApiContact
    //    {

    //        Url = new Uri("https://www.rodrigue-solutions.com/")
    //    },


    //    //License = new OpenApiLicense
    //    //{
    //    //    Name = "Example License",
    //    //    Url = new Uri("https://example.com/license")
    //    //}
    //});
    //options.DocumentFilter<ExcludeVersionFromSwagger>();

    //options.DocumentFilter<PublicApiFilter>();

    //options.SwaggerDoc("school", new OpenApiInfo
    //{
    //    Title = "School",
    //    Version = "v1",
    //    Description = "School stuff",
    //    Contact = new OpenApiContact
    //    {
    //        Name = "itsfinniii"
    //    }
    //});

    var xmlFilename = $"{Assembly.GetExecutingAssembly().GetName().Name}.xml";
    options.IncludeXmlComments(Path.Combine(AppContext.BaseDirectory, xmlFilename), includeControllerXmlComments: true);

    options.UseAllOfToExtendReferenceSchemas();

    //options.OrderActionsBy((apiDesc) => $"{apiDesc.ActionDescriptor.RouteValues["controller"]}");

    //options.SupportNonNullableReferenceTypes();

    //Assert.False(schema.Properties["StringWithRequiredDisallowNull"].Nullable);

    List<string> xmlFiles = Directory.GetFiles(AppContext.BaseDirectory, "*.xml", SearchOption.TopDirectoryOnly).ToList();
    xmlFiles.ForEach(xmlFile => options.IncludeXmlComments(xmlFile));

});

//builder.Services.AddSwaggerExamplesFromAssemblies(Assembly.GetEntryAssembly());


builder.Logging.ClearProviders();
builder.Logging.SetMinimumLevel(Microsoft.Extensions.Logging.LogLevel.Trace);

string environn = Environment.GetEnvironmentVariable("ASPNETCORE_ENVIRONMENT");

NLog.LogManager.Configuration.Variables.Add("environment", environn);

string vers = Directory.GetParent(AppContext.BaseDirectory).Name;
NLog.LogManager.Configuration.Variables.Add("version", vers);



//services.AddSwaggerGen(c =>
//{
//    c.SwaggerDoc("v1", new OpenApiInfo { Title = "Core.Themis.API.Offers", Version = "v1" });
//    var xmlFilename = $"{Assembly.GetExecutingAssembly().GetName().Name}.xml";
//    c.IncludeXmlComments(Path.Combine(AppContext.BaseDirectory, xmlFilename));
//});




var app = builder.Build();

// Configure the HTTP request pipeline.
//if (app.Environment.IsDevelopment())
{
    app.UseSwagger();
    app.UseSwaggerUI(c =>
    {
        c.SwaggerEndpoint("../swagger/v1/swagger.json", "v1");
        c.EnableDeepLinking();
        //c.SwaggerEndpoint("../swagger/internal/swagger.json", "internal");


    });
}



app.UseHttpsRedirection();



app.UseCors();

app.UseResponseCaching();

app.UseAuthentication();

app.UseAuthorization();

app.MapControllers();

app.Run();



