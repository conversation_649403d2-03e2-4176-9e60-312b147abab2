﻿using Core.Themis.Libraries.DTO.Translations;
using Core.Themis.Libraries.Utilities.ThemisSql;
using System;
using System.Collections.Generic;
using System.Data;
using Microsoft.Data.SqlClient;

namespace Core.Themis.Libraries.BLL.Translations
{
    public class TranslationsVariableManager
    {

        /// <summary>
        /// Liste des sections sur les termes 
        /// </summary>
        /// <param name="structureId"></param>
        /// <returns></returns>
        public static List<TranslationVariablesDTO> GetTranslationsVariablesList(int structureId, SqlConnection cnxWebLibrary, string scriptPathSqlCommons)
        {

            List<TranslationVariablesDTO> lstVariables = new List<TranslationVariablesDTO>();
            try
            {
                if (cnxWebLibrary.State == ConnectionState.Closed)
                    cnxWebLibrary.Open();
                //liste contenant le nom des variable
                string translationsAreasListSql = FilesForSqlRequestsManager.GetScript(structureId, "Translations\\Rodrigue\\", "getTranslationsVariablesList", scriptPathSqlCommons);

                using (SqlCommand cmd = new SqlCommand())
                {
                    cmd.Connection = cnxWebLibrary;
                    #region Rempli la liste des Variables
                    cmd.CommandText = translationsAreasListSql;

                    using (SqlDataReader reader = cmd.ExecuteReader())
                    {
                        while (reader.Read())
                        {
                            TranslationVariablesDTO variable = new TranslationVariablesDTO()
                            {
                                TranslationVariableId = reader.GetInt32(reader.GetOrdinal("id")),
                                TranslationVariableName = reader.GetString(reader.GetOrdinal("name")),
                                TranslationVariableDescription = reader.GetString(reader.GetOrdinal("description")),
                                TranslationVariableIsAutoCloseTag = reader.GetBoolean(reader.GetOrdinal("isAutoCloseTag")),

                            };

                            lstVariables.Add(variable);
                        }
                    }
                    #endregion
                }


                cnxWebLibrary.Close();
            }
            catch (Exception ex)
            {
                throw new Exception(ex.Message);
            }
            finally
            {

                if (cnxWebLibrary.State == ConnectionState.Open)
                    cnxWebLibrary.Close();
            }
            return lstVariables;

        }

        /// <summary>
        /// Ajoute une nouvelle variable
        /// </summary>
        /// <param name="structureId"></param>
        /// <param name="translationVariable"></param>
        /// <param name="cnxWebLibrary"></param>
        /// <param name="scriptPathSqlCommons"></param>
        /// <returns></returns>
        /// <exception cref="Exception"></exception>
        public static bool InsertTranslationVariable(int structureId, TranslationVariablesDTO translationVariable, SqlConnection cnxWebLibrary, string scriptPathSqlCommons)
        {
            bool result = false;
            try
            {
                if (cnxWebLibrary.State == ConnectionState.Closed)
                    cnxWebLibrary.Open();


                //liste contenant les traductions par défault dans Rodrigue
                string translationsSpecificsListSql = FilesForSqlRequestsManager.GetScript(structureId, "Translations\\Rodrigue\\", "insertTranslationsVariable", scriptPathSqlCommons);

                using (SqlCommand cmd = new SqlCommand())
                {
                    cmd.Parameters.Add(new SqlParameter("@pName", translationVariable.TranslationVariableName));
                    cmd.Parameters.Add(new SqlParameter("@pDescription", translationVariable.TranslationVariableDescription));
                    cmd.Parameters.Add(new SqlParameter("@pIsAutoCloseTag", translationVariable.TranslationVariableIsAutoCloseTag));

                    cmd.Connection = cnxWebLibrary;
                    cmd.CommandText = translationsSpecificsListSql;

                    //return (int)cmd.ExecuteScalar();
                    result = cmd.ExecuteNonQuery() == 1;
                }


                cnxWebLibrary.Close();

            }
            catch (Exception ex)
            {
                throw new Exception(ex.Message);
            }
            finally
            {
                if (cnxWebLibrary.State == ConnectionState.Open)
                    cnxWebLibrary.Close();

            }


            return result;

        }


        public static bool UpdateTranslationVariable(int structureId, TranslationVariablesDTO translationVariable, SqlConnection cnxWebLibrary, string scriptPathSqlCommons)
        {
            bool result = false;
            try
            {


                if (cnxWebLibrary.State == ConnectionState.Closed)
                    cnxWebLibrary.Open();


                //liste contenant les traductions par défault dans Rodrigue
                string translationsVariableSql = FilesForSqlRequestsManager.GetScript(structureId, "Translations\\Rodrigue\\", "updateTranslationsVariable", scriptPathSqlCommons);

                using (SqlCommand cmd = new SqlCommand())
                {
                    cmd.Parameters.Add(new SqlParameter("@pAreaId", translationVariable.TranslationVariableId));
                    cmd.Parameters.Add(new SqlParameter("@pName", translationVariable.TranslationVariableName));
                    cmd.Parameters.Add(new SqlParameter("@pDescription", translationVariable.TranslationVariableDescription));
                    cmd.Parameters.Add(new SqlParameter("@pIsAutoCloseTag", translationVariable.TranslationVariableIsAutoCloseTag));

                    cmd.Connection = cnxWebLibrary;
                    cmd.CommandText = translationsVariableSql;

                    //return (int)cmd.ExecuteScalar();
                    result = cmd.ExecuteNonQuery() == 1;
                }


                cnxWebLibrary.Close();

            }
            catch (Exception ex)
            {
                throw new Exception(ex.Message);
            }
            finally
            {
                if (cnxWebLibrary.State == ConnectionState.Open)
                    cnxWebLibrary.Close();

            }


            return result;

        }

        public static bool DeleteTranslationVariable(int structureId, int translationVariableId, SqlConnection cnxWebLibrary, string scriptPathSqlCommons)
        {
            bool result = false;
            try
            {


                if (cnxWebLibrary.State == ConnectionState.Closed)
                    cnxWebLibrary.Open();


                //liste contenant les traductions par défault dans Rodrigue
                string translationsSpecificsListSql = FilesForSqlRequestsManager.GetScript(structureId, "Translations\\Rodrigue\\", "deleteTranslationsVariable", scriptPathSqlCommons);

                using (SqlCommand cmd = new SqlCommand())
                {
                    cmd.Parameters.Add(new SqlParameter("@pVariableId", translationVariableId));

                    cmd.Connection = cnxWebLibrary;
                    cmd.CommandText = translationsSpecificsListSql;

                    //return (int)cmd.ExecuteScalar();
                    result = cmd.ExecuteNonQuery() == 1;
                }


                cnxWebLibrary.Close();

            }
            catch (Exception ex)
            {
                throw new Exception(ex.Message);
            }
            finally
            {
                if (cnxWebLibrary.State == ConnectionState.Open)
                    cnxWebLibrary.Close();

            }


            return result;

        }

        /// <summary>
        /// Ajoute une nouvelle liaison entre une variable et un fieldCode
        /// </summary>
        /// <param name="structureId"></param>
        /// <param name="fieldCodeId"></param>
        /// <param name="translationVariable"></param>
        /// <param name="cnxWebLibrary"></param>
        /// <param name="scriptPathSqlCommons"></param>
        /// <returns></returns>
        /// <exception cref="Exception"></exception>
        public static bool InsertTranslationFieldVariable(int structureId, int fieldCodeId, List<TranslationVariablesDTO> translationVariables, SqlConnection cnxWebLibrary, string scriptPathSqlCommons)
        {
            bool result = false;
            try
            {
                if (cnxWebLibrary.State == ConnectionState.Closed)
                    cnxWebLibrary.Open();

                foreach (var translationVariable in translationVariables)
                {
                    //liste contenant les traductions par défault dans Rodrigue
                    string translationsSpecificsListSql = FilesForSqlRequestsManager.GetScript(structureId, "Translations\\Rodrigue\\", "insertTranslationsFieldVariable", scriptPathSqlCommons);

                    using (SqlCommand cmd = new SqlCommand())
                    {
                        cmd.Parameters.Add(new SqlParameter("@pFieldId", fieldCodeId));
                        cmd.Parameters.Add(new SqlParameter("@pVariableId", translationVariable.TranslationVariableId));

                        cmd.Connection = cnxWebLibrary;
                        cmd.CommandText = translationsSpecificsListSql;

                        //return (int)cmd.ExecuteScalar();
                        result = cmd.ExecuteNonQuery() == 1;
                    }


                }



                cnxWebLibrary.Close();

            }
            catch (Exception ex)
            {
                throw new Exception(ex.Message);
            }
            finally
            {
                if (cnxWebLibrary.State == ConnectionState.Open)
                    cnxWebLibrary.Close();

            }


            return result;

        }

        /// <summary>
        /// Supprimes toutes les variables pour un id de champ donné
        /// </summary>
        /// <param name="structureId"></param>
        /// <param name="fieldCodeId"></param>
        /// <param name="cnxWebLibrary"></param>
        /// <param name="scriptPathSqlCommons"></param>
        /// <returns></returns>
        /// <exception cref="Exception"></exception>
        public static bool DeleteTranslationFieldVariable(int structureId, int fieldCodeId, SqlConnection cnxWebLibrary, string scriptPathSqlCommons)
        {
            bool result = false;
            try
            {
                if (cnxWebLibrary.State == ConnectionState.Closed)
                    cnxWebLibrary.Open();


                //liste contenant les traductions par défault dans Rodrigue
                string translationsSpecificsListSql = FilesForSqlRequestsManager.GetScript(structureId, "Translations\\Rodrigue\\", "deleteTranslationsFieldVariable", scriptPathSqlCommons);

                using (SqlCommand cmd = new SqlCommand())
                {
                    cmd.Parameters.Add(new SqlParameter("@pFieldId", fieldCodeId));

                    cmd.Connection = cnxWebLibrary;
                    cmd.CommandText = translationsSpecificsListSql;

                    //return (int)cmd.ExecuteScalar();
                    result = cmd.ExecuteNonQuery() > 0;
                }


                cnxWebLibrary.Close();

            }
            catch (Exception ex)
            {
                throw new Exception(ex.Message);
            }
            finally
            {
                if (cnxWebLibrary.State == ConnectionState.Open)
                    cnxWebLibrary.Close();

            }


            return result;

        }

    }
}
