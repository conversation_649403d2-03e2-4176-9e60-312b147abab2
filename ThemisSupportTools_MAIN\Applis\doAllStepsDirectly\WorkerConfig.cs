﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace doAllStepsDirectly
{
    public class WorkerConfig
    {
        public int DelaySeconds { get; set; }




        public string PathReflagApp       { get; set; }
        public string PathDoCommandeApp  { get; set; }
        public string PathDoPaiementApp   { get; set; }
     
        public string PathEditionApp      { get; set; }
        public string PathEmailtApp       { get; set; }
                                    

    }
}
