﻿using Core.Themis.Libraries.BLL.Services.Blazor;
using Microsoft.Extensions.DependencyInjection;

namespace Core.Themis.Libraries.BLL.Extentions.ServicesBuilder
{
    public static class RodrigueCommonBlazorServicesExtension
    {
        /// <summary>
        /// Extention method for add Pass culture dependancy in <see cref="IServiceCollection"/>
        /// </summary>
        /// <param name="services"></param>
        public static void AddRodrigueCommonBlazorServices(this IServiceCollection services)
        {
            services.AddTransient<WidgetModalService>();
        }
    }
}
