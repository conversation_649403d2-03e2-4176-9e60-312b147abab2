using Core.Themis.API.Offers.Handlers;
using Core.Themis.Libraries.BLL.Extentions.ServicesBuilder;
using Core.Themis.Libraries.BLL.Services.RedisCache.Interfaces;
using Core.Themis.Libraries.BLL.Services.RedisCache;
using Core.Themis.Libraries.Utilities.Logging;
using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Diagnostics;
using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Caching.Distributed;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Microsoft.IdentityModel.Tokens;
using Microsoft.OpenApi.Models;
using StackExchange.Redis;
using System;
using System.IO;
using System.Reflection;
using System.Text;

namespace Core.Themis.API.Offers
{
    public class Startup
    {
        private static readonly RodrigueNLogger logger = new();
        public Startup(IConfiguration configuration)
        {
            Configuration = configuration;
        }

        private IConfiguration Configuration { get; }

        // This method gets called by the runtime. Use this method to add services to the container.
        public void ConfigureServices(IServiceCollection services)
        {
            try
            {
                services.AddRodrigueManager();
                services.AddRodrigueDataServices();
                services.AddRodrigueMapper();

                //services.AddStackExchangeRedisCache(options => {
                //    options.Configuration = Configuration.GetConnectionString("Redis");
                //    options.InstanceName = "Rodrigue_cache_";
                //});

                services.AddMemoryCache();

                services.AddSingleton<IAuthorizationHandler, RolesAuthorizationHandler>();
                services.AddSingleton<IHttpContextAccessor, HttpContextAccessor>();


                #region Redis cache
                string instanceName = "Rodrigue_cache_" + Environment.GetEnvironmentVariable("ASPNETCORE_ENVIRONMENT") + ".";
                services.AddRodrigueRedis(Configuration.GetConnectionString("Redis")!, instanceName);

                services.AddSingleton<IRedisCacheService>(sp =>
                    new RedisCacheService(
                        instanceName,
                        sp.GetRequiredService<IDistributedCache>(),
                        sp.GetRequiredService<IConnectionMultiplexer>()
                    )
                );
                #endregion



                services.AddAuthentication(options =>
                {
                    options.DefaultScheme = JwtBearerDefaults.AuthenticationScheme;
                })
                .AddJwtBearer(options =>
                {
                    options.SaveToken = true;
                    options.TokenValidationParameters = new TokenValidationParameters()
                    {
                        ValidIssuer = "rodrigue",
                        ValidAudience = "ThemisAPI",
                        IssuerSigningKey = new SymmetricSecurityKey(Encoding.UTF8.GetBytes(this.Configuration["ApiSecretKey"]))
                    };
                    options.Authority
                        = "https://dev-damienbod.eu.auth0.com/";
                    options.Audience
                        = "https://auth0-api1";
                });

                services.AddResponseCaching();
                services.AddControllers();
                services.AddRazorPages();
                services.AddSwaggerGen(c =>
                {
                    c.SwaggerDoc("v1", new OpenApiInfo { Title = "Core.Themis.API.Offers", Version = "v1" });
                    var xmlFilename = $"{Assembly.GetExecutingAssembly().GetName().Name}.xml";
                    c.IncludeXmlComments(Path.Combine(AppContext.BaseDirectory, xmlFilename));
                });

                services.InitHelpers();
                services.ConfigureHelpers();
            }
            catch (Exception ex)
            {
                logger.Fatal(0, ex, "ConfigureServices");
            }
        }

        // This method gets called by the runtime. Use this method to configure the HTTP request pipeline.
        public void Configure(IApplicationBuilder app, IWebHostEnvironment env, ILoggerFactory ilogger)
        {
            app.UseExceptionHandler(a => a.Run(async context =>
            {
                var exceptionHandlerPathFeature = context.Features.Get<IExceptionHandlerPathFeature>();
                var exception = exceptionHandlerPathFeature.Error;

                logger.Fatal(0, exception, "ConfigureServices");

                await context.Response.WriteAsJsonAsync(new { error = exception.Message });
            }));

           // app.UseExceptionHandler("/Error");
            app.UseHsts();

            if (env.IsDevelopment())
            {
                DeveloperExceptionPageOptions options = new DeveloperExceptionPageOptions()
                {

                };
                app.UseDeveloperExceptionPage(options);
            }

            app.UseSwagger();
            app.UseSwaggerUI(c =>
            {
                c.RoutePrefix = "swagger";
                c.SwaggerEndpoint("v1/swagger.json", "process=" + env.EnvironmentName);
                c.ConfigObject.AdditionalItems.Add("syntaxHighlight", false); //Turns off syntax highlight which causing performance issues...
                c.ConfigObject.AdditionalItems.Add("theme", "agate"); //Reverts Swagger UI 2.x  theme which is simpler not much performance benefit...


            });


            NLog.LogManager.Configuration.Variables.Add("environment", Environment.GetEnvironmentVariable("ASPNETCORE_ENVIRONMENT"));
            string vers = Directory.GetParent(AppContext.BaseDirectory).Name;
            NLog.LogManager.Configuration.Variables.Add("version", vers);

            app.UseRouting();
            app.UseResponseCaching();

            app.UseAuthentication();

            app.UseAuthorization();

            // on ne verifie pas la signature dans l'api : le token vaut validation
            app.UseEndpoints(endpoints =>
            {
                endpoints.MapControllers();
            });
        }


    }
}
