﻿using Core.Themis.Libraries.BLL.Services.Access.Interfaces;
using Core.Themis.Libraries.BLL.Services.Access.Models;
using Core.Themis.Libraries.DTO.PassCulture;
using Microsoft.AspNetCore.Components;
using Microsoft.Extensions.Localization;
using Mono.Cecil;
using System;
using System.Collections.Generic;
using System.Linq;

namespace Core.Themis.Libraries.BLL.Services.Access;

public class TstAccessService : ITstAccessService
{
    public string UserName { get; private set; }
    public string[] ActiveDirectoryGroups { get; private set; }
    public List<TstAccessModel> TstAccess { get; private set; }
   private IStringLocalizer<Resource> _localizer { get; set; } = default!;

    public int StructureId { get; set; }

    public string StructureName { get; set; } = string.Empty;

    public TstAccessService(IStringLocalizer<Resource> localizer)
    {
        _localizer = localizer;
    }


    public void SetProperties(string userName, string[] activeDirectoryGroups, List<TstAccessModel> tstAccess)
    {

        UserName = userName;
        ActiveDirectoryGroups = activeDirectoryGroups;
        TstAccess = tstAccess;
    }
    public List<TstModuleModel> GetModules()
    {
        if (TstAccess is null)
        {
            throw new NullReferenceException("TstAccess is null");
        }

        var tstModules = TstAccess.Select(g => g.TsModuleModel);
        //return tstModules.Select(m => m.ModuleName).Distinct().ToList();
        return tstModules.DistinctBy(m => m.ModuleId).ToList();
    }

    public bool CheckAccessToModule(string moduleName)
    {
        return GetModules().Any(m => m.Name == moduleName);
    }

    public TstModuleModel? GetModuleByName(string moduleName)
    {
        return GetModules().FirstOrDefault(m => m.Name == moduleName);
    }

    public TstRoleModel? GetRoleByModuleName(string moduleName)
    {
        var matchingAccessEntries = TstAccess.Where(m => m.TsModuleModel.Name == moduleName);

        return matchingAccessEntries.OrderByDescending(t => t.TstRoleModel.Level)
                                    .Select(r => r.TstRoleModel)
                                    .FirstOrDefault();
    }


    public bool IsGranted(string moduleName)
    {
        return TstAccess.Any(a => a.TsModuleModel.Name == moduleName);
    }
    public string GetUserName()
    {
        if (!string.IsNullOrEmpty(UserName) && UserName.Contains('\\'))
        {
            return UserName.Split('\\').Last(); // Récupère la dernière partie après le '\'
        }
        return string.Empty;
    }

    public void SetStructureId(int structureId)
    {
        StructureId = structureId;
    }

    public void SetStructureName(string structureName)
    {
        StructureName = structureName;
    }

    public string GetActiveDirectoryGroupName(string groupName)
    {
        var group = ActiveDirectoryGroups.FirstOrDefault(g => g.Equals(groupName, StringComparison.OrdinalIgnoreCase));
        return group ?? string.Empty;
    }


    public List<TstToolModel> GetTools()
    {
        var allModules = GetModules();

        return new List<TstToolModel>
        {
            new TstToolModel
            {
                Id = "configuration",
                Name = _localizer["tst_tool_configuration_name"],
                Description = _localizer["tst_tool_configuration_description"],
                Icon = "bi bi-gear-fill",
                Color = "#0d6efd",
                Modules = allModules.Where(m => new[] {
                    "config-ini",
                    "appsettings-plateforms"
                }.Contains(m.Name)).ToList()
            },
            new TstToolModel
            {
                Id = "commerce",
                Name = _localizer["tst_tool_commerce_name"],
                Description = _localizer["tst_tool_commerce_description"],
                Icon = "bi bi-shop",
                Color = "#198754",
                Modules = allModules.Where(m => new[] {
                    "gestion-coupons-promo",
                    "modifier-temps-panier",
                    "preparation-mise-vente-checkwaitingpages",
                    "gestion-maquette-abo-fermer",
                    "transfere-pointage-photo"
                }.Contains(m.Name)).ToList()
            },
            new TstToolModel
            {
                Id = "roles",
                Name = _localizer["tst_tool_roles_name"],
                Description = _localizer["tst_tool_roles_description"],
                Icon = "bi bi-people-fill",
                Color = "#dc3545",
                Modules = allModules.Where(m => new[] {
                    "role-management"
                }.Contains(m.Name)).ToList()
            },
             new TstToolModel
            {
                Id = "parternaires",
                Name = _localizer["tst_tool_partners_name"],
                Description = _localizer["tst_tool_partners_description"],
                Icon = "bi bi-people-fill",
                Color = "#198754",
                Modules = allModules.Where(m => new[] {
                    "partners",
                    "liaison-acheteurr-revendeur"
                }.Contains(m.Name)).ToList()
            },
            new TstToolModel
            {
                Id = "traductions",
                Name = _localizer["tst_tool_traductions_name"],
                Description = _localizer["tst_tool_traductions_description"],
                Icon = "bi bi-file-text",
                Color = "#ffc107",
                Modules = allModules.Where(m => new[] {
                    "translations-terms"
                }.Contains(m.Name)).ToList()
            },
            new TstToolModel
            {
              Id = "widget-cross-selling",
              Name  = _localizer["gestion_widgets"],
              Description = _localizer["description_gestion_widgets"],
              Icon = "bi bi-cart-plus",
              Color = "#17a2b8",
              Modules = allModules.Where(m => new[] {
               "widget-waiting-list",
               "widget-catalogue-offre"
             }.Contains(m.Name)).ToList()
            },
            new TstToolModel
            {
                Id = "outils-dev",
                Name = _localizer["tst_tool_outils_dev_name"],
                Description = _localizer["tst_tool_outils_dev_description"],
                Icon = "bi bi-code-slash",
                Color = "#fd7e14",
                Modules = allModules.Where(m => new[] {
                    "preparation-mise-vente",
                    "gestion-webtracing",
                    "service-inclusion-exclusion"
                }.Contains(m.Name)).ToList()
            }

        };
    }

    public bool CanAccessTool(string toolId)
    {
        var tool = GetTools().FirstOrDefault(t => t.Id == toolId);
        return tool?.Modules.Any(m => CanAccessModule(m.Name)) ?? false;
    }

    private bool CanAccessModule(string moduleName)
    {
        var role = GetRoleByModuleName(moduleName);
        return role != null && role.CanRead;
    }

}




