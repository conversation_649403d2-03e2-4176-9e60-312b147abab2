﻿using Core.Themis.Libraries.DTO.Identity;
using Core.Themis.Libraries.DTO.InfoComps;
using Core.Themis.Libraries.DTO.InfoComps.ApiExchange.Response;
using Core.Themis.Libraries.DTO.InfoComps.Json;
using Core.Themis.Libraries.DTO.Insurance.Meetch.Subscription;
using Core.Themis.Libraries.DTO.Lookup;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace Core.Themis.Libraries.BLL.InfoComp.Interfaces;

public interface IInfoCompManager
{
    public bool UpdateStats(int structureId, string email, string eventName, string emailColumn, List<string> folders);

    Task<List<SelectLookup>> SelectLookupInfoGroupsAsync(int structureId);


    Task<List<InfoCompDTO>> GetInfoCompsByGoupIdAsync(int structureId, int infoCompGroupId);

    Task<InfoCompGroupDTO> GetInfoCompGroupByIdAsync(int structureId, int infoCompGroupId);

    Task<JsonInfoCompGroupSettings?> GetJsonInfoCompGroupSettingsAsync(int structureId);

    JsonInfoCompGroupSettings LoadJsonToObject(int structureId);

    Task<JsonInfoCompGroup> CreateJsonInfoCompGroupAsync(int structureId, int groupId);
    Task<InfoCompsResponse<bool>> SaveJsonInfoCompFileAsync(int structureId, JsonInfoCompGroupSettings jsonInfoCompGroup);
    
    bool Emailing_GroupExists(int structureId, string groupeId);
    bool Emailing_UpdateGroup(int structureId, string email, string emailColumn, string groupeId);


    Task<JsonInfoCompGroupSettings> PopulateAssignedInfoCompsAsync(int structureId, int identityId, JsonInfoCompGroupSettings jsonInfoCompGroupSettings);

    Task UpdateIdentityInfoCompAssignmentsAsync(int structureId, int identityId, JsonInfoCompGroupSettings settings);
}
