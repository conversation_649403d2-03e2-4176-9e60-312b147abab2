﻿using AutoMapper;
using Core.Themis.Libraries.BLL;
using Core.Themis.Libraries.BLL.AccessControl;
using Core.Themis.Libraries.BLL.AccessControl.Interfaces;
using Core.Themis.Libraries.BLL.Interfaces;
using Core.Themis.Libraries.BLL.Managers.Order.Interfaces;
using Core.Themis.Libraries.BLL.OrderDetails.Interfaces;
using Core.Themis.Libraries.DTO.AccessControl;
using Core.Themis.Libraries.DTO.exposedObjects;
using Core.Themis.Libraries.Utilities.Crypto;
using Core.Themis.Libraries.Utilities.Logging;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.Net.Http.Headers;

namespace Core.Themis.Api.External.Controllers
{

    //[Tags("99 Access Control")]
    //[ApiExplorerSettings(GroupName = "Access Control")]

    /// <summary>
    /// 99 access control : Work on bar codes : Check and overwrite
    /// </summary>
    [SwaggerControllerOrder(100)]
    [ApiController]
    [ApiExplorerSettings(GroupName = "ext")]
    public class AccessControlController : ControllerBase
    {

        private readonly IAccessControlManager _accessControlManager;
        private readonly IDossierManager _dossierManager;
        private readonly IOrderLineManager _orderLineManager;
        private readonly ISeatManager _seatManager;
        private readonly IMapper _mapper;

        private static readonly RodrigueNLogger Logger = new RodrigueNLogger();

        public AccessControlController(
            IConfiguration configuration,
            IMemoryCache memoryCache,
            IMapper mapper,
            IAccessControlManager accessControlManager,
            IDossierManager dossierManager,
            IOrderLineManager orderLineManager,
            ISeatManager seatManager
            )
        {
            _mapper = mapper;
            _accessControlManager = accessControlManager;
            _dossierManager = dossierManager;
            _orderLineManager = orderLineManager;
            _seatManager = seatManager;
        }

        /// <summary>
        /// Check validity of bar code
        /// </summary>
        /// <param name="structureId">The unique structure identifier</param>
        /// <param name="barCode">The bar code</param>
        /// <param name="eventId">The event identifier</param>
        /// <response code="404">Bar code not found</response>
        /// <response code="406">Bar code found but invalid</response>
        [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(Ticket))]
        [ProducesResponseType(StatusCodes.Status404NotFound, Type = typeof(ProblemDetails))]
        [ProducesResponseType(StatusCodes.Status406NotAcceptable)]
        [HttpGet]
        [Authorize(Roles = "Admin,AccessControl")]
        [Route("api/{structureId}/ticket/{barCode}/{eventId}")]
        public IActionResult CheckBarCode(int structureId, string barCode, int eventId)
        {
            Logger.Debug(structureId, $"CheckBarCode({structureId}) :{barCode}, {eventId}...");

            var accT = Request.Headers[HeaderNames.Authorization];

            if (!TokenManager.PartnerHasRight(structureId, accT))
                return Problem($"structureId {structureId} is not allowed", null, StatusCodes.Status401Unauthorized);

            try
            {

                int? eventIdBarCode = _accessControlManager.GetBarCodeManif(structureId, barCode);
                if (eventIdBarCode == null) // pas trouvé du tout
                {
                    Logger.Warn(structureId, $"CheckBarCode({structureId}) :{barCode}, {eventId} : not found");
                    return Problem($"barcode {barCode} not found", null, StatusCodes.Status404NotFound);
                }
                if (eventIdBarCode != eventId)
                {
                    Logger.Warn(structureId, $"CheckBarCode({structureId}) :{barCode}, {eventId} : event mismatch ({eventIdBarCode} != {eventId})");
                    return Problem($"event mismatch (evtid={eventIdBarCode})", null, StatusCodes.Status406NotAcceptable);
                }

                BarCodeDTO? barCodeEntity = _accessControlManager.GetBarCodeByBarCode(structureId, barCode, eventId);

                var result = AccessControlManager.CheckTicket(structureId, barCodeEntity, eventId);

                if (!result.IsSuccess)
                {
                    return Problem(
                       detail: result.ErrorMessage,
                       statusCode: (int)result.statusCode
                    );
                }


                Ticket ticket = _mapper.Map<Ticket>(barCodeEntity);

                Logger.Debug(structureId, $"CheckBarCode({structureId}) :{barCode}, {eventId} : ok (tnumber={ticket.TicketNumber})");

                return Ok(ticket);
            }
            catch (Exception ex)
            {
                Logger.Error(structureId, $"CheckBarCode({structureId}) :{barCode}, {eventId} {ex.Message} {ex.StackTrace}");
                return Problem(ex.Message, null, StatusCodes.Status500InternalServerError);
            }
        }


        /// <summary>
        /// Update bar code
        /// </summary>
        /// <param name="structureId">The unique structure identifier</param>
        /// <param name="barCode">The bar code</param>
        /// <param name="eventId">The event identifier</param>
        /// <param name="newTicket">New ticket object, contains {"barCode": "string"}</param>
        /// <returns>BarCodeEntity</returns>
        [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(Ticket))]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        [ProducesResponseType(StatusCodes.Status406NotAcceptable)]
        [HttpPut]
        [Authorize(Roles = "Admin,AccessControl")]
        // [Tags("School Endpoints")]
        //[ApiExplorerSettings(GroupName = "school")]
        [Route("api/{structureId}/ticket/{barCode}/{eventId}")]
        public IActionResult UpdateBarCode(int structureId, string barCode, int eventId, [FromBody] Ticket newTicket)
        {
            if (newTicket == null || string.IsNullOrWhiteSpace(newTicket.BarCode))
                return Problem($"newTicket.BarCode is null", null, StatusCodes.Status400BadRequest);

            Logger.Debug(structureId, $"UpdateBarCode({structureId}) :{barCode}, {eventId} => {newTicket.BarCode}...");

            var accT = Request.Headers[HeaderNames.Authorization];
            if (!TokenManager.PartnerHasRight(structureId, accT))
                return Problem($"structureId {structureId} is not allowed", null, StatusCodes.Status401Unauthorized);

            try
            {
                int partnerId = TokenManager.getPartnerIdFromToken(accT);


                if (newTicket.BarCode.Length > 50)
                    return Problem($"newTicket.BarCode length too long {newTicket.BarCode.Length}, max is 50c", null, StatusCodes.Status400BadRequest);

                var result = _accessControlManager.SetBarCode(newTicket.BarCode, barCode, eventId, structureId, partnerId.ToString());

                if (!result)
                    return Problem($"barcode {barCode} not updated", null, StatusCodes.Status404NotFound);
                else
                {
                    BarCodeDTO? barCodeEntity = _accessControlManager.GetBarCodeByBarCode(structureId, newTicket.BarCode, eventId);


                    if (barCodeEntity == null)
                    {
                        Logger.Warn(structureId, $"CheckBarCode({structureId}) :{barCode}, {eventId} : not found"); return Problem($"barcode {barCode} not found", null, StatusCodes.Status404NotFound);
                    }

                    if (barCodeEntity.Controlled)
                    {
                        Logger.Warn(structureId, $"CheckBarCode({structureId}) :{barCode}, {eventId} : controlled (tnumber={barCodeEntity.NumBillet})");
                        return Problem($"already controlled ({barCodeEntity.ControlledDate})", null, StatusCodes.Status406NotAcceptable);
                    }

                    if (barCodeEntity.TypeOperation != "E" && barCodeEntity.TypeOperation != "D")
                    {
                        Logger.Warn(structureId, $"CheckBarCode({structureId}) :{barCode}, {eventId} : not acceptable TypeOperation {barCodeEntity.TypeOperation}");
                        return Problem($"TypeOperation {barCodeEntity.TypeOperation} not acceptable", null, StatusCodes.Status406NotAcceptable);
                    }

                    if (barCodeEntity.EventId != eventId)
                    {
                        Logger.Warn(structureId, $"CheckBarCode({structureId}) :{barCode}, {eventId} : event mismatch ({barCodeEntity.EventId} != {eventId})");
                        return Problem($"event mismatch", null, StatusCodes.Status406NotAcceptable);
                    }
                    if (barCodeEntity.PaymentCb == 0)
                    {
                        Logger.Warn(structureId, $"CheckBarCode({structureId}) :{barCode}, {eventId} : payment method not allowed");
                        return Problem($" payment method not allowed", null, StatusCodes.Status406NotAcceptable);
                    }


                    string nameConsumer = "";
                    nameConsumer += string.IsNullOrEmpty(newTicket.ConsumerLastName) ? "" : newTicket.ConsumerLastName.Trim();
                    nameConsumer += " " + (string.IsNullOrEmpty(newTicket.ConsumerFirstName) ? "" : newTicket.ConsumerFirstName.Trim());
                    //nameConsumer += " " + (string.IsNullOrEmpty(newTicket.BuyerEmail) ? "" : newTicket.BuyerEmail.Trim());
                    nameConsumer = nameConsumer.Trim();


                    string dossierC = $"Reelax|{DateTime.Now.ToString("dd/MM/yy")}|{barCodeEntity.NumBillet}|{nameConsumer}|";

                    _dossierManager.UpdateDossierC(structureId, barCodeEntity.DossierId, barCodeEntity.EventId, dossierC);

                    _seatManager.SetControlAccess(structureId, barCodeEntity.EventId, barCodeEntity.SessionId, barCodeEntity.SeatId, 9);

                    /// icone = 2816 bloque le dossier : pas modifiable dans Rodrigue                        

                    // finalement on ne maj plus le dossier_icone de commande_ligne = ne plus verrouiller la commande:
                    //_orderLineManager.UpdateCommandeLigneIcone(structureId, barCodeEntity.DossierId, barCodeEntity.EventId, barCodeEntity.OrderId, 2816);

                    Ticket ticket = _mapper.Map<Ticket>(barCodeEntity);
                    return Ok(ticket);



                }




            }
            catch (Exception ex)
            {
                Logger.Error(structureId, $"CheckBarCode({structureId}) :{barCode}, {eventId} {ex.Message} {ex.StackTrace}");
                return Problem(ex.Message, null, StatusCodes.Status500InternalServerError);
            }
        }

        /// <summary>
        /// check validity of bar code
        /// </summary>
        /// <param name="structureId">structure Id</param>
        /// <param name="barCode">bar code</param>
        /// <param name="eventId">event id</param>
        /// <returns>BarCodeEntity</returns>
        [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(Ticket))]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        [ProducesResponseType(StatusCodes.Status406NotAcceptable)]
        [HttpGet]
        [Authorize(Roles = "Admin,AccessControl")]
        [Route("api/{structureId}/ticketOLD/{barCode}/{eventId}")]
        public IActionResult CheckBarCodeOLD(int structureId, string barCode, int eventId)
        {
            Logger.Debug(structureId, $"CheckBarCode({structureId}) :{barCode}, {eventId}...");

            var accT = Request.Headers[HeaderNames.Authorization];

            if (!TokenManager.PartnerHasRight(structureId, accT))
                return Problem($"structureId {structureId} is not allowed", null, StatusCodes.Status401Unauthorized);

            try
            {

                BarCodeDTO? barCodeEntity = _accessControlManager.GetBarCodeByBarCode(structureId, barCode, eventId);

                if (barCodeEntity == null)
                {
                    Logger.Warn(structureId, $"CheckBarCode({structureId}) :{barCode}, {eventId} : not found"); return Problem($"barcode {barCode} not found", null, StatusCodes.Status404NotFound);
                }

                if (barCodeEntity.Controlled)
                {
                    Logger.Warn(structureId, $"CheckBarCode({structureId}) :{barCode}, {eventId} : controlled (tnumber={barCodeEntity.NumBillet})");
                    return Problem($"already controlled ({barCodeEntity.ControlledDate})", null, StatusCodes.Status406NotAcceptable);
                }

                if (barCodeEntity.TypeOperation != "E" && barCodeEntity.TypeOperation != "D")
                {
                    Logger.Warn(structureId, $"CheckBarCode({structureId}) :{barCode}, {eventId} : not acceptable TypeOperation {barCodeEntity.TypeOperation}");
                    return Problem($"TypeOperation {barCodeEntity.TypeOperation} not acceptable", null, StatusCodes.Status406NotAcceptable);
                }


                if (barCodeEntity.PaymentCb == 0)
                {
                    Logger.Warn(structureId, $"CheckBarCode({structureId}) :{barCode}, {eventId} : payment method not allowed");
                    return Problem($" payment method not allowed", null, StatusCodes.Status406NotAcceptable);
                }
                Ticket ticket = _mapper.Map<Ticket>(barCodeEntity);
                return Ok(ticket);
            }
            catch (Exception ex)
            {
                Logger.Error(structureId, $"CheckBarCode({structureId}) :{barCode}, {eventId} {ex.Message} {ex.StackTrace}");
                return Problem(ex.Message, null, StatusCodes.Status500InternalServerError);
            }
        }
    }
}



