﻿using AutoMapper;
using Core.Themis.Libraries.BLL.Interfaces;
using Core.Themis.Libraries.Data.Entities.Open.Maquette;
using Core.Themis.Libraries.Data.Repositories.Open.Maquette.Interfaces;
using Core.Themis.Libraries.DTO;
using Core.Themis.Libraries.DTO.Lookup;
using Core.Themis.Libraries.Utilities.Helpers;
using Microsoft.Extensions.Caching.Memory;
using PdfSharpCore.Drawing;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Core.Themis.Libraries.BLL
{

    public class MaquetteManager : IMaquetteManager
    {
        private readonly IMapper _mapper;
        private readonly IMemoryCache _memoryCache;
        private readonly IMaquetteRepository _maquetteRepository;

        public MaquetteManager(
            IMapper mapper,
            IMemoryCache memoryCache,
            IMaquetteRepository maquetteRepository)
        {
            _mapper = mapper;
            _memoryCache = memoryCache;
            _maquetteRepository = maquetteRepository;
        }


        public async Task<IEnumerable<MaquetteBillet>> GetMaquetteBilletsWebByIdsAsync(int structureId, List<int> maquetteIds)
        {
            try
            {
                string memoryKey = $"GetAll_{structureId}";
                if (_memoryCache.TryGetValue(memoryKey, out IEnumerable<MaquetteBillet> result))
                    return result;

                var entities = await _maquetteRepository.GetAllAsync(structureId).ConfigureAwait(false);
                entities = entities.Where(m => m.Masquer == 'N');


                var maquetteFiltered = entities
                    .Where(m => m.TypeSupport == 4 && maquetteIds.Contains(m.MaquetteBilletId));


                var maquettes = _mapper.Map<IEnumerable<MaquetteBillet>>(maquetteFiltered);
                _memoryCache.Set(memoryKey, maquettes, TimeSpan.FromMinutes(10));
                return maquettes;
            }
            catch
            {
                throw;
            }
        }


        public async Task<IEnumerable<MaquetteBillet>> GetMaquetteBilletsByIdsAsync(int structureId, List<int> maquetteIds)
        {
            try
            {
                string memoryKey = $"GetAll_{structureId}";
                if (_memoryCache.TryGetValue(memoryKey, out IEnumerable<MaquetteBillet> result))
                    return result;

                var entities = await _maquetteRepository.GetAllAsync(structureId).ConfigureAwait(false);
                entities = entities.Where(m => m.Masquer == 'N');


                var maquetteFiltered = entities
                    .Where(m => maquetteIds.Contains(m.MaquetteBilletId));


                var maquettes = _mapper.Map<IEnumerable<MaquetteBillet>>(maquetteFiltered);
                _memoryCache.Set(memoryKey, maquettes, TimeSpan.FromMinutes(10));
                return maquettes;
            }
            catch
            {
                throw;
            }
        }

        /// <summary>
        /// recup la maquette de type coupe file
        /// </summary>
        /// <param name="structureId"></param>
        /// <returns></returns>
        public int GetMaquetteCoupeFileId(int structureId)
        {
            try
            {
                string memoryKey = $"GetMaquetteCoupeFileId_{structureId}";

                if (_memoryCache.TryGetValue(memoryKey, out int result))
                    return result;

                int id = _maquetteRepository.GetMaquetteCoupeFileId(structureId);

                _memoryCache.Set(memoryKey, id, TimeSpan.FromMinutes(10));

                return id;
            }
            catch
            {
                throw;
            }
        }

        /// <summary>
        /// recup la maquette de type coupe file
        /// </summary>
        /// <param name="structureId"></param>
        /// <returns></returns>
        public async Task<int> GetMaquetteCoupeFileIdAsync(int structureId)
        {
            try
            {
                string memoryKey = $"GetMaquetteCoupeFileId_{structureId}";

                if (_memoryCache.TryGetValue(memoryKey, out int result))
                    return result;

                int id = await _maquetteRepository.GetMaquetteCoupeFileIdAsync(structureId)
                                                  .ConfigureAwait(false);

                _memoryCache.Set(memoryKey, id, TimeSpan.FromMinutes(10));

                return id;
            }
            catch
            {
                throw;
            }
        }

        /// <summary>
        /// remplis l'objet MaquetteBillet
        /// </summary>
        /// <param name="structureId"></param>
        /// <param name="maquetteId"></param>
        /// <returns></returns>
        public MaquetteBillet? GetByIdWithDependancy(int structureId, int maquetteId)
        {
            try
            {
                string memoryKey = $"GetByIdWithDependancy_{structureId}_{maquetteId}";

                if (_memoryCache.TryGetValue(memoryKey, out MaquetteBillet? result))
                    return result;

                MaquetteBilletEntity entity = _maquetteRepository.GetByIdWithDependancy(structureId, maquetteId);
                var maquette = _mapper.Map<MaquetteBillet>(entity);

                _memoryCache.Set(memoryKey, maquette, TimeSpan.FromMinutes(10));

                return maquette;
            }
            catch
            {
                throw;
            }
        }

        /// <summary>
        /// remplis l'objet MaquetteBillet
        /// </summary>
        /// <param name="structureId"></param>
        /// <param name="maquetteId"></param>
        /// <returns></returns>
        public async Task<MaquetteBillet?> GetByIdWithDependancyAsync(int structureId, int maquetteId)
        {
            try
            {
                string memoryKey = $"GetByIdWithDependancy_{structureId}_{maquetteId}";

                if (_memoryCache.TryGetValue(memoryKey, out MaquetteBillet? result))
                    return result;

                var entity = await _maquetteRepository.GetByIdWithDependancyAsync(structureId, maquetteId)
                                                      .ConfigureAwait(false);

                var maquette = _mapper.Map<MaquetteBillet>(entity);

                _memoryCache.Set(memoryKey, maquette, TimeSpan.FromMinutes(10));

                return maquette;
            }
            catch
            {
                throw;
            }
        }


        public async Task<List<SelectLookup>> SelectLookupMaquettesAsync(int structureId)
        {
            try
            {
                var maquettes = await _maquetteRepository.GetAllAsync(structureId)
                                                         .ConfigureAwait(false);
              
                return maquettes.Select(m => new SelectLookup()
                {
                    Value = m.MaquetteBilletId.ToString(),
                    Libelle = $"{m.MaquetteBilletId.ToString()} - {m.Code}"
                })
                .ToList();
            }
            catch
            {
                throw;
            }
        }

        #region Style

        public XFont getXFont(int structureId, int policeNum, int size)
        {
            string FontName = GetFontName(structureId, policeNum);

            XFont xret = new XFont(FontName, size, GetFontStyle(policeNum));

            return xret;

        }

        public XFontStyle GetFontStyle(int policeNum)
        {
            XFontStyle ret;
            switch (policeNum)
            {
                case 2 or 3 or 20 or 22:
                    //return XFontStyle.Italic;
                    return XFontStyle.Bold;
                case 4 or 5:
                    return XFontStyle.Italic;
                case 6 or 7:
                    return XFontStyle.BoldItalic;
                default:
                    return XFontStyle.Regular;
                    break;

            }
        }

        public string GetFontName(int structureId, int policeNum)
        {

            string memoryKey = $"GetFontName{structureId}_{policeNum}";

            if (_memoryCache.TryGetValue(memoryKey, out string policeName))
                return policeName;

            var policeNameReq = _maquetteRepository.GetFontName(structureId, policeNum);

            _memoryCache.Set(memoryKey, policeNameReq, TimeSpan.FromSeconds(5));


            return policeNameReq;
        }

        #endregion

        #region Text

        public static string TAH_TexteVariableToTexteValue(string TexteWithVariable, DictionaryExtented Dico)
        {
            string newTexte = TexteWithVariable;
            int posDeb = 0;
            int posFin = 0;
            List<string> VarsToReplace = new List<string>();
            while (TexteWithVariable.IndexOf('{', posDeb) >= 0)
            {
                posDeb = TexteWithVariable.IndexOf('{', posDeb);
                posFin = TexteWithVariable.IndexOf('}', posDeb);

                string VarName = TexteWithVariable.Substring(posDeb + 1, posFin - posDeb - 1);
                VarsToReplace.Add(VarName);

                posDeb = posFin + 1;
            }
            foreach (string VarName in VarsToReplace)
            {
                string VarVal = "";
                if (Dico.TryGetValue(VarName, out VarVal))
                    newTexte = newTexte.Replace("{" + VarName + "}", VarVal);
            }

            return newTexte;
        }

        #endregion
    }
}
