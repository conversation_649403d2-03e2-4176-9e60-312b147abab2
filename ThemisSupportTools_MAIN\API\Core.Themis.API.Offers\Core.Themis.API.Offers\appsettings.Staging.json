{
  "Logging": {
    "LogLevel": {
      "Default": "Information",
      "Microsoft": "Warning",
      "Microsoft.Hosting.Lifetime": "Information"
    },
    "Console": {
      "LogLevel": {
        "Default": "Error",
        "Microsoft": "Error",
        "Core.Themis.API.Offers.Controllers": "Trace"
      }
    },
    "File": {
      "Default": "Information",
      "Microsoft": "Warning",
      "Microsoft.Hosting.Lifetime": "Information"
    },
    "RodrigueFileLogger": {
      "Options": {
        "FolderPath": "D:\\LOGS\\Webservices\\API\\TEST\\OFFERS",
        "FilePath": "log_{structureid}_{date}.log"
      },
      "LogLevel": {
        "Default": "Error",
        "Microsoft": "Error",
        "Core.Themis.API.Offers.Controllers": "Information"
      }
    }

  },
  "AllowedHosts": "*",
  "ApiSecretKey": "secret401b09eab3c013d4ca54922bb802bec8fd5318192b0a75f201d8b3727429090fb337591abd3e44453b954555b7a0812e1081c39b740293f765eae731f5a65ed1",
  "ConnectionStrings": {
    "WsAdminDB": "Server=*************;Database=WSAdmin_test;Persist Security Info=True;User ID=SupportTest;Password=****************************************************************************************;MultipleActiveResultSets=true;TrustServerCertificate=True;",
    "GlobalOpinionDB": "Server=*************;Database=GlobalOpinions;Persist Security Info=True;User ID=SupportTest;Password=****************************************************************************************;MultipleActiveResultSets=true;TrustServerCertificate=True;",
    "WebLibraryDB": "Server=*************;Database=GlobalWebLibrary;Persist Security Info=True;User ID=SphereWebTest;Password=************************************************************************************************************;MultipleActiveResultSets=true;TrustServerCertificate=True;"
  },

  "Cache": {
    //Cache pour la liste des commentaires au tarif
    "CommentsTarifAbsoluteExpiration": 10,
    "CommentsTarifSlidingExpiration": 2,
    "StructurePrefsAbsoluteExpiration": 600

  },

  "ConfigIniPath": "\\\\Srv-paiement64\\customerfiles\\TEST\\[structureId]\\CONFIGSERVER\\config.ini.xml",
  "TypeRun": "TEST",
  "PathForSqlScript": "LIBRARIES_SQLSCRIPTS\\{0}\\{1}.sql",
  "WsAdminConnectionCache": 300,
  "Meetch": {
    "InsuranceSubscriptionURL": "https://api.preprod.ticketmate.io/v1/subscriptions",
    "InsuranceSubscriptionApiKey": "API-Key 962255c5ba186c0af27bcf69f38ecddac0da634d6a1c941d83a1709328a464f406081d0cb25b04ea9501ca509166f580b2581082e51f6e3c79c504f017cac63e"
  }
}
  