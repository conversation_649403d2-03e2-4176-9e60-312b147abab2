info: Microsoft.Hosting.Lifetime[0]
      Application started. Press Ctrl+C to shut down.
info: Microsoft.Hosting.Lifetime[0]
      Hosting environment: local
info: Microsoft.Hosting.Lifetime[0]
      Content root path: D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Intranet\ThemisSupportTools.Web\ThemisSupportTools.Web
ThemisSupportTools
ThemisSupportTools
ThemisSupportTools
ThemisSupportTools
ThemisSupportTools
ThemisSupportTools
ThemisSupportTools
ThemisSupportTools
ThemisSupportTools
ThemisSupportTools
ThemisSupportTools
ThemisSupportTools
ThemisSupportTools
ThemisSupportTools
ThemisSupportTools
ThemisSupportTools
ThemisSupportTools
ThemisSupportTools
ThemisSupportTools
ThemisSupportTools
ThemisSupportTools
ThemisSupportTools
ThemisSupportTools
warn: Microsoft.AspNetCore.Components.Server.Circuits.RemoteRenderer[100]
      Unhandled exception rendering component: Value cannot be null. (Parameter 'source')
      System.ArgumentNullException: Value cannot be null. (Parameter 'source')
         at System.Linq.ThrowHelper.ThrowArgumentNullException(ExceptionArgument argument)
         at System.Linq.Enumerable.Where[TSource](IEnumerable`1 source, Func`2 predicate)
         at ThemisSupportTools.Web.Components.Pages.Modules.PreparationMiseVente.CheckWaitingPages.get_FilteredInfos() in D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Intranet\ThemisSupportTools.Web\ThemisSupportTools.Web\Components\Pages\Modules\PreparationMiseVente\CheckWaitingPages.razor.cs:line 49
         at ThemisSupportTools.Web.Components.Pages.Modules.PreparationMiseVente.CheckWaitingPages.BuildRenderTree(RenderTreeBuilder __builder)
         at Microsoft.AspNetCore.Components.Rendering.ComponentState.RenderIntoBatch(RenderBatchBuilder batchBuilder, RenderFragment renderFragment, Exception& renderFragmentException)
fail: Microsoft.AspNetCore.Components.Server.Circuits.CircuitHost[111]
      Unhandled exception in circuit 'HMpBoKalDe5yLh7qidfRddz4hUOMYiMSOXbarElkrps'.
      System.ArgumentNullException: Value cannot be null. (Parameter 'source')
         at System.Linq.ThrowHelper.ThrowArgumentNullException(ExceptionArgument argument)
         at System.Linq.Enumerable.Where[TSource](IEnumerable`1 source, Func`2 predicate)
         at ThemisSupportTools.Web.Components.Pages.Modules.PreparationMiseVente.CheckWaitingPages.get_FilteredInfos() in D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Intranet\ThemisSupportTools.Web\ThemisSupportTools.Web\Components\Pages\Modules\PreparationMiseVente\CheckWaitingPages.razor.cs:line 49
         at ThemisSupportTools.Web.Components.Pages.Modules.PreparationMiseVente.CheckWaitingPages.BuildRenderTree(RenderTreeBuilder __builder)
         at Microsoft.AspNetCore.Components.Rendering.ComponentState.RenderIntoBatch(RenderBatchBuilder batchBuilder, RenderFragment renderFragment, Exception& renderFragmentException)
fail: Microsoft.AspNetCore.Diagnostics.ExceptionHandlerMiddleware[1]
      An unhandled exception has occurred while executing the request.
      System.ArgumentNullException: Value cannot be null. (Parameter 'source')
         at System.Linq.ThrowHelper.ThrowArgumentNullException(ExceptionArgument argument)
         at System.Linq.Enumerable.Where[TSource](IEnumerable`1 source, Func`2 predicate)
         at ThemisSupportTools.Web.Components.Pages.Modules.PreparationMiseVente.CheckWaitingPages.get_FilteredInfos() in D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Intranet\ThemisSupportTools.Web\ThemisSupportTools.Web\Components\Pages\Modules\PreparationMiseVente\CheckWaitingPages.razor.cs:line 49
         at ThemisSupportTools.Web.Components.Pages.Modules.PreparationMiseVente.CheckWaitingPages.BuildRenderTree(RenderTreeBuilder __builder)
         at Microsoft.AspNetCore.Components.Rendering.ComponentState.RenderIntoBatch(RenderBatchBuilder batchBuilder, RenderFragment renderFragment, Exception& renderFragmentException)
      --- End of stack trace from previous location ---
         at Microsoft.AspNetCore.Components.RenderTree.Renderer.ProcessRenderQueue()
      --- End of stack trace from previous location ---
         at Microsoft.A