﻿

/*
declare @pOrderId int = [ORDERID]
declare @peventId int = 0
declare @pseatId int = 0

*/

/* ramene dossier ET sieges de la commande */

declare @myOrderId int = @pOrderId
DECLARE @SQL2 VARCHAR(4000),@SQL_MONTANT  VARCHAR(3000)
DECLARE @SQL VARCHAR(4000)
DECLARE  @DOSSIERID INT,@MANIFID INT,@FORMULEID INT, @SEANCEID INT,@NBENVOI INT,@PRODUITID INT
DECLARE @AUTREMONTANT DECIMAL(18,10)

--Set @LANGUAGEID=0
--set @COMMANDEID=318686
CREATE TABLE #T2 (
recette_id int,
type_operation varchar(10), dossier_v int, dossier_id int, dossier_etat VARCHAR(2), entree_etat varchar(2), entree_id INT, reference_unique_physique_id INT, type_tarif_id INT,
categorie_id INT, seance_id INT, manifestation_id INT, formule_id INT, controleacces varchar(50), 
montant DECIMAL(18,10),
frais <PERSON>(18,10),
montant1 DECIMAL(18,10),
montant2 DECIMAL(18,10),
montant3 DECIMAL(18,10),
montant4 DECIMAL(18,10),
montant5 DECIMAL(18,10),
montant6 DECIMAL(18,10),
montant7 DECIMAL(18,10),
montant8 DECIMAL(18,10),
montant9 DECIMAL(18,10),
montant10 DECIMAL(18,10)
 )


-- FETCH NEXT FROM LISTDOS INTO @FORMULEID,@DOSSIERID,@MANIFID,@SEANCEID,@AUTREMONTANT; 
DECLARE LISTDOS CURSOR SCROLL FOR 
	SELECT  cl.formule_id,clp.dossier_id, clp.manifestation_id,clp.seance_id, montant4+montant5+montant6+montant7+montant8+montant9 +montant10 as AutreMontant
	FROM commande_ligne_comp clp 
	INNER JOIN commande_ligne cl on cl.commande_ligne_id=clp.commande_ligne_id 
	WHERE  cl.type_ligne='DOS' and  clp.commande_id= @myOrderId
	AND (@peventId = 0 OR @peventId = cl.manifestation_id);

OPEN LISTDOS; 
FETCH NEXT FROM LISTDOS INTO @FORMULEID,@DOSSIERID,@MANIFID,@SEANCEID,@AUTREMONTANT; 

WHILE @@FETCH_STATUS=0 
BEGIN 
	
		SET @SQL_MONTANT = 'e.montant1 + e.montant2 + case  when modecol4=''REMISE''  then - e.montant4 
			when modecol4=''TAXE'' or modecol4=''COMMISSION'' then  e.montant4
			else 0 END +
			case  when modecol5=''REMISE''  then - e.montant5
			when modecol5=''TAXE'' or modecol5=''COMMISSION'' then  e.montant5
			else 0 END +
			case  when modecol6=''REMISE''   then - e.montant6
			when modecol6=''TAXE'' or modecol6=''COMMISSION'' then  e.montant6
			else 0 END +
			case  when modecol7=''REMISE''  then - e.montant7
			when modecol7=''TAXE'' or modecol7=''COMMISSION'' then  e.montant7
			else 0 END +
			case  when modecol8=''REMISE''  then - e.montant8
			when modecol8=''TAXE'' or modecol8=''COMMISSION'' then  e.montant8
			else 0 END +
			case  when modecol9=''REMISE''  then - e.montant9
			when modecol9=''TAXE''or modecol9=''COMMISSION'' then  e.montant9
			else 0 END +
			case  when modecol10=''REMISE''  then - e.montant10
			when modecol10=''TAXE'' or modecol10=''COMMISSION'' then  e.montant10
			else 0 END as montant, e.montant2 as frais, 
			dsvg.dossier_montant1,  
			dsvg.dossier_montant2,  
			dsvg.dossier_montant3,  
			dsvg.dossier_montant4,  
			dsvg.dossier_montant5,  
			dsvg.dossier_montant6,  
			dsvg.dossier_montant7,  
			dsvg.dossier_montant8,  
			dsvg.dossier_montant9,  
			dsvg.dossier_montant10
			FROM structure,';

	SET @SQL = 'INSERT INTO #T2 
		SELECT  r.recette_id, dsvg.type_operation, dsvg.dossier_v, esvg.dossier_id, dsvg.dossier_etat, esvg.entree_etat, e.entree_id, reference_unique_physique_id,
		e.type_tarif_id, e.categorie_id, e.seance_id,'
		+LTRIM(STR(@MANIFID)) +' as manifestation_id ,'+LTRIM(STR(@FORMULEID)) +' as formule_id, e.controleacces, '
		+ @SQL_MONTANT + 
		' dossiersvg_' + LTRIM(STR(@MANIFID)) + ' dsvg --  on d.dossier_id=esvg.dossier_id
			inner join entreesvg_' + LTRIM(STR(@MANIFID)) + ' esvg on esvg.seance_id = dsvg.seance_id and esvg.dossier_id = dsvg.dossier_id and dsvg.dossier_v=esvg.dossier_v
			inner join entree_' + LTRIM(STR(@MANIFID)) + ' e on e.entree_id = esvg.entree_id and e.seance_id = esvg.seance_id
			left join recette r on r.seance_id = e.seance_id and e.entree_id = r.entree_id and r.dossier_id = e.dossier_id and recette_id = (select max(recette_id) from recette r2 where r2.entree_id = r.entree_id and r2.dossier_id = r.dossier_id and r2.seance_id =r.seance_id)
		WHERE 		
		(dsvg.dossier_etat<>''P'' OR dsvg.type_operation<>''EDIT'') AND dsvg.type_operation <> ''DUPLI'' 
		AND esvg.dossier_id =' + LTRIM(STR(@DOSSIERID)) + '
		AND esvg.dossier_v = (SELECT MAX(esvg2.dossier_v) FROM entreesvg_' + LTRIM(STR(@MANIFID)) + ' esvg2                
                                                                   WHERE esvg2.entree_id=esvg.entree_id    
                                                                   and esvg2.dossier_id=esvg.dossier_id
																   and esvg2.seance_id=esvg.seance_Id)'


	print(@sql)
	
	EXEC(@SQL )	
--PRINT @SQL;
FETCH NEXT FROM LISTDOS INTO @FORMULEID,@DOSSIERID,@MANIFID,@SEANCEID,@AUTREMONTANT; 

END

CLOSE LISTDOS; 
DEALLOCATE LISTDOS; 



SELECT  
	#T2.type_operation, #T2.entree_etat,
	CASE WHEN LTRIM(RTRIM(motif)) ='' THEN LTRIM(RTRIM(externe)) ELSE LTRIM(RTRIM(motif)) END as barcode,
	--motif , origine,
	convert(int, numbillet) as numbillet,
	convert(int,r.recette_id) as recetteId,
		
	#T2.dossier_id, dossier_etat, dossier_v, 
	cc.cc_date_operation, 
		case when rc.Maquette_id IS null THEN s.maq_billet_id else rc.Maquette_id end as maq_billet_id ,

	#T2.formule_id ,form_abon_nom as formule_nom,l.lieu_id , l.lieu_nom ,
	m.manifestation_id , manifestation_nom ,
	convert(int,s.seance_id) as seance_id,	
	seance_date_deb,#T2.entree_id , 
	pos_x, pos_y, siege , rang , rlp.iindex,
	c.categ_id ,categ_nom ,z.zone_id ,z.zone_nom ,
	sec.section_id , sec.section_nom ,et.etage_id , 
	etage_nom , rlp.denomination_id  ,de.denom_nom as denomnom, rlp.orientation,

	ISNULL(ptribune.code,' ') as place_code_tribune, ISNULL(PTRIBUNE.NOM,'') as place_lib_tribune,ISNULL(ptribune.id, 0) as place_id_tribune, 
	ISNULL(pacces.code,'') as place_code_acces, ISNULL(PACCES.NOM,'') as place_lib_acces,ISNULL(pacces.id, 0) as place_id_acces,
	ISNULL(pporte.code,' ') as place_code_porte, ISNULL(PPORTE.NOM,'') as place_lib_porte,ISNULL(pporte.id, 0) as place_id_porte,

	CASE WHEN tt.type_tarif_id IS NULL THEN 0 ELSE tt.type_tarif_id END as type_tarif_id  ,
	CASE WHEN  tt.type_tarif_nom IS NULL THEN '' ELSE tt.type_tarif_nom END as type_tarif_nom ,
	
	montant as montant, 
	frais as frais,
	rlp.denomination_id, de.denom_nom, rlp.type_siege,
	#T2.montant1  as montant1,
	#T2.montant2  as montant2,
	#T2.montant3  as montant3,
	#T2.montant4  as montant4,
	#T2.montant5  as montant5,
	#T2.montant6  as montant6,
	#T2.montant7  as montant7,
	#T2.montant8  as montant8,
	#T2.montant9  as montant9,
	#T2.montant10 as montant10,
	tva.tva_taux, tva_libelle as tva_name,
	#T2.controleacces, 
	CASE WHEN NEPASAFFICHERDATE like 'O' THEN cast(0 as bit) ELSE cast(1 as bit) END as ShowSessionDate, 		
    CASE WHEN NEPASAFFICHERDATE like 'H' THEN cast(0 as bit) WHEN NEPASAFFICHERDATE like 'O' THEN cast(0 as bit) ELSE cast(1 as bit) END as ShowSessionHour,
	 
	producteur.producteur_id,
	producteur.producteur_nom, 
	producteur.num_licence1,
	producteur.num_licence2,
	producteur.num_licence3,
	producteur.num_licence4
	 
--	 	 CAST(1 AS BIT) as ShowSessionDate,
--	CAST(1 AS BIT) as ShowSessionHour
	
	FROM #T2 
	INNER JOIN categorie c on c.categ_id=#T2.categorie_id
	--inner join denomination d on #T2.de
	LEFT JOIN type_tarif tt on tt.type_tarif_id=#T2.type_tarif_id
	INNER JOIN seance s on s.seance_id=#T2.seance_id
	LEFT JOIN compte_client cc  on cc.dossier_id=#T2.dossier_id  and cc.seance_id = #T2.seance_id and cc.cc_typetransaction ='CREDITDOS'
	INNER JOIN tva on s.taux_tva1_id = tva.tva_id
	INNER JOIN manifestation m on m.manifestation_id=#T2.manifestation_id
	INNER JOIN reference_lieu_physique rlp on rlp.ref_uniq_phy_id=#T2.reference_unique_physique_id
	LEFT OUTER JOIN formule_abonnement fa on fa.form_abon_id=#T2.formule_id
	INNER JOIN zone z on z.zone_id=rlp.zone_id
	INNER JOIN section sec on sec.section_id=rlp.section_id
	INNER JOIN denomination de on de.denom_id=rlp.denomination_id
	INNER JOIN etage et on et.etage_id = rlp.etage_id
	INNER JOIN lieu l on l.lieu_id = rlp.lieu_id

	LEFT OUTER JOIN propriete_physique pacces ON PACCES.ID = rlp.acces AND pacces.codetable = 'ACCES'
	LEFT OUTER JOIN propriete_physique pporte ON PPORTE.ID = rlp.porte AND pporte.codetable = 'PORTE'
	LEFT OUTER JOIN propriete_physique ptribune ON PTRIBUNE.ID = rlp.tribune AND ptribune.codetable = 'TRIBUNE'

	LEFT JOIN producteur on producteur.producteur_id = m.producteur_id
	LEFT JOIN recette r on r.entree_id = #T2.entree_id and r.manifestation_id = #T2.manifestation_id 
	and r.seance_id = #T2.seance_id --and r.type_operation ='E'
	and r.dossier_id = #T2.dossier_id and r.recette_id = #T2.recette_id
	LEFT JOIN recette_complement rc on rc.recette_id = r.recette_id 

	ORDER BY s.seance_date_deb, rlp.rang, rlp.siege ASC

--PRINT @SQL2;
--EXEC(@SQL2);
--PRINT @SQLLISTFRAIS;

DROP TABLE #T2