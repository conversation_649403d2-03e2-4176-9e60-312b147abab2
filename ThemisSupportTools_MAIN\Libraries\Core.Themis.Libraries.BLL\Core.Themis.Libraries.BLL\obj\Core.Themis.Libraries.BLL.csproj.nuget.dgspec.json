{"format": 1, "restore": {"D:\\WORK\\TST_MAIN\\ThemisSupportTools_MAIN\\Libraries\\Core.Themis.Libraries.BLL\\Core.Themis.Libraries.BLL\\Core.Themis.Libraries.BLL.csproj": {}}, "projects": {"D:\\WORK\\TST_MAIN\\ThemisSupportTools_MAIN\\Libraries\\Core.Themis.Libraries.BLL\\Core.Themis.Libraries.BLL\\Core.Themis.Libraries.BLL.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\WORK\\TST_MAIN\\ThemisSupportTools_MAIN\\Libraries\\Core.Themis.Libraries.BLL\\Core.Themis.Libraries.BLL\\Core.Themis.Libraries.BLL.csproj", "projectName": "Core.Themis.Libraries.BLL", "projectPath": "D:\\WORK\\TST_MAIN\\ThemisSupportTools_MAIN\\Libraries\\Core.Themis.Libraries.BLL\\Core.Themis.Libraries.BLL\\Core.Themis.Libraries.BLL.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\WORK\\TST_MAIN\\ThemisSupportTools_MAIN\\Libraries\\Core.Themis.Libraries.BLL\\Core.Themis.Libraries.BLL\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {"D:\\WORK\\TST_MAIN\\ThemisSupportTools_MAIN\\Libraries\\Core.Themis.Libraries.Data\\Core.Themis.Librairies.Data\\Core.Themis.Libraries.Data.csproj": {"projectPath": "D:\\WORK\\TST_MAIN\\ThemisSupportTools_MAIN\\Libraries\\Core.Themis.Libraries.Data\\Core.Themis.Librairies.Data\\Core.Themis.Libraries.Data.csproj"}, "D:\\WORK\\TST_MAIN\\ThemisSupportTools_MAIN\\Libraries\\Core.Themis.Libraries.DTO\\Core.Themis.Libraries.DTO.csproj": {"projectPath": "D:\\WORK\\TST_MAIN\\ThemisSupportTools_MAIN\\Libraries\\Core.Themis.Libraries.DTO\\Core.Themis.Libraries.DTO.csproj"}, "D:\\WORK\\TST_MAIN\\ThemisSupportTools_MAIN\\Libraries\\Core.Themis.Libraries.Utilities\\Core.Themis.Libraries.Utilities\\Core.Themis.Libraries.Utilities.csproj": {"projectPath": "D:\\WORK\\TST_MAIN\\ThemisSupportTools_MAIN\\Libraries\\Core.Themis.Libraries.Utilities\\Core.Themis.Libraries.Utilities\\Core.Themis.Libraries.Utilities.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.100"}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"BarcodeLib": {"target": "Package", "version": "[3.1.4, )"}, "CoreHtmlToImage": {"target": "Package", "version": "[1.0.6, )"}, "Fluid.Core": {"target": "Package", "version": "[2.18.0, )"}, "Microsoft.AspNetCore.Mvc.Razor.RuntimeCompilation": {"target": "Package", "version": "[8.0.10, )"}, "Microsoft.EntityFrameworkCore.SqlServer": {"target": "Package", "version": "[9.0.2, )"}, "Microsoft.Extensions.Caching.StackExchangeRedis": {"target": "Package", "version": "[8.0.10, )"}, "Microsoft.Extensions.Configuration.EnvironmentVariables": {"target": "Package", "version": "[7.0.0, )"}, "RestSharp": {"target": "Package", "version": "[112.1.0, )"}, "StackExchange.Redis": {"target": "Package", "version": "[2.8.41, )"}, "Swashbuckle.AspNetCore.Annotations": {"target": "Package", "version": "[7.2.0, )"}, "System.Data.SqlClient": {"target": "Package", "version": "[4.9.0, )"}, "dotnet-passbook": {"target": "Package", "version": "[3.2.4, )"}, "xunit.extensibility.core": {"target": "Package", "version": "[2.9.3, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.103/PortableRuntimeIdentifierGraph.json"}}}, "D:\\WORK\\TST_MAIN\\ThemisSupportTools_MAIN\\Libraries\\Core.Themis.Libraries.Data\\Core.Themis.Librairies.Data\\Core.Themis.Libraries.Data.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\WORK\\TST_MAIN\\ThemisSupportTools_MAIN\\Libraries\\Core.Themis.Libraries.Data\\Core.Themis.Librairies.Data\\Core.Themis.Libraries.Data.csproj", "projectName": "Core.Themis.Libraries.Data", "projectPath": "D:\\WORK\\TST_MAIN\\ThemisSupportTools_MAIN\\Libraries\\Core.Themis.Libraries.Data\\Core.Themis.Librairies.Data\\Core.Themis.Libraries.Data.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\WORK\\TST_MAIN\\ThemisSupportTools_MAIN\\Libraries\\Core.Themis.Libraries.Data\\Core.Themis.Librairies.Data\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {"D:\\WORK\\TST_MAIN\\ThemisSupportTools_MAIN\\Libraries\\Core.Themis.Libraries.DTO\\Core.Themis.Libraries.DTO.csproj": {"projectPath": "D:\\WORK\\TST_MAIN\\ThemisSupportTools_MAIN\\Libraries\\Core.Themis.Libraries.DTO\\Core.Themis.Libraries.DTO.csproj"}, "D:\\WORK\\TST_MAIN\\ThemisSupportTools_MAIN\\Libraries\\Core.Themis.Libraries.Utilities\\Core.Themis.Libraries.Utilities\\Core.Themis.Libraries.Utilities.csproj": {"projectPath": "D:\\WORK\\TST_MAIN\\ThemisSupportTools_MAIN\\Libraries\\Core.Themis.Libraries.Utilities\\Core.Themis.Libraries.Utilities\\Core.Themis.Libraries.Utilities.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.100"}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"Dapper": {"target": "Package", "version": "[2.1.35, )"}, "Microsoft.Data.SqlClient": {"target": "Package", "version": "[5.2.2, )"}, "Microsoft.EntityFrameworkCore": {"target": "Package", "version": "[9.0.0, )"}, "Microsoft.Extensions.Configuration.Abstractions": {"target": "Package", "version": "[9.0.0, )"}, "StackExchange.Redis": {"target": "Package", "version": "[2.8.41, )"}, "Swashbuckle.AspNetCore.Annotations": {"target": "Package", "version": "[7.2.0, )"}, "System.Data.SqlClient": {"target": "Package", "version": "[4.9.0, )"}, "System.Text.Json": {"target": "Package", "version": "[9.0.5, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.103/PortableRuntimeIdentifierGraph.json"}}}, "D:\\WORK\\TST_MAIN\\ThemisSupportTools_MAIN\\Libraries\\Core.Themis.Libraries.DTO\\Core.Themis.Libraries.DTO.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\WORK\\TST_MAIN\\ThemisSupportTools_MAIN\\Libraries\\Core.Themis.Libraries.DTO\\Core.Themis.Libraries.DTO.csproj", "projectName": "Core.Themis.Libraries.DTO", "projectPath": "D:\\WORK\\TST_MAIN\\ThemisSupportTools_MAIN\\Libraries\\Core.Themis.Libraries.DTO\\Core.Themis.Libraries.DTO.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\WORK\\TST_MAIN\\ThemisSupportTools_MAIN\\Libraries\\Core.Themis.Libraries.DTO\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {"D:\\WORK\\TST_MAIN\\ThemisSupportTools_MAIN\\Libraries\\Core.Themis.Libraries.Utilities\\Core.Themis.Libraries.Utilities\\Core.Themis.Libraries.Utilities.csproj": {"projectPath": "D:\\WORK\\TST_MAIN\\ThemisSupportTools_MAIN\\Libraries\\Core.Themis.Libraries.Utilities\\Core.Themis.Libraries.Utilities\\Core.Themis.Libraries.Utilities.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.100"}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"AutoMapper": {"target": "Package", "version": "[13.0.1, )"}, "StackExchange.Redis": {"target": "Package", "version": "[2.8.41, )"}, "Swashbuckle.AspNetCore.Annotations": {"target": "Package", "version": "[7.2.0, )"}, "System.Text.Json": {"target": "Package", "version": "[9.0.5, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.103/PortableRuntimeIdentifierGraph.json"}}}, "D:\\WORK\\TST_MAIN\\ThemisSupportTools_MAIN\\Libraries\\Core.Themis.Libraries.Utilities\\Core.Themis.Libraries.Utilities\\Core.Themis.Libraries.Utilities.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\WORK\\TST_MAIN\\ThemisSupportTools_MAIN\\Libraries\\Core.Themis.Libraries.Utilities\\Core.Themis.Libraries.Utilities\\Core.Themis.Libraries.Utilities.csproj", "projectName": "Core.Themis.Libraries.Utilities", "projectPath": "D:\\WORK\\TST_MAIN\\ThemisSupportTools_MAIN\\Libraries\\Core.Themis.Libraries.Utilities\\Core.Themis.Libraries.Utilities\\Core.Themis.Libraries.Utilities.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\WORK\\TST_MAIN\\ThemisSupportTools_MAIN\\Libraries\\Core.Themis.Libraries.Utilities\\Core.Themis.Libraries.Utilities\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.100"}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"BarcodeLib": {"target": "Package", "version": "[3.1.4, )"}, "Dapper": {"target": "Package", "version": "[2.1.35, )"}, "Google.Apis.Auth": {"target": "Package", "version": "[1.68.0, )"}, "Google.Apis.IAMCredentials.v1": {"target": "Package", "version": "[1.68.0.3584, )"}, "ISO3166": {"target": "Package", "version": "[1.0.4, )"}, "JWT": {"target": "Package", "version": "[11.0.0, )"}, "Microsoft.AspNetCore.Authorization": {"target": "Package", "version": "[8.0.11, )"}, "Microsoft.AspNetCore.Http": {"target": "Package", "version": "[2.2.2, )"}, "Microsoft.AspNetCore.Http.Extensions": {"target": "Package", "version": "[2.2.0, )"}, "Microsoft.AspNetCore.Mvc.Abstractions": {"target": "Package", "version": "[2.2.0, )"}, "Microsoft.Data.SqlClient": {"target": "Package", "version": "[5.2.2, )"}, "Microsoft.Extensions.Caching.Memory": {"target": "Package", "version": "[9.0.0, )"}, "Microsoft.Extensions.Configuration": {"target": "Package", "version": "[9.0.0, )"}, "Microsoft.Extensions.Logging": {"target": "Package", "version": "[9.0.0, )"}, "Microsoft.Extensions.Logging.Abstractions": {"target": "Package", "version": "[9.0.0, )"}, "Microsoft.Extensions.Options": {"target": "Package", "version": "[9.0.0, )"}, "Microsoft.IdentityModel.Protocols.OpenIdConnect": {"target": "Package", "version": "[8.3.0, )"}, "Microsoft.IdentityModel.Tokens": {"target": "Package", "version": "[8.3.0, )"}, "Mono.Cecil": {"target": "Package", "version": "[0.11.6, )"}, "NETCore.MailKit": {"target": "Package", "version": "[2.1.0, )"}, "NLog": {"target": "Package", "version": "[5.3.4, )"}, "Newtonsoft.Json": {"target": "Package", "version": "[13.0.3, )"}, "Omnibasis.GoogleWallet": {"target": "Package", "version": "[1.0.1, )"}, "Polybioz.HtmlRenderer.PdfSharp.Core": {"target": "Package", "version": "[1.1.0, )"}, "Portable.BouncyCastle": {"target": "Package", "version": "[1.9.0, )"}, "QRCoder": {"target": "Package", "version": "[1.6.0, )"}, "RestSharp": {"target": "Package", "version": "[112.1.0, )"}, "StackExchange.Redis": {"target": "Package", "version": "[2.8.41, )"}, "System.Data.Odbc": {"target": "Package", "version": "[9.0.0, )"}, "System.Data.SqlClient": {"target": "Package", "version": "[4.9.0, )"}, "System.Drawing.Common": {"target": "Package", "version": "[9.0.0, )"}, "System.Runtime.Caching": {"target": "Package", "version": "[8.0.0, )"}, "System.Text.Json": {"target": "Package", "version": "[9.0.5, )"}, "dotnet-passbook": {"target": "Package", "version": "[3.2.4, )"}, "jose-jwt": {"target": "Package", "version": "[5.1.0, )"}, "log4net": {"target": "Package", "version": "[3.0.3, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.103/PortableRuntimeIdentifierGraph.json"}}}}}