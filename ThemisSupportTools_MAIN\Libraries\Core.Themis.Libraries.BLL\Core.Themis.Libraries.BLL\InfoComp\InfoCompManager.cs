﻿using AutoMapper;
using Core.Themis.Libraries.BLL.InfoComp.Interfaces;
using Core.Themis.Libraries.Data.Entities.Open.Identite;
using Core.Themis.Libraries.Data.Entities.Open.InfoComps;
using Core.Themis.Libraries.Data.Entities.Open.Structure;
using Core.Themis.Libraries.Data.Repositories.Open.InfoComp.Interfaces;
using Core.Themis.Libraries.Data.Repositories.Open.Language.Intrefaces;
using Core.Themis.Libraries.DTO.Identity;
using Core.Themis.Libraries.DTO.InfoComps;
using Core.Themis.Libraries.DTO.InfoComps.ApiExchange.Response;
using Core.Themis.Libraries.DTO.InfoComps.Json;
using Core.Themis.Libraries.DTO.Insurance.Meetch.Subscription;
using Core.Themis.Libraries.DTO.Lookup;
using Core.Themis.Libraries.Utilities.Extensions;
using Core.Themis.Libraries.Utilities.Logging;
using Microsoft.Extensions.Configuration;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Threading.Tasks;

public class InfoCompManager : IInfoCompManager
{

    public static readonly string[] FormatSelectOptions = ["checkbox", "radio", "select", "select_multiple"];
    public static readonly string[] PageToShowOptions = ["Basket"/*, "AccountCreate", "AccountUpdate_Contact", "AccountUpdate_Options" ,"WaitingList", "SkipQueue", "PostponeSession"*/];
    public static readonly string[] HideWhenCheckedOptions = ["Basket"/*, "AccountCreate", "AccountUpdate_Contact", "AccountUpdate_Options", "WaitingList", "SkipQueue", "PostponeSession"*/];


    private readonly IConfiguration _configuration;
    private readonly IIdentiteInfoCompRepository _identiteInfoCompRepository;
    private readonly IInfoCompGroupRepository _infoCompGroupRepository;
    private readonly IInfoCompRepository _infoCompRepository;
    private readonly ILanguageRepository _languageRepository;
    private readonly IMapper _mapper;

    private static readonly RodrigueNLogger RodrigueLogger = new RodrigueNLogger();
    public InfoCompManager(IConfiguration configuration, IIdentiteInfoCompRepository identiteInfoCompRepository,
        IInfoCompGroupRepository infoCompGroupRepository, IInfoCompRepository infoCompRepository, IMapper mapper,
        ILanguageRepository languageRepository)
    {
        _configuration = configuration;
        _identiteInfoCompRepository = identiteInfoCompRepository;
        _infoCompGroupRepository = infoCompGroupRepository;
        _infoCompRepository = infoCompRepository;
        _languageRepository = languageRepository;
        _mapper = mapper;
    }

    public bool UpdateStats(int structureId, string email, string eventName, string emailColumn, List<string> folders)
    {
        try
        {
            _identiteInfoCompRepository.Emailing_UpdateStats(structureId, email, eventName, emailColumn, folders);

            return true;

        }
        catch (System.Data.SqlClient.SqlException ex)
        {
            string foldersStr = "'" + string.Join("','", folders) + "'";

            //logger
            RodrigueLogger.Error(structureId, $"InfoCompManager.Emailing_UpdateStats(): structureId = {structureId}, email={email}, eventName={eventName}, emailColumn={emailColumn}, folders={foldersStr}, Message = {ex.Message}, StackTrace = {ex.StackTrace}");
            return false;
        }
    }

    public bool Emailing_GroupExists(int structureId, string groupeId)
    {
        try
        {
            return _infoCompRepository.Emailing_GroupExists(structureId, groupeId);

        }
        catch (System.Data.SqlClient.SqlException ex)
        {
            //logger
            RodrigueLogger.Error(structureId, $"InfoCompManager.Emailing_GroupExists(): structureId = {structureId}, groupId={groupeId}, Message = {ex.Message}, StackTrace = {ex.StackTrace}");

            return false;
        }
    }

    /// <summary>
    /// Récupère un groupe d'infocomp par son ID
    /// </summary>
    /// <param name="structureId"></param>
    /// <param name="infoCompGroupId"></param>
    /// <returns></returns>
    public async Task<InfoCompGroupDTO> GetInfoCompGroupByIdAsync(int structureId, int infoCompGroupId)
    {
        var result = await _infoCompGroupRepository.GetCustomEntityById(structureId, infoCompGroupId)
                                    .Include(ic => ic.InfoComps).ToEntityAsync();

        return _mapper.Map<InfoCompGroupDTO>(result);
    }

    /// <summary>
    /// Récupère une liste d'infocomps appartenant à un groupe
    /// </summary>
    /// <param name="structureId"></param>
    /// <param name="infoCompGroupId"></param>
    /// <returns></returns>
    public async Task<List<InfoCompDTO>> GetInfoCompsByGoupIdAsync(int structureId, int infoCompGroupId)
    {
        var result = await _infoCompRepository.FindCustomEntityBy(b => b.GroupeId == infoCompGroupId, structureId).ToEntityListAsync();

        return _mapper.Map<List<InfoCompDTO>>(result);
    }

    public async Task<JsonInfoCompGroupSettings> PopulateAssignedInfoCompsAsync(int structureId,
          int identityId,
          JsonInfoCompGroupSettings jsonInfoCompGroupSettings)
    {
        // 1) Récupère les entités qui lient l’identité aux InfoComps
        var identiteInfosCompEntities = await _identiteInfoCompRepository.
            FindByAsync(iic => iic.IdentiteId == identityId, structureId)
            .ConfigureAwait(false);
        identiteInfosCompEntities = identiteInfosCompEntities?.Where(iic => iic.Supprimer == 'N');


        // 2) Extrait les IDs d’InfoComp assignés
        var assignedIds = identiteInfosCompEntities
            .Select(e => e.InfoCompId)
            .ToHashSet();

        // 3) Pour chaque InfoComp dans chaque groupe, set IsAssignedToIdentity
        foreach (var group in jsonInfoCompGroupSettings.Groups)
        {
            foreach (var comp in group.InfoComps)
            {
                comp.IsAssignedToIdentity = assignedIds.Contains(comp.Id);
            }
        }

        return jsonInfoCompGroupSettings;
    }


    /// <summary>
    /// Met à jour en base les liaisons InfoComp & Identité
    /// selon les flags IsAssignedToIdentity du DTO JSON.
    /// </summary>
    public async Task UpdateIdentityInfoCompAssignmentsAsync(int structureId, int identityId, JsonInfoCompGroupSettings settings)
    {
        // 1) Charge les liaisons existantes
        var existingEntities = (await _identiteInfoCompRepository
                .FindByAsync(i => i.IdentiteId == identityId, structureId))
                .ToList();

        // 2) Parcourt tous les JsonInfoComp
        foreach (var comp in settings.Groups.SelectMany(g => g.InfoComps))
        {

            var infosCompEntity = existingEntities.FirstOrDefault(c => c.InfoCompId == comp.Id);

            // Si on doit l’assigner…
            if (comp.IsAssignedToIdentity)
            {
                if (infosCompEntity != null)
                {
                    // a) existe déjà : si soft‐deleted, on ré‐active
                    if (infosCompEntity.Supprimer == 'O')
                    {
                        infosCompEntity.Supprimer = 'N';
                        await _identiteInfoCompRepository.UpdateAsync(structureId, infosCompEntity);
                    }
                }
                else
                {
                    // b) n’existe pas : on crée une nouvelle ligne active
                    var toAdd = new IdentiteInfosCompEntity
                    {
                        IdentiteId = identityId,
                        InfoCompId = comp.Id,
                        Supprimer = 'N',
                        DateCreation = DateTime.Now,
                        DateModification = DateTime.Now
                    };
                    await _identiteInfoCompRepository.InsertAsync(structureId, toAdd);
                }
            }
            else
            {

                if (infosCompEntity == null)
                    continue;  


                // c) soft‐delete
                infosCompEntity.Supprimer = 'O';
                infosCompEntity.DateModification = DateTime.Now;
                await _identiteInfoCompRepository.UpdateAsync(structureId, infosCompEntity);

            }
        }
    }

    /// <summary>
    /// Récupère la liste des groupes d'infocomp visibles pour une structure, ordonnée par préférence d'affichage
    /// </summary>
    /// <param name="structureId"></param>
    /// <returns></returns>
    public async Task<List<SelectLookup>> SelectLookupInfoGroupsAsync(int structureId)
    {
        var result = await _infoCompGroupRepository.GetVisibleInfoCompGroupsOrderedByPrefAffichageAsync(structureId);

        return result?
            .Select(b => new SelectLookup()
            {
                Libelle = $"{b.Libelle} - {b.GroupeId}",
                Value = b.GroupeId.ToString(),
            })
            .ToList() ?? [];
    }


    public JsonInfoCompGroupSettings LoadJsonToObject(int structureId)
    {
        var path = GetJsonInfoCompFilePath(structureId);

        if (File.Exists(path))
        {
            string jsonString = File.ReadAllText(path);
            return jsonString.FromJson<JsonInfoCompGroupSettings>();
        }
        return new();
    }

    private string GetJsonInfoCompFilePath(int structureId)
    {
        var infocompsFilePath = _configuration["physicalPathOfInfosCompJSON"]
            ?? throw new InvalidOperationException("Configuration 'physicalPathOfInfosCompJSON' introuvable.");

        return infocompsFilePath.Replace("{structureId}", structureId.ToString("D4"));
    }

    public async Task<InfoCompsResponse<bool>> SaveJsonInfoCompFileAsync(
                 int structureId,
                 JsonInfoCompGroupSettings jsonInfoCompGroup)
    {

        try
        {
            var path = GetJsonInfoCompFilePath(structureId);
            Directory.CreateDirectory(Path.GetDirectoryName(path)!);

            var json = JsonConvert.SerializeObject(
                jsonInfoCompGroup,
                Formatting.Indented);

            await File.WriteAllTextAsync(path, json);


            return new()
            {
                Success = true,
                Result = true,
            };
        }
        catch (Exception ex)
        {
            return new()
            {
                Success = false,
                ErrorMessage = ex.Message,
            };
        }

    }

    public async Task<JsonInfoCompGroupSettings?> GetJsonInfoCompGroupSettingsAsync(int structureId)
    {
        // Chargement initial du JSON…
        var jsonSettings = LoadJsonToObject(structureId)
            ?? new JsonInfoCompGroupSettings();

        var langCodes = await GetActiveLanguageCodesAsync(structureId);

        EnsureTranslations(jsonSettings.Groups, grp => grp.TradTitle, langCodes);
        await SetGroupDetailsAsync(structureId, jsonSettings.Groups);

        foreach (var grp in jsonSettings.Groups)
        {
            
            int i = 0;
            var allInfoComps = (await _infoCompRepository
                    .FindByAsync(ic => ic.GroupeId == grp.Id, structureId))
                ?.Where(ic => ic.Masquer == 'N')
                .ToList() ?? new List<InfoCompEntity>();

            // Suppression
            var hiddenIds = allInfoComps.Where(ic => ic.Masquer == 'O')
                                        .Select(ic => ic.InfocompId)
                                        .ToHashSet();
            grp.InfoComps.RemoveAll(ic => hiddenIds.Contains(ic.Id));

            // Ajout
            foreach (var entity in allInfoComps)
            {
                if (!grp.InfoComps.Any(ic => ic.Id == entity.InfocompId))
                {
                    var comp = await CreateJsonInfoCompAsync(
                                    structureId,
                                    entity.InfocompId,
                                    i++);
                    // **On rattache le parent ici**
                    comp.ParentGroup = grp;
                    grp.InfoComps.Add(comp);
                }
            }

            // Traductions
            EnsureTranslations(grp.InfoComps, ic => ic.TradLabel, langCodes);

            // **Pour les InfoComps existants**, on rattache autant que besoin
            foreach (var existing in grp.InfoComps)
            {
                existing.ParentGroup = grp;
            }
        }

        await SetLibelleToInfoCompAsync(
            structureId,
            jsonSettings.Groups.SelectMany(g => g.InfoComps));


        jsonSettings.Groups = jsonSettings.Groups.OrderBy(o => o.PrefAffichage).ToList();


        return jsonSettings;
    }

    /// <summary>
    /// Crée un nouveau JsonInfoComp et initialise ses propriétés et traductions.
    /// Exposable publiquement pour usage lors de la sélection de groupe.
    /// </summary>
    public async Task<JsonInfoComp> CreateJsonInfoCompAsync(int structureId, int id, int iteration)
    {

        HashSet<string> langCodes = await GetActiveLanguageCodesAsync(structureId);

        return new JsonInfoComp
        {
            Id = id,
            Order = iteration,
            Enable = true,
            Mandatory = false,
            Precheck = false,
            TradLabel = langCodes
                          .Select(code => new JsonInfoCompTraduction
                          {
                              LangCode = code,
                              Traduction = string.Empty
                          })
                          .ToList()
        };
    }
    public async Task<JsonInfoCompGroup> CreateJsonInfoCompGroupAsync(int structureId, int groupId)
    {
        // Récupérer l'entité groupe
        var groupEntity = await _infoCompGroupRepository
            .GetByIdAsync(structureId, groupId)
            .ConfigureAwait(false);

        // Codes de langue actives
        var langCodes = await GetActiveLanguageCodesAsync(structureId);

        // Initialiser le DTO de groupe
        var group = new JsonInfoCompGroup
        {
            Id = groupId,
            LibelleGroup = string.Empty,
            PrefAffichage = groupEntity.PrefAffichage,
            TradTitle = langCodes
                          .Select(code => new JsonInfoCompTraduction
                          {
                              LangCode = code,
                              Traduction = string.Empty
                          })
                          .ToList(),
            Format = string.Empty,
            InfoComps = new List<JsonInfoComp>()
        };

        // Mettre à jour LibelleGroup et PrefAffichage
        await SetGroupDetailsAsync(structureId, new[] { group });

        // Charger les InfoComps visibles
        var entities = (await _infoCompRepository
                .FindByAsync(ic => ic.GroupeId == groupId, structureId)) ?? new List<InfoCompEntity>();

        entities = entities
            .Where(ic => ic.Masquer == 'N') // Filtrer les InfoComps visibles
            .OrderBy(ic => ic.InfocompId)
            .ToList();

        int i = 0;
        // Construire chaque JsonInfoComp avec libellé
        foreach (var ent in entities)
        {
            var comp = await CreateJsonInfoCompAsync(structureId, ent.InfocompId, i).ConfigureAwait(false);
            comp.ParentGroup = group;

            var detail = await _infoCompRepository
                .GetByIdAsync(structureId, ent.InfocompId)
                .ConfigureAwait(false);
            comp.Libelle = detail?.Libelle ?? string.Empty;
            group.InfoComps.Add(comp);

            i++;
        }

        return group;
    }

    public bool Emailing_UpdateGroup(int structureId, string email, string emailColumn, string groupeId)
    {
        try
        {
            _identiteInfoCompRepository.Emailing_UpdateGroup(structureId, email, emailColumn, groupeId);

            return true;

        }
        catch (System.Data.SqlClient.SqlException ex)
        {
            //logger
            RodrigueLogger.Error(structureId, $"InfoCompManager.Emailing_UpdateGroup(): structureId = {structureId}, email={email},  emailColumn={emailColumn}, groupId={groupeId}, Message = {ex.Message}, StackTrace = {ex.StackTrace}");



            return false;
        }
    }

    #region private methods
    /// <summary>
    /// Récupère les codes de langues actives (Supprimer == 'N') pour une structure donnée.
    /// </summary>
    private async Task<HashSet<string>> GetActiveLanguageCodesAsync(int structureId)
    {
        // Chargement des langues actives en base
        var languages = (await _languageRepository.GetAllAsync(structureId));
        languages = languages?.Where(l => l.Supprimer == 'N').ToList() ?? [];

        return new HashSet<string>(languages.Select(l => l.LangueCode));
    }


    /// <summary>
    /// <summary>Ajoute/élimine les codes manquants/obsolètes en TradLabel/TradTitle</summary>
    private static void EnsureTranslations<T>(
        IEnumerable<T> items,
        Func<T, List<JsonInfoCompTraduction>> getList,
        IReadOnlyCollection<string> langCodes)
    {
        foreach (var item in items)
        {
            var list = getList(item);
            // 1) Ajouter les nouvelles langues
            foreach (var code in langCodes)
                if (!list.Any(t => t.LangCode == code))
                    list.Add(new JsonInfoCompTraduction { LangCode = code, Traduction = string.Empty });
            // 2) Supprimer les anciennes
            list.RemoveAll(t => !langCodes.Contains(t.LangCode));
        }
    }




    /// <summary>
    /// Met à jour en parallèle les libellés de chaque JsonInfoComp
    /// </summary>
    private async Task SetLibelleToInfoCompAsync(int structureId, IEnumerable<JsonInfoComp> items)
    {
        // Préparer la liste
        var itemList = items as IList<JsonInfoComp> ?? items.ToList();
        itemList = itemList.OrderBy(o => o.Order).ToList();

        // Exécution en parallèle
        var tasks = itemList.Select(async item =>
        {
            var result = await _infoCompRepository
                .GetByIdAsync(structureId, item.Id)
                .ConfigureAwait(false);


            item.Libelle = result?.Libelle ?? string.Empty;
        });

        await Task.WhenAll(tasks);
    }


    /// <summary>
    /// Met à jour en parallèle les libellés et PrefAffichage de chaque JsonInfoCompGroup
    /// </summary>
    private async Task SetGroupDetailsAsync(int structureId, IEnumerable<JsonInfoCompGroup> groups)
    {
        var groupList = groups as IList<JsonInfoCompGroup> ?? groups.ToList();
        var tasks = groupList.Select(async grp =>
        {
            var result = await _infoCompGroupRepository
                .GetByIdAsync(structureId, grp.Id)
                .ConfigureAwait(false);
            grp.LibelleGroup = result?.Libelle ?? string.Empty;
            grp.PrefAffichage = result?.PrefAffichage ?? 0;
        });
        await Task.WhenAll(tasks);
    }
    #endregion

}