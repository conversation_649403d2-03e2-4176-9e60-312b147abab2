﻿using Core.Themis.Libraries.BLL.Services.Access;
using Microsoft.AspNetCore.Components;

namespace ThemisSupportTools.Web.Components.Pages.Modules.PreparationMiseVente
{
    public partial class CheckWaitingPages
    {

        private bool isLoading = false;

        public List<WsAdminStructureDTO> fileAttenteInfos;

        [Inject] private ITstAccessService TstAccessService { get; set; } = default!;
        [Inject] public NavigationManager NavigationManager { get; set; } = default!;
        [Inject] public IStringLocalizer<Resource> Localizer { get; set; } = default!;

        [Inject] public IWsAdminStructuresManager WsAdminStructuresManager { get; set; } = default!;


        private string GetErrors(IEnumerable<string> listPbsFA)
    => string.Join("\n", listPbsFA);

        private const string MODULE_NAME = "preparation-mise-vente-checkwaitingpages";

        private string filterStructureId = "";
        private string filterVersionActive = "";
        private string filterErrors = "";
        

        // Calcul des résultats filtrés
        private IEnumerable<WsAdminStructureDTO> FilteredInfos => fileAttenteInfos.Where(s =>
            (string.IsNullOrWhiteSpace(filterStructureId) || s.StructureId.ToString().Contains(filterStructureId)) &&
            (string.IsNullOrWhiteSpace(filterVersionActive) || s.versionFileAttente == filterVersionActive) &&
            (string.IsNullOrWhiteSpace(filterErrors) || s.listPbsFA.Any() == Convert.ToBoolean(int.Parse(filterErrors)))

        );

        private void Reload()
        {
            // Rien à faire ici, la vue est recalculée en direct
        }

        protected override async Task OnInitializedAsync()
        {
            if (!TstAccessService.IsGranted(MODULE_NAME))
            {
                NavigationManager.NavigateTo("");
                return;
            }

            isLoading = true;


            fileAttenteInfos = await WsAdminStructuresManager.GetWsAdminStructuresFileAttenteInfosAsync();


            isLoading = false;

        }
    }
}
