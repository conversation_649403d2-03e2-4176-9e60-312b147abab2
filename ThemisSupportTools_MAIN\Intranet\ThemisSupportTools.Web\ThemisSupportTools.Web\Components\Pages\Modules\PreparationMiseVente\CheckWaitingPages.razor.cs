﻿using Core.Themis.Libraries.BLL.Services.Access;
using Microsoft.AspNetCore.Components;

namespace ThemisSupportTools.Web.Components.Pages.Modules.PreparationMiseVente
{
    public partial class CheckWaitingPages
    {

        private bool isLoading = false;

        public List<WsAdminStructureDTO> fileAttenteInfos;

        [Inject] private ITstAccessService TstAccessService { get; set; } = default!;
        [Inject] public NavigationManager NavigationManager { get; set; } = default!;
        [Inject] public IStringLocalizer<Resource> Localizer { get; set; } = default!;

        [Inject] public IWsAdminStructuresManager WsAdminStructuresManager { get; set; } = default!;


        private string GetErrors(IEnumerable<string> listPbsFA)
            => string.Join("\n", listPbsFA);

        private string GetShortUrl(string url)
        {
            if (string.IsNullOrEmpty(url))
                return string.Empty;

            try
            {
                var uri = new Uri(url);
                var fileName = Path.GetFileName(uri.LocalPath);
                return string.IsNullOrEmpty(fileName) ? uri.Host : fileName;
            }
            catch
            {
                // Si l'URL n'est pas valide, retourner les 30 premiers caractères
                return url.Length > 30 ? url.Substring(0, 30) + "..." : url;
            }
        }

        private const string MODULE_NAME = "preparation-mise-vente-checkwaitingpages";

        private string filterStructureId = "";
        private string filterVersionActive = "";
        private string filterErrors = "";
        

        // Calcul des résultats filtrés
        private IEnumerable<WsAdminStructureDTO> FilteredInfos => fileAttenteInfos.Where(s =>
            (string.IsNullOrWhiteSpace(filterStructureId) || s.StructureId.ToString().Contains(filterStructureId)) &&
            (string.IsNullOrWhiteSpace(filterVersionActive) || s.versionFileAttente == filterVersionActive) &&
            (string.IsNullOrWhiteSpace(filterErrors) || s.listPbsFA.Any() == Convert.ToBoolean(int.Parse(filterErrors)))

        );

        private void Reload()
        {
            // Rien à faire ici, la vue est recalculée en direct
        }

        protected override async Task OnInitializedAsync()
        {
            if (!TstAccessService.IsGranted(MODULE_NAME))
            {
                NavigationManager.NavigateTo("");
                return;
            }

            isLoading = true;


            fileAttenteInfos = await WsAdminStructuresManager.GetWsAdminStructuresFileAttenteInfosAsync();


            isLoading = false;

        }
    }
}
