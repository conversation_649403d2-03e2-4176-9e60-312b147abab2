using Core.Themis.Libraries.Data.DBContext;
using Core.Themis.Libraries.Data.DBContext.Interfaces;
using Core.Themis.Libraries.Data.Repositories.WSAdmin;
using Core.Themis.Libraries.Data.Repositories.WSAdmin.Interfaces;
using doAllStepsDirectly;
using Microsoft.AspNetCore.Hosting;
using Microsoft.Extensions.Caching.Memory;

var builder = Host.CreateApplicationBuilder(args);
builder.Services.AddHostedService<Worker>();

var host = Host.CreateDefaultBuilder(args)
    .UseWindowsService(options => { options.ServiceName = "MonServiceWindows"; })
    .ConfigureServices((hostContext, services) =>
    {
        services.Configure<WorkerConfig>(
            hostContext.Configuration.GetSection("WorkerConfig"));

      
        services.AddAutoMapper(AppDomain.CurrentDomain.GetAssemblies());
        services.AddSingleton<IMemoryCache,MemoryCache>();
        services.AddSingleton<IDbContext, DbContext>();
        services.AddSingleton<IPanierGoStraightRepository, PanierGoStraightRepository>();
        
        services.AddHostedService<Worker>();
    }).Build();
host.Run();
