﻿using AutoMapper;
using Core.Themis.Libraries.BLL.Interfaces;
using Core.Themis.Libraries.Data.Entities.Open.GestionPlace;
using Core.Themis.Libraries.Data.Repositories.Open.EventsSessions.Interfaces;
using Core.Themis.Libraries.Data.Repositories.Open.GestionPlaces.Interfaces;
using Core.Themis.Libraries.DTO.EventsSessions;
using Core.Themis.Libraries.DTO.Offers;
using Core.Themis.Libraries.Utilities.ThemisSql;
using Microsoft.Extensions.Caching.Memory;
using System;
using System.Collections.Generic;
using System.Data;
using Microsoft.Data.SqlClient;
using System.Threading.Tasks;

namespace Core.Themis.Libraries.BLL
{
    public class OfferManager : IOfferManager
    {
        private readonly IOfferRepository _offerRepository;
        private readonly IOffreGestionPlaceRepository _offreGestionPlaceRepository;
        private readonly IMemoryCache _memoryCache;
        private readonly IMapper _mapper;

        public OfferManager(
            IOfferRepository offerRepository,
            IOffreGestionPlaceRepository offreGestionPlaceRepository,
            IMemoryCache memoryCache,
            IMapper mapper)
        {
            _offerRepository = offerRepository;
            _offreGestionPlaceRepository = offreGestionPlaceRepository;
            _memoryCache = memoryCache;
            _mapper = mapper;
        }


        public async Task<IEnumerable<OfferDTO>> GetOffersOnSales(int structureId)
        {
            var offersOnSalesEntity = await _offerRepository.GetOffersOnSalesAsync(structureId);

            return _mapper.Map<IEnumerable<OfferDTO>>(offersOnSalesEntity);
        }

        public static List<OfferDTO> GetOfferEntities(SqlConnection cnxOpen, int structureId, int identiteId, int profilAcheteurId, string scriptPathSqlCommons)
        {
            List<OfferDTO> listRet = new List<OfferDTO>();
            try
            {
                if (cnxOpen.State == ConnectionState.Closed)
                    cnxOpen.Open();

                //liste contenant le nom des langues
                string sql = FilesForSqlRequestsManager.GetScript(structureId, "Offers\\", "getOffersByIdentitePA", scriptPathSqlCommons);

                using (SqlCommand cmd = new SqlCommand())
                {
                    cmd.Connection = cnxOpen;

                    cmd.CommandText = sql;
                    cmd.Parameters.AddWithValue("pidentityId", identiteId);
                    cmd.Parameters.AddWithValue("pbuyerprofilId", profilAcheteurId);

                    using (SqlDataReader reader = cmd.ExecuteReader())
                    {
                        while (reader.Read())
                        {

                            OfferDTO offre = new OfferDTO()
                            {
                                OfferId = int.Parse(reader["offre_id"].ToString()),
                                OfferName = reader["offre_nom"].ToString(),
                                // AdhesionCatalogList = new List<AdhesionCatalogEntity>()
                            };
                            listRet.Add(offre);
                        }
                    }
                }
                cnxOpen.Close();
                //cnxOpen.Dispose();


            }
            catch (Exception ex)
            {
                throw;
            }
            return listRet;
        }

        public async Task<List<EventDTO>> GetAllEventsLinkedToAnOfferAsync(int structureId, int offerId)
        {
            string cacheKey = $"GetAllEventsLinkedToAnOffer_{structureId}_{offerId}";

            var cacheResult = _memoryCache.Get<List<EventDTO>>(cacheKey);

            if (cacheResult is not null)
                return cacheResult;

            var entities = await _offreGestionPlaceRepository.GetAllManifestationLinkedToAnOfferAsync(structureId, offerId)!
                                                             .ConfigureAwait(false);

            var events = _mapper.Map<List<EventDTO>>(entities);

            if (events is not null)
                _memoryCache.Set(cacheKey, events, TimeSpan.FromMinutes(2));

            return events;
        }

        public List<OffreGestionPlaceDTO> GetAllGestionPlaceLinkedToAnOffer(int structureId, int offerId)
        {

            string cachekey = $"GetAllGestionPlaceLinkedToAnOffer_{structureId}_{offerId}";

            var cacheResult = _memoryCache.Get<List<OffreGestionPlaceDTO>>(cachekey);
            if (cacheResult is not null)
                return cacheResult;


            List<OffreGestionPlaceEntity> offreGestionPlaceEntities = _offreGestionPlaceRepository
                .FindCustomEntityBy(o => o.OffreId == offerId, structureId)
                .Include(gp => gp.Offer)
                .Include(gp => gp.GestionPlace)
                .Where(o => o.Offer!.DateFinValidite > DateTime.Now && (o.GestionPlace.IsValide.HasValue && o.GestionPlace.IsValide.Value) && o.GestionPlace.Dispo > 0)
                .ToEntityList();


            return _mapper.Map<List<OffreGestionPlaceDTO>>(offreGestionPlaceEntities);

        }
    }
}
