﻿using Core.Themis.Libraries.DTO;
using Core.Themis.Libraries.DTO.EventsSessions;
using Core.Themis.Libraries.DTO.Identity;
using Core.Themis.Libraries.Utilities.Helpers;
using Core.Themis.Libraries.Utilities.Helpers.Files;
using Core.Themis.Libraries.Utilities.Logging;
using Microsoft.Extensions.Configuration;
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Xml;

namespace Core.Themis.Libraries.BLL.Helpers.Widgets
{
    public class WidgetUtilitiesHelper
    {
        private static readonly IConfiguration _configuration = ConfigurationHelper.config;
        private static readonly RodrigueNLogger _logger = new();




        /// <summary>
        /// merge 2 json, le 2em prend le dessus
        /// </summary>
        /// <param name="previousSettings"></param>
        /// <param name="overriddingSettings">json qui overwrite les valeurs du 1er</param>
        /// <returns></returns>
        public static dynamic MergeSettings(JObject previousSettings, JObject overriddingSettings)
            
        {
            JObject resultSettings = previousSettings;
            resultSettings.Merge(overriddingSettings, new JsonMergeSettings
            {
                MergeArrayHandling = MergeArrayHandling.Union

            });


            return resultSettings;

        }


        public static dynamic SettingsMerged(int structureId, int eventId, int buyerProfilId, string platformCode, string langCode, string PhysicalPathOfSettingsJSONKey)
        {
            _logger.Debug(structureId, "start SettingsMerged");

            string config = _configuration[PhysicalPathOfSettingsJSONKey];
            if (string.IsNullOrEmpty(config))
            {
                _logger.Fatal(structureId, $"can't find app.settings configuration for {PhysicalPathOfSettingsJSONKey}");
                Exception ex = new KeyNotFoundException($"can't find app.settings configuration for {PhysicalPathOfSettingsJSONKey}");
                throw ex;

            }


            //Extrait des chemins des différents appsettings.json (Default + structure)
            string settingsIndivPath = _configuration[PhysicalPathOfSettingsJSONKey]!
                .Replace("{structureId}", string.Format("{0,4:0000}", structureId))
                .Replace("[plateformCode]", platformCode)
                .Replace("{plateformCode}", platformCode);

            string settingsIndivDefaultPath = _configuration[PhysicalPathOfSettingsJSONKey]!
                .Replace("{structureId}", "DEFAULT")
                .Replace("[plateformCode]", platformCode)
                .Replace("{plateformCode}", platformCode);

            _logger.Debug(structureId, $"settingsIndivPath : {settingsIndivPath}");
            _logger.Debug(structureId, $"settingsIndivDefaultPath : {settingsIndivDefaultPath}");

            var listJsonSettingsToMerge = new List<CustomJsonSettings>
            {
                new CustomJsonSettings() { KeyOfJsonPath = settingsIndivDefaultPath, IsDefault = true },
                new CustomJsonSettings() { KeyOfJsonPath = settingsIndivPath, IsDefault = false }
            };

            dynamic settingsMerged = FilesForJsonHelper.GetMergeSettings(listJsonSettingsToMerge, structureId, eventId, buyerProfilId, platformCode, langCode);

            return settingsMerged;
        }


        public static dynamic DefaultSettings(int structureId, int eventId, int buyerProfilId, string platformCode, string langCode, string PhysicalPathOfSettingsJSONKey)
        {
            _logger.Debug(structureId, "start SettingsMerged");

            string config = _configuration[PhysicalPathOfSettingsJSONKey];
            if (string.IsNullOrEmpty(config))
            {
                _logger.Fatal(structureId, $"can't find app.settings configuration for {PhysicalPathOfSettingsJSONKey}");
                Exception ex = new KeyNotFoundException($"can't find app.settings configuration for {PhysicalPathOfSettingsJSONKey}");
                throw ex;

            }

            //Extrait des chemins des différents appsettings.json (Default + structure)
            string settingsIndivDefaultPath = _configuration[PhysicalPathOfSettingsJSONKey]!
                 .Replace("{structureId}", "DEFAULT")
                 .Replace("[plateformCode]", platformCode)
                 .Replace("{plateformCode}", platformCode);

            _logger.Debug(structureId, $"settingsIndivDefaultPath : {settingsIndivDefaultPath}");

            var listJsonSettingsToMerge = new List<CustomJsonSettings>
            {
                new CustomJsonSettings() { KeyOfJsonPath = settingsIndivDefaultPath, IsDefault = true },

            };

            dynamic settingsMerged = FilesForJsonHelper.GetMergeSettings(listJsonSettingsToMerge, structureId, eventId, buyerProfilId, platformCode, langCode);

            return settingsMerged;
        }




        /// <summary>
        /// Liste des pays à partir du fichier "iso_3166-1_list_en.xml"
        /// </summary>
        /// <param name="structureId"></param>
        /// <param name="eventId"></param>
        /// <param name="buyerProfilId"></param>
        /// <param name="langCode"></param>
        /// <param name="useDefaultIfNotExists"></param>
        /// <returns>Retourne la liste des pays</returns>
        public static List<CountryDTO> GetcountriesList(int structureId, int eventId, int buyerProfilId, string langCode, bool useDefaultIfNotExists)
        {
            List<CountryDTO> countries = new();

            string confKey = "physicalPathOfCountries";
            string xmlCountriesPath = _configuration[confKey]!;
            if (string.IsNullOrWhiteSpace(xmlCountriesPath))
            {
                _logger.Fatal(structureId, $"can't find app.settings configuration for {confKey}");
                Exception ex = new KeyNotFoundException($"can't find app.settings configuration for {confKey}");
                throw ex;
            }

            string xmlCountriesPathWithStructure = xmlCountriesPath.Replace("{structureId}", string.Format("{0,4:0000}", structureId));
            string countriesFileToFind = FilesForXMLHelper.GetCountriesFilePath(xmlCountriesPathWithStructure, "iso_3166-1_list_en.xml", "", structureId, eventId, buyerProfilId, langCode, useDefaultIfNotExists);

            if (!File.Exists(countriesFileToFind))
            {
                xmlCountriesPath = xmlCountriesPath.Replace("{structureId}", "Default");
            }
            if (File.Exists(countriesFileToFind))
            {
                XmlDocument xmlListePays = new XmlDocument();
                xmlListePays.Load(countriesFileToFind);
                XmlNodeList xmlNodesListPays = xmlListePays.SelectNodes(@"ISO_3166-1_List_en/ISO_3166-1_Entry")!;
                foreach (XmlNode xmlNodePays in xmlNodesListPays)
                {
                    CountryDTO country = new()
                    {
                        CountryName = xmlNodePays.SelectSingleNode("ISO_3166-1_Country_name")!.InnerText,
                        ISOCode = xmlNodePays.SelectSingleNode("ISO_3166-1_Alpha-2_Code_element")!.InnerText
                    };

                    countries.Add(country);
                }
            }


            dynamic consumerFormMerged = SettingsMerged(structureId, eventId, 0, "", langCode, "physicalPathOfSettingsCustomerJSON");
            if (consumerFormMerged.global.langForPhones != null)
            {
                if (!string.IsNullOrEmpty(consumerFormMerged.global.langForPhones.Value))
                {
                    // check si la langue du fichier appsettings.json existe dans la liste xml
                    var itemListOfThisLanguage = countries.Where(iso => iso.ISOCode.ToLower() == consumerFormMerged.global.langForPhones.Value.ToLower()).FirstOrDefault();
                    if (itemListOfThisLanguage != null)
                    {
                        langCode = consumerFormMerged.global.langForPhones.Value;
                    }
                }
            }

            var itemThisLanguage = countries.Where(iso => iso.ISOCode.ToLower() == langCode.ToLower()).FirstOrDefault();

            if (itemThisLanguage != null)
            {
                countries.Insert(0, itemThisLanguage);
            }

            return countries;
        }

        /// <summary>
        /// Récupère la devise de la structure via le config.ini        
        /// </summary>
        /// <param name="structureId">Structure du client</param>
        /// <returns>Retourne un objet type Devise </returns>
        public static DeviseDTO GetDeviseCode(int structureId)
        {


            //var gg = RodrigueConfigIniHelper.ConfigInit.ReadConfig(structureId);

            var mySSC = RodrigueConfigIniHelper.ConfigInit.GetDictionaryFromCache(structureId);

            DeviseDTO devise = new();

            if (mySSC.ContainsKey("PARAMDEVISE_ISO") && mySSC["PARAMDEVISE_ISO"] != null && mySSC["PARAMDEVISE_ISO"] != "")
                devise.CodeAIso = mySSC["PARAMDEVISE_ISO"].ToString();

            if (mySSC.ContainsKey("PARAMDEVISE_CODE") && mySSC["PARAMDEVISE_CODE"] != null && mySSC["PARAMDEVISE_CODE"] != "")
                devise.Code = mySSC["PARAMDEVISE_CODE"].ToString();

            if (mySSC.ContainsKey("PARAMDEVISE_BEFORE") && mySSC["PARAMDEVISE_BEFORE"] != null && mySSC["PARAMDEVISE_BEFORE"] != "")
                devise.IsBefore = Convert.ToBoolean(mySSC["PARAMDEVISE_BEFORE"].ToString());

            if (mySSC.ContainsKey("PARAMDEVISE_SEPARATOR") && mySSC["PARAMDEVISE_SEPARATOR"] != null && mySSC["PARAMDEVISE_SEPARATOR"] != "")
                devise.Separator = mySSC["PARAMDEVISE_SEPARATOR"].ToString();

            return devise;
        }

        /// <summary>
        /// Attribut les valeurs (phonenumber, etc..) à l'objet identite
        /// </summary>
        /// <param name="structureId">Structure du client</param>
        /// <param name="identite">Identite dans laquelle les valeurs eronts remplie</param>
        /// <param name="ihmKey">Clé du config.ini à lire</param>
        /// <param name="value">Valeur de la propriété</param>
        /// <exception cref="Exception"></exception>
        public static void SetValuesOfIdentity(int structureId, ref IdentityDTO identite, string ihmKey, string value)
        {
            if (!string.IsNullOrEmpty(value))
            {
                //Lit le config.ini 
                var configIniXml = RodrigueConfigIniHelper.ConfigInit.GetDictionaryFromCache(structureId);

                //lit la clé "VARIABLES" dans le config.ini 
                string valueAttributTelConfigini = configIniXml["VARIABLES" + ihmKey.ToUpper()];
                string valueAttributTypeTelConfigini = configIniXml["VARIABLESTYPE_" + ihmKey.ToUpper()];

                if (!string.IsNullOrEmpty(valueAttributTelConfigini))
                {
                    //Suivant la valeur du config.ini
                    switch (valueAttributTelConfigini)
                    {
                        case "1":
                            identite.PhoneNumber1 = value;
                            identite.LibTel1.Id = int.Parse(valueAttributTypeTelConfigini);
                            break;

                        case "2":
                            identite.PhoneNumber2 = value;
                            identite.LibTel2.Id = int.Parse(valueAttributTypeTelConfigini);
                            break;

                        case "3":
                            identite.PhoneNumber3 = value;
                            identite.LibTel3.Id = int.Parse(valueAttributTypeTelConfigini);
                            break;

                        case "4":
                            identite.PhoneNumber4 = value;
                            identite.LibTel4.Id = int.Parse(valueAttributTypeTelConfigini);
                            break;

                        case "5":
                            identite.PhoneNumber5 = value;
                            identite.LibTel5.Id = int.Parse(valueAttributTypeTelConfigini);
                            break;

                        case "6":
                            identite.PhoneNumber6 = value;
                            identite.LibTel6.Id = int.Parse(valueAttributTypeTelConfigini);
                            break;

                        case "7":
                            identite.PhoneNumber7 = value;
                            identite.LibTel7.Id = int.Parse(valueAttributTypeTelConfigini);
                            break;

                        default:
                            break;
                    }
                }
                else
                {
                    _logger.Error(structureId, "SetValuesOfIdentity Pb IHM key : " + ihmKey);
                    throw new Exception("Pb IHM key : " + ihmKey);
                }
            }
        }

        /// <summary>
        /// Récupère l'adresse URL du custom package
        /// </summary>
        /// <param name="structureId">Structure du client</param>
        /// <returns></returns>
        public static string GetUrlCustomPackage(int structureId)
        {
            string customerFilesPhysicalPath = _configuration["CustomerfilesIndivPhysicalPath"];


            string tmpCustomerFilesPhysicalPath = customerFilesPhysicalPath.Replace("{structureId}", structureId.ToString("0000"));

            string customerFilesUrl = _configuration["CustomerfilesUrlPath"];


            string customePackageFileName = "customPackage.js";
            //Le chemin vers le custompackage du client existe
            if (File.Exists(Path.Combine(tmpCustomerFilesPhysicalPath, "CUSTOMPACKAGE", customePackageFileName)))
            {
                customerFilesUrl = customerFilesUrl.Replace("{structureId}", structureId.ToString("0000"));

                return Path.Combine(customerFilesUrl, "CUSTOMPACKAGE", customePackageFileName);
            }
            else
            {
                //on prends le custompackage par defaut
                tmpCustomerFilesPhysicalPath = customerFilesPhysicalPath.Replace("{structureId}", "Default");

                if (File.Exists(Path.Combine(tmpCustomerFilesPhysicalPath, "CUSTOMPACKAGE", customePackageFileName)))
                {
                    customerFilesUrl = customerFilesUrl.Replace("{structureId}", "Default");

                    return Path.Combine(customerFilesUrl, "CUSTOMPACKAGE", customePackageFileName);
                }
            }

            return "";
        }

        /// <summary>
        /// Récupère l'adresse URL des CGV 
        /// </summary>
        /// <param name="structureId"></param>
        /// <param name="langCode"></param>
        /// <param name="settingsMerged"></param>
        /// <returns></returns>
        public static string GetCGVUrl(int structureId, string langCode, dynamic settingsMerged)
        {
            if (!string.IsNullOrEmpty(settingsMerged.basket.CGVurl.Value))
            {
                var customerFilesIndiv = _configuration["CustomerfilesIndivPhysicalPath"]!;
                string conditionsFileName = Path.GetFileName(settingsMerged.basket.CGVurl.Value);

                //chemin du fichier CGV dans la structure
                string tmpCustomerFilesPhysicalPathWithStructure = customerFilesIndiv.Replace("[structureId]", structureId.ToString("0000")).Replace("{structureId}", structureId.ToString("0000"));
                //Nom du fichier avec la langue
                string tmpConditionsFileNameWithLang = conditionsFileName.Replace("[lang]", "." + langCode);

                //Check le chemin dans la structure avec la langue code sur le fichier
                if (File.Exists(Path.Combine(tmpCustomerFilesPhysicalPathWithStructure, "FILESINFOS", tmpConditionsFileNameWithLang)))
                {
                    return settingsMerged.basket.CGVurl.ToString().Replace("[structureId]", structureId.ToString("0000")).Replace("{structureId}", structureId.ToString("0000")).Replace("[lang]", "." + langCode);
                }
                //Nom du fichier avec la langue
                string tmpConditionsFileNameWithoutLang = conditionsFileName.Replace("[lang]", "");
                //Check le chemin dans la structure sans la langue code sur le fichier
                if (File.Exists(Path.Combine(tmpCustomerFilesPhysicalPathWithStructure, "FILESINFOS", tmpConditionsFileNameWithoutLang)))
                {
                    return settingsMerged.basket.CGVurl.ToString().Replace("[structureId]", structureId.ToString("0000")).Replace("{structureId}", structureId.ToString("0000")).Replace("[lang]", "");
                }

                //Chemin sans la structure (DEFAULT)
                string tmpCustomerFilesPhysicalPathWithoutStructure = customerFilesIndiv.Replace("[structureId]", "DEFAULT").Replace("{structureId}", "DEFAULT");

                //Check le chemin sans la structure (DEFUALT) et sans la langue dans le nom du fichier
                if (File.Exists(Path.Combine(tmpCustomerFilesPhysicalPathWithoutStructure, "FILESINFOS", tmpConditionsFileNameWithoutLang)))
                {
                    return settingsMerged.basket.CGVurl.ToString().Replace("[structureId]", "DEFAULT").Replace("{structureId}", "DEFAULT").Replace("[lang]", "");
                }

                return settingsMerged.basket.CGVurl.Value;

            }
            return "";
        }

        public static List<EventDTO> SetImageToEvents(int structureId, List<EventDTO> listEvents, string eventsImagesUrlDecoded)
        {
            foreach (var evt in listEvents)
            {
                if (!string.IsNullOrEmpty(eventsImagesUrlDecoded))
                {
                    evt.eventImgUrl = eventsImagesUrlDecoded.Replace("{structureId}", structureId.ToString("0000")) + evt.EventId + ".gif";
                }
                else
                {
                    string eventImgUrl = _configuration["Images:EventsImagesUrlPath"]!.Replace("{structureId}", structureId.ToString("0000")) + evt.EventId + ".gif";
                    string eventImgUrlNoImage = _configuration["Images:EventsImagesUrlPath"]!.Replace("{structureId}", structureId.ToString("0000")) + "noimage.jpg";
                    string eventImgUrlDefaultNoImage = _configuration["Images:AssetsImagesUrlPath"]!.ToString() + "noimage.jpg";

                    string baseImagesPhysicalPath = _configuration["Images:BaseImagesPhysicalPath"]!.ToString().Replace("{structureId}", structureId.ToString("0000")); ;

                    string imagePath = Path.Combine(baseImagesPhysicalPath, $"manifestations\\{evt.EventId}.gif");
                    string noImagePath = Path.Combine(baseImagesPhysicalPath, $"manifestations\\{evt.EventId}.gif");
                    _logger.Debug(structureId, $"SetImageToEvents baseImagesPhysicalPath: {baseImagesPhysicalPath}");
                    _logger.Debug(structureId, $"SetImageToEvents imagePath: {imagePath}");
                    _logger.Debug(structureId, $"SetImageToEvents noImagePath: {noImagePath}");

                    if (File.Exists(imagePath))
                    {
                        evt.eventImgUrl = eventImgUrl;
                    }
                    else if (File.Exists(Path.Combine(baseImagesPhysicalPath, $"manifestations\\noimage.jpg")))
                    {
                        evt.eventImgUrl = eventImgUrlNoImage;
                    }
                    else
                    {
                        evt.eventImgUrl = eventImgUrlDefaultNoImage;
                    }
                }
            }

            return listEvents;
        }

    }
}
