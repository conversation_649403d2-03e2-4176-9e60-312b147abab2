{"ContentRoots": ["C:\\Users\\<USER>\\.nuget\\packages\\blazordaterangepicker\\5.3.0\\staticwebassets\\", "C:\\Users\\<USER>\\.nuget\\packages\\blazor.bootstrap\\3.2.0\\staticwebassets\\", "D:\\WORK\\TST_MAIN\\ThemisSupportTools_MAIN\\Intranet\\ThemisSupportTools.Web\\ThemisSupportTools.Web\\wwwroot\\", "D:\\WORK\\TST_MAIN\\ThemisSupportTools_MAIN\\Libraries\\Core.Themis.Libraries.Razor\\Core.Themis.Libraries.Razor\\", "D:\\WORK\\TST_MAIN\\ThemisSupportTools_MAIN\\Libraries\\Core.Themis.Libraries.Razor\\Core.Themis.Libraries.Razor\\obj\\Debug\\net8.0\\scopedcss\\projectbundle\\", "D:\\WORK\\TST_MAIN\\ThemisSupportTools_MAIN\\Libraries\\Core.Themis.Libraries.Razor\\Core.Themis.Libraries.Razor\\wwwroot\\", "D:\\WORK\\TST_MAIN\\ThemisSupportTools_MAIN\\Intranet\\ThemisSupportTools.Web\\ThemisSupportTools.Web\\obj\\Debug\\net8.0\\scopedcss\\bundle\\"], "Root": {"Children": {"_content": {"Children": {"BlazorDateRangePicker": {"Children": {"clickAndPositionHandler.js": {"Children": null, "Asset": {"ContentRootIndex": 0, "SubPath": "clickAndPositionHandler.js"}, "Patterns": null}, "BlazorDateRangePicker.bundle.scp.css": {"Children": null, "Asset": {"ContentRootIndex": 0, "SubPath": "BlazorDateRangePicker.bundle.scp.css"}, "Patterns": null}}, "Asset": null, "Patterns": null}, "Blazor.Bootstrap": {"Children": {"blazor.bootstrap.css": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "blazor.bootstrap.css"}, "Patterns": null}, "blazor.bootstrap.js": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "blazor.bootstrap.js"}, "Patterns": null}, "blazor.bootstrap.ai.js": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "blazor.bootstrap.ai.js"}, "Patterns": null}, "blazor.bootstrap.pdf.js": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "blazor.bootstrap.pdf.js"}, "Patterns": null}, "blazor.bootstrap.sortable-list.js": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "blazor.bootstrap.sortable-list.js"}, "Patterns": null}, "blazor.bootstrap.theme-switcher.js": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "blazor.bootstrap.theme-switcher.js"}, "Patterns": null}, "icon": {"Children": {"128X128.png": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "icon/128X128.png"}, "Patterns": null}}, "Asset": null, "Patterns": null}, "pdfjs-4.0.379.min.js": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "pdfjs-4.0.379.min.js"}, "Patterns": null}, "pdfjs-4.0.379.worker.min.js": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "pdfjs-4.0.379.worker.min.js"}, "Patterns": null}, "Blazor.Bootstrap.7t9tbfaemk.bundle.scp.css": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "Blazor.Bootstrap.7t9tbfaemk.bundle.scp.css"}, "Patterns": null}}, "Asset": null, "Patterns": null}, "Core.Themis.Libraries.Razor": {"Children": {"Common": {"Children": {"Components": {"Children": {"Inputs": {"Children": {"InputImageFileCustom.razor.js": {"Children": null, "Asset": {"ContentRootIndex": 3, "SubPath": "Common/Components/Inputs/InputImageFileCustom.razor.js"}, "Patterns": null}, "InputTextAreaCustom.razor.js": {"Children": null, "Asset": {"ContentRootIndex": 3, "SubPath": "Common/Components/Inputs/InputTextAreaCustom.razor.js"}, "Patterns": null}}, "Asset": null, "Patterns": null}, "Select": {"Children": {"NewSelect2.razor.js": {"Children": null, "Asset": {"ContentRootIndex": 3, "SubPath": "Common/Components/Select/NewSelect2.razor.js"}, "Patterns": null}, "Select2.razor.js": {"Children": null, "Asset": {"ContentRootIndex": 3, "SubPath": "Common/Components/Select/Select2.razor.js"}, "Patterns": null}}, "Asset": null, "Patterns": null}, "Sortable": {"Children": {"SortableList.razor.js": {"Children": null, "Asset": {"ContentRootIndex": 3, "SubPath": "Common/Components/Sortable/SortableList.razor.js"}, "Patterns": null}}, "Asset": null, "Patterns": null}}, "Asset": null, "Patterns": null}}, "Asset": null, "Patterns": null}, "Core.Themis.Libraries.Razor.rpcwcmxhht.bundle.scp.css": {"Children": null, "Asset": {"ContentRootIndex": 4, "SubPath": "Core.Themis.Libraries.Razor.bundle.scp.css"}, "Patterns": null}, "css": {"Children": {"common.less": {"Children": null, "Asset": {"ContentRootIndex": 5, "SubPath": "css/common.less"}, "Patterns": null}, "widget.css": {"Children": null, "Asset": {"ContentRootIndex": 5, "SubPath": "css/widget.css"}, "Patterns": null}, "widget-modules": {"Children": {"button.css": {"Children": null, "Asset": {"ContentRootIndex": 5, "SubPath": "css/widget-modules/button.css"}, "Patterns": null}, "loader.css": {"Children": null, "Asset": {"ContentRootIndex": 5, "SubPath": "css/widget-modules/loader.css"}, "Patterns": null}, "modal.css": {"Children": null, "Asset": {"ContentRootIndex": 5, "SubPath": "css/widget-modules/modal.css"}, "Patterns": null}}, "Asset": null, "Patterns": null}}, "Asset": null, "Patterns": null}, "js": {"Children": {"common_razor_library.js": {"Children": null, "Asset": {"ContentRootIndex": 5, "SubPath": "js/common_razor_library.js"}, "Patterns": null}, "phoneInput.js": {"Children": null, "Asset": {"ContentRootIndex": 5, "SubPath": "js/phoneInput.js"}, "Patterns": null}, "widget.js": {"Children": null, "Asset": {"ContentRootIndex": 5, "SubPath": "js/widget.js"}, "Patterns": null}}, "Asset": null, "Patterns": null}}, "Asset": null, "Patterns": [{"ContentRootIndex": 5, "Pattern": "**", "Depth": 2}]}}, "Asset": null, "Patterns": null}, "app.css": {"Children": null, "Asset": {"ContentRootIndex": 2, "SubPath": "app.css"}, "Patterns": null}, "bootstrap": {"Children": {"bootstrap.min.css": {"Children": null, "Asset": {"ContentRootIndex": 2, "SubPath": "bootstrap/bootstrap.min.css"}, "Patterns": null}, "bootstrap.min.css.map": {"Children": null, "Asset": {"ContentRootIndex": 2, "SubPath": "bootstrap/bootstrap.min.css.map"}, "Patterns": null}}, "Asset": null, "Patterns": null}, "css": {"Children": {"CheckWaitingPages.css": {"Children": null, "Asset": {"ContentRootIndex": 2, "SubPath": "css/CheckWaitingPages.css"}, "Patterns": null}}, "Asset": null, "Patterns": null}, "favicon.png": {"Children": null, "Asset": {"ContentRootIndex": 2, "SubPath": "favicon.png"}, "Patterns": null}, "js": {"Children": {"script.js": {"Children": null, "Asset": {"ContentRootIndex": 2, "SubPath": "js/script.js"}, "Patterns": null}}, "Asset": null, "Patterns": null}, "ThemisSupportTools.Web.styles.css": {"Children": null, "Asset": {"ContentRootIndex": 6, "SubPath": "ThemisSupportTools.Web.styles.css"}, "Patterns": null}}, "Asset": null, "Patterns": [{"ContentRootIndex": 2, "Pattern": "**", "Depth": 0}]}}