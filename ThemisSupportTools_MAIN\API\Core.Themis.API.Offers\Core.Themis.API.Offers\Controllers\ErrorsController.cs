﻿using Core.Themis.API.Offers.Models;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Diagnostics;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Core.Themis.API.Offers.Controllers
{
[AllowAnonymous]
[ApiExplorerSettings(IgnoreApi = true)]
    public class ErrorsController : ControllerBase
    {
        //[Route("error")]
        //public myErrorResponse Error()
        //{
        //    var context = HttpContext.Features.Get<IExceptionHandlerFeature>();
        //    var exception = new Exception("from controller Error");
            
        //    // Your exception
        //    var code = 500; // Internal Server Error by default

        //    //if (exception is MyNotFoundException) code = 404; // Not Found
        //    //else if (exception is MyUnauthException) code = 401; // Unauthorized
        //    //else if (exception is MyException) code = 400; // Bad Request

        //    Response.StatusCode = code; // You can use HttpStatusCode enum instead

        //    return new myErrorResponse(exception); // Your error model
        //}
    }
}
