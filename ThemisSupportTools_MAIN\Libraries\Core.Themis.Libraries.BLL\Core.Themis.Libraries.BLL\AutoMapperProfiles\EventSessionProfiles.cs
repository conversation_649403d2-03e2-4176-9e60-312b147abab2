﻿using AutoMapper;
using Core.Themis.Libraries.Data.Entities.Open.Abonnement;
using Core.Themis.Libraries.Data.Entities.Open.BuyerProfil;
using Core.Themis.Libraries.Data.Entities.Open.CarnetTickets;
using Core.Themis.Libraries.Data.Entities.Open.EventsSessions;
using Core.Themis.Libraries.Data.Entities.Open.GestionPlace;
using Core.Themis.Libraries.Data.Entities.Open.Identite;
using Core.Themis.Libraries.Data.Entities.Open.InfoComps;
using Core.Themis.Libraries.Data.Entities.Open.Lieu;
using Core.Themis.Libraries.Data.Entities.Open.Product;
using Core.Themis.Libraries.Data.Entities.Open.Sponsors;
using Core.Themis.Libraries.Data.Entities.Open.Structure;
using Core.Themis.Libraries.Data.Entities.Open.Tarif;
using Core.Themis.Libraries.Data.Entities.Open.Traduction;
using Core.Themis.Libraries.Data.Entities.WebTracing;
using Core.Themis.Libraries.DTO;
using Core.Themis.Libraries.DTO.CarnetTickets;
using Core.Themis.Libraries.DTO.EventsSessions;
using Core.Themis.Libraries.DTO.LinkAboFermeMaquette;
using Core.Themis.Libraries.DTO.Identity;
using Core.Themis.Libraries.DTO.InfoComps;
using Core.Themis.Libraries.DTO.Transactionnals;
using System;
using System.Collections.Generic;
using System.Linq;


namespace Core.Themis.Libraries.BLL.AutoMapperProfiles
{
    public class EventSessionProfiles : Profile
    {
        private const int PlacementLibre = 32;
        private const int PriseAuto = 16;
        private const int VuePlacement = 8;
        private const int ChoixSurPlan = 1;


        public EventSessionProfiles()
        {


            CreateMap<ManifestationEntity, EventDTO>()
                .ForMember(dest => dest.EventId, opt => opt.MapFrom(src => src.ManifestationId))
                .ForMember(dest => dest.EventName, opt => opt.MapFrom(src => src.ManifestationNom))
                .ForMember(dest => dest.EventCode, opt => opt.MapFrom(src => src.ManifestationCode))
                .ForMember(dest => dest.EventDescription1, opt => opt.MapFrom(src => src.ManifestationDescrip ?? string.Empty))
                .ForMember(dest => dest.EventDescription2, opt => opt.MapFrom(src => src.ManifestationDescrip2 ?? string.Empty))
                .ForMember(dest => dest.EventDescription3, opt => opt.MapFrom(src => src.ManifestationDescrip3 ?? string.Empty))
                .ForMember(dest => dest.EventDescription4, opt => opt.MapFrom(src => src.ManifestationDescrip4 ?? string.Empty))
                .ForMember(dest => dest.EventDescription5, opt => opt.MapFrom(src => src.ManifestationDescrip5 ?? string.Empty))
                .ForMember(dest => dest.EventGenreId, opt => opt.MapFrom(src => src.IdGenre))
                .ForMember(dest => dest.ListSessions, opt => opt.MapFrom(src => src.Seances))

                .ForMember(dest => dest.ProducerName, opt => opt.MapFrom(src => src.Producteur != null ? src.Producteur.ProducteurNom.Trim() : string.Empty))
                .ForMember(dest => dest.ProducerLicenceNum1, opt => opt.MapFrom(src => src.Producteur != null ? src.Producteur.NumLicence1.Trim() : string.Empty))
                .ForMember(dest => dest.ProducerLicenceNum2, opt => opt.MapFrom(src => src.Producteur != null ? src.Producteur.NumLicence2.Trim() : string.Empty))

                .ForMember(dest => dest.Summary, opt => opt.MapFrom(src => src.ManifestationInfos != null ? src.ManifestationInfos.ResumeManif.Trim() : string.Empty))
                .ForMember(dest => dest.Distribution, opt => opt.MapFrom(src => src.ManifestationInfos != null ? src.ManifestationInfos.Distribution.Trim() : string.Empty))
                .ForMember(dest => dest.Directed, opt => opt.MapFrom(src => src.ManifestationInfos != null ? src.ManifestationInfos.MiseEnScene.Trim() : string.Empty))
                .ForMember(dest => dest.DurationText, opt => opt.MapFrom(src => src.ManifestationInfos != null ? src.ManifestationInfos.Duree.Trim() : string.Empty))
                .ForMember(dest => dest.Duration, opt => opt.MapFrom(src => GetDurationInt(src)))

                .ForMember(dest => dest.TraductionGpWaitingEvents, opt => opt.MapFrom(src => GetTraductionsDto(src.GPManifestation.TraductionGpManifestationsForWaiting)))
                .ForMember(dest => dest.TraductionGpLockEvents, opt => opt.MapFrom(src => GetTraductionsDto(src.GPManifestation.TraductionGpManifestationsForLock)))
                .ForMember(dest => dest.EventIsLock, opt => opt.MapFrom(src => src.GPManifestation.IsLock))
                .ForMember(dest => dest.EventIsWaiting, opt => opt.MapFrom(src => src.GPManifestation.IsListeAttente))

                .ForMember(dest => dest.EventGroup, opt => opt.MapFrom(src => src.ManifestationGroupe))

                .ForMember(dest => dest.StructureId, opt => opt.MapFrom(src => src.StructureId.ToString()));




            CreateMap<ManifestationGroupeEntity, EventsGroupsDTO>()
                .ForMember(dest => dest.EventGroupId, opt => opt.MapFrom(src => src.ManifGroupeId))
                .ForMember(dest => dest.SuperGroupe, opt => opt.MapFrom(src => src.ManifestationSuperGroupe))
                .ForMember(dest => dest.EventGroupName, opt => opt.MapFrom(src => src.ManifGroupeNom))
                .ForMember(dest => dest.EventGroupCode, opt => opt.MapFrom(src => src.ManifGroupeCode))
                .ForMember(dest => dest.Events, opt => opt.MapFrom(src => src.Manifestations));

            CreateMap<ManifestationSuperGroupeEntity, EventSuperGroupDTO>()
                .ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.IdSuperGroupe))
                .ForMember(dest => dest.Name, opt => opt.MapFrom(src => src.Libelle))
                .ForMember(dest => dest.Code, opt => opt.MapFrom(src => src.Code))
                .ForMember(dest => dest.EventsGroupsDTOs, opt => opt.MapFrom(src => src.ManifestationsGroupe))
                .ForMember(dest => dest.IsVisible, opt => opt.MapFrom(src => src.Masquer=='N'));


            CreateMap<GestionPlaceEntity, GestionPlaceDTO>()
                .ForMember(dest => dest.IsPlacementLibre, opt => opt.MapFrom(src => src.PrisePlace & PlacementLibre))
                .ForMember(dest => dest.IsAutomatique, opt => opt.MapFrom(src => src.PrisePlace & PriseAuto))
                .ForMember(dest => dest.IsSurPlan, opt => opt.MapFrom(src => src.PrisePlace & ChoixSurPlan))
                .ForMember(dest => dest.IsVoirPlace, opt => opt.MapFrom(src => src.PrisePlace & VuePlacement))
                .ForMember(dest => dest.EventId, opt => opt.MapFrom(src => src.ManifId ?? 0))
                .ForMember(dest => dest.NbMin, opt => opt.MapFrom(src => src.NbMin ?? 0))
                .ForMember(dest => dest.NbMax, opt => opt.MapFrom(src => src.NbMax ?? 0))
                .ForMember(dest => dest.CategoryId, opt => opt.MapFrom(src => src.CategId ?? 0))
                .ForMember(dest => dest.Categorie, opt => opt.MapFrom(src => src.Categorie))
                .ForMember(dest => dest.Price, opt => opt.MapFrom(src => src.TypeTarif));

            CreateMap<SeanceEntity, SessionDTO>()
                .ForMember(dest => dest.SessionId, opt => opt.MapFrom(src => src.SeanceId))
                .ForMember(dest => dest.EventId, opt => opt.MapFrom(src => src.ManifestationId))
                .ForMember(dest => dest.Place, opt => opt.MapFrom(src => src.Lieu))
                .ForMember(dest => dest.EventIsLock, opt => opt.MapFrom(src => src.SeanceVerrouiller == 'N' ? false : true))
                .ForMember(dest => dest.IsShowSessionDate, opt => opt.MapFrom(src => src.NePasAfficherDate == 'O' ? false : true))
                .ForMember(dest => dest.IsShowSessionHour, opt => opt.MapFrom(src => (src.NePasAfficherDate == 'H' || src.NePasAfficherDate == 'O') ? false : true))
                .ForMember(dest => dest.GestionPlaces, opt => opt.MapFrom(src => (src.GestionPlaces)))
                .ForMember(dest => dest.SessionStartDate, opt => opt.MapFrom(src => src.SeanceDateDeb))
                .ForMember(dest => dest.SessionEndDate, opt => opt.MapFrom(src => src.SeanceDateFin))
            ;



            CreateMap<LieuEntity, PlaceDTO>()
                .ForMember(dest => dest.PlaceId, opt => opt.MapFrom(src => src.LieuId))
                .ForMember(dest => dest.PlaceName, opt => opt.MapFrom(src => src.LieuNom))
                .ForMember(dest => dest.PlaceCode, opt => opt.MapFrom(src => src.LieuCode))
                .ForMember(dest => dest.Address_address1, opt => opt.MapFrom(src => src.lieuRue1))
                .ForMember(dest => dest.Address_address2, opt => opt.MapFrom(src => src.lieuRue2))
                .ForMember(dest => dest.Address_address3, opt => opt.MapFrom(src => src.lieuRue3))
                .ForMember(dest => dest.Address_address4, opt => opt.MapFrom(src => src.lieuRue4))
                .ForMember(dest => dest.Address_city, opt => opt.MapFrom(src => src.lieuVille))
                .ForMember(dest => dest.Address_postal_code, opt => opt.MapFrom(src => src.lieuCp))
                .ForMember(dest => dest.Address_region, opt => opt.MapFrom(src => src.lieuRegion))
                .ForMember(dest => dest.Address_country, opt => opt.MapFrom(src => src.lieuPays));


            CreateMap<SponsorEntity, SponsorDTO>();

            CreateMap<SponsorPanierEntreesEntity, SponsorPanierEntreesDTO>();

            CreateMap<SponsorPaymentEntity, SponsorPaymentDTO>();

            CreateMap<GestionPlaceTypeEnvoiEntity, GestionPlaceTypeEnvoiDTO>();


            CreateMap<TypeTarifEntity, PriceDTO>()
                .ForMember(dest => dest.PriceId, opt => opt.MapFrom(src => src.TypeTarifId))
                .ForMember(dest => dest.PriceName, opt => opt.MapFrom(src => src.TypeTarifNom))
                .ForMember(dest => dest.Masquer, opt => opt.MapFrom(src => (src.Masquer == 'N') ? false : true))
                .ForMember(dest => dest.Internet, opt => opt.MapFrom(src => src.Internet == 1 ? true : false))
                .ForMember(dest => dest.PriceCode, opt => opt.MapFrom(src => src.TypeTarifCode))
                .ForMember(dest => dest.DisplayRank, opt => opt.MapFrom(src => src.PrefAffichage));


            CreateMap<ProfilAcheteurEntity, BuyerProfilDTO>()
                 .ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.Id))
                 .ForMember(dest => dest.Name, opt => opt.MapFrom(src => src.Nom))
                 .ForMember(dest => dest.Libelle, opt => opt.MapFrom(src => src.Libelle))
                 .ForMember(dest => dest.OperatorId, opt => opt.MapFrom(src => src.OperateurId))
                 .ForMember(dest => dest.OperatorPaymentId, opt => opt.MapFrom(src => src.PaiementOperateurId))
                 .ForMember(dest => dest.IdentityPaiement, opt => opt.MapFrom(src => src.PaiementProfilAcheteur))
                 .ForMember(dest => dest.IsReseller, opt => opt.MapFrom(src => src.IsRevendeur))
                 .ForMember(dest => dest.ConsumerNeeded, opt => opt.MapFrom(src => !src.ConsumerNeeded.HasValue ? false : src.ConsumerNeeded.Value == 0 ? false : true))
                 .ForMember(dest => dest.CreationDate, opt => opt.MapFrom(src => src.DateCreation))
                 .ForMember(dest => dest.IsExclusiveMode, opt => opt.MapFrom(src => src.IsExclusiveMode))
                 .ForMember(dest => dest.IsInsuranceActive, opt => opt.MapFrom(src => src.IsInsuranceActive));


            CreateMap<ProprietesOfManifsEntity, ProprietesOfManifsDTO>()
                .ForMember(dest => dest.EventId, opt => opt.MapFrom(src => src.ManifestationId))
                .ForMember(dest => dest.PropertieRefId, opt => opt.MapFrom(src => src.ProprieteRefId))
                .ForMember(dest => dest.Value, opt => opt.MapFrom(src => src.Valeur));



            CreateMap<TraductionManifestationGenreEntity, TraductionEventGenreDTO>()
                .ForMember(dest => dest.EventGenreId, opt => opt.MapFrom(src => src.Id))
                .ForMember(dest => dest.EventGenreName, opt => opt.MapFrom(src => src.Nom))
                .ForMember(dest => dest.LangueId, opt => opt.MapFrom(src => src.LangueId));

            CreateMap<TraductionManifestationSousGenreEntity, TraductionEventSousGenreDTO>()
               .ForMember(dest => dest.EventSousGenreId, opt => opt.MapFrom(src => src.Id))
               .ForMember(dest => dest.EventGenreName, opt => opt.MapFrom(src => src.Nom))
               .ForMember(dest => dest.LangueId, opt => opt.MapFrom(src => src.LangueId));


            CreateMap<ManifestationGenreEntity, EventGenreDTO>()
                .ForMember(dest => dest.EventGenreId, opt => opt.MapFrom(src => src.Id))
                .ForMember(dest => dest.EventGenreName, opt => opt.MapFrom(src => src.Nom))
                .ForMember(dest => dest.EventGenreCode, opt => opt.MapFrom(src => src.Code))
                .ForMember(dest => dest.TraductionEventGenres, opt => opt.MapFrom(src => src.TraductionManifestationGenres))
                .ForMember(dest => dest.EventsSousGenresList, opt => opt.MapFrom(src => src.ManifestationSousGenres));


            CreateMap<ManifestationSousGenreEntity, EventsSousGenreDTO>()
                .ForMember(dest => dest.SousGenreId, opt => opt.MapFrom(src => src.Id))
                .ForMember(dest => dest.SousGenreName, opt => opt.MapFrom(src => src.Nom))
                .ForMember(dest => dest.SousGenreCode, opt => opt.MapFrom(src => src.Code))
                .ForMember(dest => dest.GenreId, opt => opt.MapFrom(src => src.GroupeId))
                .ForMember(dest => dest.TraductionEventSousGenres, opt => opt.MapFrom(src => src.TraductionManifestationSousGenres))
                .ForMember(dest => dest.EventGenre, opt => opt.MapFrom(src => src.Genre));




            CreateMap<ModePaiementOfProfilAcheteur, PaymentMethodDTO>()
                .ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.ModePaieId));

            CreateMap<CibleEntity, TargetDTO>()
                .ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.Id))
                .ForMember(dest => dest.Name, opt => opt.MapFrom(src => src.Nom));

            CreateMap<LangueEntity, LanguageDTO>()
                .ForMember(dest => dest.LanguageId, opt => opt.MapFrom(src => src.LangueId))
                .ForMember(dest => dest.LanguageName, opt => opt.MapFrom(src => src.LangueNom))
                .ForMember(dest => dest.LanguageCode, opt => opt.MapFrom(src => src.LangueCode))
                .ForMember(dest => dest.LanguageIsDelete, opt => opt.MapFrom(src => src.Supprimer == 'N' ? false : true));

            CreateMap<LinkAboFermeMaquetteEntity, LinkAboFermeMaquetteDTO>()
                 .ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.Id))
                 .ForMember(dest => dest.FormuleId, opt => opt.MapFrom(src => src.FormuleId))
                 .ForMember(dest => dest.SeanceIdRef, opt => opt.MapFrom(src => src.SeanceIdRef))
                 .ForMember(dest => dest.MaquetteId, opt => opt.MapFrom(src => src.MaquetteId))
                 .ForMember(dest => dest.DateOperation, opt => opt.MapFrom(src => src.DateOperation));

            CreateMap<LinkAboFermeMaquetteDTO, LinkAboFermeMaquetteEntity>()
             .ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.Id))
             .ForMember(dest => dest.FormuleId, opt => opt.MapFrom(src => src.FormuleId))
             .ForMember(dest => dest.SeanceIdRef, opt => opt.MapFrom(src => src.SeanceIdRef))
             .ForMember(dest => dest.MaquetteId, opt => opt.MapFrom(src => src.MaquetteId))
             .ForMember(dest => dest.DateOperation, opt => opt.MapFrom(src => src.DateOperation));



            CreateMap<InfoCompGroupEntity, InfoCompGroupDTO>()
                .ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.GroupeId))
                .ForMember(dest => dest.Libelle, opt => opt.MapFrom(src => src.Libelle))
                .ForMember(dest => dest.IsVisible, opt => opt.MapFrom(src => src.Masquer == 'N' ? true : false))
                .ForMember(dest => dest.InfoComps, opt => opt.MapFrom(src => src.InfoComps));

            CreateMap<InfoCompEntity, InfoCompDTO>()
                .ForMember(dest => dest.infocomp_id, opt => opt.MapFrom(src => src.InfocompId))
                .ForMember(dest => dest.libelle, opt => opt.MapFrom(src => src.Libelle))  ;
        }




        private string GetTranslationSousGenre(ManifestationSousGenreEntity sgEntity)
        {

            if (sgEntity.TraductionManifestationSousGenres != null && sgEntity.TraductionManifestationSousGenres.Count > 0)
            {
                string trad = sgEntity.TraductionManifestationSousGenres.FirstOrDefault()?.Nom;
                if (!string.IsNullOrEmpty(trad)) { return trad; }
            }

            return sgEntity.Nom;
        }

        public static TimeSpan GetDuration(ICollection<SeanceEntity> seances)
        {
            var iDuration = seances.FirstOrDefault().CncSeanceInfos.DureeFilm;
            return TimeSpan.FromMinutes(iDuration);
        }
        private int GetDurationInt(ManifestationEntity manifestation)
        {
            if (manifestation?.ManifestationInfos != null && int.TryParse(manifestation?.ManifestationInfos.Duree, out int dureeInt))
            {
                return dureeInt;
            }
            return 0;
        }
        public static List<TraductionDTO> GetTraductionsDto(TraductionEntity? traductionEntity)
        {
            List<TraductionDTO> traductionDTOs = new List<TraductionDTO>();

            if (traductionEntity is null)
                return traductionDTOs;

            #region Convertit les colonnes de traductionsEntity en objet dto
            if (!string.IsNullOrWhiteSpace(traductionEntity.Langue1))
            {
                traductionDTOs.Add(new TraductionDTO()
                {
                    LangueId = 1,
                    TraductionId = traductionEntity.TraductionId,
                    Traduction = traductionEntity.Langue1
                });
            }
 

            if (!string.IsNullOrWhiteSpace(traductionEntity.Langue2))
            {
                traductionDTOs.Add(new TraductionDTO()
                {
                    LangueId = 2,
                    TraductionId = traductionEntity.TraductionId,
                    Traduction = traductionEntity.Langue2
                });
            }

            if (!string.IsNullOrWhiteSpace(traductionEntity.Langue3))
            {
                traductionDTOs.Add(new TraductionDTO()
                {
                    LangueId = 3,
                    TraductionId = traductionEntity.TraductionId,
                    Traduction = traductionEntity.Langue3
                });
            }

            if (!string.IsNullOrWhiteSpace(traductionEntity.Langue4))
            {
                traductionDTOs.Add(new TraductionDTO()
                {
                    LangueId = 4,
                    TraductionId = traductionEntity.TraductionId,
                    Traduction = traductionEntity.Langue4
                });
            }

            if (!string.IsNullOrWhiteSpace(traductionEntity.Langue5))
            {
                traductionDTOs.Add(new TraductionDTO()
                {
                    LangueId = 5,
                    TraductionId = traductionEntity.TraductionId,
                    Traduction = traductionEntity.Langue5
                });
            }
         

            if (!string.IsNullOrWhiteSpace(traductionEntity.Langue6))
            {
                traductionDTOs.Add(new TraductionDTO()
                {
                    LangueId = 6,
                    TraductionId = traductionEntity.TraductionId,
                    Traduction = traductionEntity.Langue6
                });
            }

            if (!string.IsNullOrWhiteSpace(traductionEntity.Langue7))
            {
                traductionDTOs.Add(new TraductionDTO()
                {
                    LangueId = 7,
                    TraductionId = traductionEntity.TraductionId,
                    Traduction = traductionEntity.Langue7
                });
            }


            if (!string.IsNullOrWhiteSpace(traductionEntity.Langue8))
            {
                traductionDTOs.Add(new TraductionDTO()
                {
                    LangueId = 8,
                    TraductionId = traductionEntity.TraductionId,
                    Traduction = traductionEntity.Langue8
                });
            }

            if (!string.IsNullOrWhiteSpace(traductionEntity.Langue9))
            {
                traductionDTOs.Add(new TraductionDTO()
                {
                    LangueId = 9,
                    TraductionId = traductionEntity.TraductionId,
                    Traduction = traductionEntity.Langue9
                });
            }

            if (!string.IsNullOrWhiteSpace(traductionEntity.Langue10))
            {
                traductionDTOs.Add(new TraductionDTO()
                {
                    LangueId = 10,
                    TraductionId = traductionEntity.TraductionId,
                    Traduction = traductionEntity.Langue10
                });
            }

            #endregion

            return traductionDTOs;

        }
    }
}
