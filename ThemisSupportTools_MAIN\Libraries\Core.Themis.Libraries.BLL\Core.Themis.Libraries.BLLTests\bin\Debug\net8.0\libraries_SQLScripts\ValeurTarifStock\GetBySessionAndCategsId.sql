﻿/*
DECLARE @pCategsId varchar(max)= '10002, 10004, 10005,10007'
DECLARE @pSessionsId VARCHAR(MAX) = '22,2369'
DECLARE @pLangCode VARCHAR(2) = 'FR'
*/

DECLARE @langueId int = (SELECT langue_id FROM langue WHERE langue_code = @pLangCode)

SELECT vts.type_tarif_id, ISNULL(ttt.type_tarif_nom, tt.type_tarif_nom) as type_tarif_nom 
FROM valeur_tarif_stock[eventId] vts
INNER JOIN type_tarif tt ON tt.type_tarif_id = vts.type_tarif_id
INNER JOIN type_tarif_groupe ttg ON ttg.type_tarif_groupe_id = tt.type_tarif_groupe
INNER JOIN filieres_droits fd ON fd.type_tarif_id = tt.type_tarif_id
INNER JOIN filiere f ON f.filiere_id = fd.filiere_id
LEFT OUTER JOIN traduction_type_tarif ttt ON ttt.type_tarif_id = tt.type_tarif_id AND ttt.langue_id = @langueId
 WHERE ttg.type_tarif_groupe_code in ('INTERNET', 'TGR', 'TSC', 'TIL', 'TAU','ADH' )

AND seance_id IN (SELECT name FROM splitstring(@pSessionsId, ',')) 
AND categ_id IN (SELECT name FROM splitstring(@pCategsId,','))

GROUP BY vts.type_tarif_id, ISNULL(ttt.type_tarif_nom, tt.type_tarif_nom)
ORDER BY vts.type_tarif_id, ISNULL(ttt.type_tarif_nom, tt.type_tarif_nom);
		 --ORDER BY vts.type_tarif_id, ISNULL(ttt.type_tarif_nom, tt.type_tarif_nom)

