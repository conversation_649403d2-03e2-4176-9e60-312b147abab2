﻿
Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 17
VisualStudioVersion = 17.13.36105.23 d17.13
MinimumVisualStudioVersion = 10.0.40219.1
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "doAllStepsDirectly", "doAllStepsDirectly.csproj", "{F24F7291-D45B-4443-9E29-F7DD049BC62D}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Core.Themis.Libraries.Utilities", "..\..\Libraries\Core.Themis.Libraries.Utilities\Core.Themis.Libraries.Utilities\Core.Themis.Libraries.Utilities.csproj", "{2275439B-0301-F6A6-80F6-FD346D03061F}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Core.Themis.Libraries.UtilitiesTests", "..\..\Libraries\Core.Themis.Libraries.Utilities\Core.Themis.Libraries.UtilitiesTests\Core.Themis.Libraries.UtilitiesTests.csproj", "{B4C47872-07E3-890D-F599-08A7A5C6E1D4}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Core.Themis.Libraries.DTO", "..\..\Libraries\Core.Themis.Libraries.DTO\Core.Themis.Libraries.DTO.csproj", "{15FB3A52-29F7-2199-FB96-02A0BC3260F3}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Core.Themis.Libraries.BLL", "..\..\Libraries\Core.Themis.Libraries.BLL\Core.Themis.Libraries.BLL\Core.Themis.Libraries.BLL.csproj", "{C741B4B4-6DD2-F9F7-AC09-B5948240A774}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Core.Themis.Libraries.BLLTests", "..\..\Libraries\Core.Themis.Libraries.BLL\Core.Themis.Libraries.BLLTests\Core.Themis.Libraries.BLLTests.csproj", "{51581BD5-8D3A-34C1-81DC-8DD19E8834B6}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Core.Themis.Libraries.Data", "..\..\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\Core.Themis.Libraries.Data.csproj", "{70A781DA-9DA2-9133-6122-BD6D9BA68732}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Core.Themis.Libraries.DataTests", "..\..\Libraries\Core.Themis.Libraries.Data\Core.Themis.Libraries.DataTests\Core.Themis.Libraries.DataTests.csproj", "{BCE3FBED-3ACD-920A-BAF4-E91AEC6C631C}"
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|Any CPU = Debug|Any CPU
		Release|Any CPU = Release|Any CPU
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{F24F7291-D45B-4443-9E29-F7DD049BC62D}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{F24F7291-D45B-4443-9E29-F7DD049BC62D}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{F24F7291-D45B-4443-9E29-F7DD049BC62D}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{F24F7291-D45B-4443-9E29-F7DD049BC62D}.Release|Any CPU.Build.0 = Release|Any CPU
		{2275439B-0301-F6A6-80F6-FD346D03061F}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{2275439B-0301-F6A6-80F6-FD346D03061F}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{2275439B-0301-F6A6-80F6-FD346D03061F}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{2275439B-0301-F6A6-80F6-FD346D03061F}.Release|Any CPU.Build.0 = Release|Any CPU
		{B4C47872-07E3-890D-F599-08A7A5C6E1D4}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{B4C47872-07E3-890D-F599-08A7A5C6E1D4}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{B4C47872-07E3-890D-F599-08A7A5C6E1D4}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{B4C47872-07E3-890D-F599-08A7A5C6E1D4}.Release|Any CPU.Build.0 = Release|Any CPU
		{15FB3A52-29F7-2199-FB96-02A0BC3260F3}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{15FB3A52-29F7-2199-FB96-02A0BC3260F3}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{15FB3A52-29F7-2199-FB96-02A0BC3260F3}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{15FB3A52-29F7-2199-FB96-02A0BC3260F3}.Release|Any CPU.Build.0 = Release|Any CPU
		{C741B4B4-6DD2-F9F7-AC09-B5948240A774}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{C741B4B4-6DD2-F9F7-AC09-B5948240A774}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{C741B4B4-6DD2-F9F7-AC09-B5948240A774}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{C741B4B4-6DD2-F9F7-AC09-B5948240A774}.Release|Any CPU.Build.0 = Release|Any CPU
		{51581BD5-8D3A-34C1-81DC-8DD19E8834B6}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{51581BD5-8D3A-34C1-81DC-8DD19E8834B6}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{51581BD5-8D3A-34C1-81DC-8DD19E8834B6}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{51581BD5-8D3A-34C1-81DC-8DD19E8834B6}.Release|Any CPU.Build.0 = Release|Any CPU
		{70A781DA-9DA2-9133-6122-BD6D9BA68732}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{70A781DA-9DA2-9133-6122-BD6D9BA68732}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{70A781DA-9DA2-9133-6122-BD6D9BA68732}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{70A781DA-9DA2-9133-6122-BD6D9BA68732}.Release|Any CPU.Build.0 = Release|Any CPU
		{BCE3FBED-3ACD-920A-BAF4-E91AEC6C631C}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{BCE3FBED-3ACD-920A-BAF4-E91AEC6C631C}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{BCE3FBED-3ACD-920A-BAF4-E91AEC6C631C}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{BCE3FBED-3ACD-920A-BAF4-E91AEC6C631C}.Release|Any CPU.Build.0 = Release|Any CPU
	EndGlobalSection
	GlobalSection(SolutionProperties) = preSolution
		HideSolutionNode = FALSE
	EndGlobalSection
	GlobalSection(ExtensibilityGlobals) = postSolution
		SolutionGuid = {C0026E1D-FB52-43C2-AA0F-19CE12251065}
	EndGlobalSection
	GlobalSection(TeamFoundationVersionControl) = preSolution
		SccNumberOfProjects = 9
		SccEnterpriseProvider = {4CA58AB2-18FA-4F8D-95D4-32DDF27D184C}
		SccTeamFoundationServer = http://v-dev-tfs:8080/tfs/appscollection
		SccLocalPath0 = .
		SccProjectUniqueName1 = doAllStepsDirectly.csproj
		SccLocalPath1 = .
		SccProjectUniqueName2 = ..\\..\\Libraries\\Core.Themis.Libraries.Utilities\\Core.Themis.Libraries.Utilities\\Core.Themis.Libraries.Utilities.csproj
		SccProjectName2 = ../../Libraries/Core.Themis.Libraries.Utilities/Core.Themis.Libraries.Utilities
		SccLocalPath2 = ..\\..\\Libraries\\Core.Themis.Libraries.Utilities\\Core.Themis.Libraries.Utilities
		SccProjectUniqueName3 = ..\\..\\Libraries\\Core.Themis.Libraries.Utilities\\Core.Themis.Libraries.UtilitiesTests\\Core.Themis.Libraries.UtilitiesTests.csproj
		SccProjectName3 = ../../Libraries/Core.Themis.Libraries.Utilities/Core.Themis.Libraries.UtilitiesTests
		SccLocalPath3 = ..\\..\\Libraries\\Core.Themis.Libraries.Utilities\\Core.Themis.Libraries.UtilitiesTests
		SccProjectUniqueName4 = ..\\..\\Libraries\\Core.Themis.Libraries.DTO\\Core.Themis.Libraries.DTO.csproj
		SccProjectName4 = ../../Libraries/Core.Themis.Libraries.DTO
		SccLocalPath4 = ..\\..\\Libraries\\Core.Themis.Libraries.DTO
		SccProjectUniqueName5 = ..\\..\\Libraries\\Core.Themis.Libraries.BLL\\Core.Themis.Libraries.BLL\\Core.Themis.Libraries.BLL.csproj
		SccProjectName5 = ../../Libraries/Core.Themis.Libraries.BLL/Core.Themis.Libraries.BLL
		SccLocalPath5 = ..\\..\\Libraries\\Core.Themis.Libraries.BLL\\Core.Themis.Libraries.BLL
		SccProjectUniqueName6 = ..\\..\\Libraries\\Core.Themis.Libraries.BLL\\Core.Themis.Libraries.BLLTests\\Core.Themis.Libraries.BLLTests.csproj
		SccProjectName6 = ../../Libraries/Core.Themis.Libraries.BLL/Core.Themis.Libraries.BLLTests
		SccLocalPath6 = ..\\..\\Libraries\\Core.Themis.Libraries.BLL\\Core.Themis.Libraries.BLLTests
		SccProjectUniqueName7 = ..\\..\\Libraries\\Core.Themis.Libraries.Data\\Core.Themis.Librairies.Data\\Core.Themis.Libraries.Data.csproj
		SccProjectName7 = ../../Libraries/Core.Themis.Libraries.Data/Core.Themis.Librairies.Data
		SccLocalPath7 = ..\\..\\Libraries\\Core.Themis.Libraries.Data\\Core.Themis.Librairies.Data
		SccProjectUniqueName8 = ..\\..\\Libraries\\Core.Themis.Libraries.Data\\Core.Themis.Libraries.DataTests\\Core.Themis.Libraries.DataTests.csproj
		SccProjectName8 = ../../Libraries/Core.Themis.Libraries.Data/Core.Themis.Libraries.DataTests
		SccLocalPath8 = ..\\..\\Libraries\\Core.Themis.Libraries.Data\\Core.Themis.Libraries.DataTests
	EndGlobalSection
EndGlobal
