{"Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning"}}, "AllowedHosts": "*", "ConnectionStrings": {"WsAdminDB": "Server=************;Database=WSAdmin;Persist Security Info=True;User ID=SphereWeb;Password=************************************************************************************************************;MultipleActiveResultSets=true;TrustServerCertificate=True;", "GlobalOpinionDB": "Server=*************;Database=GlobalOpinions_DEV;Persist Security Info=True;User ID=SupportTest;Password=****************************************************************************************;MultipleActiveResultSets=true;TrustServerCertificate=True;", "WebLibraryDB": "Server=*************;Database=GlobalWebLibrary_DEV;Persist Security Info=True;User ID=SupportTest;Password=****************************************************************************************;MultipleActiveResultSets=true;TrustServerCertificate=True;", "QueuingDB": "Server=**************;Database=WAITINGPAGES;Persist Security Info=True;User ID=ROD_WAITINGPAGES;Password=****************************************************************;MultipleActiveResultSets=true;TrustServerCertificate=True;", "ThemisSupportTools": "Server=************;Database=WSAdmin;Persist Security Info=True;User ID=SphereWeb;Password=************************************************************************************************************;MultipleActiveResultSets=true;TrustServerCertificate=True;", "Redis": "**************:6555,password=tS4pDJfVCN3Y0j5pvrdEBCpH0gXLZsZ/QCdN2zh6lJwoi+Va4eBUwtkZEUNYCcCl", "DEV": {"WsAdminDB": "Server=*************;Database=WSAdmin_test;Persist Security Info=True;User ID=SupportTest;Password=****************************************************************************************;MultipleActiveResultSets=true;TrustServerCertificate=True;"}, "TEST": {"WsAdminDB": "Server=*************;Database=WSAdmin_test;Persist Security Info=True;User ID=SupportTest;Password=****************************************************************************************;MultipleActiveResultSets=true;TrustServerCertificate=True;"}, "PROD": {"WsAdminDB": "Server=************;Database=WSAdmin;Persist Security Info=True;User ID=SphereWeb;Password=************************************************************************************************************;MultipleActiveResultSets=true;TrustServerCertificate=True;"}}, "ServiceRattrapePaths": {"PreProdPath": "\\\\*************\\applis\\servicesRattrapeP\\preprod", "ProdPath": "\\\\*************\\applis\\servicesRattrapeP\\prod"}, "PathForSqlScript": "LIBRARIES_SQLSCRIPTS\\{0}\\{1}.sql", "ConfigIniPath": "\\\\Srv-paiement64\\customerfiles\\PROD\\{structureId}\\CONFIGSERVER\\config.ini.xml", "CustomerFilesPath": "\\\\Srv-paiement64\\customerfiles\\{environment}", "ResourcesFilesOfPlatformsPath": "\\\\**************\\customerfiles\\{environment}\\default\\{platform}\\RESOURCES", "ImagesSeatingPlansPath": "\\\\**************\\customerfiles\\{environment}\\{structureId}\\INDIV\\IMAGES\\seatingplans", "DetailedErrors": true, "timerInMinute": 5, "CodePromoLetters": "ABCDEFGHIJKLMNOPQRSTUVWXYZ", "CodePromoDigits": "0123456789", "TypeRun": "PROD", "ENVIRONMENT": "PROD"}