﻿using Core.Themis.Libraries.Data.Entities.Open.Order;
using Core.Themis.Libraries.DTO;
using Core.Themis.Libraries.DTO.AccessControl;
using Core.Themis.Libraries.DTO.CustomerArea.Order.Details;
using Core.Themis.Libraries.DTO.CustomerArea.Order.Invoices;
using Core.Themis.Libraries.DTO.CustomerArea.Order.OrderCard;
using Core.Themis.Libraries.DTO.exposedObjects;
using Core.Themis.Libraries.DTO.Identity;
using Core.Themis.Libraries.DTO.Lookup;
using Core.Themis.Libraries.DTO.Orders;
using Core.Themis.Libraries.DTO.PassCulture;
using Core.Themis.Libraries.DTO.Transactionnals;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace Core.Themis.Libraries.BLL.Interfaces
{
    public interface IOrderManager
    {
        /// <summary>
        /// récupération d'une commande par son id : juste la table [commande]
        /// </summary>
        /// <param name="structureId"></param>
        /// <param name="langCode"></param>
        /// <param name="orderId"></param>
        /// <returns></returns>
        OrderDTO GetById(int structureId, int orderId);


        List<OrderDTO> GetAllByIdentityId(int structureId, int identityId);

        /// <summary>
        /// récupération d'une commande par son id : avec les lignes, les paiements, les codes barres
        /// </summary>
        /// <param name="structureId"></param>
        /// <param name="langCode"></param>
        /// <param name="orderId"></param>
        /// <param name="identityId"></param>
        /// <returns></returns>
        [Obsolete("Use LoadAsync instead")]
        OrderDTO Load(int structureId, string langCode, int orderId, int identityId);

        /// <summary>
        /// récupération d'une commande par son id : avec les lignes, les paiements, les codes barres
        /// </summary>
        /// <param name="structureId"></param>
        /// <param name="langCode"></param>
        /// <param name="orderId"></param>
        /// <param name="identityId"></param>
        /// <returns></returns>
        Task<OrderDTO> LoadAsync(int structureId, string langCode, int orderId, int identityId);

        OrderDTO? GetOrderByIdForInsurance(int structureId, int orderId);

        OrderDTO CreateOrderFromPanier(int structureId,
              BasketDTO bask,
            int idOperateur, int idFiliereCreationCompteAnonyme, int idFiliereAbo, int idFiliereVU, int idFiliereReAbo, int idFiliereProduits,
            bool identiteAnonyme, int idIdentite, string dossierClientNom);

        /// <summary>
        /// recoit l'objet CreateOrderDemand (api external)
        /// </summary>
        /// <param name="structureId"></param>
        /// <param name="basketId"></param>
        /// <param name="orderDemand"></param>
        /// <returns></returns>
        OrderDTO CreateFromDemand(int structureId, int basketId, int amountPaye, CreateOrderDemand orderDemand);

        OrderDTO CreateFullFromBasket(int structureId, int basketId, int amountPaye, string dossierClientNom, IdentityDTO consommateur, string myReferenceTransaction, List<BarCodeDTO> listBarCodeToOverWrite = null);

        OrderDTO Pay(int structureId,
            BasketDTO bask,
            int idOperateur,
            int idFiliere,
            int idIdentitePayeur, OrderDTO cmdToPay, List<PaymentMethodDTO> listModePaiement,
            List<int> commandeLignesToPay, string transactionId, string web_PosteName,
        int web_PosteId);

        /// <summary>
        /// annulation de commande
        /// </summary>
        /// <param name="structureId"></param>
        /// <param name="orderId"></param>
        /// <returns></returns>
        public bool Cancel(int structureId, int orderId);

        OrderDTO? DoEdition(int structureId, int idOperateur, OrderDTO cmdToPay, List<BarCodeDTO> listBarCodeToOverWrite = null);

        OrderCardInfo? GetOrderCardInfo(int structureId, int orderId);

        Task<OrderInfoDetails?> GetOrderDetailsAsync(int structureId, int orderId);

        Task<PaginationLookup<OrderCardInfo>?> GetAllOrderCardWithoutReservationInfoByIdentiteIdAsync(int structureId, int identiteId, int page = 0, int pageSize = 10000, DateTime? dateFilter = null);

        Task<List<OrderCardInfo>?> GetAllReservationOrderCardInfoByIdentiteIdAsync(int structureId, int identiteId);

        Task<Invoice?> GetInvoiceTemplateAsync(int structureId, int orderId);

        bool CancelReservation(int structureId, int orderId);
    }
}
