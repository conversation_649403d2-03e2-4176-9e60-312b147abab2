<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Check Waiting Pages - Beautiful Design</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <!-- Custom CSS -->
    <link rel="stylesheet" href="CheckWaitingPages.css">
</head>
<body>
    <div class="check-waiting-pages-container">
        <!-- Header Section -->
        <div class="page-header">
            <div class="container-fluid">
                <div class="row align-items-center">
                    <div class="col-md-8">
                        <div class="header-content">
                            <div class="header-icon">
                                <i class="fas fa-clock"></i>
                            </div>
                            <div class="header-text">
                                <h1 class="page-title">Check waiting pages</h1>
                                <p class="page-subtitle">Liste FileAttentes</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4 text-end">
                        <div class="environment-badge">
                            <span class="badge bg-warning text-dark">
                                <i class="fas fa-server me-1"></i>
                                Environnement: DEV
                            </span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Filters Section -->
        <div class="filters-section">
            <div class="container-fluid">
                <div class="card shadow-sm border-0">
                    <div class="card-body">
                        <div class="row g-3 align-items-end">
                            <div class="col-md-3">
                                <label class="form-label fw-semibold">
                                    <i class="fas fa-filter me-1"></i>
                                    Filtre StructureId
                                </label>
                                <input type="text" class="form-control form-control-lg" placeholder="Entrez l'ID de structure...">
                            </div>
                            <div class="col-md-2">
                                <label class="form-label fw-semibold">Version active</label>
                                <select class="form-select form-select-lg">
                                    <option value="all">Toutes</option>
                                    <option value="v1" selected>v1</option>
                                </select>
                            </div>
                            <div class="col-md-2">
                                <label class="form-label fw-semibold">Erreurs</label>
                                <select class="form-select form-select-lg">
                                    <option value="all" selected>Toutes</option>
                                    <option value="errors">Avec erreurs</option>
                                    <option value="no-errors">Sans erreurs</option>
                                </select>
                            </div>
                            <div class="col-md-2">
                                <button class="btn btn-primary btn-lg w-100">
                                    <i class="fas fa-search me-2"></i>
                                    Filtrer
                                </button>
                            </div>
                            <div class="col-md-3">
                                <div class="stats-info">
                                    <small class="text-muted">
                                        <i class="fas fa-info-circle me-1"></i>
                                        12 structures trouvées
                                    </small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Table Section -->
        <div class="table-section">
            <div class="container-fluid">
                <div class="card shadow border-0">
                    <div class="card-header bg-white border-bottom">
                        <div class="row align-items-center">
                            <div class="col">
                                <h5 class="card-title mb-0">
                                    <i class="fas fa-table me-2 text-primary"></i>
                                    Résultats de la vérification
                                </h5>
                            </div>
                            <div class="col-auto">
                                <div class="btn-group" role="group">
                                    <button class="btn btn-outline-secondary btn-sm">
                                        <i class="fas fa-download me-1"></i>
                                        Exporter
                                    </button>
                                    <button class="btn btn-outline-secondary btn-sm">
                                        <i class="fas fa-sync-alt me-1"></i>
                                        Actualiser
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="card-body p-0">
                        <div class="table-responsive">
                            <table class="table table-hover mb-0 modern-table">
                                <thead class="table-header">
                                    <tr>
                                        <th class="sortable">
                                            <div class="th-content">
                                                ID
                                                <i class="fas fa-sort ms-1"></i>
                                            </div>
                                        </th>
                                        <th class="sortable">
                                            <div class="th-content">
                                                Nom
                                                <i class="fas fa-sort ms-1"></i>
                                            </div>
                                        </th>
                                        <th class="text-center">Version active</th>
                                        <th class="text-center">urlV1 OK</th>
                                        <th class="text-center">urlV1</th>
                                        <th class="text-center">urlV2 OK</th>
                                        <th class="text-center">urlV2</th>
                                        <th class="text-center">url Maintenance OK</th>
                                        <th class="text-center">url Maintenance</th>
                                        <th class="text-center">Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <!-- Row 1 - Success -->
                                    <tr class="table-row">
                                        <td class="fw-bold text-primary">0003</td>
                                        <td>
                                            <div class="structure-name">
                                                <i class="fas fa-building me-2 text-muted"></i>
                                                TRISTAN BERNARD (THEATRE)
                                            </div>
                                        </td>
                                        <td class="text-center">
                                            <span class="badge bg-info">v1</span>
                                        </td>
                                        <td class="text-center">
                                            <span class="status-icon success">
                                                <i class="fas fa-check-circle"></i>
                                            </span>
                                        </td>
                                        <td class="url-cell">
                                            <a href="https://queue.themisweb.fr/queuing_0003.htm" class="url-link" target="_blank">
                                                <i class="fas fa-external-link-alt"></i>
                                                queuing_0003.htm
                                            </a>
                                        </td>
                                        <td class="text-center">
                                            <span class="status-icon success">
                                                <i class="fas fa-check-circle"></i>
                                            </span>
                                        </td>
                                        <td class="url-cell">
                                            <a href="https://queue.themisweb.fr/queuing_0003.htm" class="url-link" target="_blank">
                                                <i class="fas fa-external-link-alt"></i>
                                                queuing_0003.htm
                                            </a>
                                        </td>
                                        <td class="text-center">
                                            <span class="status-icon success">
                                                <i class="fas fa-check-circle"></i>
                                            </span>
                                        </td>
                                        <td class="url-cell">
                                            <a href="https://queue.themisweb.fr/maintenance_0003.htm" class="url-link" target="_blank">
                                                <i class="fas fa-external-link-alt"></i>
                                                maintenance_0003.htm
                                            </a>
                                        </td>
                                        <td class="text-center">
                                            <div class="btn-group btn-group-sm">
                                                <button class="btn btn-outline-primary" title="Voir détails">
                                                    <i class="fas fa-eye"></i>
                                                </button>
                                                <button class="btn btn-outline-secondary" title="Tester">
                                                    <i class="fas fa-play"></i>
                                                </button>
                                            </div>
                                        </td>
                                    </tr>

                                    <!-- Row 2 - Warning -->
                                    <tr class="table-row warning-row">
                                        <td class="fw-bold text-primary">0005</td>
                                        <td>
                                            <div class="structure-name">
                                                <i class="fas fa-building me-2 text-muted"></i>
                                                SAINT GEORGES (THEATRE)
                                            </div>
                                        </td>
                                        <td class="text-center">
                                            <span class="badge bg-info">v1</span>
                                        </td>
                                        <td class="text-center">
                                            <span class="status-icon warning">
                                                <i class="fas fa-exclamation-triangle"></i>
                                            </span>
                                        </td>
                                        <td class="text-muted">urlV1 not OK</td>
                                        <td class="text-center">
                                            <span class="status-icon warning">
                                                <i class="fas fa-exclamation-triangle"></i>
                                            </span>
                                        </td>
                                        <td class="text-muted">urlV2 not OK</td>
                                        <td class="text-center">
                                            <span class="status-icon warning">
                                                <i class="fas fa-exclamation-triangle"></i>
                                            </span>
                                        </td>
                                        <td class="text-muted">urlMaintenance not OK</td>
                                        <td class="text-center">
                                            <div class="btn-group btn-group-sm">
                                                <button class="btn btn-outline-warning" title="Voir détails">
                                                    <i class="fas fa-eye"></i>
                                                </button>
                                                <button class="btn btn-outline-secondary" title="Diagnostiquer">
                                                    <i class="fas fa-stethoscope"></i>
                                                </button>
                                            </div>
                                        </td>
                                    </tr>

                                    <!-- Row 3 - Success -->
                                    <tr class="table-row">
                                        <td class="fw-bold text-primary">0008</td>
                                        <td>
                                            <div class="structure-name">
                                                <i class="fas fa-building me-2 text-muted"></i>
                                                BRUYERE (THEATRE DE LA)
                                            </div>
                                        </td>
                                        <td class="text-center">
                                            <span class="badge bg-info">v1</span>
                                        </td>
                                        <td class="text-center">
                                            <span class="status-icon success">
                                                <i class="fas fa-check-circle"></i>
                                            </span>
                                        </td>
                                        <td class="url-cell">
                                            <a href="https://queue.themisweb.fr/maintenance_0008.htm" class="url-link" target="_blank">
                                                <i class="fas fa-external-link-alt"></i>
                                                maintenance_0008.htm
                                            </a>
                                        </td>
                                        <td class="text-center">
                                            <span class="status-icon success">
                                                <i class="fas fa-check-circle"></i>
                                            </span>
                                        </td>
                                        <td class="url-cell">
                                            <a href="https://queue.themisweb.fr/maintenance_0008.htm" class="url-link" target="_blank">
                                                <i class="fas fa-external-link-alt"></i>
                                                maintenance_0008.htm
                                            </a>
                                        </td>
                                        <td class="text-center">
                                            <span class="status-icon success">
                                                <i class="fas fa-check-circle"></i>
                                            </span>
                                        </td>
                                        <td class="url-cell">
                                            <a href="https://queue.themisweb.fr/maintenance_0008.htm" class="url-link" target="_blank">
                                                <i class="fas fa-external-link-alt"></i>
                                                maintenance_0008.htm
                                            </a>
                                        </td>
                                        <td class="text-center">
                                            <div class="btn-group btn-group-sm">
                                                <button class="btn btn-outline-primary" title="Voir détails">
                                                    <i class="fas fa-eye"></i>
                                                </button>
                                                <button class="btn btn-outline-secondary" title="Tester">
                                                    <i class="fas fa-play"></i>
                                                </button>
                                            </div>
                                        </td>
                                    </tr>

                                    <!-- Additional rows can be added here -->
                                </tbody>
                            </table>
                        </div>
                    </div>
                    <div class="card-footer bg-light border-top-0">
                        <div class="row align-items-center">
                            <div class="col">
                                <small class="text-muted">
                                    <i class="fas fa-info-circle me-1"></i>
                                    Affichage de 1 à 10 sur 12 résultats
                                </small>
                            </div>
                            <div class="col-auto">
                                <nav>
                                    <ul class="pagination pagination-sm mb-0">
                                        <li class="page-item disabled">
                                            <span class="page-link">Précédent</span>
                                        </li>
                                        <li class="page-item active">
                                            <span class="page-link">1</span>
                                        </li>
                                        <li class="page-item">
                                            <a class="page-link" href="#">2</a>
                                        </li>
                                        <li class="page-item">
                                            <a class="page-link" href="#">Suivant</a>
                                        </li>
                                    </ul>
                                </nav>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- Custom JavaScript -->
    <script>
        // Add some interactive functionality
        document.addEventListener('DOMContentLoaded', function() {
            // Sortable headers
            const sortableHeaders = document.querySelectorAll('.sortable');
            sortableHeaders.forEach(header => {
                header.addEventListener('click', function() {
                    // Add sorting logic here
                    console.log('Sorting by:', this.textContent.trim());
                });
            });

            // Filter button
            const filterBtn = document.querySelector('.btn-primary');
            filterBtn.addEventListener('click', function() {
                // Add filter logic here
                console.log('Filtering data...');
            });

            // Action buttons
            const actionBtns = document.querySelectorAll('.btn-group .btn');
            actionBtns.forEach(btn => {
                btn.addEventListener('click', function(e) {
                    e.preventDefault();
                    console.log('Action clicked:', this.title);
                });
            });
        });
    </script>
</body>
</html>
