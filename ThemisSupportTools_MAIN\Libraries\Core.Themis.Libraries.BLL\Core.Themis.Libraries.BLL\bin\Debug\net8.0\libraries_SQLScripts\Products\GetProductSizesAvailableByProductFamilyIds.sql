﻿--DECLARE @pProductFamilyIds VARCHAR(50) = '4'
	
SELECT DISTINCT
    (SELECT name FROM splitstring(p.produit_nom, ' ') WHERE rownum = 2) AS Size
FROM produit p
INNER JOIN Produit_Lien_Sous_Famille plsf ON p.produit_id = plsf.Produit_id
INNER JOIN Produit_Famille pf ON plsf.Produit_Famille_ID = pf.Produit_Famille_ID
INNER JOIN Produit_Sous_Famille psf ON plsf.Produit_Sous_Famille_ID = psf.Produit_Sous_Famille_ID
INNER JOIN produit_stock ps ON p.produit_id = ps.produit_id
WHERE p.internet = 1 
AND pf.Produit_Famille_ID IN (SELECT name FROM splitstring(@pProductFamilyIds, ','))
AND pf.Masquer = 0
AND psf.Masquer = 0
AND psf.Regroupement_couleur = 1
AND ps.manifestation_id = 0
AND ps.seance_id = 0
