﻿using AutoMapper;
using Core.Themis.Api.External.Helpers.Interfaces;
using Core.Themis.Libraries.BLL.Interfaces;
using Core.Themis.Libraries.BLL.WT.Interfaces;
using Core.Themis.Libraries.DTO;
using Core.Themis.Libraries.DTO.AccessControl;
using Core.Themis.Libraries.DTO.Enums;
using Core.Themis.Libraries.DTO.EventsSessions;
using Core.Themis.Libraries.DTO.exposedObjects;
using Core.Themis.Libraries.DTO.Orders;
using Core.Themis.Libraries.DTO.Seats;
using Core.Themis.Libraries.Utilities.Crypto;
using Core.Themis.Libraries.Utilities.Helpers.Order;
using Core.Themis.Libraries.Utilities.Logging;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.Logging;
using Microsoft.Net.Http.Headers;

using Newtonsoft.Json;
using Order = Core.Themis.Libraries.DTO.exposedObjects.Order;

namespace Core.Themis.Api.External.Controllers
{
    /// <summary>
    /// Work on orders (sells)
    /// </summary>
    [ApiController]
    [ApiExplorerSettings(GroupName = "ext")]
    public class OrderController : ControllerBase
    {
        private readonly IConfiguration _configuration;
        private readonly IMemoryCache _memoryCache;
        private readonly IGestionTraceManager _gestionTrace;

        private readonly IApiOffersClient _apiOffersClients;
        private readonly IOrderManager _orderManager;
        private readonly IBuyerProfilManager _buyerProfilManager;
        private readonly IWebUserManager _webUserManager;
        private readonly IEventManager _eventManager;
        private readonly ISeatManager _seatManager;
        private readonly IIdentiteManager _identiteManager;
        private readonly IBasketManager _basketManager;
        private readonly IGestionPlaceManager _gestionPlaceManager;

        private IMapper Mapper
        {
            get;
        }
        private static readonly RodrigueNLogger Logger = new RodrigueNLogger();


        public OrderController(IConfiguration configuration, IMemoryCache memoryCache,
            IGestionTraceManager gestionTraceManager,
            IWebUserManager webUserManager,
            ISeatManager seatManager,
            IEventManager eventManager,
            IBasketManager basketManager,
            IMapper mapper,
            IApiOffersClient apiOffersClient,
            IGestionPlaceManager gestionPlaceManager,
            IOrderManager orderManager,
            IIdentiteManager identiteManager,
            IBuyerProfilManager buyerProfilManager)
        {
            _configuration = configuration;
            _gestionTrace = gestionTraceManager;
            _memoryCache = memoryCache;
            Mapper = mapper;
            _apiOffersClients = apiOffersClient;
            _orderManager = orderManager;
            _identiteManager = identiteManager;
            _eventManager = eventManager;
            _basketManager = basketManager;
            _seatManager = seatManager;
            _gestionPlaceManager = gestionPlaceManager;
            _buyerProfilManager = buyerProfilManager;
            _webUserManager = webUserManager;
        }




        /// <summary>
        /// Get orders by email of the buyer
        /// </summary>
        /// <param name="structureId">The unique structure identifier</param>       
        /// <param name="email">email of the user</param>
        /// <param name="langCode">The Language code set in Rodrigue (fr,de,en,it...). Note **fr** or **de** is set by default</param>
        /// <returns>List of orders</returns>
        /// <response code="200">List of orders</response>
        /// <response code="404">Email not found or no orders for this email</response>   
        /// <remarks>
        /// Example of request: 
        ///
        ///     GET api/987/Orders?email=<EMAIL>&amp;langcode=it
        /// </remarks>
        [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(List<Order>))]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        [HttpGet]
        [Route("api/{structureId}/Orders")]
        [Authorize(Roles = "FullHistoIdent,Admin")]

        public async Task<IActionResult> GetOrdersByIdentite(int structureId, string email, string langcode ="fr")
        {

            /// get identite 
            var accT = Request.Headers[HeaderNames.Authorization];
            if (!TokenManager.PartnerHasRight(structureId, accT))
            {
                //Exception ex = new UnauthorizedAccessException ($"structureId {structureId} is not allowed");
                var pb = Problem($"structureId {structureId} is not allowed", null, StatusCodes.Status401Unauthorized);
                return pb;
            }
            try
            {
                var idDTO = _identiteManager.Get(structureId, email, langcode, "");
                if (idDTO == null || idDTO.IdentiteId == 0)
                {
                    Logger.Error(structureId, $"GetOrders : email {email} not found");
                    return Problem($"email {email} not found", statusCode: StatusCodes.Status404NotFound);
                }

                var ordersList = _orderManager.GetAllByIdentityId(structureId, idDTO.IdentiteId);
      
                // 1. Lance tous les LoadAsync en parallèle, stocke les tasks
                var tasks = ordersList.Select(ordE =>
                    _orderManager.LoadAsync(structureId, langcode, ordE.OrderId, 0)
                ).ToList();

                var results = await Task.WhenAll(tasks);

                var listRet = new List<Order>();

                foreach (var ordContent in results)
                {
                    Order ret = (Order)Mapper.Map(ordContent, typeof(OrderDTO), typeof(Order));
                    ret = OrderEntityToOrder(ordContent, ret);
                    listRet.Add(ret);
                }
                return Ok(listRet);
            }
            catch (Exception ex)
            {

                Logger.Error(structureId, $"GetOrder({structureId},{email}) , " +
                         $"{ex.Message} {ex.StackTrace}");
                return Problem(ex.Message, statusCode: StatusCodes.Status500InternalServerError);
            }
        }

        /// <summary>
        /// Get an order
        /// </summary>
        /// <param name="structureId">The unique structure identifier</param>
        /// <param name="orderId">Pass an order id</param>
        /// <param name="basketId">Or basket id</param>
        /// <returns>An order</returns>
        [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(Order))]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [HttpGet]
        [Route("api/{structureId}/Order/{orderId}/{basketId}")]
        [Authorize(Roles = "Revendeur,Integrateur,Admin")]
        public async Task<IActionResult> GetOrder(int structureId, int orderId, int basketId)
        {
            var accT = Request.Headers[HeaderNames.Authorization];
            if (!TokenManager.PartnerHasRight(structureId, accT))
            {
                //Exception ex = new UnauthorizedAccessException ($"structureId {structureId} is not allowed");
                var pb = Problem($"structureId {structureId} is not allowed", null, StatusCodes.Status401Unauthorized);
                return pb;
            }
            try
            {
                _gestionTrace.WriteLogMessage(structureId, 0, basketId, $"GetOrder({structureId}, {orderId}, {basketId})", TypeLog.LogGenerique);

                string codeLangTodo = "fr";
                var ordE = await _orderManager.LoadAsync(structureId, codeLangTodo, orderId, 0);

                //var ordE = tordE.Result;

                Order ret = (Order)Mapper.Map(ordE, typeof(OrderDTO), typeof(Order));
                ret = OrderEntityToOrder(ordE, ret);

                Logger.Debug(structureId, $"GetOrder({structureId},{basketId}) ok (id={ret.Id})");

                return Ok(ret);

            }
            catch (Exception ex)
            {
                _gestionTrace.WriteLogErrorMessage(structureId, 0, basketId, $"GetOrder({structureId}, {orderId}, {basketId}) ko, {ex.Message} {ex.StackTrace}");

                Logger.Error(structureId, $"GetOrder({structureId},{orderId},{basketId}) , " +
                         $"{ex.Message} {ex.StackTrace}");
                return Problem(ex.Message, null, StatusCodes.Status500InternalServerError);
            }
        }


        /// <summary>
        /// Get orders paid by buyerprofil
        /// </summary>
        /// <param name="structureId">The unique structure identifier</param>
        /// <param name="bpLogin">The buyer profil login</param>
        /// <param name="bpPassword">The buyer profil password</param>
        /// <returns>Order list</returns>
        [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(Order))]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [HttpGet]
        [Route("api/{structureId}/Orders/{bpLogin}/{bpPassword}")]
        //  [Authorize(Roles = "Revendeur,Integrateur,Admin")]
        public async Task<IActionResult> GetOrders(int structureId, string bpLogin, string bpPassword)
        {
            var accT = Request.Headers[HeaderNames.Authorization];
            if (!TokenManager.PartnerHasRight(structureId, accT))
            {
                //Exception ex = new UnauthorizedAccessException ($"structureId {structureId} is not allowed");
                var pb = Problem($"structureId {structureId} is not allowed", null, StatusCodes.Status401Unauthorized);
                return pb;
            }
            int orderId = 0;
            try
            {

                int myBprofilId = 0;

                BuyerProfilDTO buyerProfil = _buyerProfilManager.GetBuyerProfilByIdORLoginPassword(structureId, 0, bpLogin, bpPassword);
                if (buyerProfil != null && buyerProfil.Id > 0 && buyerProfil.IdentityPaiement > 0)
                {
                    myBprofilId = buyerProfil.Id;

                    int identiteId = buyerProfil.IdentityPaiement;

                    var OrdersWithoutReservation2 = await _orderManager.GetAllOrderCardWithoutReservationInfoByIdentiteIdAsync(structureId,
                        identiteId,
                       0
                       ).ConfigureAwait(false);

                    string codeLangTodo = "fr";
                    var ordE = await _orderManager.LoadAsync(structureId, codeLangTodo, 123, 0); // ????????????

                    Order ret = (Order)Mapper.Map(ordE, typeof(OrderDTO), typeof(Order));
                    ret = OrderEntityToOrder(ordE, ret);

                    Logger.Debug(structureId, $"GetOrder({structureId},{orderId}) ok (id={ret.Id})");

                    return Ok(ret);
                }
                else
                {
                    return Problem("buyer profil is not correct", null, StatusCodes.Status404NotFound);
                }

            }
            catch (Exception ex)
            {
                Logger.Error(structureId, $"GetOrder({structureId},{orderId}) , " +
                         $"{ex.Message} {ex.StackTrace}");
                return Problem(ex.Message, null, StatusCodes.Status500InternalServerError);
            }
        }


        /// <summary>
        /// Get an order
        /// </summary>
        /// <param name="structureId">The unique structure identifier</param>
        /// <param name="orderId">Pass an order id</param>
        /// <param name="basketId">Or basket id</param>
        /// <returns>An order</returns>
        [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(Order))]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [HttpGet]
        [Route("api/{structureId}/Order/{orderId}")]
        //  [Authorize(Roles = "Revendeur,Integrateur,Admin")]

        public async Task<IActionResult> GetOrder(int structureId, int orderId)
        {
            var accT = Request.Headers[HeaderNames.Authorization];
            if (!TokenManager.PartnerHasRight(structureId, accT))
            {
                //Exception ex = new UnauthorizedAccessException ($"structureId {structureId} is not allowed");
                var pb = Problem($"structureId {structureId} is not allowed", null, StatusCodes.Status401Unauthorized);
                return pb;
            }
            try
            {
                string codeLangTodo = "fr";
                var ordE = await _orderManager.LoadAsync(structureId, codeLangTodo, orderId, 0);

                Order ret = (Order)Mapper.Map(ordE, typeof(OrderDTO), typeof(Order));
                ret = OrderEntityToOrder(ordE, ret);

                Logger.Debug(structureId, $"GetOrder({structureId},{orderId}) ok (id={ret.Id})");

                return Ok(ret);

            }
            catch (Exception ex)
            {
                Logger.Error(structureId, $"GetOrder({structureId},{orderId}) , " +
                         $"{ex.Message} {ex.StackTrace}");
                return Problem(ex.Message, null, StatusCodes.Status500InternalServerError);
            }
        }

        /// <summary>
        /// Cancel an order
        /// </summary>
        /// <param name="structureId">The unique structure identifier</param>
        /// <param name="orderId">Pass an order id</param>
        /// <returns></returns>
        [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(Order))]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [HttpDelete]
        [Authorize(Roles = "Revendeur,Integrateur,Admin")]
        [Route("api/{structureId}/Order/{orderId}")]
        [ApiVersion("2")]
        public async Task<IActionResult> CancelOrder(int structureId, int orderId, [FromBody] ReadOrderDemand orderKeyDTO)
        {
            var accT = Request.Headers[HeaderNames.Authorization];
            if (!TokenManager.PartnerHasRight(structureId, accT))
            {
                //Exception ex = new UnauthorizedAccessException ($"structureId {structureId} is not allowed");
                Logger.Error(structureId, $"structureId {structureId} is not allowed");
                var pb = Problem($"structureId {structureId} is not allowed", null, StatusCodes.Status401Unauthorized);
                return pb;
            }
            try
            {
                Logger.Info(structureId, $"CancelOrder({structureId}, {orderId},  {JsonConvert.SerializeObject(orderKeyDTO)})...");

                #region check la clé CKey passée en parametre

                if (!OrderHelper.CancelOrder_CheckKey(orderId, orderKeyDTO.orderKey))
                {
                    string msg = $"key {orderKeyDTO.orderKey} is not valid for {orderId}";
                    Logger.Error(structureId, msg);
                    var pb = Problem(msg, null, StatusCodes.Status401Unauthorized);
                    return pb;
                }

                #endregion

                bool result = _orderManager.Cancel(structureId, orderId);

                string codeLangTodo = "fr";
                var ordE = await _orderManager.LoadAsync(structureId, codeLangTodo, orderId, 0);

                Order ret = (Order)Mapper.Map(ordE, typeof(OrderDTO), typeof(Order));
                ret = OrderEntityToOrder(ordE, ret);

                Logger.Info(structureId, $"CancelOrder({structureId}, {orderId}, {JsonConvert.SerializeObject(orderKeyDTO)}) return {result}");

                return Ok(ret);

            }
            catch (Exception ex)
            {
                Logger.Error(structureId, $"CancelOrder({structureId}, {orderId}, {JsonConvert.SerializeObject(orderKeyDTO)}) : " +
                    $"{ex.Message} {ex.StackTrace}");
                return Problem(ex.Message, null, StatusCodes.Status500InternalServerError);
            }
        }

        /// <summary>
        /// Create order straightly from demand and pay it
        /// </summary>
        /// <param name="structureId">The unique structure identifier</param>
        /// <param name="bpLogin">The buyer profil login</param>
        /// <param name="bpPassword">The buyer profil password</param>
        /// <param name="amount">Amount paid, in cents</param>
        /// <returns></returns>
        [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(Order))]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [HttpPost]
        [Authorize(Roles = "Revendeur,Integrateur,Admin")]
        [Route("api/{structureId}/Order/{bpLogin}/{bpPassword}")]
        public async Task<IActionResult> CreateOrderFromDemand(int structureId, string bpLogin, string bpPassword, int amount, [FromBody] CreateOrderDemandFull demand)
        {
            var accT = Request.Headers[HeaderNames.Authorization];
            if (!TokenManager.PartnerHasRight(structureId, accT))
            {
                //Exception ex = new UnauthorizedAccessException ($"structureId {structureId} is not allowed");
                Logger.Error(structureId, $"structureId {structureId} is not allowed");
                var pb = Problem($"structureId {structureId} is not allowed", null, StatusCodes.Status401Unauthorized);
                return pb;
            }

            try
            {
                // create webUser
                int myBprofilId = 0;

                Logger.Debug(structureId, $"CreateOrder demand reçu : {JsonConvert.SerializeObject(demand)}");


                Logger.Debug(structureId, $"CreateOrderFromDemand CreateWebUser({structureId}, 0, {bpLogin}, {bpPassword})...");

                if (!string.IsNullOrEmpty(bpLogin) || !string.IsNullOrEmpty(bpPassword))
                {
                    BuyerProfilDTO buyerProfil = _buyerProfilManager.GetBuyerProfilByIdORLoginPassword(structureId, 0, bpLogin, bpPassword);

                    //CreateOrderDemandFull demand2 = _buyerProfilManager.GetBuyerProfilByIdORLoginPasswordddddd(structureId, 0, bpLogin, bpPassword, demand);
                    if (buyerProfil != null)
                    {
                        myBprofilId = buyerProfil.Id;
                        if (buyerProfil.IsReseller)
                        {
                            demand.webUser.IdentityId = buyerProfil.IdentityPaiement;
                        }
                    }
                    else
                    {
                        var pb = Problem($"can't retrieve buyer profil", null, StatusCodes.Status401Unauthorized);
                        return pb;
                    }
                }
                Libraries.DTO.WTObjects.WebUser wuDTO =
        (Libraries.DTO.WTObjects.WebUser)Mapper.Map(demand.webUser, typeof(Libraries.DTO.exposedObjects.WebUser), typeof(Libraries.DTO.WTObjects.WebUser));

                wuDTO.ProfilAcheteurId = myBprofilId;

                wuDTO.SessionWebId = "";
                wuDTO.ApplicationPath = $"{Request.Scheme}://{Request.Host.Value}/";
                wuDTO.Browser = "";
                wuDTO.AddressIp = Request.HttpContext.Connection.RemoteIpAddress?.ToString();

                var wu = _webUserManager.SetWT(structureId, wuDTO);
                demand.webUser.Id = wu.UserId;

                Logger.Debug(structureId, $"CreateWebUser({structureId}, 0, {bpLogin}, {bpPassword}) ok : {wu.UserId}");

                BasketDTO bask = _basketManager.CreateBasketIfNotExists(structureId, demand.webUser.Id, 0, "C", null);

                CreateOrderDemand creCmd = new CreateOrderDemand();
                creCmd.Consumer = demand.Consumer;
                creCmd.AmountPaid = demand.AmountPaid;
                creCmd.TransactionReference = demand.TransactionReference;
                creCmd.BarCodes = new();

                List<BarCodeDTO> recetteLineDTOs = new();

                foreach (var sessionCategDmd in demand.ListDemands)
                {
                    FlagDemand flgDemand = new FlagDemand()
                    {
                        WebUserId = demand.webUser.Id,
                        ListDemands = new List<SessionCategDemand>() { sessionCategDmd },
                        // ListDemands = sessionCategDmd.ListRules
                    };


                    // ***** flag seats
                    var flaggedResponse = _seatManager.FlagAuto(structureId, flgDemand);

                    // ***** create basket 

                    var listGpAsked = sessionCategDmd.ListRules.Select(r => r.RuleId).Distinct().ToList();
                    List<EventDTO> listEvent =
                        listEvent = _eventManager.GetEventsSessionsCategsPrices("", structureId, demand.webUser.IdentityId,
                         0, sessionCategDmd.SessionId, myBprofilId, "", listGpAsked);

                    var zones = listEvent[0].ListSessions[0].ListZones;
                    var floors = zones.SelectMany(f => f.ListFloors).ToList();
                    var sections = floors.Distinct().ToList().SelectMany(s => s.ListSections).Distinct().ToList();
                    var categories = sections.Distinct().ToList().SelectMany(c => c.ListCategories).Distinct().ToList();
                    var gpList = categories.Distinct().ToList().SelectMany(gp => gp.ListGestionPlace).Distinct().ToList();

                    int idxSeatAttribue = 0;

                    foreach (rule r in sessionCategDmd.ListRules)
                    {
                        var thisGpS = _gestionPlaceManager.Load(structureId, new List<int> { r.RuleId });
                        var thisGp = thisGpS.FirstOrDefault();

                        var flaggedRule = flaggedResponse.ListDemands[0].ListRules.Where(s => s.RuleId == r.RuleId).FirstOrDefault();


                        //foreach (var seat in r.ListSeats)
                        for (int i = 0; i < r.SeatsCount; i++)
                        {
                            //List<BarCodeDTO> recetteLineDTOs = (List<BarCodeDTO>)Mapper.Map(orderDemand.BarCodes, typeof(List<Ticket>), typeof(List<BarCodeDTO>));
                            var thisFlaggedSeat = flaggedRule.ListSeats[idxSeatAttribue];

                            if (r.ListSeats.Count > i && r.ListSeats[i] != null && !string.IsNullOrEmpty(r.ListSeats[i].BarCode))
                            {
                                creCmd.BarCodes.Add(new Ticket
                                {
                                    SeatId = thisFlaggedSeat.SeatId,
                                    SessionId = thisFlaggedSeat.SessionId,
                                    BarCode = r.ListSeats[i].BarCode
                                }
                                );
                            }

                            // List<Seat> listS = _mapper.Map<List<Seat>>(seatsList);
                            SeatDTO sdto = (SeatDTO)Mapper.Map(thisFlaggedSeat, typeof(Seat), typeof(SeatDTO));

                            _basketManager.AddSeatInBasketLine(structureId, bask.BasketId, listEvent[0], listEvent[0].ListSessions[0], sdto, thisGp, 0);

                            //creCmd.BarCodes = new();
                            idxSeatAttribue++;
                        }
                    }
                    // create order

                }


                Logger.Debug(structureId, $"CreateOrderFromDemand basket créé : {bask.BasketId}");

                var ordE = _orderManager.CreateFromDemand(structureId, bask.BasketId, amount, creCmd);

                Logger.Debug(structureId, $"CreateOrderFromDemand ({structureId}, {bask.BasketId}) retour {JsonConvert.SerializeObject(ordE)}...");

                if (ordE == null || ordE.OrderId == 0)
                {
                    var pb = Problem(
                        ordE == null ? $"ordE is null !?" : $"ordE equal 0 !?", null, StatusCodes.Status500InternalServerError);
                    return pb;
                }

                OrderDTO ordE2 = await _orderManager.LoadAsync(structureId, "en", ordE.OrderId, 0);

                Order ret = (Order)Mapper.Map(ordE2, typeof(OrderDTO), typeof(Order));

                ret = OrderEntityToOrder(ordE2, ret);

                Logger.Info(structureId, $"CreateOrder({structureId}, {bask.BasketId}) return {JsonConvert.SerializeObject(ret)}...");
                _gestionTrace.WriteLogMessage(structureId, 0, bask.BasketId, $"CreateOrder({structureId}, {bask.BasketId}) ok, cmd {ret.Id}", TypeLog.LogGenerique);
                return Ok(ret);


            }
            catch (Exception ex)
            {
                //_gestionTrace.WriteLogErrorMessage(structureId, 0, 0, $"CreateOrder_straight({structureId}, {0}) ko, {ex.Message} {ex.StackTrace}");

                Logger.Error(structureId, $"CreateOrderstraight({structureId}, {demand}) , " +
                    $"{ex.Message} {ex.StackTrace}");


                // On retourne uniquement le type d'exception
                var errorType = ex.GetType().Name;
                return Problem(
                    detail: null, // Pas de détails exposés
                    title: $"Internal Error : {errorType}",
                    statusCode: StatusCodes.Status500InternalServerError
                );

            }
        }





        /// <summary>
        /// Create order from a basket and pay it
        /// </summary>
        /// <param name="structureId">The unique structure identifier</param>
        /// <param name="basketId">The basket identifier of the internet user</param>
        /// <param name="amount">Amount paid, in cents</param>       
        /// <returns></returns>
        [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(Order))]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [HttpPost]
        [Authorize(Roles = "Revendeur,Integrateur,Admin")]
        [Route("api/{structureId}/Order/{basketId}")]
        public async Task<IActionResult> CreateOrder(int structureId, int basketId, int amount, [FromBody] CreateOrderDemand orderDemand)
        {

            var accT = Request.Headers[HeaderNames.Authorization];
            if (!TokenManager.PartnerHasRight(structureId, accT))
            {
                //Exception ex = new UnauthorizedAccessException ($"structureId {structureId} is not allowed");
                Logger.Error(structureId, $"structureId {structureId} is not allowed");
                var pb = Problem($"structureId {structureId} is not allowed", null, StatusCodes.Status401Unauthorized);
                return pb;
            }
            Logger.Info(structureId, $"CreateOrder({structureId}, {basketId}) {JsonConvert.SerializeObject(orderDemand)}...");
            _gestionTrace.WriteLogMessage(structureId, 0, basketId, $"CreateOrder({structureId}, {basketId})", TypeLog.LogGenerique);

            try
            {

                var ordE = _orderManager.CreateFromDemand(structureId, basketId, amount, orderDemand);

                Logger.Debug(structureId, $"CreateOrder({structureId}, {basketId}) retour {JsonConvert.SerializeObject(ordE)}...");

                if (ordE == null || ordE.OrderId == 0)
                {
                    var pb = Problem(
                        ordE == null ? $"ordE is null !?" : $"ordE equal 0 !?", null, StatusCodes.Status500InternalServerError);
                    return pb;
                }

                OrderDTO ordE2 = await _orderManager.LoadAsync(structureId, "en", ordE.OrderId, 0);

                Order ret = (Order)Mapper.Map(ordE2, typeof(OrderDTO), typeof(Order));

                ret = OrderEntityToOrder(ordE2, ret);

                Logger.Info(structureId, $"CreateOrder({structureId}, {basketId}) return {JsonConvert.SerializeObject(ret)}...");
                _gestionTrace.WriteLogMessage(structureId, 0, basketId, $"CreateOrder({structureId}, {basketId}) ok, cmd {ret.Id}", TypeLog.LogGenerique);
                return Ok(ret);

            }
            catch (Exception ex)
            {
                _gestionTrace.WriteLogErrorMessage(structureId, 0, basketId, $"CreateOrder({structureId}, {basketId}) ko, {ex.Message} {ex.StackTrace}");

                Logger.Error(structureId, $"CreateOrder({structureId}, {basketId}) , " +
                    $"{ex.Message} {ex.StackTrace}");
                return Problem(ex.Message, null, StatusCodes.Status500InternalServerError);
            }
        }

        /// <summary>
        /// /!\ o.ListSeats est epuré => ne contient pas les places en A
        /// </summary>
        /// <param name="ordE"></param>
        /// <param name="ord"></param>
        /// <returns></returns>
        private Order OrderEntityToOrder(OrderDTO ordE, Order ord)
        {


            foreach (var o in ordE.listEvents)
            {
                Event ev = (Event)Mapper.Map(o, typeof(EventDTO), typeof(Event));
                ord.Events.Add(ev);
            }


            foreach (var o in ordE.ListDossiersSeats)
            {

                int sessionId = o.Session.SessionId;
                int thisEventId = o.Event.EventId;

                //var thisEv = ord.Events.Where(e => e.Id == o.Event.EventId).FirstOrDefault();
                var thisEv = ord.Events.FirstOrDefault(e => e.Id == thisEventId);
                if (thisEv == null)
                {
                    thisEv = new Event()
                    {

                        Id = thisEventId,
                        Name = o.Event.EventName,
                        EventDescription1 = o.Event.EventDescription1,
                        EventDescription2 = o.Event.EventDescription2,
                        EventDescription3 = o.Event.EventDescription3,
                        EventDescription4 = o.Event.EventDescription4,
                        EventDescription5 = o.Event.EventDescription5,
                        ProducerLicenceNum1 = o.Event.ProducerLicenceNum1,
                        ProducerLicenceNum2 = o.Event.ProducerLicenceNum2,
                        ProducerName = o.Event.ProducerName,
                    };
                    ord.Events.Add(thisEv);

                }
                //thisEv = ord.Events.Where(e => e.Id == o.Event.EventId).FirstOrDefault();


                //var thisSession = thisEv.Sessions.Where(e => e.Id == thisSessionId).FirstOrDefault();
                var session = thisEv.Sessions.FirstOrDefault(s => s.Id == sessionId);

                if (session == null)
                {

                    session = new Session()
                    {
                        Id = sessionId,
                        StartDate = o.Session.SessionStartDate,
                        EndDate = o.Session.SessionEndDate,
                        EventId = thisEventId,
                        Place = (Place)Mapper.Map(o.Event.ListSessions[0].Place, typeof(PlaceDTO), typeof(Place))
                    };
                    thisEv.Sessions.Add(session);
                }
                //thisSession = thisEv.Sessions.Where(e => e.Id == sessionId).FirstOrDefault();


                // Ajout des sièges à la racine (exclure ceux avec l'état 'A' annulé)
                foreach (var sE in o.ListSeats) 
                {
                    if (sE.State != 'A')
                    {
                        var s = (Seat)Mapper.Map(sE, typeof(SeatDTO), typeof(Seat));
                        session.Seats.Add(s);
                    }
                }

            }
            return ord;

        }

    }
}
