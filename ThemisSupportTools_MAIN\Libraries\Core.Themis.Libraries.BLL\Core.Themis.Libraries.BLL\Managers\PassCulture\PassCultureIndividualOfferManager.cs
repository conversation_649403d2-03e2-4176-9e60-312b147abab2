﻿using AutoMapper;
using Core.Themis.Libraries.BLL.Managers.PassCulture.Interfaces;
using Core.Themis.Libraries.BLL.Services.PassCulture.Interfaces;
using Core.Themis.Libraries.Data.Entities.Open.GestionPlace;
using Core.Themis.Libraries.Data.Entities.Open.Partner;
using Core.Themis.Libraries.Data.Entities.Open.Structure;
using Core.Themis.Libraries.Data.Repositories.Open.EventsSessions.Interfaces;
using Core.Themis.Libraries.Data.Repositories.Open.GestionPlaces.Interfaces;
using Core.Themis.Libraries.Data.Repositories.Open.Partner.Interfaces;
using Core.Themis.Libraries.Data.Repositories.Open.Structure.Interfaces;
using Core.Themis.Libraries.DTO.Enums;
using Core.Themis.Libraries.DTO.Lookup;
using Core.Themis.Libraries.DTO.PassCulture;
using Core.Themis.Libraries.DTO.PassCulture.ApiExchange.Response;
using Core.Themis.Libraries.Utilities.Helpers;
using Core.Themis.Libraries.Utilities.Helpers.Images;
using Core.Themis.Libraries.Utilities.Logging;
using System;
using System.Collections.Generic;
using System.Drawing.Imaging;
using System.Linq;
using System.Threading.Tasks;

namespace Core.Themis.Libraries.BLL.Managers.PassCulture
{
    public class PassCultureIndividualOfferManager : IPassCultureIndividualOfferManager
    {
        private static readonly RodrigueNLogger RodrigueLogger = new();

        private readonly IOfferRepository _offerRepository;
        private readonly IOffreGestionPlaceRepository _offreGestionPlaceRepository;
        private readonly IMapper _mapper;
        private readonly IPassCultureService _passCultureService;
        private readonly IPartnerIdsProviderRepository _partnerIdsProviderRepository;
        private readonly IOpenPartnerRepository _partnerRepository;
        private readonly IStructurePrefsRepository _structurePrefsRepository;
        private readonly IEventRepository _eventRepository;
        private readonly ISessionRepository _sessionRepository;
        private readonly IStructureRepository _structureRepository;

        public PassCultureIndividualOfferManager(
            IOfferRepository offerRepository,
            IOffreGestionPlaceRepository offreGestionPlaceRepository,
            IMapper mapper,
            IPassCultureService passCultureService,
            IPartnerIdsProviderRepository partnerIdsProviderRepository,
            IOpenPartnerRepository partnerRepository,
            IStructurePrefsRepository structurePrefsRepository,
            IEventRepository eventRepository,
            ISessionRepository sessionRepository,
            IStructureRepository structureRepository)
        {
            _offerRepository = offerRepository;
            _offreGestionPlaceRepository = offreGestionPlaceRepository;
            _mapper = mapper;
            _passCultureService = passCultureService;
            _partnerIdsProviderRepository = partnerIdsProviderRepository;
            _partnerRepository = partnerRepository;
            _structurePrefsRepository = structurePrefsRepository;
            _eventRepository = eventRepository;
            _sessionRepository = sessionRepository;
            _structureRepository = structureRepository;
        }


        public async Task<(List<InfoSuppForm>, List<CategoryEventInfoForm>)> GetIndividualOfferFormsForUpdateAsync(int structureId, int offerId)
        {
            var idProviders = _partnerIdsProviderRepository.FindCustomEntityBy(pip => pip.PartnerIdsProviderTypeId == (int)PartnerIdsProviderType.PassCulture_OffreIndividuel
                                                                        && pip.PartnerTableRodrigueTypeId == (int)TableRodrigueType.Offre
                                                                        && pip.RodrigueId == offerId, structureId)
                                                            .Include(pip => pip.RodrigueIdsCombine)
                                                            .ToEntityList()
                ?? throw new NullReferenceException($"idProviders for offerId '{offerId}' for structureId '{structureId}' not found");

            int[] eventIdsFromRodrigue = (await _offreGestionPlaceRepository.GetAllManifestationLinkedToAnOfferAsync(structureId, offerId).ConfigureAwait(false))
                                            .Select(m => m.ManifestationId)
                                            .ToArray();

            int[] eventOfferIds = idProviders.Where(pip => pip.RodrigueIdsCombine?.FirstOrDefault(c => c.PartnerTableRodrigueTypeId == (int)TableRodrigueType.Manifestation) is not null
                                                    && eventIdsFromRodrigue.Contains(pip.RodrigueIdsCombine.First(c => c.PartnerTableRodrigueTypeId == (int)TableRodrigueType.Manifestation).RodrigueId))
                                             .Select(pip => int.Parse(pip.IdProviderValue))
                                             .ToArray();

            List<EventOffer> eventOffers = [];

            foreach (int id in eventOfferIds)
            {
                EventOffer eventOffer = await _passCultureService.GetEventByIdAsync(structureId, id)
                    ?? throw new NullReferenceException($"pass culture eventOffer with eventOfferId '{id}' for structureId '{structureId}' not found");

                eventOffers.Add(eventOffer);
            }

            List<InfoSuppForm> infoSuppForms = eventOffers.GroupBy(e => new {
                e.BookingContact,
                e.BookingEmail,
                e.Location.VenueId,
                e.Location.AddressId,
                e.Accessibility.AudioDisabilityCompliant,
                e.Accessibility.MentalDisabilityCompliant,
                e.Accessibility.VisualDisabilityCompliant,
                e.Accessibility.MotorDisabilityCompliant,
            })
            .Select(gp =>
            {
                int[] eventIds = gp.Select(e => e.IdAtProvider!.Split('-')
                                                            .Select(s => int.Parse(s))
                                                            .ToArray()[1])
                                   .ToArray();

                InfoSuppForm form = _mapper.Map<InfoSuppForm>(gp.First());
                form.EventIds = eventIds;

                return form;
            }).ToList();

            List<CategoryEventInfoForm> categoryForms = eventOffers.GroupBy(e => new {
                e.CategoryRelatedFields.Performer,
                e.CategoryRelatedFields.Speaker,
                e.CategoryRelatedFields.Visa,
                e.CategoryRelatedFields.Author,
                e.CategoryRelatedFields.Category,
                e.CategoryRelatedFields.MusicType,
                e.CategoryRelatedFields.ShowType,
                e.CategoryRelatedFields.StageDirector
            })
            .Select(gp =>
            {
                int[] eventIds = gp.Select(e => e.IdAtProvider!.Split('-')
                                                            .Select(s => int.Parse(s))
                                                            .ToArray()[1])
                                   .ToArray();

                CategoryEventInfoForm form = _mapper.Map<CategoryEventInfoForm>(gp.First());
                form.EventIds = eventIds;

                return form;
            }).ToList();

            return (infoSuppForms, categoryForms);
        }

        public async Task<PassCultureResponse<List<EventOffer>>> CreateIndividualOffersAsync(int structureId, int offerId, List<InfoSuppForm> infoSuppPassCultures, List<CategoryEventInfoForm> categoryEventPassCultures)
        {
            try
            {
                List<EventOffer> result = [];

                var listOffreGP = _offreGestionPlaceRepository.GetAllOffreGestionPlaceEntityByIdOfferForPassCulture(structureId, offerId) ?? [];

                var gpGroup = listOffreGP.Select(ogp => ogp.GestionPlace)
                                         .GroupBy(gp => gp.ManifId);

                if (gpGroup.Any())
                {
                    foreach (var group in gpGroup)
                    {
                        int eventId = group.Key!.Value;
                        List<GestionPlaceEntity> gestionPlaces = [.. group];

                        InfoSuppForm infoSuppPassCulture = infoSuppPassCultures.First(i => i.EventIds.Contains(eventId));
                        CategoryEventInfoForm categoryEventPassCulture = categoryEventPassCultures.First(i => i.EventIds.Contains(eventId));

                        SetPassCultureEmailPreferance(structureId, infoSuppPassCulture);

                        EventOffer newEventOffer = await AddEventOfferInPassCultureAsync(structureId, offerId, eventId, gestionPlaces, infoSuppPassCulture, categoryEventPassCulture);

                        result.Add(newEventOffer);
                    }

                    return new()
                    {
                        Success = true,
                        Result = result,
                    };
                }

                throw new Exception("L'offre ne contient aucun groupe compatible pour l'offre individuel");
            }
            catch (Exception ex)
            {
                RodrigueLogger.Error(structureId, ex.Message);
                RodrigueLogger.Error(structureId, ex.Message);
                return new()
                {
                    Success = false,
                    ErrorMessage = ex.Message
                };
            }
        }

        public async Task<PassCultureResponse<List<EventOffer>>> UpdateIndividualOffersAsync(int structureId, int offerId, List<InfoSuppForm> infoSuppPassCultures, List<CategoryEventInfoForm> categoryEventPassCultures)
        {
            try
            {
                List<EventOffer> result = [];

                var listOffreGP = _offreGestionPlaceRepository.GetAllOffreGestionPlaceEntityByIdOfferForPassCulture(structureId, offerId) ?? [];

                var gpGroup = listOffreGP.Select(ogp => ogp.GestionPlace)
                                         .GroupBy(gp => gp.ManifId);

                int[] eventIdInRodrigueOffer = gpGroup.Select(gp => gp.Key!.Value).ToArray();

                var idProvidersForOffer = _partnerIdsProviderRepository.FindCustomEntityBy(p => p.PartnerIdsProviderTypeId == (int)PartnerIdsProviderType.PassCulture_OffreIndividuel
                                                                                            && p.PartnerTableRodrigueTypeId == (int)TableRodrigueType.Offre
                                                                                            && p.RodrigueId == offerId, structureId)
                                                                        .Include(p => p.RodrigueIdsCombine)
                                                                        .ToEntityList();

                int[] eventIdsPushInPassCulture = idProvidersForOffer.SelectMany(p => p.RodrigueIdsCombine!.Where(c => c.PartnerTableRodrigueTypeId == (int)TableRodrigueType.Manifestation)
                                                                                                          .Select(c => c.RodrigueId)).ToArray();

                var eventIdsToDelete = eventIdsPushInPassCulture.Where(id => !eventIdInRodrigueOffer.Contains(id));

                foreach (int eventIdToDelete in eventIdsToDelete)
                {
                    bool eventIsCancelled = await _eventRepository.CheckIfEventIsCancelledAsync(structureId, eventIdToDelete);

                    if (eventIsCancelled)
                        await CancelEventOfferInPassCultureAsync(structureId, offerId, eventIdToDelete);
                    else
                        await RemoveEventOfferStockInPassCultureAsync(structureId, offerId, eventIdToDelete);
                }

                if (gpGroup.Any())
                {
                    foreach (var group in gpGroup)
                    {
                        int eventId = group.Key!.Value;
                        List<GestionPlaceEntity> gestionPlaces = [.. group];

                        InfoSuppForm infoSuppPassCulture = infoSuppPassCultures.First(i => i.EventIds.Contains(eventId));
                        CategoryEventInfoForm categoryEventPassCulture = categoryEventPassCultures.First(i => i.EventIds.Contains(eventId));

                        SetPassCultureEmailPreferance(structureId, infoSuppPassCulture);

                        if (!eventIdsPushInPassCulture.Contains(eventId))
                        {
                            EventOffer newEventOffer = await AddEventOfferInPassCultureAsync(structureId, offerId, eventId, gestionPlaces, infoSuppPassCulture, categoryEventPassCulture);
                            result.Add(newEventOffer);
                        }
                        else if (eventIdInRodrigueOffer.Contains(eventId))
                        {
                            EventOffer updatedEventOffer = await UpdateEventOfferInPassCultureAsync(structureId, offerId, eventId, gestionPlaces, infoSuppPassCulture, categoryEventPassCulture);
                            result.Add(updatedEventOffer);
                        }
                    }

                    return new()
                    {
                        Success = true,
                        Result = result,
                    };
                }

                throw new Exception("L'offre ne contient aucun groupe compatible pour l'offre individuel");
            }
            catch (Exception ex)
            {
                RodrigueLogger.Error(structureId, ex.Message);
                return new()
                {
                    Success = false,
                    ErrorMessage = ex.Message
                };
            }
        }

        public async Task<List<KeyValuePair<int, string>>> GetOfferEventsKeyValuesByOfferIdAsync(int structureId, int offerId)
        {
            var eventsEntity = await _offreGestionPlaceRepository.GetAllManifestationLinkedToAnOfferAsync(structureId, offerId)
                                                                 .ConfigureAwait(false);

            return eventsEntity.Select(e => new KeyValuePair<int, string>(e.ManifestationId, e.ManifestationNom)).ToList();
        }

        #region SelectLookup

        public async Task<List<SelectLookup>> SelectLookupOfferEventsAsync(int structureId, int offerId)
        {
            var events = await GetOfferEventsKeyValuesByOfferIdAsync(structureId, offerId);

            return events.Select(v => new SelectLookup()
            {
                Value = v.Key.ToString(),
                Libelle = v.Value
            })
                .ToList();
        }

        public List<SelectLookup> SelectLookupOfferForPassCulture(int structureId, bool forUpdate = false)
        {
            int? buyeurProfil = _partnerRepository.GetById(structureId, (int)PartnerType.PassCulture)?.ProfilAcheteurId;

            if (buyeurProfil is null)
                return [];

            var offers = _offerRepository.GetOffersLinkedToBuyerProfilRevendeur(structureId, buyeurProfil.Value)
                                         .Where(o => o.DateFinValidite > DateTime.Now)
                                         .ToList();

            if (forUpdate)
            {
                offers = offers!.Where(o => _partnerIdsProviderRepository.FindFirstOrDefault(p => p.PartnerIdsProviderTypeId == (int)PartnerIdsProviderType.PassCulture_OffreIndividuel
                                                                            && p.PartnerTableRodrigueTypeId == (int)TableRodrigueType.Offre
                                                                            && p.RodrigueId == o.OffreId, structureId) is not null).ToList();
            }
            else
            {
                offers = offers!.Where(o => _partnerIdsProviderRepository.FindFirstOrDefault(p => p.PartnerIdsProviderTypeId == (int)PartnerIdsProviderType.PassCulture_OffreIndividuel
                                                                                            && p.PartnerTableRodrigueTypeId == (int)TableRodrigueType.Offre
                                                                                            && p.RodrigueId == o.OffreId, structureId) is null).ToList();
            }

            return offers.Select(o => new SelectLookup()
            {
                Value = o.OffreId.ToString(),
                Libelle = $"{o.OffreId} - {o.OffreNom}"
            })
            .ToList();
        }

        #endregion

        #region Private 

        //CRUD EventOffer
        private async Task<EventOffer> GetPassCultureEventOfferByRodrigueOfferIdAsync(int structureId, int rodrigueOfferId, int rodrigueEventId)
        {
            var partnerIdsProvider = _partnerIdsProviderRepository.FindCustomEntityBy(pip => pip.PartnerIdsProviderTypeId == (int)PartnerIdsProviderType.PassCulture_OffreIndividuel
                                                                                        && pip.PartnerTableRodrigueTypeId == (int)TableRodrigueType.Offre
                                                                                        && pip.RodrigueId == rodrigueOfferId, structureId)
                                                                  .Include(pip => pip.RodrigueIdsCombine)
                                                                  .Where(pip => pip.RodrigueIdsCombine != null
                                                                            && pip.RodrigueIdsCombine.All(ric => ric.PartnerTableRodrigueTypeId == (int)TableRodrigueType.Manifestation
                                                                                                            && ric.RodrigueId == rodrigueEventId))
                                                                  .ToEntity();

            if (partnerIdsProvider is null)
                throw new NullReferenceException($"orderId: {rodrigueOfferId} and eventId: {rodrigueEventId} from structure {structureId}, don't linked to pass culture individual");

            var eventOffer = await _passCultureService.GetEventByIdAsync(structureId, int.Parse(partnerIdsProvider.IdProviderValue))
                                                      .ConfigureAwait(false);

            if (eventOffer is null)
                throw new NullReferenceException($"pass culture individual offer with id {partnerIdsProvider.IdProviderValue} not found");

            return eventOffer;
        }

        private EventOffer GetEventOfferWithFormInfoSupp(int structureId, int offerId, int eventId, GestionPlaceEntity gestionPlace, InfoSuppForm infoSuppPassCulture, CategoryEventInfoForm categoryEventPassCulture)
        {
            EventOffer eventOffer = _mapper.Map<EventOffer>(gestionPlace);

            int venueId = _passCultureService.GetVenueIdByStructureId(structureId)
                ?? throw new NullReferenceException($"Venue id for structure {structureId} doesn't exist, check pass culture settings");

            if (infoSuppPassCulture.VenueId == 0)
                infoSuppPassCulture.VenueId = venueId;

            eventOffer.BookingContact = infoSuppPassCulture.BookingContact;
            eventOffer.BookingEmail = string.IsNullOrWhiteSpace(infoSuppPassCulture.BookingEmail) ? null : infoSuppPassCulture.BookingEmail;
            eventOffer.Accessibility = infoSuppPassCulture.GetAccessibility();
            eventOffer.CategoryRelatedFields = categoryEventPassCulture.GetCategoryRelatedFields();
            eventOffer.IdAtProvider = $"{offerId}-{eventId}";
            eventOffer.Location = infoSuppPassCulture.GetLocation();

            string? imagePath = UrlHelper.GetEventImageUrl(structureId, eventId, true);

            if (imagePath is not null)
            {
                string base64Image = ImagesHelper.GetResizedBase64Image(imagePath, new(400, 600), ImageFormat.Png);

                string? structureName = _structureRepository.GetById(structureId, structureId)?.StructureNom;

                eventOffer.Image = new()
                {
                    Credit = structureName ?? "Rodrigue",
                    File = base64Image,
                };
            }

            return eventOffer;
        }

        private async Task<EventOffer> AddEventOfferInPassCultureAsync(int structureId, int offerId, int eventId, List<GestionPlaceEntity> gestionPlaces, InfoSuppForm infoSuppPassCulture, CategoryEventInfoForm categoryEventPassCulture)
        {
            EventOffer eventOffer = GetEventOfferWithFormInfoSupp(structureId, offerId, eventId, gestionPlaces.First(), infoSuppPassCulture, categoryEventPassCulture);

            var newEventOffer = await _passCultureService.CreateEventOfferAsync(structureId, eventOffer)
                                                         .ConfigureAwait(false);

            if (newEventOffer is not null)
            {
                SavePassCultureEventOfferIdsProvider(structureId, newEventOffer!.Id!.Value.ToString(), offerId, eventId);

                List<PriceCategory> priceCategoriesToAdd = GetPriceCategoriesByOfferGestionPlaces(offerId, gestionPlaces);
                List<PriceCategory> newPriceCategories = await AddPriceCategoriesInPassCultureAsync(structureId, newEventOffer.Id!.Value, priceCategoriesToAdd);

                foreach (var priceCategory in newPriceCategories)
                {
                    List<Date> datesToAdd = GetDatesByOfferGestionPlaces(offerId, gestionPlaces, priceCategory);
                    await AddDatesInPassCultureAsync(structureId, newEventOffer.Id!.Value, datesToAdd);
                }

                return newEventOffer;
            }

            throw new Exception("Erreur lors de la création de l'offre individuelle sur pass culture");
        }

        private async Task<EventOffer> UpdateEventOfferInPassCultureAsync(int structureId, int offerId, int eventId, List<GestionPlaceEntity> gestionPlaces, InfoSuppForm infoSuppPassCulture, CategoryEventInfoForm categoryEventPassCulture)
        {
            EventOffer newEventOffer = GetEventOfferWithFormInfoSupp(structureId, offerId, eventId, gestionPlaces.First(), infoSuppPassCulture, categoryEventPassCulture);

            var currentEventOffer = await GetPassCultureEventOfferByRodrigueOfferIdAsync(structureId, offerId, eventId);

            if (newEventOffer.CategoryRelatedFields.Category != currentEventOffer.CategoryRelatedFields.Category)
            {
                return await CancelEventOfferInPassCultureAsync(structureId, offerId, eventId)
                                .ContinueWith(async (task) =>
                                    await AddEventOfferInPassCultureAsync(structureId, offerId, eventId, gestionPlaces, infoSuppPassCulture, categoryEventPassCulture)
                                ).Result;
            }
            else
            {
                var eventOfferToUpdate = _mapper.Map(newEventOffer, currentEventOffer);

                EventOffer? updatedEventOffer = await _passCultureService.UpdateEventOfferAsync(structureId, currentEventOffer.Id!.Value, new(eventOfferToUpdate))
                                                                         .ConfigureAwait(false);

                if (updatedEventOffer is not null)
                {
                    List<PriceCategory> newPriceCategories = GetPriceCategoriesByOfferGestionPlaces(offerId, gestionPlaces);
                    List<PriceCategory> updatedPriceCategories = await UpdatePriceCategoriesInPassCultureAsync(structureId, offerId, eventId, updatedEventOffer.Id!.Value, newPriceCategories);

                    foreach (var priceCategory in updatedPriceCategories)
                    {
                        List<Date> newDates = GetDatesByOfferGestionPlaces(offerId, gestionPlaces, priceCategory);
                        await UpdateDatesInPassCultureAsync(structureId, currentEventOffer.Id!.Value, priceCategory.Id!.Value, newDates);
                    }

                    return newEventOffer;
                }
            }

            throw new Exception("Erreur lors de l'update de l'offre individuelle sur pass culture");
        }

        private async Task CancelEventOfferInPassCultureAsync(int structureId, int offerId, int eventId)
        {
            var eventOffer = await GetPassCultureEventOfferByRodrigueOfferIdAsync(structureId, offerId, eventId);

            eventOffer.IsActive = false;
            eventOffer.IdAtProvider = null;

            foreach (PriceCategory priceCategory in eventOffer.PriceCategories)
                await PurgeAllDatesLinkedToPriceCategoryAsync(structureId, eventOffer.Id!.Value, priceCategory.Id!.Value);

            await _passCultureService.UpdateEventOfferAsync(structureId, eventOffer.Id!.Value, new(eventOffer));

            bool provideIdIsDeleted = _partnerIdsProviderRepository.DeleteProviderId(structureId, eventOffer.Id!.Value, PartnerIdsProviderType.PassCulture_OffreIndividuel);

            if (!provideIdIsDeleted)
                throw new Exception($"Provide pass culture event offer id '{eventOffer.Id!.Value}', not deleted in bdd for structure '{structureId}'");
        }

        private async Task RemoveEventOfferStockInPassCultureAsync(int structureId, int offerId, int eventId)
        {
            var eventOffer = await GetPassCultureEventOfferByRodrigueOfferIdAsync(structureId, offerId, eventId);

            foreach (PriceCategory priceCategory in eventOffer.PriceCategories)
                await PurgeAllDatesLinkedToPriceCategoryAsync(structureId, eventOffer.Id!.Value, priceCategory.Id!.Value);
        }

        //CRUD PriceCategory
        private async Task<List<PriceCategory>> GetPassCulturePriceCategoryByRodrigueOfferIdAsync(int structureId, int rodrigueOfferId, int rodrigueEventId)
        {
            var eventOffer = await GetPassCultureEventOfferByRodrigueOfferIdAsync(structureId, rodrigueOfferId, rodrigueEventId)
                                                      .ConfigureAwait(false);

            if (eventOffer is null)
                throw new NullReferenceException($"pass culture individual offer not found");

            return eventOffer.PriceCategories;
        }

        private List<PriceCategory> GetPriceCategoriesByOfferGestionPlaces(int offerId, List<GestionPlaceEntity> gestionPlaces)
        {
            List<PriceCategory> priceCategories = _mapper.Map<List<PriceCategory>>(gestionPlaces);
            var priceGroup = priceCategories.GroupBy(p => new { p.ManifestationId, p.TypeTarifId, p.CategoryId });

            List<PriceCategory> priceCategoriesToReturn = priceGroup.Select(g => g.First())
                                                                    .DistinctBy(p => new { p.ManifestationId, p.TypeTarifId, p.CategoryId })
                                                                    .ToList();

            priceCategoriesToReturn.ForEach(p => p.IdAtProvider = $"{offerId}-{p.ManifestationId}-{p.CategoryId}-{p.TypeTarifId}");

            return priceCategoriesToReturn;
        }

        private async Task<List<PriceCategory>> AddPriceCategoriesInPassCultureAsync(int structureId, int eventOfferId, List<PriceCategory> priceCategories)
        {
            var newPriceCategories = await _passCultureService.CreatePriceCategoriesAsync(structureId, eventOfferId, priceCategories)
                                                              .ConfigureAwait(false);

            if (newPriceCategories != null)
            {
                newPriceCategories.ForEach(pc => SavePassCulturePriceCategoryIdsProvider(structureId, pc));

                return newPriceCategories;
            }

            throw new Exception("Erreur lors de la création des categories de prix sur pass culture");
        }

        private async Task<List<PriceCategory>> UpdatePriceCategoriesInPassCultureAsync(int structureId, int offerId, int eventId, int eventOfferId, List<PriceCategory> updatedPriceCategories)
        {
            List<PriceCategory> priceCategoriesToReturn = [];

            List<PriceCategory> currentPriceCategories = await GetPassCulturePriceCategoryByRodrigueOfferIdAsync(structureId, offerId, eventId)
                                                                    .ConfigureAwait(false);

            List<PriceCategory> priceCategoriesToAdd = updatedPriceCategories.Where(upc => !currentPriceCategories.Any(pc => pc.IdAtProvider == upc.IdAtProvider)).ToList();

            if (priceCategoriesToAdd.Count != 0)
            {
                List<PriceCategory> newPriceCategories = await AddPriceCategoriesInPassCultureAsync(structureId, eventOfferId, priceCategoriesToAdd);
                priceCategoriesToReturn.AddRange(newPriceCategories);
            }

            List<PriceCategory> currentPriceCategoriesToDelete = currentPriceCategories.Where(pc => !updatedPriceCategories.Any(upc => pc.IdAtProvider == upc.IdAtProvider)).ToList();

            if (currentPriceCategoriesToDelete.Count != 0)
            {
                foreach (PriceCategory priceCategory in currentPriceCategoriesToDelete)
                    await PurgeAllDatesLinkedToPriceCategoryAsync(structureId, eventOfferId, priceCategory.Id!.Value);
            }

            List<PriceCategory> currentPriceCategoriesToUpdate = currentPriceCategories.Where(pc => updatedPriceCategories.Any(upc => pc.IdAtProvider == upc.IdAtProvider)).ToList();

            if (currentPriceCategoriesToUpdate.Count != 0)
            {
                foreach (PriceCategory currentPriceCategory in currentPriceCategoriesToUpdate)
                {
                    PriceCategory newPriceCategory = updatedPriceCategories!.First(upc => currentPriceCategory.IdAtProvider == upc.IdAtProvider);

                    var priceCategoryToUpdate = _mapper.Map(newPriceCategory, currentPriceCategory);

                    var updatedPriceCategory = await _passCultureService.UpdatePriceCategoryAsync(structureId, eventOfferId, currentPriceCategory.Id!.Value, priceCategoryToUpdate)
                                                                        .ConfigureAwait(false);

                    if (_partnerIdsProviderRepository.GetRodrigueIdByPartnerIdProvide(structureId, updatedPriceCategory!.Id.Value, PartnerIdsProviderType.PassCulture_PriceCategory) == 0)
                        SavePassCulturePriceCategoryIdsProvider(structureId, updatedPriceCategory);

                    if (updatedPriceCategory is not null)
                        priceCategoriesToReturn.Add(updatedPriceCategory);
                }
            }

            return priceCategoriesToReturn;
        }

        //CRUD Date event stock
        private List<Date> GetDatesByOfferGestionPlaces(int offerId, List<GestionPlaceEntity> gestionPlaces, PriceCategory priceCategoryParent)
        {
            int[] rodrigueIds = priceCategoryParent.IdAtProvider!.Split('-')
                                                            .Select(s => int.Parse(s))
                                                            .ToArray();

            List<Date> dates = gestionPlaces
                .GroupBy(d => new { d.ManifId, d.SeanceId, d.CategId, d.TypeTarifId })
                .Select(g => g.First())
                .DistinctBy(d => new { d.ManifId, d.SeanceId, d.CategId, d.TypeTarifId })
                .Where(d => d.CategId == rodrigueIds[2] && d.TypeTarifId == rodrigueIds[3])
                .Select(gp => new Date()
                {
                    IdAtProvider = $"{offerId}-{gp.ManifId}-{gp.SeanceId}-{gp.CategId}-{gp.TypeTarifId}",
                    BeginningDatetime = gp.Seance!.SeanceDateDeb.ToUniversalTime(),
                    BookingLimitDatetime = gp.Seance!.SeanceDateDeb.AddHours(-2).ToUniversalTime(),
                    Quantity = gp.Dispo,
                    PriceCategoryId = priceCategoryParent.Id
                })
                .ToList();

            return dates;
        }

        private async Task<List<Date>> AddDatesInPassCultureAsync(int structureId, int eventOfferId, List<Date> dates)
        {
            var newDates = await _passCultureService.AddStocksToAnEventAsync(structureId, eventOfferId, dates)
                                                    .ConfigureAwait(false);

            if (newDates != null)
            {
                newDates.ForEach(d => SavePassCultureDatesIdsProvider(structureId, d));
                return newDates;
            }

            throw new Exception("Erreur lors de la création des dates de séance sur pass culture");
        }

        private async Task<List<Date>> UpdateDatesInPassCultureAsync(int structureId, int eventOfferId, int priceCategoryId, List<Date> datesToUpdate)
        {
            List<Date> datesToReturn = [];

            List<Date> currentDates = await GetPassCultureDateEventStockByEventOfferIdAsync(structureId, eventOfferId, priceCategoryId, string.Join(',', datesToUpdate.Select(d => d.IdAtProvider)));

            List<Date> datesToAdd = datesToUpdate.Where(upc => !currentDates.Any(pc => pc.IdAtProvider == upc.IdAtProvider)).ToList();

            if (datesToAdd.Count != 0)
            {
                List<Date> newDates = await AddDatesInPassCultureAsync(structureId, eventOfferId, datesToAdd);
                datesToReturn.AddRange(newDates);
            }

            List<Date> currentDatesToDelete = currentDates.Where(pc => !datesToUpdate.Any(upc => pc.IdAtProvider == upc.IdAtProvider) && pc.BeginningDatetime > DateTime.Now).ToList();

            if (currentDatesToDelete.Count != 0)
                await DeleteOrRemoveStockDatesAsync(structureId, eventOfferId, currentDatesToDelete);

            List<Date> currentDatesToUpdate = currentDates.Where(pc => datesToUpdate.Any(upc => pc.IdAtProvider == upc.IdAtProvider)).ToList();

            if (currentDatesToUpdate.Count != 0)
            {
                foreach (Date currentDate in currentDatesToUpdate)
                {
                    Date newDate = datesToUpdate!.First(upc => currentDate.IdAtProvider == upc.IdAtProvider);

                    var dateToUpdate = _mapper.Map(newDate, currentDate);
                    dateToUpdate.Id = currentDate.Id;

                    var updatedDate = await UpdateDateInPassCultureAsync(structureId, eventOfferId, dateToUpdate);

                    if (updatedDate is not null)
                        datesToReturn.Add(updatedDate);
                }
            }

            return datesToReturn;

            throw new Exception("Erreur lors de la création des dates de séance sur pass culture");
        }

        private async Task<Date> UpdateDateInPassCultureAsync(int structureId, int eventOfferId, Date dateToUpdate)
        {
            var updatedDate = await _passCultureService.UpdateEventStockAsync(structureId, eventOfferId, dateToUpdate.Id!.Value, new(dateToUpdate))
                                           .ConfigureAwait(false);

            if (_partnerIdsProviderRepository.GetRodrigueIdByPartnerIdProvide(structureId, dateToUpdate.Id!.Value, PartnerIdsProviderType.PassCulture_Dates) == 0)
                SavePassCultureDatesIdsProvider(structureId, updatedDate!);

            return updatedDate!;
        }

        private async Task<bool> DeleteDatesInPassCultureAsync(int structureId, int eventOfferId, int dateId)
        {
            bool datesIsDeleted = await _passCultureService.DeleteEventStockAsync(structureId, eventOfferId, dateId)
                                                            .ConfigureAwait(false);

            if (datesIsDeleted)
            {
                await _passCultureService.CancelOrdersByDateIdInRodrigueAsync(structureId, dateId);
                DeletePassCultureDatesIdsProvider(structureId, dateId);

                return datesIsDeleted;
            }

            throw new Exception("Erreur lors de la création des dates de séance sur pass culture");
        }

        private async Task<List<Date>> GetPassCultureDateEventStockByEventOfferIdAsync(int structureId, int eventOfferId, int priceCategoryId, string? idsAtProvider = null)
        {
            List<Date> eventOfferDates = await _passCultureService.GetEventStocksByIdEventAsync(structureId, eventOfferId, idsAtProvider: idsAtProvider);

            while (eventOfferDates.Count == _passCultureService.Limit)
            {
                List<Date> moreEventOfferDates = await _passCultureService.GetEventStocksByIdEventAsync(structureId, eventOfferId, firstIndex: eventOfferDates.Last().Id + 1, idsAtProvider: idsAtProvider);
                eventOfferDates.AddRange(moreEventOfferDates);
            }

            return eventOfferDates.Where(d => d.PriceCategory!.Id == priceCategoryId && d.BeginningDatetime > DateTime.Now).ToList();
        }

        private async Task PurgeAllDatesLinkedToPriceCategoryAsync(int structureId, int eventOfferId, int priceCategoryId)
        {
            List<Date> dates = await GetPassCultureDateEventStockByEventOfferIdAsync(structureId, eventOfferId, priceCategoryId);

            await DeleteOrRemoveStockDatesAsync(structureId, eventOfferId, dates);
        }

        private async Task DeleteOrRemoveStockDatesAsync(int structureId, int eventOfferId, List<Date> dates)
        {
            foreach (Date date in dates)
            {
                int dateId = date.Id!.Value;
                int sessionId = _partnerIdsProviderRepository.GetRodrigueIdByPartnerIdProvide(structureId, dateId, PartnerIdsProviderType.PassCulture_Dates);

                bool sessionIsCancelled = await _sessionRepository.CheckIfSessionIsCancelled(structureId, sessionId);

                if (sessionIsCancelled)
                {
                    await DeleteDatesInPassCultureAsync(structureId, eventOfferId, dateId);
                }
                else
                {
                    date.Quantity = 0;
                    await UpdateDateInPassCultureAsync(structureId, eventOfferId, date);
                }
            }
        }


        //CRUD idsProvider
        private void SavePassCultureEventOfferIdsProvider(int structureId, string providerId, int offerId, int eventId)
        {
            PartnerIdsProviderEntity offerIdsProvider = new()
            {
                IdProviderValue = providerId,
                PartnerIdsProviderTypeId = (int)PartnerIdsProviderType.PassCulture_OffreIndividuel,
                PartnerTableRodrigueTypeId = (int)TableRodrigueType.Offre,
                RodrigueId = offerId,
                RodrigueIdsCombine =
                [
                    new()
                    {
                        PartnerTableRodrigueTypeId = (int)TableRodrigueType.Manifestation,
                        RodrigueId = eventId,
                    }
                ]
            };

            _partnerIdsProviderRepository.Insert(structureId, offerIdsProvider);
        }

        private void SavePassCulturePriceCategoryIdsProvider(int structureId, PriceCategory priceCategory)
        {
            int[] rodrigueIds = priceCategory.IdAtProvider!.Split('-')
                                                            .Select(s => int.Parse(s))
                                                            .ToArray();

            PartnerIdsProviderEntity priceCategorieIdsProvider = new()
            {
                IdProviderValue = priceCategory.Id!.Value.ToString(),
                PartnerIdsProviderTypeId = (int)PartnerIdsProviderType.PassCulture_PriceCategory,
                PartnerTableRodrigueTypeId = (int)TableRodrigueType.Categorie,
                RodrigueId = rodrigueIds[2],
                RodrigueIdsCombine =
                [
                    new()
                    {
                        PartnerTableRodrigueTypeId = (int)TableRodrigueType.Offre,
                        RodrigueId = rodrigueIds[0],
                    },
                    new()
                    {
                        PartnerTableRodrigueTypeId = (int)TableRodrigueType.Manifestation,
                        RodrigueId = rodrigueIds[1],
                    },
                    new()
                    {
                        PartnerTableRodrigueTypeId = (int)TableRodrigueType.TypeTarif,
                        RodrigueId = rodrigueIds[3],
                    },
                ]
            };

            _partnerIdsProviderRepository.Insert(structureId, priceCategorieIdsProvider);
        }

        private void SavePassCultureDatesIdsProvider(int structureId, Date date)
        {
            int[] rodrigueIds = date.IdAtProvider!.Split('-')
                                                  .Select(s => int.Parse(s))
                                                  .ToArray();

            PartnerIdsProviderEntity dateIdsProvider = new()
            {
                IdProviderValue = date.Id!.Value.ToString(),
                PartnerIdsProviderTypeId = (int)PartnerIdsProviderType.PassCulture_Dates,
                PartnerTableRodrigueTypeId = (int)TableRodrigueType.Seance,
                RodrigueId = rodrigueIds[2],
                RodrigueIdsCombine =
                [
                    new()
                    {
                        PartnerTableRodrigueTypeId = (int)TableRodrigueType.Offre,
                        RodrigueId = rodrigueIds[0],
                    },
                    new()
                    {
                        PartnerTableRodrigueTypeId = (int)TableRodrigueType.Manifestation,
                        RodrigueId = rodrigueIds[1],
                    },
                    new()
                    {
                        PartnerTableRodrigueTypeId = (int)TableRodrigueType.Categorie,
                        RodrigueId = rodrigueIds[3],
                    },
                    new()
                    {
                        PartnerTableRodrigueTypeId = (int)TableRodrigueType.TypeTarif,
                        RodrigueId = rodrigueIds[4],
                    },
                ]
            };

            _partnerIdsProviderRepository.Insert(structureId, dateIdsProvider);
        }

        private void DeletePassCultureDatesIdsProvider(int structureId, int dateId)
        {
            string dateIdInString = dateId.ToString();

            PartnerIdsProviderEntity? partnerIdsProviderToDelete = _partnerIdsProviderRepository.FindFirstOrDefault(pip => pip.IdProviderValue == dateIdInString
                                                                                                                    && pip.PartnerIdsProviderTypeId == (int)PartnerIdsProviderType.PassCulture_Dates, structureId);
            if (partnerIdsProviderToDelete is not null)
                _partnerIdsProviderRepository.Delete(structureId, partnerIdsProviderToDelete.PartnerIdsProviderId);
        }

        //CRUD preference
        private void SetPassCultureEmailPreferance(int structureId, InfoSuppForm infos)
        {
            string bookingContactKey = "BOOKING_ISSUE_EMAIL";
            StructurePrefsEntity? structurePrefsBookingContactEntity = _structurePrefsRepository.FindFirstOrDefault(s => s.StructureId == structureId && s.PreferenceCle == bookingContactKey, structureId);

            if (structurePrefsBookingContactEntity is null)
            {
                _structurePrefsRepository.Insert(structureId, new()
                {
                    StructureId = structureId,
                    PreferenceCle = bookingContactKey,
                    PreferenceValeur = infos.BookingContact
                });
            }
            else
            {
                if (infos.UpdateBookingContact && !string.IsNullOrWhiteSpace(infos.BookingContact))
                {
                    structurePrefsBookingContactEntity.PreferenceValeur = infos.BookingContact;
                    _structurePrefsRepository.Update(structureId, structurePrefsBookingContactEntity);
                }
            }

            string bookingEmailKey = "BOOKING_NOTIF_EMAIL";
            StructurePrefsEntity? structurePrefsBookingEmailEntity = _structurePrefsRepository.FindFirstOrDefault(s => s.StructureId == structureId && s.PreferenceCle == bookingEmailKey, structureId);

            if (structurePrefsBookingEmailEntity is null)
            {
                _structurePrefsRepository.Insert(structureId, new()
                {
                    StructureId = structureId,
                    PreferenceCle = bookingEmailKey,
                    PreferenceValeur = infos.BookingEmail
                });
            }
            else
            {
                if (infos.UpdateBookingEmail && !string.IsNullOrWhiteSpace(infos.BookingEmail))
                {
                    structurePrefsBookingEmailEntity.PreferenceValeur = infos.BookingEmail;
                    _structurePrefsRepository.Update(structureId, structurePrefsBookingEmailEntity);
                }
            }
        }

        #endregion
    }
}
