﻿using Core.Themis.Libraries.DTO;
using Core.Themis.Libraries.DTO.EventsSessions;
using Core.Themis.Libraries.DTO.LinkAboFermeMaquette;
using Core.Themis.Libraries.DTO.Lookup;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace Core.Themis.Libraries.BLL.Interfaces
{
    public interface IAbonnementManager
    {
        /// <summary>
        /// Récupère les abonnements fermés
        /// </summary>
        /// <param name="structureId"></param>
        /// <returns></returns>
        Task<IEnumerable<FormulaDTO>> GetAbonnementClosed(int structureId);

        /// <summary>
        /// Récupère les id des formules (abonnement fermés) dans le fichier Config.ini
        /// </summary>
        /// <param name="structureId"></param>
        /// <returns></returns>
        List<int> GetFormulasIdInConfigIni(int structureId);
        //Task<List<AbonnementMaquetteFormuleDTO>> GetAbonnementMaquettesAsync(int structureId);
        Task<int> InsertLinkAboFermeMaquetteAsync(int structureId, LinkAboFermeMaquetteDTO linkAboFermeMaquetteDTO);
        Task<List<FormulaDTO>?> GetAbonnementClosedAllAsync(int structureId);
        Task<IEnumerable<SessionDTO>> GetSessionsByFormulesAsync(int structureId, List<FormulaDTO> formules);
        Task<IEnumerable<MaquetteBillet>> GetVisibleWebMaquettesAsync(int structureId);

        /// <summary>
        /// get existing associations link_abo_ferme_maquette for structureId
        /// </summary>
        /// <param name="structureId"></param>
        /// <returns></returns>
        Task<IEnumerable<LinkAboFermeMaquetteDTO>> GetExistingAssociationsAsync(int structureId);

        /// <summary>
        /// delete link_abo_ferme_maquette where id=linkId
        /// </summary>
        /// <param name="structureId"></param>
        /// <param name="linkId"></param>
        /// <returns></returns>
        Task<bool> DeleteLinkAboFermeMaquetteAsync(int structureId, int linkId);
    }
}
