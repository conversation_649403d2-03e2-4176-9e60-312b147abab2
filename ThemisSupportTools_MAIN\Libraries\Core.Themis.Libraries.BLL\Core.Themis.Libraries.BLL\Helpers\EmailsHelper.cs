﻿using Core.Themis.Libraries.Data.DBContext;
using Core.Themis.Libraries.Data.DBContext.Interfaces;
using Core.Themis.Libraries.Data.Entities.WebTracing.DemandPasswordReset;
using Core.Themis.Libraries.Data.Repositories.Open.Language.Intrefaces;
using Core.Themis.Libraries.Data.Repositories.Open.Structure.Interfaces;
using Core.Themis.Libraries.DTO.CustomerArea.Tickets_Session.TicketCard;
using Core.Themis.Libraries.DTO.Identity;
using Core.Themis.Libraries.Utilities.Helpers;
using Core.Themis.Libraries.Utilities.Logging;
using Core.Themis.Libraries.Utilities.smtpMessages;
using Google;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Newtonsoft.Json.Linq;
using PdfSharpCore.Pdf.IO;
using RestSharp;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Runtime.InteropServices;
using System.Security.Cryptography;
using System.Text;
using static Dapper.SqlMapper;

namespace Core.Themis.Libraries.BLL.Helpers
{
    public class EmailsHelper
    {
        //private readonly IDbContext _dbContext;
        private static readonly IConfiguration _configuration = ConfigurationHelper.config;
        private static readonly RodrigueNLogger _logger = new();
        private static IDbContext _dbContext;


        private readonly string _pathForSqlScript;

        public static void Configure(IDbContext dbContext)
        {
            _dbContext = dbContext;
           

        }


        //public EmailsHelper(IDbContext dbContext, IConfiguration config)
        //{
        //    _dbContext = dbContext;

        //}

        private static string GeneratePartnerSignature(string toHash, string saltPartner)
        {

            // log.Debug("--- start GeneratePartnerSignature --- " + toHash + " " + saltPartner);
            string signature;
            //string toHash = request.Method + " " + request.RequestUri;

            byte[] secretBytes = Encoding.UTF8.GetBytes(saltPartner);
            byte[] valueBytes = Encoding.UTF8.GetBytes(toHash);


            using (HMACSHA256 hmac = new HMACSHA256(secretBytes))
            {
                byte[] hash = hmac.ComputeHash(valueBytes);
                signature = Convert.ToBase64String(hash);
            }
            return signature;
        }


        /// <summary>
        /// call controller SendFullOrderToCustomer,  retourne le contenu de la view
        /// </summary>
        /// <param name="structureId"></param>
        /// <param name="langCode"></param>
        /// <param name="expediteur"></param>
        /// <param name="recipt"></param>
        /// <returns></returns>
        private static string templatingSendOrderFromRodrigue(
                      int structureId, string langCode, int orderId, int paramId, IdentityDTO recipt)
        {

            string widgetCustomerUrl = _configuration["WidgetCustomerUrl"];

            string url = @$"{widgetCustomerUrl}SendFullOrderToCustomer/{structureId}/{langCode}/{recipt.IdentiteId}/{orderId}/{paramId}";

            var client = new RestClient(url);

            
            //string requestUrl = $"{urlWidgetCustomer}EditionPartialViewForEmailConfirmation/{structureId}/{orderId}/{langCode}";
            string apiPartenaireSalt = _configuration["PartnerRodSK"];
            string signature = GeneratePartnerSignature("/$GET", apiPartenaireSalt);




            var request = new RestRequest(url, Method.Get);
            request.AddHeader("Signature", signature);
            request.AddHeader("Authorization", "pouet");
            request.AddHeader("Content-Type", "application/x-www-form-urlencoded");
            request.AddQueryParameter("partnerName", "RODRIGUE");

            //request.AddBody(classModel);
            var re = client.Execute(request);
            if (re.IsSuccessful)
            {
                return re.Content;
            }
            else
            {
                return "";
            }
        }


        /// <summary>
        /// envoi d'une commande
        /// </summary>
        /// <param name="structureId"></param>
        /// <param name="langCode"></param>
        /// <param name="smtpClientIp"></param>
        /// <param name="ExpediteurAdress"></param>
        /// <param name="ExpediteurName"></param>
        /// <param name="expediteur"></param>
        /// <param name="recipt"></param>
        /// <param name="SeatIInfo"></param>
        /// <param name="attachmentPath"></param>
        /// <returns></returns>
        public static bool SendEmail_sendOrderToCustomer(
            int structureId,
            string langCode,
            string smtpClientIp,
            string ExpediteurAdress,
            string ExpediteurName,
            int OrderId,
            IdentityDTO recipt, TicketInfo SeatIInfo, int paramId, string attachmentPath)
        {
            try
            {

                EmailConfiguration config = new EmailConfiguration()
                {
                    SmtpServer = smtpClientIp,
                    From = ExpediteurAdress,
                    FromName = ExpediteurName,
                };

                //"PathEmails": "\\\\*************\\emails\\TEST\\[structureId]\\envoisok\\",

                config.EmailSaveCompletePath = _configuration["PathEmails"].Replace("[structureId]", structureId.ToString("0000"))
                    + $"rod.{recipt.IdentiteId}.{DateTime.Now.Ticks}.eml";

                config.AttachmentsPath.Add(attachmentPath);

                EmailSender emS = new EmailSender(config);

                List<string> listTo = new()
                    {
                        recipt.Email
                    };

                //IdentityDTO identityDTO = new IdentityDTO();

                var viewResult = templatingSendOrderFromRodrigue(structureId, langCode, OrderId, paramId, recipt);

                _logger.Info(structureId, $"SendEmail_sendOrderToCustomer viewResult.Length={viewResult.Length}");

                if (!string.IsNullOrEmpty(viewResult))
                {

                    string subject = GetSubject(viewResult);

                    Message msg = new Message(listTo, subject, viewResult);

                    emS.SendEmailAsync(msg);

                    return true;
                }
                else
                {
                    Exception ex = new Exception("content is empty !"); 
                    throw ex;
                }
            }
            catch (Exception ex)
            {
                _logger.Error(structureId, ex, $"SendEmail_sendOrderToCustomer error {ex.Message} {ex.StackTrace}");
                throw ex;
            }


        }


        /// <summary>
        /// call controller sendonetickettocustomer, ticket envoyé depuis l'espace client, d'une identité au beneficiaire du ticket, retourne le contenu de la view
        /// </summary>
        /// <param name="structureId"></param>
        /// <param name="langCode"></param>
        /// <param name="expediteur"></param>
        /// <param name="recipt"></param>
        /// <param name="SeatIInfo"></param>
        /// <returns></returns>
        private static string templatingSendOneTicketToCustomer(
                      int structureId, string langCode,
            IdentityDTO expediteur, IdentityDTO recipt, TicketInfo SeatIInfo)
        {

            string widgetCustomerUrl = _configuration["WidgetCustomerUrl"];

            string url = @$"{widgetCustomerUrl}sendonetickettocustomer/{structureId}/{langCode}/{expediteur.IdentiteId}/{recipt.IdentiteId}/{SeatIInfo.OrderId}/{SeatIInfo.SessionId}/{SeatIInfo.SeatId}";

            var client = new RestClient(url);

            var request = new RestRequest(url, Method.Get);


            _logger.Trace(structureId, $"templatingSendOneTicketToCustomer url={url}");

            //request.AddBody(classModel);
            var re = client.Execute(request);
            if (re.IsSuccessful)
            {
                return re.Content;
            }
            else
            {
                return "";
            }
        }

        /// <summary>
        /// envoi du ticket, d'une identité au beneficiaire du ticket 
        /// </summary>
        /// <param name="structureId"></param>
        /// <param name="langCode"></param>
        /// <param name="smtpClientIp"></param>
        /// <param name="ExpediteurAdress"></param>
        /// <param name="ExpediteurName"></param>
        /// <param name="expediteur"></param>
        /// <param name="recipt"></param>
        /// <param name="SeatIInfo"></param>
        /// <param name="attachmentPath"></param>
        /// <returns></returns>
        public static bool SendEmail_sendOnePfdToCustomer(
            int structureId,
            string langCode,
            string smtpClientIp,
            string ExpediteurAdress,
            string ExpediteurName,
            IdentityDTO expediteur, IdentityDTO recipt, TicketInfo SeatIInfo, string attachmentPath)
        {
            EmailConfiguration config = new EmailConfiguration()
            {
                SmtpServer = smtpClientIp,
                From = ExpediteurAdress,
                FromName = ExpediteurName,
            };

            //"PathEmails": "\\\\*************\\emails\\TEST\\[structureId]\\envoisok\\",

            config.EmailSaveCompletePath = _configuration["PathEmails"].Replace("[structureId]", structureId.ToString("0000"))
                + $"ca.oneT.{expediteur.IdentiteId}.{DateTime.Now.Ticks}.eml";

            config.AttachmentsPath.Add(attachmentPath);

            EmailSender emS = new EmailSender(config);

            List<string> listTo = new()
                    {
                        recipt.Email
                    };


            var viewResult = templatingSendOneTicketToCustomer(structureId, langCode, expediteur, recipt, SeatIInfo);

            string subject = GetSubject(viewResult);

            Message msg = new Message(listTo, subject, viewResult);

            emS.SendEmailAsync(msg);

            return true;
        }




        /// <summary>
        /// call controller sendonetickettocustomer, ticket envoyé depuis l'espace client, d'une identité au beneficiaire du ticket, retourne le contenu de la view
        /// </summary>
        /// <param name="structureId"></param>
        /// <param name="langCode"></param>
        /// <param name="expediteur"></param>
        /// <param name="recipt"></param>
        /// <returns></returns>
        private static string templatingSendResetPasswordToCustomer(
                      int structureId, string langCode, IdentityDTO recipt)
        {

            string widgetCustomerUrl = _configuration["WidgetCustomerUrl"];

            string url = @$"{widgetCustomerUrl}sendresetpasswordtocustomer/{structureId}/{langCode}/{recipt.IdentiteId}";

            var client = new RestClient(url);

            var request = new RestRequest(url, Method.Get);

            //request.AddBody(classModel);
            var re = client.Execute(request);
            if (re.IsSuccessful)
            {
                return re.Content;
            }
            else
            {
                return "";
            }
        }



        /// <summary>
        /// envoi du lien de reset password 
        /// </summary>
        /// <param name="structureId"></param>
        /// <param name="langCode"></param>
        /// <param name="smtpClientIp"></param>
        /// <param name="ExpediteurAdress"></param>
        /// <param name="ExpediteurName"></param>
        /// <param name="expediteur"></param>
        /// <param name="recipt"></param>
        /// <param name="attachmentPath"></param>
        /// <returns></returns>
        public static bool SendEmail_sendPassWordResetLink(
            int structureId,
            string langCode,
            string smtpClientIp,
            string ExpediteurAdress,
            string ExpediteurName,
            IdentityDTO recipt)
        {
            EmailConfiguration config = new EmailConfiguration()
            {
                SmtpServer = smtpClientIp,
                From = ExpediteurAdress,
                FromName = ExpediteurName,
            };

            //"PathEmails": "\\\\*************\\emails\\TEST\\[structureId]\\envoisok\\",

            config.EmailSaveCompletePath = _configuration["PathEmailsPassword"].Replace("[structureId]", structureId.ToString("0000"))
                + $"passwordReset.{recipt.IdentiteId}.{DateTime.Now.Ticks}.eml";

           
            EmailSender emS = new EmailSender(config);

            List<string> listTo = new()
                    {
                        recipt.Email
                    };
            //string Subject = "Réinitialisation de mot de passe";
            //string Body = $"Cliquez sur le lien suivant pour réinitialiser votre mot de passe : {resetLink}";

            var viewResult = templatingSendResetPasswordToCustomer(structureId, langCode, recipt);
            //var viewResult = $"<html><title>{Subject}</title>" +
            //    "<body>" +
            //    $"{Body}" +
            //    "</body>" +
            //    "</html>";

            string subject = GetSubject(viewResult);

            Message msg = new Message(listTo, subject, viewResult);

            emS.SendEmailAsync(msg);

            return true;
        }


        /// <summary>
        /// extrait la chaine entre <title></title>
        /// </summary>
        /// <param name="content"></param>
        /// <returns></returns>
        private static string GetSubject(string content)
        {
            string subject = "";
            string baliseTitleStart = "<TITLE>";
            string baliseTitleEnd = "</TITLE>";
            if (content.ToUpper().Contains(baliseTitleStart) && content.ToUpper().Contains(baliseTitleEnd))
            {
                int posStart = content.ToUpper().IndexOf(baliseTitleStart) + baliseTitleStart.Length;
                int posEnd = content.ToUpper().IndexOf(baliseTitleEnd);

                if (posEnd > posStart)
                    subject = content.Substring(posStart, posEnd - posStart);
            }
            return subject;
        }
    }
}
