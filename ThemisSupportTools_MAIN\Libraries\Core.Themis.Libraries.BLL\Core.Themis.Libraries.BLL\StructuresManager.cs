﻿using Core.Themis.Libraries.BLL.Interfaces;
using Core.Themis.Libraries.Data.Entities.Open.EventsSessions;
using Core.Themis.Libraries.Data.Entities.Open.Tarif;
using Core.Themis.Libraries.Data.Repositories.Open.Structure.Interfaces;
using Core.Themis.Libraries.Data.Repositories.Open.Tarif.Interfaces;
using Core.Themis.Libraries.DTO;
using Core.Themis.Libraries.DTO.Lookup;
using Core.Themis.Libraries.Utilities.Helpers;
using Core.Themis.Libraries.Utilities.ThemisSql;
using System.Collections.Generic;
using System.Data;
using Microsoft.Data.SqlClient;
using System.Linq;
using System.Threading.Tasks;

namespace Core.Themis.Libraries.BLL
{
    public class StructuresManager : IStructuresManager
    {
        private readonly IStructureRepository _structureRepository;
        private readonly ITypeTarifRepository _typeTarifRepository;

        public StructuresManager(IStructureRepository structureRepository, ITypeTarifRepository typeTarifRepository)
        {
            _structureRepository = structureRepository;
            _typeTarifRepository = typeTarifRepository;
        }

        /// <summary>
        /// dictionnaire colonnes / valeur pour utilisation dans les maquettes
        /// </summary>
        public static DictionaryExtented GetDictionnaryForPdf(SqlConnection cnxOpen, string scriptPathSqlCommons, int structureId, string langCode)
        {
            DictionaryExtented dicToReturn = new DictionaryExtented();
            if (cnxOpen.State == ConnectionState.Closed)
                cnxOpen.Open();
            string sql = FilesForSqlRequestsManager.GetScript(structureId, "Structures\\", "open_getStructureInfos", scriptPathSqlCommons);

            using (SqlCommand cmd = new SqlCommand())
            {
                cmd.Connection = cnxOpen;
                cmd.CommandText = sql;
                cmd.Parameters.Add(new SqlParameter("@plangCode", langCode));
                cmd.Parameters.Add(new SqlParameter("@pstructureId", structureId));
                using (SqlDataReader reader = cmd.ExecuteReader())
                {
                    if (reader.HasRows)
                    {
                        while (reader.Read())
                        {
                            //var columns = new List<string>();
                            var columns = Enumerable.Range(0, reader.FieldCount).Select(reader.GetName).ToList();
                            foreach (var col in columns)
                            {
                                dicToReturn.Add(col.ToUpper(), reader[col].ToString());
                            }
                        }
                    }
                }
            }
            return dicToReturn;
        }

        public static StructureDTO GetStructure(SqlConnection cnxOpen, string scriptPathSqlCommons, int structureId)
        {
            try
            {

                StructureDTO structure = new();

                if (cnxOpen.State == ConnectionState.Closed)
                    cnxOpen.Open();

                string sql = FilesForSqlRequestsManager.GetScript(structureId, "Structures\\", "getStructure", scriptPathSqlCommons);

                using (SqlCommand cmd = new SqlCommand())
                {
                    cmd.Connection = cnxOpen;
                    cmd.CommandText = sql;
                    cmd.Parameters.Add(new SqlParameter("@pStructureId", structureId));
                    using (SqlDataReader reader = cmd.ExecuteReader())
                    {
                        if (reader.HasRows)
                        {
                            while (reader.Read())
                            {
                                structure.StructureId = int.Parse(reader["structure_id"].ToString());
                                structure.StructureName = reader["name"].ToString();
                            }
                        }
                    }
                }
                cnxOpen.Close();
                //cnxOpen.Dispose();

                return structure;

            }
            catch (SqlException ex)
            {
                throw ex;
            }
            finally
            {
                if (cnxOpen.State == ConnectionState.Open)
                    cnxOpen.Close();
                //cnxOpen.Dispose();
            }
        }

        public DictionaryExtented GetDictionnaryForPdf(int structureId, string langCode)
        {
            try
            {
                //return null;
                return _structureRepository.GetDictionnaryForPdf(structureId, langCode);
            }
            catch
            {
                throw;
            }
        }

        #region SelectLookup

        public async Task<List<SelectLookup>> SelectLookupTypeTarifAsync(int structureId, bool unmaskedOnly = true)
        {
            var query = _typeTarifRepository.FindCustomEntityBy(t => t.StructureId == structureId, structureId);

            if (unmaskedOnly)
                query.Where(t => t.Masquer == 'N');

            IEnumerable<TypeTarifEntity> typeTarif = await query.ToEntityListAsync()
                                                                .ConfigureAwait(false);

            return typeTarif.Select(t => new SelectLookup()
            {
                Value = t.TypeTarifId.ToString(),
                Libelle = t.TypeTarifNom
            })
            .ToList();
        }

        #endregion
    }
}
