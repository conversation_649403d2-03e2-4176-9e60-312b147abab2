﻿using AutoMapper;
using Core.Themis.Libraries.BLL.Extentions;
using Core.Themis.Libraries.BLL.Helpers;
using Core.Themis.Libraries.BLL.Managers.Catalog.Interfaces;
using Core.Themis.Libraries.BLL.Managers.Interfaces;
using Core.Themis.Libraries.BLL.Services.RedisCache.Interfaces;
using Core.Themis.Libraries.BLL.Translations.Interfaces;
using Core.Themis.Libraries.Data.DBContext;
using Core.Themis.Libraries.Data.DBContext.Interfaces;
using Core.Themis.Libraries.Data.Entities.Open.Abonnement;
using Core.Themis.Libraries.Data.Entities.Open.EventsSessions;
using Core.Themis.Libraries.Data.Entities.Open.GestionPlace;
using Core.Themis.Libraries.Data.Entities.Open.Offers;
using Core.Themis.Libraries.Data.Entities.WebLibrary.Catalog;
using Core.Themis.Libraries.Data.Repositories.Open.Abonnement.Interfaces;
using Core.Themis.Libraries.Data.Repositories.Open.BuyerProfil.Interfaces;
using Core.Themis.Libraries.Data.Repositories.Open.Cinema.Interfaces;
using Core.Themis.Libraries.Data.Repositories.Open.EventsSessions;
using Core.Themis.Libraries.Data.Repositories.Open.EventsSessions.Interfaces;
using Core.Themis.Libraries.Data.Repositories.Open.GestionPlaces.Interfaces;
using Core.Themis.Libraries.Data.Repositories.Open.Places.Interfaces;
using Core.Themis.Libraries.Data.Repositories.Open.Produit.Boutique.Interfaces;
using Core.Themis.Libraries.Data.Repositories.Open.Produit.Interfaces;
using Core.Themis.Libraries.Data.Repositories.Open.Traduction.Interfaces;
using Core.Themis.Libraries.Data.Repositories.WebLibrary.Catalog.Interfaces;
using Core.Themis.Libraries.DTO.Catalog.Event;
using Core.Themis.Libraries.DTO.Catalog.Filter;
using Core.Themis.Libraries.DTO.Catalog.Models;
using Core.Themis.Libraries.DTO.Catalog.Product;
using Core.Themis.Libraries.DTO.Enums;
using Core.Themis.Libraries.DTO.Enums.Catalog;
using Core.Themis.Libraries.DTO.Lookup;
using Core.Themis.Libraries.Utilities.Extensions;
using Core.Themis.Libraries.Utilities.Helpers.Cache;
using Core.Themis.Libraries.Utilities.Helpers.Interfaces;
using Core.Themis.Libraries.Utilities.Logging;
using log4net;
using log4net.Repository.Hierarchy;
using Microsoft.Extensions.Configuration;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Core.Themis.Libraries.BLL.Managers.Catalog
{
    public class CatalogManager : ICatalogManager
    {
        private readonly IConfiguration _configuration;
        private readonly IDbContext _dbContext;
        private readonly IRodrigueConfigIniDictionnary _rodrigueConfigIniDictionnary;
        private readonly IEventRepository _eventRepository;
        private readonly ISessionRepository _sessionRepository;
        private readonly IPlaceRepository _placeRepository;
        private readonly IEventGenreRepository _eventGenreRepository;
        private readonly IEventSousGenreRepository _eventSousGenreRepository;
        private readonly IGPManifestationRepository _gPManifestationRepository;
        private readonly IManifestationInfosRepository _manifestationInfosRepository;
        private readonly ITraductionManifestationInfosRepository _traductionManifestationInfosRepository;
        private readonly IEventGroupRepository _eventGroupRepository;
        private readonly IGestionPlaceRepository _gestionPlaceRepository;
        private readonly ICibleRepository _targetRepository;
        private readonly IMapper _mapper;
        private readonly ILowerPriceEventRepository _lowerPriceEventRepository;
        private readonly IOfferRepository _offerRepository;
        private readonly ICncSeanceInfosRepository _cncSeanceInfosRepository;
        private readonly ILocalisationRepository _localisationRepository;
        private readonly IDisciplineRepository _disciplineRepository;
        private readonly ITraductionRepository _traductionRepository;
        private readonly ITranslateManager _translateManager;
        private readonly ILanguageManager _languageManager;
        private readonly IBuyerProfilRepository _buyerProfilRepository;
        private readonly IOffreProfilAcheteurRepository _offreProfilAcheteurRepository;
        private readonly IManifestationGroupeRepository _manifestationGroupeRepository;
        private readonly ILowerPriceAboRepository _lowerPriceAboRepository;
        private readonly IFormuleAbonnementRepository _formuleAboRepository;
        private readonly IFormuleGroupeRepository _formuleGroupeRepository;
        private readonly ICatalogRepository _catalogRepository;
        private readonly ICatalogDestinationPageRepository _catalogDestinationPageRepository;
        private readonly ICatalogDisplayGroupTypeRepository _catalogDisplayGroupTypeRepository;
        private readonly ICatalogFilterTypeRepository _catalogFilterTypeRepository;
        private readonly ICatalogSortGroupTypeRepository _catalogSortGroupTypeRepository;
        private readonly ICatalogGroupingTypeRepository _catalogGroupingTypeRepository;
        private readonly ICatalogMainMenuTypeRepository _catalogMainMenuTypeRepository;
        private readonly IProduitRepository _produitRepository;
        private readonly IProduitFamilleRepository _produitFamilleRepository;
        private readonly IRedisCacheService _redisCacheService;

        private static readonly RodrigueNLogger _log = new();

        public CatalogManager(
            IEventRepository eventRepository,
            IConfiguration configuration,
            IDbContext dbContext,
            IMapper mapper,
            IRodrigueConfigIniDictionnary rodrigueConfigIniDictionnary,
            ISessionRepository sessionRepository,
            IPlaceRepository placeRepository,
            IEventGenreRepository eventGenreRepository,
            IEventSousGenreRepository eventSousGenreRepository,
            IGPManifestationRepository gPManifestationRepository,
            IManifestationInfosRepository manifestationInfosRepository,
            IEventGroupRepository eventGroupRepository,
            IGestionPlaceRepository gestionPlaceRepository,
            ILowerPriceEventRepository lowerPriceEventRepository,
            IOfferRepository offerRepository,
            ICncSeanceInfosRepository cncSeanceInfosRepository,
            ICibleRepository targetRepository,
            ILocalisationRepository localisationRepository,
            IDisciplineRepository disciplineRepository,
            ITraductionManifestationInfosRepository traductionManifestationInfosRepository,
            ITraductionRepository traductionRepository,
            ITranslateManager translateManager,
            ILanguageManager languageManager,
            IBuyerProfilRepository buyerProfilRepository,
            IOffreProfilAcheteurRepository offreProfilAcheteurRepository,
            IManifestationGroupeRepository manifestationGroupeRepository,
            ILowerPriceAboRepository lowerPriceAboRepository,
            IFormuleAbonnementRepository formuleAboRepository,
            IFormuleGroupeRepository formuleGroupeRepository,
            ICatalogRepository catalogRepository,
            ICatalogDestinationPageRepository catalogDestinationPageRepository,
            ICatalogDisplayGroupTypeRepository catalogDisplayGroupTypeRepository,
            ICatalogSortGroupTypeRepository catalogSortGroupTypeRepository,
            ICatalogFilterTypeRepository catalogFilterTypeRepository,
            ICatalogGroupingTypeRepository catalogGroupingTypeRepository,
            ICatalogMainMenuTypeRepository catalogMainMenuTypeRepository,
            IProduitRepository produitRepository,
            IProduitFamilleRepository produitFamilleRepository,
            IRedisCacheService redisCacheService)
        {
            _eventRepository = eventRepository;
            _configuration = configuration;
            _dbContext = dbContext;
            _mapper = mapper;
            _rodrigueConfigIniDictionnary = rodrigueConfigIniDictionnary;
            _sessionRepository = sessionRepository;
            _placeRepository = placeRepository;
            _eventGenreRepository = eventGenreRepository;
            _eventSousGenreRepository = eventSousGenreRepository;
            _gPManifestationRepository = gPManifestationRepository;
            _manifestationInfosRepository = manifestationInfosRepository;
            _eventGroupRepository = eventGroupRepository;
            _lowerPriceEventRepository = lowerPriceEventRepository;
            _offerRepository = offerRepository;
            _gestionPlaceRepository = gestionPlaceRepository;
            _targetRepository = targetRepository;
            _cncSeanceInfosRepository = cncSeanceInfosRepository;
            _localisationRepository = localisationRepository;
            _disciplineRepository = disciplineRepository;
            _traductionManifestationInfosRepository = traductionManifestationInfosRepository;
            _traductionRepository = traductionRepository;
            _translateManager = translateManager;
            _languageManager = languageManager;
            _buyerProfilRepository = buyerProfilRepository;
            _offreProfilAcheteurRepository = offreProfilAcheteurRepository;
            _manifestationGroupeRepository = manifestationGroupeRepository;
            _lowerPriceAboRepository = lowerPriceAboRepository;
            _formuleAboRepository = formuleAboRepository;
            _formuleGroupeRepository = formuleGroupeRepository;
            _catalogRepository = catalogRepository;
            _catalogDestinationPageRepository = catalogDestinationPageRepository;
            _catalogDisplayGroupTypeRepository = catalogDisplayGroupTypeRepository;
            _catalogFilterTypeRepository = catalogFilterTypeRepository;
            _catalogSortGroupTypeRepository = catalogSortGroupTypeRepository;
            _catalogGroupingTypeRepository = catalogGroupingTypeRepository;
            _catalogMainMenuTypeRepository = catalogMainMenuTypeRepository;
            _produitRepository = produitRepository;
            _produitFamilleRepository = produitFamilleRepository;
            _redisCacheService = redisCacheService;
        }

        public async Task<PaginationLookup<CardEventInfoBase>?> GetEventsCardsInfoPaginationAsync(int structureId, SearchForm filter, int pageNumber = 0, int pageSize = 9)
        {
            var jsonParameter = JsonConvert.SerializeObject(filter);
            string cacheKey = $"{structureId}_Catalog_GetEventsCardsInfoPagination_json={jsonParameter}_pn={pageNumber}_ps={pageSize}";

 
            var cacheResult = await _redisCacheService.GetRecordAsync<PaginationLookup<CardEventInfoBase>>(cacheKey);
     
            if (cacheResult is not null)
                return cacheResult;

            List<ManifestationEntity> manifestationEntities = _eventRepository.GetCatalogEventsByFilter(structureId, filter);

            List<ManifestationEntity> eventToPaginate = manifestationEntities.Skip(pageNumber * pageSize)
                                                                             .Take(pageSize)
                                                                             .ToList();

            List<CardEventInfoBase> cardInfoEvents = await GetAllEventsCardInfoAsync(structureId, filter, eventToPaginate);

            PaginationLookup<CardEventInfoBase> entityPaginationLookup = new(manifestationEntities.Count, cardInfoEvents);

            await _redisCacheService.SetRecordAsync(cacheKey, entityPaginationLookup, GetCatalogCacheDuration());

            return entityPaginationLookup;
        }

        public async Task<PaginationLookup<CardEventInfoBase>?> GetAbosCardsInfoPaginationAsync(int structureId, SearchForm filter, int pageNumber = 0, int pageSize = 9)
        {
            var jsonParameter = JsonConvert.SerializeObject(filter);
            string cacheKey = $"{structureId}_Catalog_GetAbosCardsInfoPagination_json={jsonParameter}_pn={pageNumber}_ps={pageSize}";

            var cacheResult = await _redisCacheService.GetRecordAsync<PaginationLookup<CardEventInfoBase>>(cacheKey);

            if (cacheResult is not null)
                return cacheResult;

            List<FormuleAbonnementEntity> aboEntities = _formuleAboRepository.GetCatalogFormuleAboByFilter(structureId, filter);

            List<FormuleAbonnementEntity> aboToPaginate = aboEntities.Skip(pageNumber * pageSize)
                                                                     .Take(pageSize)
                                                                     .ToList();

            List<CardEventInfoBase> cardInfoAbos = await GetAllAbosCardInfoAsync(structureId, aboToPaginate);

            PaginationLookup<CardEventInfoBase> paginationLookup = new(aboEntities.Count, cardInfoAbos);

            await _redisCacheService.SetRecordAsync(cacheKey, paginationLookup, GetCatalogCacheDuration());

            return paginationLookup;
        }

        public async Task<PaginationLookup<CardProductInfoBase>?> GetProductsCardsInfoPaginationAsync(int structureId, SearchForm filter, int pageNumber = 0, int pageSize = 9)
        {
            var jsonParameter = JsonConvert.SerializeObject(filter);
            string cacheKey = $"{structureId}_Catalog_GetProductsCardsInfoPagination_json={jsonParameter}_pn={pageNumber}_ps={pageSize}";

            var cacheResult = await _redisCacheService.GetRecordAsync<PaginationLookup<CardProductInfoBase>>(cacheKey);

            if (cacheResult is not null)
                return cacheResult;

            List<ProductInfoResultDTO> productResult = (await _produitRepository.GetCatalogProductsByFilterAsync(structureId, filter)) ?? [];

            List<ProductInfoResultDTO> productToPaginate = productResult.Skip(pageNumber * pageSize)
                                                                        .Take(pageSize)
                                                                        .ToList();

            List<CardProductInfoBase> cardInfoProducts = GetAllProductsCardInfo(productToPaginate);

            PaginationLookup <CardProductInfoBase> paginationLookup = new(productResult.Count, cardInfoProducts);

            await _redisCacheService.SetRecordAsync(cacheKey, paginationLookup, GetCatalogCacheDuration());

            return paginationLookup;
        }

        public async Task<List<FilterItem>> GetDynamicFiltersAsync(int structureId, string langCode, int[] mainMenuIds, int[] subMenuIds, EnumFilterItemType mainFilterItemType, EnumFilterItemType? subFilterItemType, int identiteId, int buyerProfilId)
        {
            string cacheKey = $"{structureId}_Catalog_GetDynamicFilters_{langCode}_mm={string.Join("_", mainMenuIds)}_sm={string.Join("_", subMenuIds)}_mft={mainFilterItemType}_sft={subFilterItemType}_id={identiteId}_bp={buyerProfilId}";

            var cacheResult = await _redisCacheService.GetRecordAsync<List<FilterItem>>(cacheKey);

            if (cacheResult != null)
                return cacheResult;

            List<FilterItem> dynamicFilters = [];

            if (subFilterItemType == null || subMenuIds.Length == 0)
            {
                if (mainFilterItemType == EnumFilterItemType.EventType)
                    dynamicFilters.AddRange(await GetEventTypeFiltersAsync(structureId, langCode, mainMenuIds, identiteId, buyerProfilId));

                if(mainFilterItemType == EnumFilterItemType.ProductShopWithParamsType)
                    dynamicFilters.AddRange(await GetProductShopWithParamsTypeFiltersAsync(structureId, mainMenuIds));
            }
            else
            {
                if (mainFilterItemType == EnumFilterItemType.EventType && subFilterItemType == EnumFilterItemType.EventGroup)
                    dynamicFilters.AddRange(await GetEventTypeGroupFiltersAsync(structureId, langCode, mainMenuIds, subMenuIds, identiteId, buyerProfilId));

                if (mainFilterItemType == EnumFilterItemType.EventType && subFilterItemType == EnumFilterItemType.Genre)
                    dynamicFilters.AddRange(await GetEventTypeGenreFiltersAsync(structureId, langCode, mainMenuIds, subMenuIds, identiteId, buyerProfilId));

                if (mainFilterItemType == EnumFilterItemType.EventType && subFilterItemType == EnumFilterItemType.Target)
                    dynamicFilters.AddRange(await GetEventTypeTargetFiltersAsync(structureId, langCode, mainMenuIds, subMenuIds, identiteId, buyerProfilId));

                if (mainFilterItemType == EnumFilterItemType.ProductShopWithParamsType && subFilterItemType == EnumFilterItemType.SubFamily)
                    dynamicFilters.AddRange(await GetProductTypeSubFamilyFiltersAsync(structureId, subMenuIds));
            }

            await _redisCacheService.SetRecordAsync(cacheKey, dynamicFilters, GetCatalogCacheDuration());

            return dynamicFilters;
        }

        public async Task InsertLowerPrice(int structureId, List<int> offersId, List<int> manifestationsId, bool deleteAllRows = false)
        {
            try
            {
                if (await _lowerPriceEventRepository.CheckExistTableAsync(structureId))
                {
                    if (deleteAllRows)
                        await _lowerPriceEventRepository.DeleteAllAsync(structureId);


                    if (offersId.Count == 0)
                        _lowerPriceEventRepository.InsertLowerPriceListManif(structureId, manifestationsId);
                    else
                        _lowerPriceEventRepository.InsertLowerPriceList(structureId, manifestationsId, offersId);
                }
            }
            catch
            {
                throw;
            }

        }

        public async Task InsertLowestPricesAboAsync(int structureId, List<int> formulasIds, bool deleteAllRows = false)
        {

            try
            {
                if (await _lowerPriceAboRepository.CheckExistTableAsync(structureId))
                {
                    if (deleteAllRows)
                        await _lowerPriceAboRepository.DeleteAllAsync(structureId);

                    _lowerPriceAboRepository.InsertLowerPriceListAbo(structureId, formulasIds);
                }
            }
            catch (Exception)
            {

                throw;
            }
        }

        #region CRUD

        public async Task<OfferCatalogDTO> GetOfferCatalogForDestinationPageAsync(int structureId, EnumDestinationPageType destinationPageType, string langCode, int buyerProfilId)
        {
            var destinationPage = _catalogDestinationPageRepository.FindFirstOrDefault(d => d.StructureId == structureId && d.DestinationPageTypeId == (int)destinationPageType);

            if (destinationPage is null)
            {
                destinationPage = _catalogDestinationPageRepository.FindFirstOrDefault(d => d.StructureId == 0 && d.DestinationPageTypeId == (int)destinationPageType)
                    ?? throw new NullReferenceException($"Default catalog for page destination '{destinationPageType}' is null");
            }

            return await GetOfferCatalogById(structureId, destinationPage.CatalogId, langCode, buyerProfilId);
        }

        public async Task<OfferCatalogDTO> GetOfferCatalogById(int structureId, int catalogId, string langCode, int buyerProfilId)
        {
            string cacheKey = $"{structureId}_Catalog_GetOfferCatalogById_{catalogId}_{langCode}_{buyerProfilId}";

            var cacheResult = await _redisCacheService.GetRecordAsync<OfferCatalogDTO>(cacheKey);

            if (cacheResult is not null)
                return cacheResult;

            _log.Trace(structureId, $"GetOfferCatalogById {structureId}, {catalogId}, {langCode}, {buyerProfilId} load...");


            var catalogEntity = await GetCatalogEntityByIdWithDependancyAsync(catalogId);

            _log.Trace(structureId, $"GetOfferCatalogById {structureId}, {catalogId}, {langCode}, {buyerProfilId} load...");



            var result = _mapper.Map<OfferCatalogDTO>(catalogEntity);

            foreach (OfferCatalogAreaDTO area in result.OfferAreas)
            {
                if (area.SearchForm.MenuFilterItems.Any(m => m.ItemType == EnumFilterItemType.Abo))
                {
                    int[] formulasIdIntoConfigIni = _rodrigueConfigIniDictionnary.GetFormulasId(structureId);

                    if (formulasIdIntoConfigIni.Length == 0)
                        area.SearchForm.MenuFilterItems.RemoveAll(m => m.ItemType == EnumFilterItemType.Abo);
                }

                if (area.SearchForm.MenuFilterItems.Any(m => m.ItemType == EnumFilterItemType.EventType))
                {
                    var eventsTypesOnSales = await _eventGroupRepository.GetEventsTypeOnSaleAsync(structureId)
                                                                        .ConfigureAwait(false);

                    area.SearchForm.MenuFilterItems.RemoveAll(m => !eventsTypesOnSales.Contains(m.Id) && m.Id != -1 && m.ItemType == EnumFilterItemType.EventType);

                    foreach (var menufilterItem in area.SearchForm.MenuFilterItems)
                    {
                        int[] eventTypeIds;

                        if (menufilterItem.Id == -1)
                            eventTypeIds = [.. area.SearchForm.MenuFilterItems.Where(m => m.Id != -1).Select(m => m.Id)];
                        else
                            eventTypeIds = [menufilterItem.Id];

                        if (string.IsNullOrWhiteSpace(menufilterItem.Name))
                            menufilterItem.Name = await GetMainMenuTranslationAsync(structureId, langCode, menufilterItem);

                        if (menufilterItem.SubFiltersItemType.HasValue)
                            await SetSubMenuItemsAsync(structureId, menufilterItem, langCode, eventTypeIds);
                    }
                }

                if (area.SearchForm.MenuFilterItems.Any(m => m.ItemType == EnumFilterItemType.ProductType || m.ItemType == EnumFilterItemType.ProductShopType || m.ItemType == EnumFilterItemType.ProductShopWithParamsType))
                {
                    foreach (var menufilterItem in area.SearchForm.MenuFilterItems)
                    {
                        int[] productTypeIds;

                        if (menufilterItem.Id == -1)
                            productTypeIds = [.. area.SearchForm.MenuFilterItems.Where(m => m.Id != -1).Select(m => m.Id)];
                        else
                            productTypeIds = [menufilterItem.Id];

                        if (string.IsNullOrWhiteSpace(menufilterItem.Name))
                            menufilterItem.Name = await GetMainMenuTranslationAsync(structureId, langCode, menufilterItem);

                        if (menufilterItem.SubFiltersItemType.HasValue)
                            await SetSubMenuItemsAsync(structureId, menufilterItem, langCode, productTypeIds);
                    }
                }

                if (area.SearchForm.MenuFilterItems.Count(m => m.Id != -1) == 1)
                    area.SearchForm.MenuFilterItems.RemoveAll(m => m.Id == -1);

                bool isLowerPriceTableExist = await _lowerPriceEventRepository.CheckExistTableAsync(structureId);

                if (!isLowerPriceTableExist)
                    area.SortByTypes.RemoveAll(s => s.SortType == EnumSortBy.PriceAsc || s.SortType == EnumSortBy.PriceDesc);

                area.SearchForm.InitCheckedMenuItems();
            }

            if (catalogEntity is not null)
                await _redisCacheService.SetRecordAsync(cacheKey, result, GetCatalogCacheDuration());

            _log.Trace(structureId, $"GetOfferCatalogById {structureId}, {catalogId}, {langCode}, {buyerProfilId} ok");

            return result;
        }

        public async Task<CatalogDTO> GetCatalogSettingsByIdAsync(int catalogId, int structureId, string langCode)
        {
            string cacheKey = $"{structureId}_Catalog_GetCatalogSettingsById_{catalogId}_{langCode}";

            var cacheResult = _redisCacheService.GetRecord<CatalogDTO>(cacheKey);

            if (cacheResult is not null)
                return cacheResult;

            var catalogEntity = await GetCatalogEntityByIdWithDependancyAsync(catalogId);

            var result = _mapper.Map<CatalogDTO>(catalogEntity);

            result.SetAvailableDisplaySetting(GetAllDisplaySettingsAvailable());

            foreach (var area in result.Areas)
            {
                area.SetAvailableFilter(GetAllFiltersAvailable());
                area.SetAvailableSortBy(GetAllSortByAvailable());
                area.SetAvailableGrouping(GetAllGroupingsAvailable());

                foreach(var mainMenu in area.MainMenu)
                {
                    if (string.IsNullOrWhiteSpace(mainMenu.Name))
                        mainMenu.Name = await GetMainMenuTranslationForAdminAsync(structureId, langCode, mainMenu);
                }
            }

            if (catalogEntity is not null)
                _redisCacheService.SetRecord(cacheKey, result, GetCatalogCacheDuration());             

            return result;
        }

        public async Task<CatalogDTO> GetNewCatalogSettingsAsync(int structureId, string langCode, EnumDestinationPageType destinationPage, int? modelCatalogId = null)
        {
            if (string.IsNullOrWhiteSpace(langCode))
                throw new ArgumentNullException(langCode);

            if (modelCatalogId.HasValue)
            {
                CatalogDTO catalog = new(await GetCatalogSettingsByIdAsync(modelCatalogId.Value, structureId, langCode))
                {
                    DestinationPageType = destinationPage
                };

                return catalog;
            }

            return GetEmptyCatalog(structureId, langCode, destinationPage);
        }

        public AreaDTO GetNewCatalogArea(int structureId, string langCode)
        {
            AreaDTO area = new() 
            {
                Filters = GetAllFiltersAvailable(),
                SortsBy = GetAllSortByAvailable(),
                GroupingsBy = GetAllGroupingsAvailable(),
            };

            area.AddMainMenuItem(new()
            {
                IdLinked = -1,
                Name = DicoHelper.GetTranslationByKeyword(structureId, "Widget_AdminCatalog_LblAll", langCode),
                MainMenuType = EnumMainMenuType.EventType,
                IsVisible = false,
                IsDisabled = true,
            });

            return area;
        }

        public List<MainMenuTypeDTO> GetAllMainMenuType()
        {
            var mainTypes = _catalogMainMenuTypeRepository.GetAllCustomEntities()
                                                          .Include(m => m.SubMenuTypes)
                                                          .ToEntityList();

            return _mapper.Map<List<MainMenuTypeDTO>>(mainTypes);
        }

        public EnumMainMenuType[] GetAllParentMainMenuTypes()
        {
            return _catalogMainMenuTypeRepository
                        .GetAll()?
                        .Where(m => m.ParentTypeId == null)
                        .Select(m => (EnumMainMenuType)m.MainMenuTypeId)
                        .ToArray() ?? [];
        }

        public async Task<List<SubMenuItemDTO>> GetSubMenuItemsAsync(int structureId, string langCode, EnumSubMenuType subMenuType, int[] linkedIds)
        {
            List<KeyValuePair<int, string>> items = subMenuType switch
            {
                EnumSubMenuType.EventGroup => await _eventGroupRepository.GetAllEventsGroupsByEventTypeIdAsync(structureId, langCode, linkedIds).ConfigureAwait(false),
                EnumSubMenuType.Genre => await _eventGenreRepository.GetAllGenresByEventTypeIdAsync(structureId, langCode, linkedIds).ConfigureAwait(false),
                EnumSubMenuType.Target => await _targetRepository.GetAllTargetsByEventTypeIdAsync(structureId, langCode, linkedIds).ConfigureAwait(false),
                EnumSubMenuType.SubFamily => await _produitRepository.GetAllSubFamilyByProductFamilyIdAsync(structureId, langCode, linkedIds).ConfigureAwait(false),
                _ => [],
            };

            var submenu = items.Select(i => new SubMenuItemDTO()
            {
                IdLinked = i.Key,
                Name = i.Value,
                IsVisible = true,
                SubMenuItemType = subMenuType
            }).ToList();

            if (submenu.Count > 1)
            {
                submenu.Insert(0, new SubMenuItemDTO()
                {
                    IdLinked = -1,
                    Name = DicoHelper.GetTranslationByKeyword(structureId, "Widget_AdminCatalog_LblAll", langCode),
                    IsVisible = true,
                    SubMenuItemType = subMenuType
                });
            }

            return submenu;
        }

        private async Task<List<FilterItem>> GetSubMenuFilterItemsByStructureIdAndEventTypeAsync(int structureId, string langCode, EnumFilterItemType subMenuType, int[] linkedIds)
        {
            List<KeyValuePair<int, string>> items = subMenuType switch
            {
                EnumFilterItemType.EventGroup => await _eventGroupRepository.GetEventsGroupsOnSaleByEventTypeIdAsync(structureId, langCode, linkedIds),
                EnumFilterItemType.Genre => await _eventGenreRepository.GetGenresOnSaleByEventTypeIdAsync(structureId, langCode, linkedIds),
                EnumFilterItemType.Target => await _targetRepository.GetTargetsOnSaleByEventTypeIdAsync(structureId, langCode, linkedIds),
                EnumFilterItemType.SubFamily => await _produitRepository.GetAllSubFamilyByProductFamilyIdAsync(structureId, langCode, linkedIds),
                _ => [],
            };

            var submenu = items.Select(i => new FilterItem()
            {
                Id = i.Key,
                Name = i.Value,
                ItemType = subMenuType
            }).ToList();

            if (submenu.Count > 1)
            {
                submenu.Insert(0, new FilterItem()
                {
                    Id = -1,
                    Name = DicoHelper.GetTranslationByKeyword(structureId, "Widget_Catalog_LblAll", langCode),
                    ItemType = subMenuType
                });
            }

            return submenu;
        }

        public List<EnumSubMenuType> GetAllSubMenuTypeByMainMenuType(EnumMainMenuType mainMenuType)
        {
            var mainType = _catalogMainMenuTypeRepository.GetCustomEntityById((int)mainMenuType)
                                                         .Include(m => m.SubMenuTypes)
                                                         .ToEntity();

            return mainType?.SubMenuTypes?.Select(s => (EnumSubMenuType)s.SubMenuTypeId).ToList() ?? new();
        }

        public List<CatalogDTO> GetAllCatalogsByStructureId(int structureId)
        {
            string cacheKey = $"{structureId}_Catalog_GetAllCatalogsByStructureId";

            var cacheResult = _redisCacheService.GetRecord<List<CatalogDTO>>(cacheKey);

            if (cacheResult is not null)
                return cacheResult;

            var catalogEntities = _catalogRepository.FindCustomEntityBy(c => c.StructureId == structureId)
                                                    .Include(c => c.DestinationPage)
                                                    .ToEntityList();

            var result = _mapper.Map<List<CatalogDTO>>(catalogEntities);

            if (catalogEntities is not null && catalogEntities.Any())
                _redisCacheService.SetRecord(cacheKey, result, GetCatalogCacheDuration());

            return result;
        }

        public async Task<string[]> GetProductColorChoicesAvalaibleAsync(int structureId)
        {
            return await _produitRepository.GetAllProductColorsAvailableAsync(structureId);
        }

        public async Task<string[]> GetProductSizeChoicesAvalaibleAsync(int structureId)
        {
            return await _produitRepository.GetAllProductSizesAvailableAsync(structureId);
        }

        public int AddNewCatalog(CatalogDTO catalog)
        {
            CatalogEntity entity = _mapper.Map<CatalogEntity>(catalog);

            using var dbContextTransactionScope = _dbContext.BeginTransaction(ContextType.WebLibrary);

            try
            {
                int newId = _catalogRepository.Insert(entity);

                if (newId == 0)
                    throw new Exception($"Echec to insert catalog");

                SetDestinationPage(entity.StructureId, newId, catalog.DestinationPageType, entity.DestinationPage);

                dbContextTransactionScope.Commit();

                //_redisCacheService.Cache.RemoveAsync($"GetAllCatalogsByStructureId_{catalog.StructureId}");

                _redisCacheService.RemoveKeysContainPartOfKey($"{catalog.StructureId}_Catalog");

                return newId;
            }
            catch
            {
                dbContextTransactionScope.Rollback();
                throw;
            }
        }

        public bool UpdateCatalog(CatalogDTO catalog)
        {
            CatalogEntity entity = _mapper.Map<CatalogEntity>(catalog);

            using var dbContextTransactionScope = _dbContext.BeginTransaction(ContextType.WebLibrary);

            try
            {
                bool isUpdated = _catalogRepository.Update(entity);

                if (!isUpdated)
                    throw new Exception($"Echec to update catalog with id: {catalog.CatalogId}");

                SetDestinationPage(entity.StructureId, entity.CatalogId, catalog.DestinationPageType, entity.DestinationPage);

                dbContextTransactionScope.Commit();

                _redisCacheService.RemoveKeysContainPartOfKey($"{catalog.StructureId}_Catalog");
 
                /*_redisCacheService.RemoveKeysContainPartOfKey($"GetCatalogSettingsById_{catalog.StructureId}_{catalog.CatalogId}");
                _redisCacheService.RemoveKeysContainPartOfKey($"GetOfferCatalogById_{catalog.StructureId}_{catalog.CatalogId}");
                _redisCacheService.Cache.RemoveAsync($"GetAllCatalogsByStructureId_{catalog.StructureId}");*/

                return isUpdated;
            }
            catch
            {
                dbContextTransactionScope.Rollback();
                throw;
            }
        }

        public bool DeleteCatalog(CatalogDTO catalog)
        {
            using var dbContextTransactionScope = _dbContext.BeginTransaction(ContextType.WebLibrary);

            try
            {
                bool isDeleted = _catalogRepository.Delete(catalog.CatalogId);

                if (!isDeleted)
                    throw new Exception($"Echec to delete catalog with id: {catalog.CatalogId}");

                dbContextTransactionScope.Commit();

                /*_redisCacheService.RemoveKeysContainPartOfKey($"GetCatalogSettingsById_{catalog.StructureId}_{catalog.CatalogId}");
                _redisCacheService.Cache.RemoveAsync($"GetAllCatalogsByStructureId_{catalog.StructureId}");*/

                _redisCacheService.RemoveKeysContainPartOfKey($"{catalog.StructureId}_Catalog");

                return isDeleted;
            }
            catch
            {
                dbContextTransactionScope.Rollback();
                throw;
            }
        }

        public int AddDestinationPage(DestinationPageDTO destinationPage)
        {
            CatalogDestinationPageEntity entity = _mapper.Map<CatalogDestinationPageEntity>(destinationPage);

            using var dbContextTransactionScope = _dbContext.BeginTransaction(ContextType.WebLibrary);

            try
            {
                int newId = _catalogDestinationPageRepository.Insert(entity);

                if (newId == 0)
                    throw new Exception($"Echec to insert destination page");

                dbContextTransactionScope.Commit();

                //_redisCacheService.Cache.RemoveAsync($"GetAllCatalogsByStructureId_{destinationPage.StructureId}");

                _redisCacheService.RemoveKeysContainPartOfKey($"{destinationPage.StructureId}_Catalog");

                return newId;
            }
            catch
            {
                dbContextTransactionScope.Rollback();
                throw;
            }
        }

        public bool UpdateDestinationPage(DestinationPageDTO destinationPage)
        {
            CatalogDestinationPageEntity entity = _mapper.Map<CatalogDestinationPageEntity>(destinationPage);

            using var dbContextTransactionScope = _dbContext.BeginTransaction(ContextType.WebLibrary);

            try
            {
                bool isUpdated = _catalogDestinationPageRepository.Update(entity);

                if (!isUpdated)
                    throw new Exception($"Echec to update destination page with id: {destinationPage.DestinationPageId}");

                dbContextTransactionScope.Commit();

                //_redisCacheService.Cache.RemoveAsync($"GetAllCatalogsByStructureId_{destinationPage.StructureId}");

                _redisCacheService.RemoveKeysContainPartOfKey($"{destinationPage.StructureId}_Catalog");

                return isUpdated;
            }
            catch
            {
                dbContextTransactionScope.Rollback();
                throw;
            }
        }

        public bool DeleteDestinationPage(DestinationPageDTO destinationPage)
        {
            using var dbContextTransactionScope = _dbContext.BeginTransaction(ContextType.WebLibrary);

            try
            {
                bool idDeleted = _catalogDestinationPageRepository.Delete(destinationPage.DestinationPageId);

                if (!idDeleted)
                    throw new Exception($"Echec to delete destination page");

                dbContextTransactionScope.Commit();

                //_redisCacheService.Cache.RemoveAsync($"GetAllCatalogsByStructureId_{destinationPage.StructureId}"); 

                _redisCacheService.RemoveKeysContainPartOfKey($"{destinationPage.StructureId}_Catalog");

                return idDeleted;
            }
            catch
            {
                dbContextTransactionScope.Rollback();
                throw;
            }
        }

        #endregion

        #region Méthodes private

        private async Task<List<CardEventInfoBase>> GetAllAbosCardInfoAsync(int structureId, List<FormuleAbonnementEntity> formuleAboEntities)
        {
            var listCardInfo = new List<CardEventInfoBase>();

            if (formuleAboEntities.Count() > 0)
            {
                List<FormuleAbonnementEntity> result = new();

                foreach (var entity in formuleAboEntities)
                    result.Add(SetFormuleGroupesDependancies(structureId, entity));

                List<int> formuleIds = formuleAboEntities.SelectMany(a => a.FormuleGroupes.Select(f => f.FormuleId)).Distinct().ToList();
                await InsertLowestPricesAboAsync(structureId, formuleIds);


                var listAboCardInfo = _mapper.Map<List<AbonnementCardInfo>>(result);
                listCardInfo.AddRange(listAboCardInfo);

                await SetBeginPriceAboAsync(structureId, listAboCardInfo);

            }

            return listCardInfo;
        }

        private async Task<List<CardEventInfoBase>> GetAllEventsCardInfoAsync(int structureId, SearchForm filter, List<ManifestationEntity> manifestationEntities)
        {
            var listEventCardInfo = new List<CardEventInfoBase>();

            var offersEntities = (await _offerRepository.GetOffersByIdentityBuyerProfilAsync(
                structureId,
                filter.IdentiteId,
                filter.BuyerProfilId
            ).ConfigureAwait(false)).ToList();

            SetOffreAndBuyerProfil(structureId, offersEntities);


            if (manifestationEntities.Count > 0)
            {
                List<int> offresIds = offersEntities.Select(o => o.OffreId).ToList();
                List<int> manifestationsIds = manifestationEntities.Select(m => m.ManifestationId).ToList();

                await InsertLowerPrice(structureId, offresIds, manifestationsIds);

                var tasks = manifestationEntities.Select(evt => SetSessionsAndInformationsDependanciesAsync(structureId, filter.LangCode, filter.BuyerProfilId, evt, offersEntities));

                var result = await Task.WhenAll(tasks).ConfigureAwait(false);
            }

            var events = manifestationEntities.Where(m => m.ManifestationGroupe.TypeEvenement == (int)EventType.Event);
            if (events.Any())
            {
                var eventCardInfos = _mapper.Map<List<EventCardInfo>>(events);
                listEventCardInfo.AddRange(eventCardInfos);
            }

            var sport = manifestationEntities.Where(m => m.ManifestationGroupe.TypeEvenement == (int)EventType.Sport);
            if (sport.Any())
            {
                var sportCardInfos = _mapper.Map<List<SportCardInfo>>(sport);
                listEventCardInfo.AddRange(sportCardInfos);
            }

            var cinema = manifestationEntities.Where(m => m.ManifestationGroupe.TypeEvenement == (int)EventType.Cinema);
            if (cinema.Any())
            {
                var cinemaCardInfos = _mapper.Map<List<CinemaCardInfo>>(cinema);
                listEventCardInfo.AddRange(cinemaCardInfos);
            }

            await SetBeginPriceAsync(structureId, listEventCardInfo, offersEntities);

            var trads = listEventCardInfo.SelectMany(e => e.TraductionsForWaitingEvents).ToList();

            _languageManager.SetLangCode(structureId, trads);


            var eventsCardWaiting = listEventCardInfo.Where(ec => ec.IsWaitingEvent).ToList();
            foreach (var item in eventsCardWaiting)
            {
                string? trad = item.GetTraductionByLangCode(filter.LangCode);

                if (trad == null)
                {
                    trad = _translateManager.GetTranslationByKeyWord(structureId, "Widget_Catalog_MsgEventWaitingList", filter.LangCode);
                }

                item.EventWaitingMessage = trad;
            }


            var tradsForEventsLock = listEventCardInfo.SelectMany(e => e.TraductionsForLockEvents).ToList();
            _languageManager.SetLangCode(structureId, tradsForEventsLock);

            var eventsCardLocked = listEventCardInfo.Where(ec => ec.IsEventLocked).ToList();
            foreach (var item in eventsCardLocked)
            {
                string? trad = item.GetTraductionByLangCode(filter.LangCode);

                if (trad == null)
                {
                    trad = _translateManager.GetTranslationByKeyWord(structureId, "Widget_Catalog_MsgEventLockedList", filter.LangCode);
                }

                item.EventLockedMessage = trad;
            }


            return listEventCardInfo;
        }

        private List<CardProductInfoBase> GetAllProductsCardInfo(List<ProductInfoResultDTO> productToPaginate)
        {
            List<CardProductInfoBase> cardInfoProducts = [];

            foreach (var product in productToPaginate) 
            {
                if (product.GroupId == (int)EnumProductType.CarteAdherent)
                    cardInfoProducts.Add(_mapper.Map<CardAdhesionInfo>(product));

                else if(product.GroupId == (int)EnumProductType.CarnetJetons)
                    cardInfoProducts.Add(_mapper.Map<CardFeedbookInfo>(product));

                else if(product.GroupId == (int)EnumProductType.Produit && product.UseParams)
                    cardInfoProducts.Add(_mapper.Map<CardShopInfo>(product));

                else
                    cardInfoProducts.Add(_mapper.Map<CardProductInfoBase>(product));
            }

            return cardInfoProducts;
        }

        /// <summary>
        /// /!\ ca filtre sur les séance en ventes !
        /// </summary>
        private async Task<ManifestationEntity> SetSessionsAndInformationsDependanciesAsync(int structureId, string langCode, int buyerProfilId, ManifestationEntity manifestation, List<OfferEntity> offersEntities)
        {
            try
            {
                manifestation.ManifestationInfos = await _manifestationInfosRepository.GetByEventIdAsync(structureId, manifestation.ManifestationId).ConfigureAwait(false);

                bool checkExistTraductionManifInfosTable = await _traductionManifestationInfosRepository.CheckExistTableAsync(structureId).ConfigureAwait(false);
                if (manifestation.ManifestationInfos != null && checkExistTraductionManifInfosTable)
                {
                    if (manifestation.ManifestationInfos.TraductionManifestationInfos == null)
                        manifestation.ManifestationInfos.TraductionManifestationInfos = new List<TraductionManifestationInfosEntity>();

                    var traductionManifestationInfos = await _traductionManifestationInfosRepository.GetTraductionByEventIdAndLangCodeAsync(structureId, manifestation.ManifestationId, langCode);

                    if (traductionManifestationInfos != null)
                        manifestation.ManifestationInfos.TraductionManifestationInfos.Add(traductionManifestationInfos);
                }


                manifestation.Discipline = await _disciplineRepository.GetDisciplineByEventIdAsync(structureId, manifestation.ManifestationId).ConfigureAwait(false);

                if (manifestation.Discipline != null)
                {
                    var traductionsDiscipline = await _disciplineRepository.GetTranslationOfDisciplineByIdAndLangCodeAsync(structureId, manifestation.Discipline.Id, langCode)
                                              .ConfigureAwait(false);

                    if (manifestation.Discipline.TraductionsDiscipline == null)
                        manifestation.Discipline.TraductionsDiscipline = new List<TraductionDisciplineEntity>();

                    manifestation.Discipline.TraductionsDiscipline.Add(traductionsDiscipline);

                }

                var sousGenre = await _eventSousGenreRepository.GetByIdAsync(structureId, manifestation.IdGenre, langCode)
                                             .ConfigureAwait(false);

                manifestation.SousGenre = sousGenre;





                var sessionsOnSale = await _sessionRepository.GetAllSessionsByEventIdOnSaleAsync(structureId, manifestation.ManifestationId)
                                                .ConfigureAwait(false);


                foreach (var seance in sessionsOnSale)
                {
                    seance.Lieu = await _placeRepository.GetByIdAsync(structureId, seance.LieuId)
                                                .ConfigureAwait(false);

                    seance.CncSeanceInfos = await _cncSeanceInfosRepository.FindFirstOrDefaultAsync(s => s.SeanceId == seance.SeanceId, structureId).ConfigureAwait(false);

                    seance.Cibles = (await _targetRepository.GetTargetsBySessionIdAsync(structureId, seance.SeanceId)
                                .ConfigureAwait(false)).ToList();

                    seance.Cibles.ToList().ForEach(async o =>
                    {
                        var translationTarget = await _targetRepository.GetTranslationOfTargetByIdAndLangCodeAsync(structureId, o.Id, langCode);
                        if (translationTarget != null && !string.IsNullOrEmpty(translationTarget.Nom))
                            o.Nom = translationTarget.Nom;
                    });

                    seance.Localisations = (await _localisationRepository.GetLocalisationsBySessionIdAsync(structureId, seance.SeanceId).ConfigureAwait(false)).ToList();

                    seance.Localisations.ToList().ForEach(async o =>
                    {
                        var translationLocalisation = await _localisationRepository.GetTranslationOfLocalisationByIdAndLangCodeAsync(structureId, o.Id, langCode);
                        if (translationLocalisation != null && !string.IsNullOrEmpty(translationLocalisation.Nom))
                            o.Nom = translationLocalisation.Nom;
                    });

                    if (manifestation.ManifestationGroupeId > 0)
                    {
                        manifestation.ManifestationGroupe = await _manifestationGroupeRepository.GetByIdAsync(structureId, manifestation.ManifestationGroupeId).ConfigureAwait(false);
                    }

                    List<int> offersId = offersEntities.Select(o => o.OffreId).ToList();

                    List<GestionPlaceEntity> gestionPlaces = new List<GestionPlaceEntity>();
                    //Aucun profil acheteur
                    if (buyerProfilId == 0)
                    {
                        //Prends les règles de ventes Tout public et celles des offres
                        var gestionPlacesToutPublics = (await _gestionPlaceRepository.GetAllByEventIdAndSessionIdAsync(structureId, seance.ManifestationId, seance.SeanceId).ConfigureAwait(false)).ToList();
                        gestionPlacesToutPublics = gestionPlacesToutPublics.Where(gp => !gp.IsContrainteIdentite).ToList();
                        gestionPlaces.AddRange(gestionPlacesToutPublics);

                        var gestionPlacesAdhesions = (await _gestionPlaceRepository.GetAllAdhesionsByEventIdAndSessionIdAsync(structureId, seance.ManifestationId, seance.SeanceId).ConfigureAwait(false)).ToList();
                        gestionPlacesAdhesions = gestionPlacesAdhesions.Where(gp => gp.IsContrainteIdentite).ToList();
                        gestionPlaces.AddRange(gestionPlacesAdhesions);

                        //A t-on des offfres (identite)
                        if (offersEntities.Count > 0)
                        {
                            var gestionPlacesOffres = (await _gestionPlaceRepository.GetAllByEventIdAndSessionIdAndOffersIdAsync(structureId, seance.ManifestationId, seance.SeanceId, offersId).ConfigureAwait(false)).ToList();
                            gestionPlaces.AddRange(gestionPlacesOffres);
                        }

                    }
                    else
                    {
                        //Get le profil acheteur
                        var buyerProfil = await _buyerProfilRepository.GetByIdAsync(structureId, buyerProfilId).ConfigureAwait(false);

                        if (buyerProfil is not null && buyerProfil.AddGeneralsRules.HasValue)
                        {
                            //Liste des offres de ce profil acheteur
                            var offersOfThisBuyerProfil = offersEntities.SelectMany(o => o.OffreProfilAcheteurs.Where(opa => opa.ProfilAcheteurId == buyerProfilId)).Select(o => o.OffreId).ToList();


                            //Profil acheteur a la checkebox (Ajouter les règles de vente tout public) coché
                            if (buyerProfil.AddGeneralsRules == true || offersOfThisBuyerProfil.Count == 0)
                            {
                                var gestionPlacesToutPublics = (await _gestionPlaceRepository.GetAllByEventIdAndSessionIdAsync(structureId, seance.ManifestationId, seance.SeanceId).ConfigureAwait(false)).ToList();
                                gestionPlacesToutPublics = gestionPlacesToutPublics.Where(gp => !gp.IsContrainteIdentite).ToList();
                                gestionPlaces.AddRange(gestionPlacesToutPublics);

                                var gestionPlacesAdhesions = (await _gestionPlaceRepository.GetAllAdhesionsByEventIdAndSessionIdAsync(structureId, seance.ManifestationId, seance.SeanceId).ConfigureAwait(false)).ToList();
                                gestionPlacesAdhesions = gestionPlacesAdhesions.Where(gp => gp.IsContrainteIdentite).ToList();
                                gestionPlaces.AddRange(gestionPlacesAdhesions);
                            }

                            if (offersOfThisBuyerProfil.Count > 0)
                                gestionPlaces.AddRange((await _gestionPlaceRepository.GetAllByEventIdAndSessionIdAndOffersIdAsync(structureId, seance.ManifestationId, seance.SeanceId, offersOfThisBuyerProfil).ConfigureAwait(false)).ToList());
                        }
                        else
                        {
                            //Prends les règles de ventes Tout public et celles des offres
                            gestionPlaces = (await _gestionPlaceRepository.GetAllByEventIdAndSessionIdAsync(structureId, seance.ManifestationId, seance.SeanceId).ConfigureAwait(false)).ToList();
                            gestionPlaces = gestionPlaces.Where(gp => !gp.IsContrainteIdentite).ToList();
                        }
                    }

                    seance.GestionPlaces = gestionPlaces;
                }

                manifestation.Seances = sessionsOnSale.ToList();

                manifestation.GPManifestation = await _gPManifestationRepository.GetByIdAsync(structureId, manifestation.ManifestationId).ConfigureAwait(false);

                if (manifestation.GPManifestation.GpManifestationListeAttenteManifCommentId != null)
                {
                    var traduction = await _traductionRepository.GetByIdAsync(structureId, manifestation.GPManifestation.GpManifestationListeAttenteManifCommentId.Value)
                                                            .ConfigureAwait(false);

                    manifestation.GPManifestation.TraductionGpManifestationsForWaiting = traduction;

                }

                if (manifestation.GPManifestation.GpManifestationLockManifCommentId != null)
                {
                    var traduction = await _traductionRepository.GetByIdAsync(structureId, manifestation.GPManifestation.GpManifestationLockManifCommentId.Value)
                                                            .ConfigureAwait(false);

                    manifestation.GPManifestation.TraductionGpManifestationsForLock = traduction;

                }

                return manifestation;
            }
            catch
            {

                throw;
            }


        }

        private FormuleAbonnementEntity SetFormuleGroupesDependancies(int structureId, FormuleAbonnementEntity formuleAbonnementEntity)
        {
            var formuleGroupes = _formuleGroupeRepository.FindCustomEntityBy(fg => fg.FormuleId == formuleAbonnementEntity.FormAbonId, structureId)
                .Include(fg => fg.AbonnementManifestations)
                .Include(fg => fg.AbonnementManifestations.Select(am => am.Manifestation))
                .Include(fg => fg.AbonnementManifestations.Select(am => am.Manifestation.Seances))
                .Include(fg => fg.AbonnementManifestations.Select(am => am.Manifestation.Seances.Select(s => s.Lieu)))
            .ToEntityList();

            formuleAbonnementEntity.FormuleGroupes = formuleGroupes;

            return formuleAbonnementEntity;
        }

        private async Task SetBeginPriceAsync(int structureId, List<CardEventInfoBase> cardInfo, List<OfferEntity> offerEntities)
        {
            bool checkExistTable = await _lowerPriceEventRepository.CheckExistTableAsync(structureId).ConfigureAwait(false);
            if (checkExistTable)
            {
                List<LowerPriceEventEntity> listLowerPriceEvent = (await _lowerPriceEventRepository.GetAllAsync(structureId).ConfigureAwait(false)).ToList();
                cardInfo.ForEach(m =>
                {
                    m.BeginPrice = _lowerPriceEventRepository.GetLowerPriceByEventIdAndOffres(listLowerPriceEvent, m.EventId, offerEntities.Select(o => o.OffreId).ToList());
                });
            }
        }

        private async Task SetBeginPriceAboAsync(int structureId, List<AbonnementCardInfo> cardInfo)
        {
            bool checkExistTable = await _lowerPriceAboRepository.CheckExistTableAsync(structureId).ConfigureAwait(false);
            if (checkExistTable)
            {
                List<LowerPriceAboEntity> listLowerPriceEvent = (await _lowerPriceAboRepository.GetAllAsync(structureId).ConfigureAwait(false)).ToList();
                cardInfo.ForEach(a =>
                {
                    a.BeginPrice = _lowerPriceAboRepository.GetLowerPriceByAboId(listLowerPriceEvent, a.FormuleId);
                });
            }
        }

        private void SetOffreAndBuyerProfil(int structureId, List<OfferEntity> offers)
        {

            if (offers.Count() > 0)
            {
                foreach (var offer in offers)
                {
                    var offreProfilacheteurs = _offreProfilAcheteurRepository.FindCustomEntityBy(pa => pa.OffreId == offer.OffreId, structureId).ToEntityList();
                    offer.OffreProfilAcheteurs = offreProfilacheteurs;
                }

            }

        }

        private void SetDestinationPage(int structureId, int catalogId, EnumDestinationPageType newPageDestination, CatalogDestinationPageEntity? entity)
        {
            if (newPageDestination == EnumDestinationPageType.None && entity is not null)
            {
                bool isDeleted = _catalogDestinationPageRepository.Delete(entity.DestinationPageId);

                if (!isDeleted)
                    throw new Exception($"Echec to delete destination with id: {entity.DestinationPageId}");
            }


            if (newPageDestination != EnumDestinationPageType.None)
            {
                var structureDestinations = _catalogDestinationPageRepository.FindBy(d => d.StructureId == structureId) ?? [];

                var destinationEntity = structureDestinations.FirstOrDefault(d => d.DestinationPageTypeId == (int)newPageDestination || d.CatalogId == catalogId);

                if (destinationEntity is not null)
                {
                    destinationEntity.CatalogId = catalogId;
                    destinationEntity.DestinationPageTypeId = (int)newPageDestination;

                    bool isUpdatedDestination = _catalogDestinationPageRepository.Update(destinationEntity);

                    if (!isUpdatedDestination)
                        throw new Exception($"Echec to update destination with id: {destinationEntity.DestinationPageId}");
                }
                else
                {
                    destinationEntity = new()
                    {
                        CatalogId = catalogId,
                        DestinationPageTypeId = (int)newPageDestination,
                        StructureId = structureId
                    };

                    bool isInserted = _catalogDestinationPageRepository.InsertWithoutIdReturn(destinationEntity);

                    if (!isInserted)
                        throw new Exception($"Echec to insert destination");
                }
            }
        }

        private CatalogDTO GetEmptyCatalog(int structureId, string langCode, EnumDestinationPageType destinationPageType)
        {
            return new CatalogDTO()
            {
                DisplaySettings = GetAllDisplaySettingsAvailable(),
                DestinationPageType = destinationPageType,
                Areas =
                [
                    GetNewCatalogArea(structureId, langCode),
                ]
            };
        }

        private List<DisplaySettingsDTO> GetAllDisplaySettingsAvailable()
        {
            var displayGroupEntities = _catalogDisplayGroupTypeRepository.GetAllCustomEntities()
                                                                         .Include(d => d.DisplayElementTypes)
                                                                         .ToEntityList();

            return _mapper.Map<List<DisplaySettingsDTO>>(displayGroupEntities);
        }

        private List<FilterDTO> GetAllFiltersAvailable()
        {
            var filterTypesEntity = _catalogFilterTypeRepository.GetAllCustomEntities()
                                                                .Include(g => g.MainMenuTypes)
                                                                .ToEntityList();

            return _mapper.Map<List<FilterDTO>>(filterTypesEntity);
        }

        private List<GroupingDTO> GetAllGroupingsAvailable()
        {
            var groupingTypesEntity = _catalogGroupingTypeRepository.GetAllCustomEntities()
                                                                    .Include(g => g.MainMenuTypes)
                                                                    .ToEntityList();

            return _mapper.Map<List<GroupingDTO>>(groupingTypesEntity);
        }

        private List<SortBySettingsDTO> GetAllSortByAvailable()
        {
            var sortGroupTypeEntities = _catalogSortGroupTypeRepository.GetAllCustomEntities()
                                                                        .Include(g => g.SortByTypes)
                                                                        .Include(g => g.MainMenuTypes)
                                                                        .ToEntityList();

            return _mapper.Map<List<SortBySettingsDTO>>(sortGroupTypeEntities);
        }

        private async Task<string?> GetMainMenuTranslationForAdminAsync(int structureId, string langCode, MainMenuItemDTO mainMenuItem)
        {
            if (mainMenuItem.IdLinked == -1)
                return DicoHelper.GetTranslationByKeyword(structureId, "Widget_AdminCatalog_LblAll", langCode);

            return mainMenuItem.MainMenuType switch
            {
                EnumMainMenuType.EventType => DicoHelper.GetTranslationByKeyword(structureId, $"Widget_AdminCatalog_Lbl{(EventType)mainMenuItem.IdLinked}", langCode),
                EnumMainMenuType.AboType => DicoHelper.GetTranslationByKeyword(structureId, "Widget_AdminCatalog_LblSubscription", langCode),
                EnumMainMenuType.ProductType => DicoHelper.GetTranslationByKeyword(structureId, $"Widget_AdminCatalog_Lbl{(EnumProductType)mainMenuItem.IdLinked}", langCode),
                EnumMainMenuType.ProductShopType => await GetProductShopTranslationAsync(structureId, mainMenuItem.IdLinked, langCode),
                EnumMainMenuType.ProductShopWithParamsType => await GetProductShopWithParamsTranslationAsync(structureId, mainMenuItem.IdLinked, langCode),
                _ => "",
            };
        }

        private async Task<string?> GetMainMenuTranslationAsync(int structureId, string langCode, FilterItem mainMenuFilterItem)
        {
            if (mainMenuFilterItem.Id == -1)
                return DicoHelper.GetTranslationByKeyword(structureId, "Widget_Catalog_LblAll", langCode);

            return mainMenuFilterItem.ItemType switch
            {
                EnumFilterItemType.EventType => DicoHelper.GetTranslationByKeyword(structureId, $"Widget_Catalog_Lbl{(EventType)mainMenuFilterItem.Id}", langCode),
                EnumFilterItemType.Abo => DicoHelper.GetTranslationByKeyword(structureId, "Widget_Catalog_LblSubscription", langCode),
                EnumFilterItemType.ProductType => DicoHelper.GetTranslationByKeyword(structureId, $"Widget_Catalog_Lbl{(EnumProductType)mainMenuFilterItem.Id}", langCode),
                EnumFilterItemType.ProductShopType => await GetProductShopTranslationAsync(structureId, mainMenuFilterItem.Id, langCode),
                EnumFilterItemType.ProductShopWithParamsType => await GetProductShopWithParamsTranslationAsync(structureId, mainMenuFilterItem.Id, langCode),
                _ => "",
            };
        }

        private async Task<string> GetProductShopTranslationAsync(int structureId, int idProduit, string langCode)
        {
            var products = await _produitFamilleRepository.GetAllProductFamilyKeyValuePairAsync(structureId, false, langCode);

            return products.FirstOrDefault(p => p.Key == idProduit).Value;
        }

        private async Task<string> GetProductShopWithParamsTranslationAsync(int structureId, int idProduit, string langCode)
        {
            var products = await _produitFamilleRepository.GetAllProductFamilyKeyValuePairAsync(structureId, true, langCode);

            return products.FirstOrDefault(p => p.Key == idProduit).Value;
        }

        private async Task<CatalogEntity?> GetCatalogEntityByIdWithDependancyAsync(int catalogId)
        {
            var query = _catalogRepository.GetCustomEntityById(catalogId);

            Task[] includes1 =
            [
                query.IncludeAsync(c => c.DestinationPage),
                query.IncludeAsync(c => c.DisplaySettings),
                query.IncludeAsync(c => c.Areas)
            ];

            await Task.WhenAll(includes1).ConfigureAwait(false);

            Task[] includes2 =
            [
                query.IncludeAsync(c => c.DisplaySettings.Select(d => d.SettingsElements)),
                query.IncludeAsync(c => c.Areas.Select(a => a.MainMenu)),
                query.IncludeAsync(c => c.Areas.Select(a => a.SortSettings)),
                query.IncludeAsync(c => c.Areas.Select(a => a.Filters)),
                query.IncludeAsync(c => c.Areas.Select(a => a.Groupings))
            ];

            await Task.WhenAll(includes2).ConfigureAwait(false);

            Task[] includes3 =
            [
                query.IncludeAsync(c => c.DisplaySettings.Select(d => d.SettingsElements.Select(elt => elt.DisplayElementType))),
                query.IncludeAsync(c => c.Areas.Select(a => a.MainMenu.Select(m => m.SubMenu))),
                query.IncludeAsync(c => c.Areas.Select(a => a.SortSettings.Select(s => s.SortGroupType))),
                query.IncludeAsync(c => c.Areas.Select(a => a.Filters.Select(f => f.FilterType))),
                query.IncludeAsync(c => c.Areas.Select(a => a.Groupings.Select(f => f.GroupingType)))
            ];

            await Task.WhenAll(includes3).ConfigureAwait(false);

            Task[] includes4 =
            [
                query.IncludeAsync(c => c.Areas.Select(a => a.SortSettings.Select(s => s.SortGroupType.SortByTypes))),
                query.IncludeAsync(c => c.Areas.Select(a => a.SortSettings.Select(s => s.SortGroupType.MainMenuTypes))),
                query.IncludeAsync(c => c.Areas.Select(a => a.Filters.Select(f => f.FilterType.MainMenuTypes))),
                query.IncludeAsync(c => c.Areas.Select(a => a.Groupings.Select(g => g.GroupingType.MainMenuTypes)))
            ];

            await Task.WhenAll(includes4).ConfigureAwait(false);

            return query.ToEntity();
        }

        private async Task SetSubMenuItemsAsync(int structureId, FilterItem menufilterItem, string langCode, int[] linkedIds)
        {
            var submenu = (await GetSubMenuFilterItemsByStructureIdAndEventTypeAsync(structureId, langCode, menufilterItem.SubFiltersItemType!.Value, linkedIds)
                                                        .ConfigureAwait(false)).Copy() ?? [];

            if (menufilterItem.SubFilterItems is not null && menufilterItem.SubFilterItems.Count > 0)
                menufilterItem.SubFilterItems.ForEach(m => m.Name = submenu.FirstOrDefault(s => s.Id == m.Id)?.Name);
            else
                menufilterItem.SubFilterItems = submenu;
        }

        // Get Dynamic filter

        private async Task<List<FilterItem>> GetEventTypeFiltersAsync(int structureId, string langCode, int[] mainMenuIds, int identiteId, int buyerProfilId)
        {
            List<FilterItem> dynamicFilters = [];


            ////////// obtenir en 1er les manifs auquelles on a droit plutot que les recalculer en permanence

            // get groupes

            dynamicFilters.AddRange(await _eventGroupRepository.GetFilterItemsEventsGroupsOnSaleByEventsTypesIdAsync(structureId, langCode, mainMenuIds, identiteId, buyerProfilId) ?? []);

            // get genres
            dynamicFilters.AddRange(await _eventGenreRepository.GetFilterItemsGenresOnSaleByEventsTypesIdAsync(structureId, langCode, mainMenuIds, identiteId, buyerProfilId) ?? []);

            // get sous genres
            dynamicFilters.AddRange(await _eventSousGenreRepository.GetFilterItemsSubGenresOnSaleByEventsTypesIdAsync(structureId, langCode, mainMenuIds, identiteId, buyerProfilId) ?? []);

            // get cibles
            dynamicFilters.AddRange(await _targetRepository.GetFilterItemsTargetsOnSaleByEventsTypesIdAsync(structureId, langCode, mainMenuIds, identiteId, buyerProfilId) ?? []);

            return dynamicFilters;
        }

        private async Task<List<FilterItem>> GetProductShopWithParamsTypeFiltersAsync(int structureId, int[] mainMenuIds)
        {
            List<FilterItem> dynamicFilters = [];

            var colors = await _produitRepository.GetProductColorsAvailableByProductFamilyIdsAsync(structureId, mainMenuIds);

            dynamicFilters.AddRange(colors.Select(c => new FilterItem()
            {
                Name = c,
                ItemType = EnumFilterItemType.Color
            }));

            var sizes = await _produitRepository.GetProductSizesAvailableByProductFamilyIdsAsync(structureId, mainMenuIds);

            dynamicFilters.AddRange(sizes.Select(s => new FilterItem()
            {
                Name = s,
                ItemType = EnumFilterItemType.Size
            }));

            return dynamicFilters;
        }
        
        private async Task<List<FilterItem>> GetEventTypeGroupFiltersAsync(int structureId, string langCode, int[] mainMenuIds, int[] subMenuIds, int identiteId, int buyerProfilId)
        {
            List<FilterItem> dynamicFilters = [];

            dynamicFilters.AddRange(await _eventGenreRepository.GetFilterItemsGenresOnSaleByEventsTypesIdAndEventsGroupsIdAsync(structureId, langCode, mainMenuIds, subMenuIds, identiteId, buyerProfilId) ?? []);

            dynamicFilters.AddRange(await _eventSousGenreRepository.GetFilterItemsSubGenresOnSaleByEventsTypesIdAndEventsGroupsIdAsync(structureId, langCode, mainMenuIds, subMenuIds, identiteId, buyerProfilId) ?? []);

            dynamicFilters.AddRange(await _targetRepository.GetFilterItemsTargetsOnSaleByEventsTypesIdAndEventsGroupsIdAsync(structureId, langCode, mainMenuIds, subMenuIds, identiteId, buyerProfilId) ?? []);

            return dynamicFilters;
        }
                
        private async Task<List<FilterItem>> GetEventTypeGenreFiltersAsync(int structureId, string langCode, int[] mainMenuIds, int[] subMenuIds, int identiteId, int buyerProfilId)
        {
            List<FilterItem> dynamicFilters = [];

            dynamicFilters.AddRange(await _eventGroupRepository.GetFilterItemsEventsGroupsOnSaleByEventsTypesIdAndGenresIdAsync(structureId, langCode, mainMenuIds, subMenuIds, identiteId, buyerProfilId) ?? []);

            dynamicFilters.AddRange(await _eventSousGenreRepository.GetFilterItemsSubGenresOnSaleByEventsTypesIdAndGenresIdAsync(structureId, langCode, mainMenuIds, subMenuIds, identiteId, buyerProfilId) ?? []);

            dynamicFilters.AddRange(await _targetRepository.GetFilterItemsTargetsOnSaleByEventsTypesIdAndGenresIdAsync(structureId, langCode, mainMenuIds, subMenuIds, identiteId, buyerProfilId) ?? []);

            return dynamicFilters;
        }
                        
        private async Task<List<FilterItem>> GetEventTypeTargetFiltersAsync(int structureId, string langCode, int[] mainMenuIds, int[] subMenuIds, int identiteId, int buyerProfilId)
        {
            List<FilterItem> dynamicFilters = [];

            dynamicFilters.AddRange(await _eventGroupRepository.GetFilterItemsEventsGroupsOnSaleByEventsTypesIdAndTargetsIdAsync(structureId, langCode, mainMenuIds, subMenuIds, identiteId, buyerProfilId) ?? []);

            dynamicFilters.AddRange(await _eventGenreRepository.GetFilterItemsGenresOnSaleByEventsTypesIdAndTargetsIdAsync(structureId, langCode, mainMenuIds, subMenuIds, identiteId, buyerProfilId) ?? []);

            dynamicFilters.AddRange(await _eventSousGenreRepository.GetFilterItemsSubGenresOnSaleByEventsTypesIdAndTargetsIdAsync(structureId, langCode, mainMenuIds, subMenuIds, identiteId, buyerProfilId) ?? []);

            return dynamicFilters;
        }
                                
        private async Task<List<FilterItem>> GetProductTypeSubFamilyFiltersAsync(int structureId, int[] subMenuIds)
        {
            List<FilterItem> dynamicFilters = [];

            var colors = await _produitRepository.GetProductColorsAvailableByProductSubFamilyIdsAsync(structureId, subMenuIds);

            dynamicFilters.AddRange(colors.Select(c => new FilterItem()
            {
                Name = c,
                ItemType = EnumFilterItemType.Color
            }));

            var sizes = await _produitRepository.GetProductSizesAvailableByProductSubFamilyIdsAsync(structureId, subMenuIds);

            dynamicFilters.AddRange(sizes.Select(s => new FilterItem()
            {
                Name = s,
                ItemType = EnumFilterItemType.Size
            }));

            return dynamicFilters;
        }

        private TimeSpan GetCatalogCacheDuration()
        {
            string? configDuration = _configuration["RedisCacheMinuteDuration:OfferCatalog"];

            if (configDuration is not null && double.TryParse(configDuration, out double configDurationConvert))
                return TimeSpan.FromSeconds(configDurationConvert);

            return TimeSpan.FromMinutes(30);
        }

        #endregion
    }
}
