﻿using AutoMapper;
using Core.Themis.Libraries.BLL.Helpers.Widgets;
using Core.Themis.Libraries.BLL.Interfaces;
using Core.Themis.Libraries.BLL.WT.Interfaces;
using Core.Themis.Libraries.Data.DBContext;
using Core.Themis.Libraries.Data.DBContext.Interfaces;
using Core.Themis.Libraries.Data.Entities.Open.Dossier;
using Core.Themis.Libraries.Data.Entities.Open.EventsSessions;
using Core.Themis.Libraries.Data.Entities.Open.Histo;
using Core.Themis.Libraries.Data.Entities.Open.Order;
using Core.Themis.Libraries.Data.Repositories.Open.BuyerProfil.Interfaces;
using Core.Themis.Libraries.Data.Repositories.Open.EventsSessions.Interfaces;
using Core.Themis.Libraries.Data.Repositories.Open.Histo.Interfaces;
using Core.Themis.Libraries.Data.Repositories.Open.Identities.Interfaces;
using Core.Themis.Libraries.Data.Repositories.Open.Lieu.Interfaces;
using Core.Themis.Libraries.Data.Repositories.Open.Orders.Dossier.Interfaces;
using Core.Themis.Libraries.Data.Repositories.Open.Orders.Entree.Interfaces;
using Core.Themis.Libraries.Data.Repositories.Open.Orders.Interfaces;
using Core.Themis.Libraries.Data.Repositories.Open.Orders.OrderDetails.Interfaces;
using Core.Themis.Libraries.Data.Repositories.Open.Orders.OrderOpinion.Interfaces;
using Core.Themis.Libraries.Data.Repositories.Open.PlaceObjects.Interfaces;
using Core.Themis.Libraries.Data.Repositories.Open.Places.Interfaces;
using Core.Themis.Libraries.Data.Repositories.Open.Produit.Interfaces;
using Core.Themis.Libraries.Data.Repositories.Open.Structure.Interfaces;
using Core.Themis.Libraries.Data.Repositories.Open.Tarif.Interfaces;
using Core.Themis.Libraries.Data.Repositories.Open.Transactionnals.Interfaces;
using Core.Themis.Libraries.Data.Repositories.WebTracing.Interfaces;
using Core.Themis.Libraries.DTO;
using Core.Themis.Libraries.DTO.AccessControl;
using Core.Themis.Libraries.DTO.CustomerArea.Order.Details;
using Core.Themis.Libraries.DTO.CustomerArea.Order.Invoices;
using Core.Themis.Libraries.DTO.CustomerArea.Order.OrderCard;
using Core.Themis.Libraries.DTO.Enums;
using Core.Themis.Libraries.DTO.exposedObjects;
using Core.Themis.Libraries.DTO.Identity;
using Core.Themis.Libraries.DTO.Lookup;
using Core.Themis.Libraries.DTO.Orders;
using Core.Themis.Libraries.DTO.PassCulture;
using Core.Themis.Libraries.DTO.Seats;
using Core.Themis.Libraries.DTO.Transactionnals;
using Core.Themis.Libraries.Utilities.Helpers;
using Core.Themis.Libraries.Utilities.Helpers.Interfaces;
using Core.Themis.Libraries.Utilities.Logging;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.Configuration;
using Parlot.Fluent;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Core.Themis.Libraries.BLL
{
    public class OrderManager : IOrderManager
    {
        private static readonly RodrigueNLogger RodrigueLogger = new();

        private readonly IMapper _mapper;
        private readonly IMemoryCache _memoryCache;
        private readonly IDbContext _dbContext;
        private readonly IConfiguration _config;
        private readonly IRodrigueConfigIniDictionnary _rodrigueConfig;
        private readonly ISeatRepository _seatRepository;
        private readonly IOrderRepository _orderRepository;
        private readonly IBasketRepository _basketRepository;
        private readonly IIdentityRepository _identiteRepository;
        private readonly IBuyerProfilRepository _buyerProfilRepository;
        private readonly ISessionRepository _sessionRepository;
        private readonly IBasketManager _basketManager;
        private readonly IOrderLineRepository _orderLigneRepository;
        private readonly IOrderInfoRepository _orderInfoRepository;
        private readonly ICommandeLigneCompRepository _commandeLigneCompRepository;
        private readonly IDossierRepository _dossierRepository;
        private readonly IDossierSvgRepository _dossierSvgRepository;
        private readonly IDossierProduitRepository _dossierProduitRepository;
        private readonly IEntreeRepository _entreeRepository;
        private readonly IEntreeSvgRepository _entreeSvgRepository;
        private readonly IProduitRepository _produitRepository;
        private readonly IDossierProduitReservationRepository _dossierProduitReservationRepository;
        private readonly IEventRepository _eventRepository;
        private readonly ICategoryRepository _categoryRepository;
        private readonly ITypeTarifRepository _typeTarifRepository;
        private readonly IReferenceLieuPhysiqueRepository _referenceLieuPhysiqueRepository;
        private readonly IZoneRepository _zoneRepository;
        private readonly IFloorRepository _floorRepository;
        private readonly ISectionRepository _sectionRepository;
        private readonly IPlaceRepository _placeRepository;
        private readonly IOpinionOrderRepository _opinionOrderRepository;
        private readonly IProprietesOfManifsRepository _proprietesOfManifsRepository;
        private readonly IProprietesReferencesOfManifsRepository _proprietesReferencesOfManifsRepository;
        private readonly IHistoDossierRepository _histoDossierRepository;
        private readonly IProduitStockRepository _produitStockRepository;
        private readonly IStructureRepository _structureRepository;
        private readonly ITvaRepository _tvaRepository;
        private readonly ICompteClientRepository _compteClientRepository;
        private readonly IRodrigueConfigIniDictionnary _rodrigueConfigIniDictionnary;

        public OrderManager(
            IMapper mapper,
            IMemoryCache memoryCache,
            IDbContext dbContext,
            IConfiguration config,
            IRodrigueConfigIniDictionnary rodrigueConfig,
            ISeatRepository seatRepository,
            IOrderRepository orderRepository,
            IBuyerProfilRepository buyerProfilRepository,
            ISessionRepository sessionRepository,
            IBasketRepository basketRepository,
            IIdentityRepository identityRepository,
            IBasketManager basketManager,
            IOrderLineRepository orderLineRepository,
            IOrderInfoRepository orderInfoRepository,
            ICommandeLigneCompRepository commandeLigneCompRepository,
            IDossierRepository dossierRepository,
            IDossierSvgRepository dossierSvgRepository,
            IDossierProduitRepository dossierProduitRepository,
            IEntreeRepository entreeRepository,
            IEntreeSvgRepository entreeSvgRepository,
            IProduitRepository produitRepository,
            IDossierProduitReservationRepository dossierProduitReservationRepository,
            IEventRepository eventRepository,
            ICategoryRepository categoryRepository,
            ITypeTarifRepository typeTarifRepository,
            IReferenceLieuPhysiqueRepository referenceLieuPhysiqueRepository,
            IZoneRepository zoneRepository,
            IFloorRepository floorRepository,
            ISectionRepository sectionRepository,
            IPlaceRepository placeRepository,
            IOpinionOrderRepository opinionOrderRepository,
            IProprietesOfManifsRepository proprietesOfManifsRepository,
            IProprietesReferencesOfManifsRepository proprietesReferencesOfManifsRepository,
            IHistoDossierRepository histoDossierRepository,
            IProduitStockRepository produitStockRepository,
            IStructureRepository structureRepository,
            ITvaRepository tvaRepository,
            ICompteClientRepository compteClientRepository,
            IRodrigueConfigIniDictionnary rodrigueConfigIniDictionnary)
        {
            _mapper = mapper;
            _memoryCache = memoryCache;
            _dbContext = dbContext;
            _config = config;
            _rodrigueConfig = rodrigueConfig;
            _seatRepository = seatRepository;
            _orderRepository = orderRepository;
            _buyerProfilRepository = buyerProfilRepository;
            _sessionRepository = sessionRepository;
            _basketRepository = basketRepository;
            _identiteRepository = identityRepository;
            _basketManager = basketManager;
            _orderLigneRepository = orderLineRepository;
            _orderInfoRepository = orderInfoRepository;
            _commandeLigneCompRepository = commandeLigneCompRepository;
            _dossierRepository = dossierRepository;
            _dossierSvgRepository = dossierSvgRepository;
            _dossierProduitRepository = dossierProduitRepository;
            _entreeRepository = entreeRepository;
            _entreeSvgRepository = entreeSvgRepository;
            _produitRepository = produitRepository;
            _dossierProduitReservationRepository = dossierProduitReservationRepository;
            _eventRepository = eventRepository;
            _categoryRepository = categoryRepository;
            _typeTarifRepository = typeTarifRepository;
            _referenceLieuPhysiqueRepository = referenceLieuPhysiqueRepository;
            _zoneRepository = zoneRepository;
            _floorRepository = floorRepository;
            _sectionRepository = sectionRepository;
            _placeRepository = placeRepository;
            _opinionOrderRepository = opinionOrderRepository;
            _proprietesOfManifsRepository = proprietesOfManifsRepository;
            _proprietesReferencesOfManifsRepository = proprietesReferencesOfManifsRepository;
            _histoDossierRepository = histoDossierRepository;
            _produitStockRepository = produitStockRepository;
            _structureRepository = structureRepository;
            _tvaRepository = tvaRepository;
            _compteClientRepository = compteClientRepository;
            _rodrigueConfigIniDictionnary = rodrigueConfigIniDictionnary;
        }


        public bool Cancel(int structureId, int orderId)
        {
            try
            {

                var orderE = _orderRepository.Load(structureId, orderId);
                if (orderE == null)
                {
                    throw new Exception($"can't retrieve order {orderId}");
                }
                // check commande lignes 
                List<int> listSeancesId = orderE.listAllSeats.Select(l => l.SessionId).Distinct().ToList();
                if (orderE.listAllSeats.Count > 0)
                {
                    var listSess = _sessionRepository.LoadE(structureId, listSeancesId);

                    List<SeanceEntity> ListSessionPbs = listSess.Where(s => s.SeanceCloturer == "O" || s.SeanceMasquer == "O" || s.SeanceVerrouiller == 'O').ToList();
                    if (ListSessionPbs.Count > 0)
                    {
                        throw new Exception($"cannot do cancellation on sessions {string.Join(",", ListSessionPbs.Select(l => l.SeanceId).Distinct().ToList())}");
                    }
                }
                List<char> listStatesSeats = orderE.listAllSeats.Select(s => s.State).Distinct().ToList();
                if (listStatesSeats.Count > 1)
                {
                    // j'ai des places en differents etat dans cette commande ! annulation impossible !
                    throw new Exception($"cannot do cancellation when seats are in different states ({string.Join(",", listStatesSeats.ToList())})");
                }
                if (listStatesSeats.Count > 0)
                {
                    if (listStatesSeats[0] != 'B' && listStatesSeats[0] != 'P' && listStatesSeats[0] != 'R')
                    {
                        throw new Exception($"cannot do cancellation when seats are in state ({listStatesSeats[0]})");
                    }
                }

                var DicoConfigIni = _rodrigueConfig.GetDictionaryFromCache(structureId);

                int operatorId = 0;
                operatorId = OperatorManager.GetOperateurPaiement(DicoConfigIni);

                int posteId = 0;
                if (DicoConfigIni.ContainsKey("PAIEMENTWEBPOSTID") && DicoConfigIni["PAIEMENTWEBPOSTID"] != null && DicoConfigIni["PAIEMENTWEBPOSTID"] != "")
                {
                    posteId = int.Parse(DicoConfigIni["PAIEMENTWEBPOSTID"].ToString());
                }


                _orderRepository.AnnulationEtMiseEnAcompte(structureId, orderId, operatorId, posteId);


                return true; // _orderRepository.Cancel(structureId, orderId, operatorId);
            }
            catch (Exception ex)
            {
                throw ex;
            }
        }

        /// <summary>
        /// lecture de la commande
        /// </summary>
        /// <param name="structureId"></param>
        /// <param name="langCode"></param>
        /// <param name="orderId"></param>
        /// <param name="identityId"></param>
        /// <returns></returns>
        [Obsolete("Use LoadAsync instead")]
        public OrderDTO Load(int structureId, string langCode, int orderId, int identityId)
        {
            return _orderRepository.Load(structureId, orderId, identityId);
        }


        /// <summary>
        /// lecture de la commande
        /// </summary>
        /// <param name="structureId"></param>
        /// <param name="langCode"></param>
        /// <param name="orderId"></param>
        /// <param name="identityId"></param>
        /// <returns></returns>
        public Task<OrderDTO> LoadAsync(int structureId, string langCode, int orderId, int identityId)
        {
            return _orderRepository.LoadAsync(structureId, orderId, identityId);
        }


        public OrderDTO GetById(int structureId, int orderId)
        {
            var cmdEnt = _orderRepository.GetById(structureId, orderId);


            return _mapper.Map<OrderDTO>(cmdEnt);
        }

        public List<OrderDTO> GetAllByIdentityId(int structureId, int identityId)
        {
            var listCmdEnt = _orderRepository.GetOrdersByIdentiteId(structureId, identityId);

            return _mapper.Map<List<OrderDTO>>(listCmdEnt);
        }

        /// <summary>
        /// GetViaExterne an order by id order
        /// </summary>
        /// <param name="structureId"></param>
        /// <param name="orderId"></param>
        /// <returns></returns>
        public OrderDTO? GetOrderByIdForInsurance(int structureId, int orderId)
        {
            try
            {
                var entity = _orderRepository.GetOrderByIdForInsurance(structureId, orderId);
                return _mapper.Map<OrderDTO>(entity);
            }
            catch
            {
                throw;
            }
        }

        public OrderDTO CreateOrderFromPanier(int structureId, BasketDTO bask, int idOperateur, int idFiliereCreationCompteAnonyme,
            int idFiliereAbo, int idFiliereVU, int idFiliereReAbo, int idFiliereProduits, bool identiteAnonyme, int idIdentite, string dossierClientNom)
        {
            try
            {
                return _orderRepository.CreateOrderFromPanier(structureId, bask, idOperateur, idFiliereCreationCompteAnonyme, idFiliereAbo, idFiliereVU, idFiliereReAbo, idFiliereProduits, identiteAnonyme, idIdentite, dossierClientNom);
            }
            catch
            {
                throw;
            }
        }

        /// <summary>
        /// insert recette
        /// </summary>
        /// <param name="structureId"></param>
        /// <param name="idOperateur"></param>
        /// <param name="cmdToPay"></param>
        /// <returns></returns>
        public OrderDTO? DoEdition(int structureId, int idOperateur, OrderDTO cmdToPay, List<BarCodeDTO> listBarCodeToOverWrite = null)
        {
            try
            {
                return _orderRepository.DoEtapeEdition(structureId, idOperateur, cmdToPay, listBarCodeToOverWrite);
            }
            catch
            {
                throw;
            }
        }

        /// <summary>
        /// Payement commande : methode principale
        /// </summary>
        /// <param name="structureId"></param>
        /// <param name="bask"></param>
        /// <param name="idOperateur"></param>
        /// <param name="idFiliere"></param>
        /// <param name="idIdentitePayeur"></param>
        /// <param name="cmdToPay"></param>
        /// <param name="listModePaiement"></param>
        /// <param name="commandeLignesToPay"></param>
        /// <param name="transactionId"></param>
        /// <param name="web_PosteName"></param>
        /// <param name="web_PosteId"></param>
        /// <returns></returns>
        public OrderDTO Pay(int structureId, BasketDTO bask, int idOperateur, int idFiliere, int idIdentitePayeur, OrderDTO cmdToPay, List<PaymentMethodDTO> listModePaiement, List<int> commandeLignesToPay, string transactionId, string web_PosteName, int web_PosteId)
        {
            try
            {
                return _orderRepository.Pay(structureId, bask, idOperateur, idIdentitePayeur, cmdToPay, listModePaiement, commandeLignesToPay, transactionId, web_PosteName, web_PosteId);
            }
            catch
            {
                throw;
            }
        }

        private string GetDossierClientNom(CreateOrderDemand orderDemand)
        {
            string dossierClientNom = "";
            if (orderDemand != null && orderDemand.Consumer != null)
            {
                dossierClientNom = ((string.IsNullOrEmpty(orderDemand.Consumer.CustomerLastName) ? "" : orderDemand.Consumer.CustomerLastName) + " " +
                    (string.IsNullOrEmpty(orderDemand.Consumer.CustomerFirstName) ? "" : orderDemand.Consumer.CustomerFirstName) + " " +
                    (string.IsNullOrEmpty(orderDemand.Consumer.Email) ? "" : orderDemand.Consumer.Email)).Trim();
                if (dossierClientNom.Length > 50)
                {
                    dossierClientNom = dossierClientNom.Substring(0, 50);
                }
            }
            return dossierClientNom;
        }



        public OrderDTO CreateFromDemand(int structureId, int basketId, int amount, CreateOrderDemand orderDemand)
        {

            string dossierClientNom = GetDossierClientNom(orderDemand);

            string myReferenceTransaction = "";
            if (orderDemand.TransactionReference != null && !string.IsNullOrEmpty(orderDemand.TransactionReference))
            {
                myReferenceTransaction = orderDemand.TransactionReference;
            }

            IdentityDTO identityDTO = new IdentityDTO()
            {
                Email = string.IsNullOrEmpty(orderDemand.Consumer.Email) ? "" : orderDemand.Consumer.Email.Trim(),
                FirstName = string.IsNullOrEmpty(orderDemand.Consumer.CustomerFirstName) ? "" : orderDemand.Consumer.CustomerFirstName.Trim(),
                SurName = string.IsNullOrEmpty(orderDemand.Consumer.CustomerLastName) ? "" : orderDemand.Consumer.CustomerLastName.Trim(),
            };

            // Order ret = (Order)Mapper.Map(ordE, typeof(OrderDTO), typeof(Order));

            List<BarCodeDTO> recetteLineDTOs = (List<BarCodeDTO>)_mapper.Map(orderDemand.BarCodes, typeof(List<Ticket>), typeof(List<BarCodeDTO>));
            if (recetteLineDTOs is not null && recetteLineDTOs.Count >0)
            {
                var bDepass = recetteLineDTOs?.Where(r => r.BarCode.Length > 40);
                //if (bDepass.Count() > 0)
                if (bDepass.Any())
                {
                    var bList = string.Join(';', bDepass.Select(b => b.BarCode).ToList());
                    Exception ex = new Exception($"bar code max length = 40 ! ({bList})"); throw ex;
                }
            }

            var ordE = CreateFullFromBasket(structureId, basketId, amount, dossierClientNom, identityDTO, myReferenceTransaction, recetteLineDTOs);

            return ordE;
        }





        public OrderDTO CreateFullFromBasket(int structureId, int basketId, int amountPaye, string dossierClientNom, IdentityDTO? consommateur, string myReferenceTransaction, List<BarCodeDTO> listBarCodeToOverWrite = null)
        {

            try
            {
                //var baskDto = _basketRepository.GetBasketInfoByOrderId(structureId, basketId);
                //                var BasketE = _basketRepository.GetBasketById(structureId, basketId);

                BasketDTO baskDto = _basketManager.GetBasketById(structureId, basketId);

                //return 
                //BasketDTO baskDto = _mapper.Map<BasketDTO>(BasketE);

                if (baskDto == null)
                {
                    throw new Exception($"can't retrieve basket {basketId}");
                    //return Problem($"can't retrieve basket {basketId}", null, StatusCodes.Status404NotFound);
                }
                switch (baskDto.Etat)
                {
                    case "P":
                    case "V":
                        throw new Exception($"basket {basketId} state is already in state {baskDto.Etat} ");
                        break;
                    default:
                        break;
                }
                if ((baskDto.IdentityId == 0 || baskDto.IdentityId == null) && baskDto.WebUser.IdentiteId > 0)
                {
                    baskDto.IdentityId = baskDto.WebUser.IdentiteId;
                    _basketRepository.UpdateIdentity(structureId, basketId, (int)baskDto.IdentityId);
                }

                _basketRepository.UpdateEtatPanierSetPaiementDone(structureId, basketId);

                _basketRepository.SetEtapeCreateCommande(structureId, baskDto, "REFLAGPLACES", EtapeCreationCmd.PrisEnCompte, "CreateFullFromBasket");

                #region reflag et blocage des places

                List<SeatDTO> listSeatsUpdated = _seatRepository.FlagAndLockSeats(structureId, baskDto.ListAllSeats(), baskDto.UserId.ToString());
                if (baskDto.ListAllSeats().Count != listSeatsUpdated.Count)
                {
                    _basketRepository.SetEtapeCreateCommande(structureId, baskDto, "REFLAGPLACES", EtapeCreationCmd.Echoue, "CreateFullFromBasket reflag unsucceeded");
                    return null;
                }

                _basketRepository.SetEtapeCreateCommande(structureId, baskDto, "REFLAGPLACES", EtapeCreationCmd.Termine, "CreateFullFromBasket reflag succeeded");

                #endregion

                #region creation commande

                _basketRepository.SetEtapeCreateCommande(structureId, baskDto, "CREATIONCMD", EtapeCreationCmd.PrisEnCompte, "CreateFullFromBasket");

                var DicoConfigIni = _rodrigueConfig.GetDictionaryFromCache(structureId);
                int posteId = 0;
                if (DicoConfigIni.ContainsKey("PAIEMENTWEBPOSTID") && DicoConfigIni["PAIEMENTWEBPOSTID"] != null && DicoConfigIni["PAIEMENTWEBPOSTID"] != "")
                {
                    posteId = int.Parse(DicoConfigIni["PAIEMENTWEBPOSTID"].ToString());
                }


                int idFiliereCreatProfilAnony = FiliereManager.GetFiliereCreationProfil(DicoConfigIni);
                int idFiliereVU = FiliereManager.GetFiliereVU(DicoConfigIni);
                int idFiliereAbo = FiliereManager.GetFiliereABO(DicoConfigIni);
                int idFiliereReabo = FiliereManager.GetFiliereReABO(DicoConfigIni);
                int idFiliereProduits = FiliereManager.GetFiliereProduits(DicoConfigIni);
                int operatorId = 0;
                operatorId = OperatorManager.GetOperateurPaiement(DicoConfigIni);


                int identitePayeur = baskDto.IdentityId;
                int identiteCmd = baskDto.IdentityId;


                BuyerProfilDTO bp = new();

                List<PaymentMethodDTO> listPM = new();
                if (baskDto.WebUser.ProfilAcheteurId != 0)
                {
                    bp = _buyerProfilRepository.Get(structureId, (int)baskDto.WebUser.ProfilAcheteurId);

                    operatorId = bp.OperatorPaymentId;
                    if (bp.IsReseller && bp.ListPaymentMethods != null && bp.ListPaymentMethods.Count == 1)
                    {
                        listPM.Add(bp.ListPaymentMethods[0]);
                    }

                    #region creation identité si email n'existe pas
                    if (consommateur != null && !string.IsNullOrEmpty(consommateur.Email) && consommateur.Email.Contains("@"))
                    {
                        int postalTelEmail = 6;
                        if (DicoConfigIni.ContainsKey("VARIABLESEMAIL") && DicoConfigIni["VARIABLESEMAIL"] != null && DicoConfigIni["VARIABLESEMAIL"] != "")
                        {
                            postalTelEmail = int.Parse(DicoConfigIni["VARIABLESEMAIL"].ToString());
                        }
                        consommateur.Comment = bp.Libelle;

                        var r = _identiteRepository.Load(structureId, new List<string> { consommateur.Email }, postalTelEmail, "fr", "");
                        var rr = r.Where(i => i.FicheSupprimer != "O");
                        if (rr.Count() == 0)
                        {
                            // on ne connait pas cet email => cree une identite à mot de passe vide
                            identiteCmd = _identiteRepository.AddConsumer(structureId, identitePayeur, postalTelEmail, consommateur, false);
                        }
                        else
                            identiteCmd = rr.ToList()[0].IdentiteId; // attribue la commande à la 1ere identite qui a cet email
                    } 
                    #endregion

                }
                if (listPM.Count == 1)
                {
                    listPM[0].Montant = amountPaye;
                }




                /************** creation commande, entrees en R ********/
                OrderDTO ord = CreateOrderFromPanier(structureId, baskDto, operatorId, idFiliereCreatProfilAnony, idFiliereAbo, idFiliereVU, idFiliereReabo, idFiliereProduits, false, identiteCmd, dossierClientNom);

                if (ord.OrderId > 0)
                {
                    int orderAmount = 0;
                    foreach (var p in ord.ListDossiersSeats)
                    {
                        orderAmount += p.Amount;
                    }
                    foreach (var p in ord.ListDossiersProduct)
                    {
                        orderAmount += p.TotalTTCAmount;
                    }
                    if (amountPaye == 0)
                        amountPaye = orderAmount;


                    _basketRepository.UpdateOrderId(structureId, baskDto.BasketId, ord.OrderId);
                    baskDto.OrderId = ord.OrderId;

                    _basketRepository.SetEtapeCreateCommande(structureId, baskDto, "CREATIONCMD", EtapeCreationCmd.Termine, $"CreateFullFromBasket ok : {ord.OrderId}");

                    /************** end creation commande ********/

                    /************** paiement commande, en P *******/

                    _basketRepository.SetEtapeCreateCommande(structureId, baskDto, "CREATIONPAIEM", EtapeCreationCmd.PrisEnCompte, "CreateFullFromBasket");

                    try
                    {
                        ord = Pay(structureId, baskDto, operatorId, idFiliereVU, identitePayeur, ord, listPM, null, myReferenceTransaction, Environment.MachineName, posteId);

                        if (!string.IsNullOrEmpty(ord.MsgErreur))
                        {
                            _basketRepository.SetEtapeCreateCommande(structureId, baskDto, "CREATIONPAIEM", EtapeCreationCmd.Echoue, $"CreateFullFromBasket {ord.MsgErreur}");
                            throw new Exception(ord.MsgErreur);
                        }
                        else
                        {

                            _basketRepository.SetEtapeCreateCommande(structureId, baskDto, "CREATIONPAIEM", EtapeCreationCmd.Termine, "CreateFullFromBasket paye ok");
                            _basketRepository.SetEtapeCreateCommande(structureId, baskDto, "EDITION", EtapeCreationCmd.PrisEnCompte, "CreateFullFromBasket");

                            DoEdition(structureId, operatorId, ord, listBarCodeToOverWrite);

                            _basketRepository.SetEtapeCreateCommande(structureId, baskDto, "EDITION", EtapeCreationCmd.Termine, "CreateFullFromBasket");
                            _basketRepository.UpdateEtatPanier(structureId, baskDto.BasketId, "V");
                        }
                    }
                    catch
                    {
                        throw;
                    }
                }


                #endregion

                return ord;
            }
            catch
            {
                throw;
            }

        }

        public bool CancelReservation(int structureId, int orderId)
        {
            var dicoConfigIni = _rodrigueConfig.GetDictionaryFromCache(structureId);
            int operatorId = OperatorManager.GetOperateurPaiement(dicoConfigIni);

            var orderEntity = _orderRepository.GetCustomEntityById(structureId, orderId)
                                              .Include(o => o.Lignes)
                                              .Include(o => o.DossiersProduits)
                                              .Include(o => o.Lignes.Select(l => l.Dossier))
                                              .Include(o => o.Lignes.Select(l => l.Dossier.Entrees))
                                              .Include(o => o.DossiersProduits.Select(dp => dp.ProduitStock))
                                              .Include(o => o.DossiersProduits.Select(dp => dp.DossierProduitReservation))
                                              .ToEntity();

            if (orderEntity is null)
                throw new ArgumentNullException(nameof(orderEntity), $"{nameof(orderEntity)} with id '{orderId}' is not found");

            if (orderEntity.DossiersProduits is null && orderEntity.DossiersProduits!.All(dp => dp.DossierProduitReservation is null))
                throw new ArgumentException($"Order haven't reservation product");

            var dossiers = orderEntity.Lignes?.Where(l => l.Dossier is not null && l.Dossier.State == TypeStateEnum.Reserver)
                                              .Select(l => l.Dossier)
                                              .ToList();

            var dossiersProduit = orderEntity.DossiersProduits?.Where(dp => dp.State == TypeStateEnum.Reserver)
                                                               .ToList();

            using var dbContextTransactionScope = _dbContext.BeginTransaction(ContextType.Open, structureId);

            try
            {
                var dosProdReservation = orderEntity.DossiersProduits?.FirstOrDefault(dp => dp.DossierProduitReservation is not null)?.DossierProduitReservation;

                if (dosProdReservation != null)
                {
                    dosProdReservation.Etat = "A";
                    bool dosProdReservationIsUpdated = _dossierProduitReservationRepository.Update(structureId, dosProdReservation);

                    if (!dosProdReservationIsUpdated) throw new Exception($"Dossier produit reservation is not updated");
                }

                dossiers.ForEach(d =>
                {
                    d.DossierV += 1;
                    d.OperateurId = operatorId;
                    d.DossierC = "";
                    d.DossierEtat = "A";
                    d.DossierIcone = 4097;
                    d.DossierMontantPayer = 0;
                    d.DossierFacture = 0;
                    d.DossierClientNom = "";
                    d.DateOperation = DateTime.Now;
                    d.OperateurId = 0;

                    bool dossierIsUpdated = _dossierRepository.Update(991, d, d.ManifestationId);

                    if (!dossierIsUpdated) throw new Exception($"Dossier is not updated");

                    DossierSvgEntity dossierSvgEntity = _mapper.Map<DossierSvgEntity>(d);
                    dossierSvgEntity.TypeOperation = "LIBERE";

                    bool dossierSvgIsInserted = _dossierSvgRepository.InsertWithoutIdReturn(structureId, dossierSvgEntity, d.ManifestationId, false);

                    if (!dossierSvgIsInserted) throw new Exception($"Dossier svg is not inserted");

                    //iser histo dossier 
                    HistoDossierEntity histoDossier = _mapper.Map<HistoDossierEntity>(d);

                    bool histoDossierIsInserted = _histoDossierRepository.InsertWithoutIdReturn(structureId, histoDossier, null);

                    if (!histoDossierIsInserted) throw new Exception($"histo dossier is not inserted");

                    var entrees = d.Entrees.ToList();

                    entrees.ForEach(e =>
                    {
                        e.EntreeEtat = 'L';
                        e.DossierId = 0;
                        e.TypeTarifId = 0;
                        e.ValeurTarifStockId = 0;
                        e.FlagSelection = "";
                        e.Montant1 = 0;
                        e.Montant2 = 0;
                        e.Montant3 = 0;
                        e.Montant4 = 0;
                        e.Montant5 = 0;
                        e.Montant6 = 0;
                        e.Montant7 = 0;
                        e.Montant8 = 0;
                        e.Montant9 = 0;
                        e.Montant10 = 0;
                        e.DateOperation = DateTime.Now;
                        e.ControleAcces = null;

                        bool entreeIsUpdated = _entreeRepository.Update(structureId, e, e.ManifestationId);

                        if (!entreeIsUpdated) throw new Exception($"Entree is not updated");

                        EntreeSvgEntity entreeSvgEntity = _mapper.Map<EntreeSvgEntity>(e);
                        entreeSvgEntity.DossierV = d.DossierV;
                        entreeSvgEntity.ValeurTarifStockVersion = 0;

                        bool entreeSvgIsInserted = _entreeSvgRepository.InsertWithoutIdReturn(structureId, entreeSvgEntity, e.ManifestationId, false);

                        if (!entreeSvgIsInserted) throw new Exception($"Entree svg is not inserted");

                    });

                });

                dossiersProduit.ForEach(dp =>
                {
                    int nbProduit = dp.NbProduit;

                    dp.DosProdV += 1;
                    dp.OperateurId = operatorId;
                    dp.DosProdEtat = "A";
                    dp.DateOperation = DateTime.Now;

                    bool dossierProduitIsUpdated = _dossierProduitRepository.Update(structureId, dp);

                    if (!dossierProduitIsUpdated) throw new Exception($"Dossier produit is not updated");

                    var produitStockEntity = dp.ProduitStock!;
                    produitStockEntity.Restant += dp.NbProduit;

                    bool produitStockIsUpdated = _produitStockRepository.Update(structureId, produitStockEntity);

                    if (!produitStockIsUpdated) throw new Exception($"Produit stock is not updated");
                });

                dbContextTransactionScope.Commit();

                return true;
            }
            catch
            {
                dbContextTransactionScope.Rollback();

                return false;
            }
        }

        #region CustomerArea => Order

        public OrderCardInfo? GetOrderCardInfo(int structureId, int orderId)
        {
            string cacheKey = $"GetOrderCardInfo_{structureId}_{orderId}";

            var cacheResult = _memoryCache.Get<OrderCardInfo>(cacheKey);

            if (cacheResult is not null)
                return cacheResult;

            var orderEntity = _orderRepository.GetById(structureId, orderId);

            if (orderEntity is not null)
                SetOrderDependancies(structureId, orderEntity);

            var orderCardInfo = _mapper.Map<OrderCardInfo>(orderEntity);

            if (orderCardInfo is not null)
                _memoryCache.Set(cacheKey, orderCardInfo, TimeSpan.FromMinutes(2));

            return orderCardInfo;
        }

        public async Task<OrderInfoDetails?> GetOrderDetailsAsync(int structureId, int orderId)
        {
            string cacheKey = $"GetOrderDetails_{structureId}_{orderId}";

            var cacheResult = _memoryCache.Get<OrderInfoDetails>(cacheKey);

            if (cacheResult is not null)
                return cacheResult;

            var orderEntity = await _orderRepository.GetByIdAsync(structureId, orderId).ConfigureAwait(false);

            if (orderEntity is not null)
                orderEntity = await SetOrderDependanciesForOrderDetailsAsync(structureId, orderEntity).ConfigureAwait(false);

            var orderCardInfo = _mapper.Map<OrderInfoDetails>(orderEntity);

            await SetOpinionOrderInfoInHeaderDetailsAsync(structureId, orderCardInfo.HeaderDetails).ConfigureAwait(false);

            if (orderCardInfo is not null)
                _memoryCache.Set(cacheKey, orderCardInfo, TimeSpan.FromSeconds(5));

            return orderCardInfo;
        }

        public async Task<Invoice?> GetInvoiceTemplateAsync(int structureId, int orderId)
        {
            string cacheKey = $"GetInvoiceTemplate_{structureId}_{orderId}";

            var invoice = _memoryCache.Get<Invoice>(cacheKey);

            if (invoice is null)
            {
                var structure = _structureRepository.GetById(structureId, structureId)
                    ?? throw new NullReferenceException($"Structure {structureId} is null");

                var query = _orderRepository.GetCustomEntityById(structureId, orderId);

                Task[] includes1 = new Task[]
                {
                    query.IncludeAsync(c => c.CommandeInfos),
                    query.IncludeAsync(c => c.DossiersProduits),
                    query.IncludeAsync(c => c.Buyer),
                    query.IncludeAsync(c => c.Lignes),
                };
                await Task.WhenAll(includes1).ConfigureAwait(false);

                Task[] includes2 = new Task[]
                {
                    query.IncludeAsync(c => c.DossiersProduits.Select(dp => dp.Produit)),
                    query.IncludeAsync(c => c.Lignes.Select(l => l.Dossier)),
                };

                await Task.WhenAll(includes2).ConfigureAwait(false);

                Task[] includes3 = new Task[]
                {
                    query.IncludeAsync(c => c.DossiersProduits.Select(dp => dp.Produit.Tva)),
                    query.IncludeAsync(c => c.DossiersProduits.Select(dp => dp.Produit.FeeTva)),
                    query.IncludeAsync(c => c.Lignes.Select(l => l.Dossier.Entrees)),
                    query.IncludeAsync(c => c.Lignes.Select(l => l.Dossier.DossiersSvg))
                };

                await Task.WhenAll(includes3).ConfigureAwait(false);

                Task[] includes4 = new Task[]
                {
                    query.IncludeAsync(c => c.Lignes.Select(l => l.Dossier.Entrees.Select(e => e.Categorie))),
                    query.IncludeAsync(c => c.Lignes.Select(l => l.Dossier.Entrees.Select(e => e.TypeTarif))),
                    query.IncludeAsync(c => c.Lignes.Select(l => l.Dossier.Entrees.Select(e => e.Seance)))
                };

                await Task.WhenAll(includes4).ConfigureAwait(false);

                Task[] includes5 = new Task[]
                {
                    query.IncludeAsync(c => c.Lignes.Select(l => l.Dossier.Entrees.Select(e => e.Seance.Manifestation))),
                    query.IncludeAsync(c => c.Lignes.Select(l => l.Dossier.Entrees.Select(e => e.Seance.TvaEntree))),
                    query.IncludeAsync(c => c.Lignes.Select(l => l.Dossier.Entrees.Select(e => e.Seance.TvaFrais)))
                };

                await Task.WhenAll(includes5).ConfigureAwait(false);

                Task[] includes6 = new Task[]
                {
                    query.IncludeAsync(c => c.Lignes.Select(l => l.Dossier.Entrees.Select(e => e.Seance.Manifestation.TvaMontant4))),
                    query.IncludeAsync(c => c.Lignes.Select(l => l.Dossier.Entrees.Select(e => e.Seance.Manifestation.TvaMontant5))),
                    query.IncludeAsync(c => c.Lignes.Select(l => l.Dossier.Entrees.Select(e => e.Seance.Manifestation.TvaMontant6))),
                    query.IncludeAsync(c => c.Lignes.Select(l => l.Dossier.Entrees.Select(e => e.Seance.Manifestation.TvaMontant7))),
                    query.IncludeAsync(c => c.Lignes.Select(l => l.Dossier.Entrees.Select(e => e.Seance.Manifestation.TvaMontant8))),
                    query.IncludeAsync(c => c.Lignes.Select(l => l.Dossier.Entrees.Select(e => e.Seance.Manifestation.TvaMontant9))),
                    query.IncludeAsync(c => c.Lignes.Select(l => l.Dossier.Entrees.Select(e => e.Seance.Manifestation.TvaMontant10))),
                    query.IncludeAsync(c => c.Lignes.Select(l => l.Dossier.Entrees.Select(e => e.Seance.Manifestation.Structure)))
                };

                await Task.WhenAll(includes6).ConfigureAwait(false);

                var orderEntity = query.ToEntity();

                invoice = _mapper.Map<Invoice>(orderEntity);

                invoice.StructureName = structure.StructureNom;
                invoice.StructurePostalAddress = $"{structure.StructureRue1} {structure.StructureRue2} {structure.StructureRue3} {structure.StructureCP}, {structure.StructureVille}";
                invoice.StructureContry = structure.StructurePays;

                invoice.BannerImagePath = UrlHelper.GetBannerUrl(structureId);
                invoice.DeviseCode = _rodrigueConfigIniDictionnary.GetDeviseCode(structureId);

                SetCompteClientOperations(structureId, orderId, invoice);

                if (invoice is not null)
                    _memoryCache.Set(cacheKey, invoice, TimeSpan.FromMinutes(2));
            }

            return invoice;
        }

        public async Task<PaginationLookup<OrderCardInfo>?> GetAllOrderCardWithoutReservationInfoByIdentiteIdAsync(int structureId, int identiteId, int page = 0, int pageSize = 10000, DateTime? dateFilter = null)
        {
            string cacheKey = $"GetAllOrderCardWithoutReservationInfoByIdentiteId_{structureId}_{identiteId}_{page}_{pageSize}_{dateFilter}";

            var orderCardInfoLookup = _memoryCache.Get<PaginationLookup<OrderCardInfo>>(cacheKey);

            if (orderCardInfoLookup is null)
            {
                var query = _orderRepository.GetAllOrdersWithoutReservationByIdentiteId(structureId, identiteId);

                if (dateFilter is not null)
                    query.Where(c => c.BuyDate > dateFilter);

                query.Page(page, pageSize);

                Task[] includes1 = new Task[]
                {
                    query.IncludeAsync(c => c.CommandeInfos),
                    query.IncludeAsync(c => c.DossiersProduits),
                    query.IncludeAsync(c => c.Lignes)
                };
                await Task.WhenAll(includes1).ConfigureAwait(false);

                Task[] includes2 = new Task[]
                {
                    query.IncludeAsync(c => c.DossiersProduits.Select(dp => dp.Produit)),
                    query.IncludeAsync(c => c.DossiersProduits.Select(dp => dp.DossierProduitReservation)),
                    query.IncludeAsync(c => c.Lignes.Select(l => l.Dossier))
                };
                await Task.WhenAll(includes2).ConfigureAwait(false);

                Task[] includes3 = new Task[]
                {
                    query.IncludeAsync(c => c.Lignes.Select(l => l.Dossier.Entrees)),
                    query.IncludeAsync(c => c.Lignes.Select(l => l.Dossier.EntreesSvg)),
                };

                await Task.WhenAll(includes3).ConfigureAwait(false);

                Task[] includes4 = new Task[]
                {
                    query.IncludeAsync(c => c.Lignes.Select(l => l.Dossier.Entrees.Select(e => e.Seance))),
                    query.IncludeAsync(c => c.Lignes.Select(l => l.Dossier.EntreesSvg.Select(e => e.Seance)))
                };

                await Task.WhenAll(includes4).ConfigureAwait(false);

                Task[] includes5 = new Task[]
                {
                    query.IncludeAsync(c => c.Lignes.Select(l => l.Dossier.Entrees.Select(e => e.Seance.Manifestation))),
                    query.IncludeAsync(c => c.Lignes.Select(l => l.Dossier.EntreesSvg.Select(e => e.Seance.Manifestation)))
                };

                await Task.WhenAll(includes5).ConfigureAwait(false);

                var entityPaginationLookup = query.ToPaginationLookup();

                var listOrderCardInfo = _mapper.Map<List<OrderCardInfo>>(entityPaginationLookup.Items);

                var tasksOpinion = listOrderCardInfo.Select(o => SetOpinionOrderInfoInOrderCardInfoAsync(structureId, o));
                var resultsOpinion = await Task.WhenAll(tasksOpinion).ConfigureAwait(false);
                listOrderCardInfo = resultsOpinion.ToList();

                orderCardInfoLookup = new PaginationLookup<OrderCardInfo>(entityPaginationLookup.TotalItems, listOrderCardInfo);

                if (listOrderCardInfo is not null && listOrderCardInfo.Count > 0)
                    _memoryCache.Set(cacheKey, orderCardInfoLookup, TimeSpan.FromMinutes(2));
            }

            return orderCardInfoLookup;
        }



        public async Task<List<OrderCardInfo>?> GetAllReservationOrderCardInfoByIdentiteIdAsync(int structureId, int identiteId)
        {
            string cacheKey = $"GetAllReservationOrderCardInfoByIdentiteId_{structureId}_{identiteId}";

            var listOrderCardInfo = _memoryCache.Get<List<OrderCardInfo>>(cacheKey);

            if (listOrderCardInfo is null)
            {
                var paginationEntity = await _orderRepository.GetAllReservationByIdentiteIdAsync(structureId, identiteId)
                                                             .ConfigureAwait(false);

                if (paginationEntity is not null)
                {
                    var tasks = paginationEntity.Items.Select(o => SetOrderDependanciesAsync(structureId, o));
                    var results = await Task.WhenAll(tasks).ConfigureAwait(false);
                    listOrderCardInfo = _mapper.Map<List<OrderCardInfo>>(results);
                }

                if (listOrderCardInfo is not null && listOrderCardInfo.Count > 0)
                    _memoryCache.Set(cacheKey, listOrderCardInfo, TimeSpan.FromMinutes(2));
            }

            return listOrderCardInfo;
        }





        #endregion

        #region Private 

        private void SetOrderDependancies(int structureId, CommandeEntity commande)
        {
            try
            {
                commande.Buyer = _identiteRepository.GetById(structureId, commande.IdentiteId);
                commande.CommandeInfos = _orderInfoRepository.GetById(structureId, commande.CommandeId);
                commande.Lignes = _orderLigneRepository.FindBy(l => l.CommandeId == commande.CommandeId, structureId)?.ToList();

                if (commande.Lignes is not null)
                {
                    foreach (var ligne in commande.Lignes)
                    {
                        ligne.CommandeLigneComp = _commandeLigneCompRepository.GetById(structureId, ligne.CommandeLigneId);
                        ligne.Buyer = _identiteRepository.GetById(structureId, ligne.IdentiteId);

                        if (ligne.ManifestationId > 0)
                        {
                            var dossier = _dossierRepository.GetById(structureId, ligne.DossierId, ligne.ManifestationId);


                            if (dossier is not null)
                            {
                                dossier.DossiersSvg = _dossierSvgRepository.FindBy(dsvg => dsvg.DossierId == dossier.DossierId, structureId, ligne.ManifestationId)?.ToList();
                                //dossier.Entrees = _entreeRepository.GetAllEntreesByDossierIdAndSeanceId(structureId, ligne.ManifestationId, dossier.DossierId, dossier.SeanceId)?.ToList();
                                dossier.Entrees = _entreeRepository.FindBy(e => e.DossierId == dossier.DossierId
                                                                            && e.SeanceId == dossier.SeanceId,
                                                                            structureId,
                                                                            ligne.ManifestationId)
                                                                    ?.ToList();

                                dossier.EntreesSvg = _entreeSvgRepository.FindBy(esvg => esvg.DossierId == dossier.DossierId
                                                                                && esvg.SeanceId == dossier.SeanceId,
                                                                                structureId,
                                                                                ligne.ManifestationId)?.ToList();

                                if (dossier.Entrees is not null)
                                {
                                    foreach (var entree in dossier.Entrees)
                                    {
                                        var seance = _sessionRepository.GetById(structureId, entree.SeanceId);

                                        if (seance is not null)
                                            seance.Manifestation = _eventRepository.GetById(structureId, seance.ManifestationId);

                                        entree.Seance = seance;
                                    }
                                }
                            }

                            ligne.Dossier = dossier;
                        }
                    }
                }

                commande.DossiersProduits = _dossierProduitRepository.FindBy(dp => dp.CommandeId == commande.CommandeId, structureId)?.ToList();

                if (commande.DossiersProduits is not null)
                {
                    foreach (var dossierP in commande.DossiersProduits)
                    {
                        dossierP.Produit = _produitRepository.GetById(structureId, dossierP.ProduitId);
                        dossierP.DossierProduitReservation = _dossierProduitReservationRepository.FindBy(dpr => dpr.DosProdId == dossierP.DosProdId, structureId)
                                                                                                ?.FirstOrDefault();
                    }
                }
            }
            catch
            {
                throw;
            }
        }

        private async Task<CommandeEntity> SetOrderDependanciesAsync(int structureId, CommandeEntity commande)
        {
            try
            {
                commande.Buyer = await _identiteRepository.GetByIdAsync(structureId, commande.IdentiteId)
                                                          .ConfigureAwait(false);

                commande.CommandeInfos = await _orderInfoRepository.GetByIdAsync(structureId, commande.CommandeId)
                                                                   .ConfigureAwait(false);

                commande.Lignes = (await _orderLigneRepository.FindByAsync(l => l.CommandeId == commande.CommandeId, structureId)
                                                              .ConfigureAwait(false))
                                                              ?.ToList();

                if (commande.Lignes is not null)
                {
                    foreach (var ligne in commande.Lignes)
                    {
                        ligne.CommandeLigneComp = await _commandeLigneCompRepository.GetByIdAsync(structureId, ligne.CommandeLigneId)
                                                                                    .ConfigureAwait(false);

                        ligne.Buyer = await _identiteRepository.GetByIdAsync(structureId, ligne.IdentiteId)
                                                               .ConfigureAwait(false);

                        if (ligne.ManifestationId > 0)
                        {
                            var dossier = await _dossierRepository.GetByIdAsync(structureId, ligne.DossierId, ligne.ManifestationId)
                                                                  .ConfigureAwait(false);

                            if (dossier is not null)
                            {
                                dossier.DossiersSvg = (await _dossierSvgRepository.FindByAsync(dsvg => dsvg.DossierId == dossier.DossierId, structureId, ligne.ManifestationId)
                                                                                  .ConfigureAwait(false))
                                                                                  ?.ToList();

                                dossier.Entrees = (await _entreeRepository.FindByAsync(e => e.DossierId == dossier.DossierId
                                                                                        && e.SeanceId == dossier.SeanceId,
                                                                                        structureId,
                                                                                        ligne.ManifestationId)
                                                                          .ConfigureAwait(false))
                                                                          ?.ToList();

                                dossier.EntreesSvg = (await _entreeSvgRepository.FindByAsync(esvg => esvg.DossierId == dossier.DossierId
                                                                                            && esvg.SeanceId == dossier.SeanceId,
                                                                                            structureId,
                                                                                            ligne.ManifestationId)
                                                                                .ConfigureAwait(false))
                                                                                ?.ToList();

                                if (dossier.Entrees is not null)
                                {
                                    foreach (var entree in dossier.Entrees)
                                    {
                                        var seance = await _sessionRepository.GetByIdAsync(structureId, entree.SeanceId)
                                                                             .ConfigureAwait(false);

                                        if (seance is not null)
                                            seance.Manifestation = await _eventRepository.GetByIdAsync(structureId, seance.ManifestationId)
                                                                                         .ConfigureAwait(false);

                                        entree.Seance = seance;
                                    }
                                }
                            }

                            ligne.Dossier = dossier;
                        }
                    }
                }

                commande.DossiersProduits = (await _dossierProduitRepository.FindByAsync(dp => dp.CommandeId == commande.CommandeId, structureId)
                                                                            .ConfigureAwait(false))
                                                                            ?.ToList();

                if (commande.DossiersProduits is not null)
                {
                    foreach (var dossierP in commande.DossiersProduits)
                    {
                        dossierP.Produit = await _produitRepository.GetByIdAsync(structureId, dossierP.ProduitId)
                                                                   .ConfigureAwait(false);

                        dossierP.DossierProduitReservation = await _dossierProduitReservationRepository.FindFirstOrDefaultAsync(dpr => dpr.DosProdId == dossierP.DosProdId, structureId)
                                                                                                        .ConfigureAwait(false);
                    }
                }

                return commande;
            }
            catch
            {
                throw;
            }
        }

        private async Task<CommandeEntity> SetOrderDependanciesForOrderDetailsAsync(int structureId, CommandeEntity commande)
        {
            try
            {
                dynamic settingsMerged = WidgetUtilitiesHelper.SettingsMerged(structureId, 0, 0, "", "", "physicalPathOfSettingsJSON");
                dynamic showPlaceName = settingsMerged.sessions.showPlaceName;

                commande.Lignes = (await _orderLigneRepository.FindByAsync(l => l.CommandeId == commande.CommandeId, structureId)
                                                              .ConfigureAwait(false))
                                                              ?.ToList();

                commande.CommandeInfos = await _orderInfoRepository.GetByIdAsync(structureId, commande.CommandeId)
                                                                   .ConfigureAwait(false);

                if (commande.Lignes is not null)
                {
                    foreach (var ligne in commande.Lignes)
                    {
                        if (ligne.ManifestationId > 0)
                        {
                            var dossier = await _dossierRepository.GetByIdAsync(structureId, ligne.DossierId, ligne.ManifestationId)
                                                                  .ConfigureAwait(false);

                            if (dossier is not null)
                            {
                                var seance = await _sessionRepository.GetByIdAsync(structureId, dossier.SeanceId)
                                                                     .ConfigureAwait(false);

                                if (seance is not null)
                                {
                                    var manifestation = await _eventRepository.GetByIdAsync(structureId, seance.ManifestationId)
                                                                                .ConfigureAwait(false);

                                    if (manifestation is not null)
                                    {
                                        var propsOfManif = await _proprietesOfManifsRepository.FindByAsync(m => m.ManifestationId == manifestation.ManifestationId, structureId)
                                                                                              .ConfigureAwait(false);

                                        if (propsOfManif is not null)
                                        {
                                            foreach (var props in propsOfManif)
                                            {
                                                props.ProprieteRef = await _proprietesReferencesOfManifsRepository.GetByIdAsync(structureId, props.ProprieteRefId)
                                                                                                                  .ConfigureAwait(false);
                                            }

                                            manifestation.ProprietesOfManifs = propsOfManif.ToList();
                                        }

                                        manifestation.Structure = await _structureRepository.GetByIdAsync(structureId, manifestation.StructureId)
                                                                                            .ConfigureAwait(false);
                                    }


                                    seance.Manifestation = manifestation;

                                    seance.TvaEntree = await _tvaRepository.GetByIdAsync(structureId, seance.TauxTva1Id)
                                                                           .ConfigureAwait(false);

                                    bool showDefaultPlaceName = showPlaceName["default"];
                                    bool? showPlaceNameOfSeance = showPlaceName[seance.LieuId.ToString()];

                                    if (showDefaultPlaceName || (showPlaceNameOfSeance.HasValue && showPlaceNameOfSeance.Value))
                                        seance.Lieu = await _placeRepository.GetByIdAsync(structureId, seance.LieuId)
                                                                            .ConfigureAwait(false);

                                }

                                dossier.Seance = seance;

                                dossier.Entrees = (await _entreeRepository.FindByAsync(e => e.DossierId == dossier.DossierId
                                                                                        && e.SeanceId == dossier.SeanceId,
                                                                                        structureId,
                                                                                        ligne.ManifestationId)
                                                                          .ConfigureAwait(false))
                                                                          ?.ToList();


                                dossier.EntreesSvg = (await _entreeSvgRepository.FindByAsync(esvg => esvg.DossierId == dossier.DossierId
                                                                                            && esvg.SeanceId == dossier.SeanceId,
                                                                                            structureId,
                                                                                            ligne.ManifestationId)
                                                                                .ConfigureAwait(false))
                                                                                ?.ToList();

                                if (dossier.EntreesSvg is not null)
                                {
                                    foreach (var entreeSvg in dossier.EntreesSvg)
                                    {
                                        entreeSvg.Seance = seance;
                                    }
                                }

                                dossier.DossiersSvg = (await _dossierSvgRepository.FindByAsync(dsvg => dsvg.DossierId == dossier.DossierId, structureId, ligne.ManifestationId)
                                                                                  .ConfigureAwait(false))
                                                                                  ?.ToList();

                                if (dossier.Entrees is not null)
                                {
                                    foreach (var entree in dossier.Entrees)
                                    {
                                        entree.Categorie = await _categoryRepository.GetByIdAsync(structureId, entree.CategorieId)
                                                                                    .ConfigureAwait(false);

                                        entree.TypeTarif = await _typeTarifRepository.GetByIdAsync(structureId, entree.TypeTarifId)
                                                                                     .ConfigureAwait(false);

                                        var refLieuPhysique = await _referenceLieuPhysiqueRepository.GetByIdAsync(structureId, entree.ReferenceUniquePhysiqueId)
                                                                                                    .ConfigureAwait(false);

                                        if (refLieuPhysique is not null)
                                        {
                                            refLieuPhysique.Zone = await _zoneRepository.GetByIdAsync(structureId, refLieuPhysique.ZoneId)
                                                                                        .ConfigureAwait(false);

                                            refLieuPhysique.Etage = await _floorRepository.GetByIdAsync(structureId, refLieuPhysique.EtageId)
                                                                                          .ConfigureAwait(false);

                                            refLieuPhysique.Section = await _sectionRepository.GetByIdAsync(structureId, refLieuPhysique.SectionId)
                                                                                              .ConfigureAwait(false);

                                            entree.ReferenceLieuPhysique = refLieuPhysique;
                                        }

                                        entree.Seance = seance;
                                    }
                                }
                            }

                            ligne.Dossier = dossier;
                        }
                    }
                }

                commande.DossiersProduits = (await _dossierProduitRepository.FindByAsync(dp => dp.CommandeId == commande.CommandeId, structureId)
                                                                            .ConfigureAwait(false))
                                                                            ?.ToList();

                if (commande.DossiersProduits is not null)
                {
                    foreach (var dossierP in commande.DossiersProduits)
                    {
                        var produit = await _produitRepository.GetByIdAsync(structureId, dossierP.ProduitId)
                                                                   .ConfigureAwait(false);

                        if (produit is not null)
                            produit.Tva = await _tvaRepository.GetByIdAsync(structureId, produit.Tva1)
                                                              .ConfigureAwait(false);

                        dossierP.Produit = produit;

                        dossierP.Identite = await _identiteRepository.GetByIdAsync(structureId, dossierP.IdentiteId)
                                                                     .ConfigureAwait(false);
                    }
                }

                return commande;
            }
            catch
            {
                throw;
            }
        }

        private async Task<OrderCardInfo> SetOpinionOrderInfoInOrderCardInfoAsync(int structureId, OrderCardInfo orderCardInfo)
        {
            bool useOpinionOrder = await _opinionOrderRepository.OpinionOrderExistAsync(structureId, orderCardInfo.OrderId).ConfigureAwait(false);

            if (useOpinionOrder)
                orderCardInfo.OpinionNote = await _opinionOrderRepository.GetOpinionNoteAverageByOrderIdAsync(structureId, orderCardInfo.OrderId).ConfigureAwait(false);

            orderCardInfo.UseOpinionOrder = useOpinionOrder;

            return orderCardInfo;
        }

        private async Task SetOpinionOrderInfoInHeaderDetailsAsync(int structureId, HeaderDetails headerDetails)
        {
            bool useOpinionOrder = await _opinionOrderRepository.OpinionOrderExistAsync(structureId, headerDetails.OrderId).ConfigureAwait(false);

            if (useOpinionOrder)
                headerDetails.OpinionNote = await _opinionOrderRepository.GetOpinionNoteAverageByOrderIdAsync(structureId, headerDetails.OrderId).ConfigureAwait(false);

            headerDetails.UseOpinionOrder = useOpinionOrder;
        }

        private void SetCompteClientOperations(int structureId, int orderId, Invoice invoice)
        {
            var compteClientOperations = _compteClientRepository.GetAllOperationByOrderId(structureId, orderId)
                                                                .Include(o => o.ModePaiement)
                                                                .ToEntityList();

            var paymentDetails = compteClientOperations.Where(o => o.ModePaiement is not null && o.CcDebit > 0)
                                                       .Select(o => new PaymentDetail()
                                                       {
                                                           Amount = o.CcDebit,
                                                           PaymentMethodLabel = o.ModePaiement.ModePaieNom
                                                       })
                                                       .ToList();

            invoice.PaymentDetail = paymentDetails;
        }

        #endregion


    }
}
