﻿
Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 17
VisualStudioVersion = 17.11.35327.3
MinimumVisualStudioVersion = 10.0.40219.1
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "MiseEnVentes", "MiseEnVentes.csproj", "{1AD66DD4-731B-49BC-BAFA-A803038C5CBD}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "MiseEnVentesTests", "..\MiseEnVentesTests\MiseEnVentesTests.csproj", "{5D8E3E46-4B88-443A-8CEB-D3AE3C6B90E3}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Core.Themis.Libraries.Utilities", "..\..\Libraries\Core.Themis.Libraries.Utilities\Core.Themis.Libraries.Utilities\Core.Themis.Libraries.Utilities.csproj", "{2275439B-0301-F6A6-80F6-FD346D03061F}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Core.Themis.Libraries.UtilitiesTests", "..\..\Libraries\Core.Themis.Libraries.Utilities\Core.Themis.Libraries.UtilitiesTests\Core.Themis.Libraries.UtilitiesTests.csproj", "{B4C47872-07E3-890D-F599-08A7A5C6E1D4}"
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|Any CPU = Debug|Any CPU
		Debug|x64 = Debug|x64
		Debug|x86 = Debug|x86
		Release|Any CPU = Release|Any CPU
		Release|x64 = Release|x64
		Release|x86 = Release|x86
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{1AD66DD4-731B-49BC-BAFA-A803038C5CBD}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{1AD66DD4-731B-49BC-BAFA-A803038C5CBD}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{1AD66DD4-731B-49BC-BAFA-A803038C5CBD}.Debug|x64.ActiveCfg = Debug|Any CPU
		{1AD66DD4-731B-49BC-BAFA-A803038C5CBD}.Debug|x64.Build.0 = Debug|Any CPU
		{1AD66DD4-731B-49BC-BAFA-A803038C5CBD}.Debug|x86.ActiveCfg = Debug|Any CPU
		{1AD66DD4-731B-49BC-BAFA-A803038C5CBD}.Debug|x86.Build.0 = Debug|Any CPU
		{1AD66DD4-731B-49BC-BAFA-A803038C5CBD}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{1AD66DD4-731B-49BC-BAFA-A803038C5CBD}.Release|Any CPU.Build.0 = Release|Any CPU
		{1AD66DD4-731B-49BC-BAFA-A803038C5CBD}.Release|x64.ActiveCfg = Release|Any CPU
		{1AD66DD4-731B-49BC-BAFA-A803038C5CBD}.Release|x64.Build.0 = Release|Any CPU
		{1AD66DD4-731B-49BC-BAFA-A803038C5CBD}.Release|x86.ActiveCfg = Release|Any CPU
		{1AD66DD4-731B-49BC-BAFA-A803038C5CBD}.Release|x86.Build.0 = Release|Any CPU
		{5D8E3E46-4B88-443A-8CEB-D3AE3C6B90E3}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{5D8E3E46-4B88-443A-8CEB-D3AE3C6B90E3}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{5D8E3E46-4B88-443A-8CEB-D3AE3C6B90E3}.Debug|x64.ActiveCfg = Debug|Any CPU
		{5D8E3E46-4B88-443A-8CEB-D3AE3C6B90E3}.Debug|x64.Build.0 = Debug|Any CPU
		{5D8E3E46-4B88-443A-8CEB-D3AE3C6B90E3}.Debug|x86.ActiveCfg = Debug|Any CPU
		{5D8E3E46-4B88-443A-8CEB-D3AE3C6B90E3}.Debug|x86.Build.0 = Debug|Any CPU
		{5D8E3E46-4B88-443A-8CEB-D3AE3C6B90E3}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{5D8E3E46-4B88-443A-8CEB-D3AE3C6B90E3}.Release|Any CPU.Build.0 = Release|Any CPU
		{5D8E3E46-4B88-443A-8CEB-D3AE3C6B90E3}.Release|x64.ActiveCfg = Release|Any CPU
		{5D8E3E46-4B88-443A-8CEB-D3AE3C6B90E3}.Release|x64.Build.0 = Release|Any CPU
		{5D8E3E46-4B88-443A-8CEB-D3AE3C6B90E3}.Release|x86.ActiveCfg = Release|Any CPU
		{5D8E3E46-4B88-443A-8CEB-D3AE3C6B90E3}.Release|x86.Build.0 = Release|Any CPU
		{2275439B-0301-F6A6-80F6-FD346D03061F}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{2275439B-0301-F6A6-80F6-FD346D03061F}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{2275439B-0301-F6A6-80F6-FD346D03061F}.Debug|x64.ActiveCfg = Debug|Any CPU
		{2275439B-0301-F6A6-80F6-FD346D03061F}.Debug|x64.Build.0 = Debug|Any CPU
		{2275439B-0301-F6A6-80F6-FD346D03061F}.Debug|x86.ActiveCfg = Debug|Any CPU
		{2275439B-0301-F6A6-80F6-FD346D03061F}.Debug|x86.Build.0 = Debug|Any CPU
		{2275439B-0301-F6A6-80F6-FD346D03061F}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{2275439B-0301-F6A6-80F6-FD346D03061F}.Release|Any CPU.Build.0 = Release|Any CPU
		{2275439B-0301-F6A6-80F6-FD346D03061F}.Release|x64.ActiveCfg = Release|Any CPU
		{2275439B-0301-F6A6-80F6-FD346D03061F}.Release|x64.Build.0 = Release|Any CPU
		{2275439B-0301-F6A6-80F6-FD346D03061F}.Release|x86.ActiveCfg = Release|Any CPU
		{2275439B-0301-F6A6-80F6-FD346D03061F}.Release|x86.Build.0 = Release|Any CPU
		{B4C47872-07E3-890D-F599-08A7A5C6E1D4}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{B4C47872-07E3-890D-F599-08A7A5C6E1D4}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{B4C47872-07E3-890D-F599-08A7A5C6E1D4}.Debug|x64.ActiveCfg = Debug|Any CPU
		{B4C47872-07E3-890D-F599-08A7A5C6E1D4}.Debug|x64.Build.0 = Debug|Any CPU
		{B4C47872-07E3-890D-F599-08A7A5C6E1D4}.Debug|x86.ActiveCfg = Debug|Any CPU
		{B4C47872-07E3-890D-F599-08A7A5C6E1D4}.Debug|x86.Build.0 = Debug|Any CPU
		{B4C47872-07E3-890D-F599-08A7A5C6E1D4}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{B4C47872-07E3-890D-F599-08A7A5C6E1D4}.Release|Any CPU.Build.0 = Release|Any CPU
		{B4C47872-07E3-890D-F599-08A7A5C6E1D4}.Release|x64.ActiveCfg = Release|Any CPU
		{B4C47872-07E3-890D-F599-08A7A5C6E1D4}.Release|x64.Build.0 = Release|Any CPU
		{B4C47872-07E3-890D-F599-08A7A5C6E1D4}.Release|x86.ActiveCfg = Release|Any CPU
		{B4C47872-07E3-890D-F599-08A7A5C6E1D4}.Release|x86.Build.0 = Release|Any CPU
	EndGlobalSection
	GlobalSection(SolutionProperties) = preSolution
		HideSolutionNode = FALSE
	EndGlobalSection
	GlobalSection(ExtensibilityGlobals) = postSolution
		SolutionGuid = {92F4C764-6A8E-4E20-BDD9-33E0F9FC6217}
	EndGlobalSection
	GlobalSection(TeamFoundationVersionControl) = preSolution
		SccNumberOfProjects = 5
		SccEnterpriseProvider = {4CA58AB2-18FA-4F8D-95D4-32DDF27D184C}
		SccTeamFoundationServer = http://v-dev-tfs:8080/tfs/appscollection
		SccLocalPath0 = .
		SccProjectUniqueName1 = MiseEnVentes.csproj
		SccLocalPath1 = .
		SccProjectUniqueName2 = ..\\MiseEnVentesTests\\MiseEnVentesTests.csproj
		SccProjectName2 = ../MiseEnVentesTests
		SccLocalPath2 = ..\\MiseEnVentesTests
		SccProjectUniqueName3 = ..\\..\\Libraries\\Core.Themis.Libraries.Utilities\\Core.Themis.Libraries.Utilities\\Core.Themis.Libraries.Utilities.csproj
		SccProjectName3 = ../../Libraries/Core.Themis.Libraries.Utilities/Core.Themis.Libraries.Utilities
		SccLocalPath3 = ..\\..\\Libraries\\Core.Themis.Libraries.Utilities\\Core.Themis.Libraries.Utilities
		SccProjectUniqueName4 = ..\\..\\Libraries\\Core.Themis.Libraries.Utilities\\Core.Themis.Libraries.UtilitiesTests\\Core.Themis.Libraries.UtilitiesTests.csproj
		SccProjectName4 = ../../Libraries/Core.Themis.Libraries.Utilities/Core.Themis.Libraries.UtilitiesTests
		SccLocalPath4 = ..\\..\\Libraries\\Core.Themis.Libraries.Utilities\\Core.Themis.Libraries.UtilitiesTests
	EndGlobalSection
EndGlobal
