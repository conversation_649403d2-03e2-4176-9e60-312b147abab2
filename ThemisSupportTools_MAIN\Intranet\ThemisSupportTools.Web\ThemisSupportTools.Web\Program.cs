using Core.Themis.Libraries.BLL.Extentions.ServicesBuilder;
using Core.Themis.Libraries.BLL.Middleware.ThemisSupportTools;
using Microsoft.AspNetCore.Authentication.Negotiate;
using ThemisSupportTools.Web.Components;
using Microsoft.AspNetCore.Components.Server.Circuits;
using Microsoft.Extensions.DependencyInjection.Extensions;
using Core.Themis.Libraries.BLL.Services.Access;
using Core.Themis.Libraries.BLL.Services;
using Microsoft.AspNetCore.Localization;
using System.Globalization;
using Microsoft.Extensions.Options;
using BlazorBootstrap;
using Core.Themis.Libraries.Data.DBContext;
using Core.Themis.Libraries.Data.DBContext.Interfaces;
using Microsoft.Extensions.DependencyInjection;
using Core.Themis.Libraries.BLL.Services.RedisCache.Interfaces;
using Core.Themis.Libraries.BLL.Services.RedisCache;
using Microsoft.Extensions.Caching.Distributed;
using StackExchange.Redis;



var builder = WebApplication.CreateBuilder(args);



builder.Services.AddAuthentication(options =>
{
    options.DefaultScheme = NegotiateDefaults.AuthenticationScheme;

})
    .AddNegotiate();


// In-Memory Caching
builder.Services.AddMemoryCache();
builder.Services.AddHttpContextAccessor();

//// Rodrigue injection
builder.Services.AddRodrigueDataServices();
builder.Services.AddRodrigueManager();
builder.Services.AddRodrigueMapper();
builder.Services.AddRazorPages();


builder.Services.InitHelpers();
builder.Services.ConfigureHelpers();
                    


builder.WebHost.UseStaticWebAssets();


builder.Services.AddSingleton<ITstAccessService, TstAccessService>();

builder.Services.AddScoped<CircuitHandler, CustomCircuitHandler>();
builder.Services.AddScoped<CustomCircuitService>();



builder.Services.AddScoped<IAuthorizationMiddlewareResultHandler, ThemisSupportToolsAuthorizationMiddlewareResultHandler>();
builder.Services.AddScoped<UserService>();
builder.Services.TryAddEnumerable(
    ServiceDescriptor.Scoped<CircuitHandler, UserCircuitHandler>());

builder.WebHost.UseStaticWebAssets();
//builder.Services.AddCascadingAuthenticationState();
//builder.Services.AddTransient<IAuthorizationMiddlewareResultHandler, ThemisSupportToolsAuthorizationService>();

// Ajoute explicitement ToastService
builder.Services.AddSingleton<ToastService>();

builder.Services.AddBlazorBootstrap();

#region Redis cache
string instanceName = "Rodrigue_cache_" + Environment.GetEnvironmentVariable("ASPNETCORE_ENVIRONMENT") + ".";
builder.Services.AddRodrigueRedis(builder.Configuration.GetConnectionString("Redis")!, instanceName);

builder.Services.AddSingleton<IRedisCacheService>(sp =>
    new RedisCacheService(
        instanceName,
        sp.GetRequiredService<IDistributedCache>(),
        sp.GetRequiredService<IConnectionMultiplexer>()
    )
);
#endregion


builder.Services.AddLocalization();
builder.Services.Configure<RequestLocalizationOptions>(options =>
{
    var supportedCultures = new List<CultureInfo>
    {
       new CultureInfo("fr"),
       new CultureInfo("de"),
       new CultureInfo("en")
    };

    options.SetDefaultCulture("fr");
    options.SupportedCultures = supportedCultures;
    options.SupportedUICultures = supportedCultures;
});

// Add services to the container.
builder.Services.AddRazorComponents()
    .AddInteractiveServerComponents();

var app = builder.Build();


// Configure the HTTP request pipeline.
if (!app.Environment.IsDevelopment())
{
    app.UseExceptionHandler("/Error", createScopeForErrors: true);
    // The default HSTS value is 30 days. You may want to change this for production scenarios, see https://aka.ms/aspnetcore-hsts.
    app.UseHsts();
}

// Configurer la localisation
//var supportedCultures = new[]
//{
//    new CultureInfo("fr"),
//    new CultureInfo("de"),
//    new CultureInfo("en")
//};

//app.UseRequestLocalization(new RequestLocalizationOptions
//{
//    DefaultRequestCulture = new RequestCulture("fr"),
//    SupportedCultures = supportedCultures,
//    SupportedUICultures = supportedCultures,
//    ApplyCurrentCultureToResponseHeaders = true
//});




app.UseAuthorization();
app.UseAuthentication();
app.UseHttpsRedirection();


app.UseRequestLocalization(app.Services.GetRequiredService<IOptions<RequestLocalizationOptions>>().Value);

app.MapGet("Culture/Set", (string culture, string redirectUri, HttpContext context) =>
{
    if (culture is not null)
    {
        context.Response.Cookies.Append(CookieRequestCultureProvider.DefaultCookieName,
            CookieRequestCultureProvider.MakeCookieValue(new RequestCulture(culture, culture)),
            new CookieOptions { Expires = DateTimeOffset.Now.AddDays(30) });
    }

    return Results.LocalRedirect(redirectUri);
});

app.UseStaticFiles();
app.UseAntiforgery();

app.MapRazorComponents<App>()
    .AddInteractiveServerRenderMode();

app.Run();
