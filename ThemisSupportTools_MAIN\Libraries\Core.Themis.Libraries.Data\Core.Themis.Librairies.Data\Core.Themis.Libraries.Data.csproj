<Project Sdk="Microsoft.NET.Sdk">
  <PropertyGroup>
    <SSDTUnitTestPath Condition="'$(SSDTUnitTestPath)' == ''">$(VsInstallRoot)\Common7\IDE\Extensions\Microsoft\SQLDB</SSDTUnitTestPath>
  </PropertyGroup>
  <PropertyGroup>
    <SSDTPath Condition="'$(SSDTPath)' == ''">$(VsInstallRoot)\Common7\IDE\Extensions\Microsoft\SQLDB\DAC</SSDTPath>
  </PropertyGroup>
  <PropertyGroup>
    <VisualStudioVersion Condition="'$(VisualStudioVersion)' == ''">10.0</VisualStudioVersion>
  </PropertyGroup>
  <PropertyGroup Label="Globals">
    <SccProjectName>SAK</SccProjectName>
    <SccProvider>SAK</SccProvider>
    <SccAuxPath>SAK</SccAuxPath>
    <SccLocalPath>SAK</SccLocalPath>
  </PropertyGroup>
  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <Nullable>enable</Nullable>
  </PropertyGroup>
  <ItemGroup>
    <Compile Remove="Entities\Structures\**" />
    <Compile Remove="Repositories\HomeModular\**" />
    <Compile Remove="Repositories\NewFolder1\**" />
    <Compile Remove="Repositories\NewFolder2\**" />
    <Compile Remove="Repositories\NewFolder3\**" />
    <Compile Remove="Repositories\Structures\**" />
    <EmbeddedResource Remove="Entities\Structures\**" />
    <EmbeddedResource Remove="Repositories\HomeModular\**" />
    <EmbeddedResource Remove="Repositories\NewFolder1\**" />
    <EmbeddedResource Remove="Repositories\NewFolder2\**" />
    <EmbeddedResource Remove="Repositories\NewFolder3\**" />
    <EmbeddedResource Remove="Repositories\Structures\**" />
    <None Remove="Entities\Structures\**" />
    <None Remove="Repositories\HomeModular\**" />
    <None Remove="Repositories\NewFolder1\**" />
    <None Remove="Repositories\NewFolder2\**" />
    <None Remove="Repositories\NewFolder3\**" />
    <None Remove="Repositories\Structures\**" />
  </ItemGroup>
  <ItemGroup>
    <Compile Remove="Entities\CouponsPromo\CouponsPromo.cs" />
  </ItemGroup>
  <ItemGroup>
    <None Remove="libraries_SQLScripts\Exports\ExportLieusSeancesFutures.sql" />
    <None Remove="libraries_SQLScripts\planSalle\loadPlanSalleAvailabilitiesParam.sql" />
  </ItemGroup>
  <ItemGroup>
    <Content Include="libraries_SQLScripts\Exports\ExportLieusSeancesFutures.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Content>
  </ItemGroup>
  <ItemGroup>
    <PackageReference Include="Dapper" Version="2.1.35" />
    <PackageReference Include="Microsoft.Data.SqlClient" Version="5.2.2" />
    <PackageReference Include="Microsoft.EntityFrameworkCore" Version="9.0.0" />
    <PackageReference Include="Microsoft.Extensions.Configuration.Abstractions" Version="9.0.0" />
    <PackageReference Include="StackExchange.Redis" Version="2.8.41" />
    <PackageReference Include="Swashbuckle.AspNetCore.Annotations" Version="7.2.0" />
    <PackageReference Include="System.Data.SqlClient" Version="4.9.0" />
    <PackageReference Include="System.Text.Json" Version="9.0.5" />
  </ItemGroup>
  <ItemGroup>
    <Folder Include="libraries_SQLScripts\OpenPartner\" />
    <Folder Include="Repositories\WSAdmin\Utilities\" />
    <Folder Include="StoredProcedure\WebLibrary\" />
    <Folder Include="StoredProcedure\WebTracing\" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\..\Core.Themis.Libraries.DTO\Core.Themis.Libraries.DTO.csproj" />
    <ProjectReference Include="..\..\Core.Themis.Libraries.Utilities\Core.Themis.Libraries.Utilities\Core.Themis.Libraries.Utilities.csproj" />
  </ItemGroup>
  <ItemGroup>
    <Content Include="libraries_SQLScripts\planSalle\loadPlanSalleAvailabilitiesParam.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Content>
  </ItemGroup>
  <ItemGroup>
    <None Include="bin\Debug\net8.0\libraries_SQLScripts\GestionPlaces\getGpsByIds.sql" />
  </ItemGroup>
  <ItemGroup>
    <Compile Update="Repositories\Open\ParamsDeflag\Interfaces\IParamsDeflagRepository.cs">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Compile>
  </ItemGroup>
  <ItemGroup>
    <None Update="libraries_SQLScripts\Abonnement\GetAllFormulasWithTranslations.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\Abonnement\GetCatalogFormuleAboByFilter.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\Abonnement\GetFormuleAbonnement.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\Adhesions\checkAdhesionToConsumer.146.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\Adhesions\checkAdhesionToConsumer.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\Adhesions\checkAdhesionToConsumerProduct.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\Adhesions\getAdherentIdByIdentityId.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\Adhesions\getAdhesionCatalogsAndProperties.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\Adhesions\getAdhesionCatalogsOfIdentity.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\Adhesions\getAdhesionFromGpId.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\Adhesions\getAdhesionOfIdentity.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\Adhesions\getDetailAdhesionOfIdentity.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\Adhesions\insertAdhesionDossierEntree.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\BarCode\GetBarCodeManif.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\BarCode\GetBarCodeByBarCodeAndColEmail.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\BarCode\SetBarCode.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\Boutique\GetAllFamilliesProducts.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\Boutique\GetBoutiqueProductById.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\Boutique\GetFamilleById.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\Boutique\GetSousFamilleById.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\Boutique\LoadFamiliesSubFamiliesProducts.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\Categories\GetCategoriesForSessions.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\Cinema\GetTranslationOfDisciplineByIdAndLangCode.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\Consumers\createConsumer.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\BuyerProfil\getBuyerProfilById.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\BuyerProfil\getBuyerProfilByLoginPassw.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\Cinema\LoadDisciplineByEventId.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\Cinema\LoadLocalisationsBySessionId.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\CoupeFile\loadOrdersOfInternetUser.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\CrossSelling\getCrossSelling.146.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\CrossSelling\getCrossSelling.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\DepotVente\depotVente_Get.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\DepotVente\depotVente_getMontantReprise.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\DepotVente\depotVente_IsAutorise.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\DepotVente\depotVente_push.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\Distanciation\getDistanciation.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\Distanciation\updateSeatsDistanciation.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\Cinema\GetTranslationOfLocalisationByIdAndLangCode.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\EventsSessions\EventsGroups\GetAllEventsGroupsByEventTypeId.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\EventsSessions\EventsGroups\GetAllEventsGroupWithEvents.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\EventsSessions\EventsGroups\GetAllEventsGroupWithEventsForSelect.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\EventsSessions\EventsGroups\GetEventGroupTranslationById.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\EventsSessions\EventsGroups\GetEventsGroupsByEventTypesIdAndGenresId.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\EventsSessions\EventsGroups\GetEventsGroupsByEventTypesId.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\EventsSessions\EventsGroups\GetEventsGroupsByEventTypesIdAndTargetsId.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\EventsSessions\EventsGroups\GetEventsGroupsOnSaleByEventsTypes.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\EventsSessions\EventsGroups\GetEventsTypeOnSale.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\EventsSessions\Genres\GetAllGenresByEventTypeId.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\EventsSessions\Genres\GetEventsGenreByEventSubGenreId.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\EventsSessions\Genres\GetEventsGenreOnSaleByEventsTypes.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\EventsSessions\Genres\GetGenresByEventTypesId.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\EventsSessions\Genres\GetGenresByEventTypesIdAndEventGroupsId.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\EventsSessions\Genres\GetGenresByEventTypesIdAndTargetsId.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\EventsSessions\GetCatalogEventsByFilter.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\EventsSessions\getEventGenreById.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\EventsSessions\getEventGroupById.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\EventsSessions\GetManifestationImageByEventId.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\EventsSessions\getSessionsEntitiesInAnInterval.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\EventsSessions\getEventSessionOfSession.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\EventsSessions\GetEventsGeneralsAndAdhesionsOfferByFilter.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\EventsSessions\getEventsGroupsOfEventsSales.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\EventsSessions\getEventsInfos.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\EventsSessions\getEventsInAnInterval.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\EventsSessions\GetEventsOnSale.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\EventsSessions\GetEventsOnSaleIntoAdhesionOffer.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\EventsSessions\GetAllEventsAfterADate.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\EventsSessions\getIndivScheduleByIdentitePA.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\EventsSessions\GetInfosByEventId.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\EventsSessions\GetInfosEventForHomeModular.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\EventsSessions\GetInfosOfEventByIdAndLangCode.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\EventsSessions\GetInfosOfEventByIdAndLangCodeWithDependencies.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\EventsSessions\getSessionsInfos.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\EventsSessions\getSubGenreById.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\EventsSessions\loadEventsOfThisFormulaWithTranslation.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\EventsSessions\loadPricesGridOfEvents.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\EventsSessions\SousGenres\GetSubGenresByEventsTypesId.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\EventsSessions\SousGenres\GetSubGenresByEventTypesIdAndEventGroupsId.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\EventsSessions\SousGenres\GetSubGenresByEventTypesIdAndGenresId.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\EventsSessions\SousGenres\GetSubGenresByEventTypesIdAndTargetsId.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\EventsSessions\GetTranslationEventInfos.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\EventsSessions\GetTranslationOfEventByIdAndLangCode.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\EventsSessions\GetTranslationOfLockManifByEventId.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\EventsSessions\GetTranslationOfWaitingListById.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\EventsSessions\GetTranslationOfWaitingListByEventIdAndLangCode.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\EventsSessions\GetTranslationOfLockByEventIdAndLangCode.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\EventsSessions\InsertLowestPrice.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\EventsSessions\InsertLowestPriceAbo.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\EventsSessions\InsertLowestPriceManif.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\EventsSessions\loadEventsFeatures - Copie.sql">
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\EventsSessions\loadEventsFeatures.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\EventsSessions\SousGenres\GetTranslationOfSousGenreAndLangCode.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\Exports\ExportDetailBilletsByEvent.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\Exports\ExportEtatBilletterie.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\Exports\exportsVardar.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\Exports\ExportsVardarAccessControl.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\Exports\ExportVardarAllEventsSessionsPlaces.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\GestionPlaces\GetAllAdhesionsByEventIdAndSessionId.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\GestionPlaces\getGrilleTarifs_BigOneV2.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\GestionPlaces\GetTTPublicByEventIdAndSessionId.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\GestionPlaces\GetAllByEventIdAndSessionIdAndOffersId.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\GestionPlaces\GetDispoFromGestionPlacesWithOffer.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\GestionPlaces\GetGestionPlaceTypeEnvoiByEventSeddionCategTypeTarif.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\GestionPlaces\GetGestionPlaceWithOfferBySessionIdAndCategoryId.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\GestionPlaces\getGrilleTarifs_BigOne.744.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\GestionPlaces\getGrilleTarifs_BigOne.614.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\GestionPlaces\getMObentionByProductsIds.412.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\GestionPlaces\GetReserveIdsFromGestionPlacesWithOffer.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\GestionPlaces\PanierTransformatorAddSupp.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>

    <None Update="libraries_SQLScripts\GestionPlaces\PanierTransformatorAdhesion.661.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\GestionPlaces\PanierTransformatorAdhesion.347_saison2324.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\GestionPlaces\getMObentionByProductsIds.655.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\GpManifestation\CreateColumnsForWaitingListAndTraduction.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\GpSeance\CreateColumnTraductionForLock.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\Identity\UpdatePasswordIdentity.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\infoComp\GetVisibleInfoCompGroupsOrderedByPrefAffichage.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\infoComp\InsertInfoCompForEmailsGroup.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\Maquettes\GetDefaultMaquette.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\Maquettes\GetFontName.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\LogsPartenaires\getLogPartnerById.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\Maquettes\GetVisibleWebMaquettes.sql">
    <CopyToOutputDirectory>Always</CopyToOutputDirectory>
		<CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
	  <None Update="libraries_SQLScripts\Offers\GetOffersLinkedToBuyeurProfil.sql">
	  </None>
    <None Update="libraries_SQLScripts\Offers\GetOffersLinkedToBuyerProfilRevendeur.sql">
    
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\Offers\GetOffresProfilAcheteur.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\OpenPartner\CheckPartnerIsValidRevender.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\Order\GetAllReservationOrderAvailableForPassCultureCollectiveOffer.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\Order\GetAllReservationUpdatablePassCultureCollectiveOffer.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\Order\GetOrderIdsByBarcodes.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\Partner\updatePartnerStructuresLink.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\Partner\updatePartnerRoleLink.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\Products\GetAllFamily.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\Products\GetAllProductColorsAvailable.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\Products\GetAllProductFamily.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\Products\GetAllProductFamilyKeyValues.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\Products\GetAllProductSizesAvailable.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\Products\GetAllSubFamilyByProductFamilyId.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\Products\GetCatalogProductsByFilter.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\Products\GetAllProductsWithTranslations.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\Products\GetProductColorsAvailableByProductFamilyIds.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\Products\GetProductColorsAvailableByProductSubFamilyIds.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\Products\GetProductSizesAvailableByProductFamilyIds.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\Products\GetProductSizesAvailableByProductSubFamilyIds.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\Products\GetAllFamily.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\Products\GetAllProductFamily.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\Products\GetAllProductFamilyKeyValues.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\Products\GetAllSubFamilyByProductFamilyId.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\Products\GetAllProductsWithTranslations.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\Seats\UpdateEntreeControleAccess.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\Session\GetSessionsByFormulaId.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\Targets\GetAllTargetsByEventTypeId.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\Session\GetSessionsByFormulaId.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\Targets\GetAllTargetsByEventTypeId.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\Targets\GetTargetsByEventTypesId.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\Targets\GetTargetsByEventTypesIdAndEventGroupsId.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\Targets\GetTargetsByEventTypesIdAndGenresId.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\Targets\GetTranslationOfTargetByIdAndLangCode.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\Targets\LoadTargetsBySessionId.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\Targets\LoadTargetsOnSaleByEventsTypes.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\EventsSessions\loadEventsGroups.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\EventsSessions\loadGenreSousGenre.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\EventsSessions\loadSessionsPricesOfEvents.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\EventsSessions\loadSessionsZoneFloorSectionCategPricesOfEvents.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\EventsSessions\ProprertiesOfManifs\DeletePropertieOfManif.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\EventsSessions\ProprertiesOfManifs\GetPropertieOfManifsByManifIdAndRefCode.sql">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\EventsSessions\ProprertiesOfManifs\getPropertieRefIdByCode.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\EventsSessions\ProprertiesOfManifs\InsertPropertieOfManif.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\EventsSessions\ProprertiesOfManifs\UpdatePropertieOfManif.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\EventsSessions\ProprertiesReferences\GetPropertieReferenceOfManifsByRefCode.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\EventsSessions\SousGenres\GetSousGenresOnSaleByGenre.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\Exports\exportCustomersInfoCompChanges.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\Exports\exportCustomersChanges.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\Exports\ExportEtatBilletterieEmis.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\Exports\exportEventsGauges.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\Exports\ExportEventsGaugev0.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\Exports\ExportEventsPriceGridBCubix.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\Exports\ExportEventsPriceGridReelax.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\Exports\exportGlobalMoves.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\Exports\ExportMouvementsProducts.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\Exports\ExportMouvementsSeats.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\Exports\ExportRestantBySeanceCategReserve.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\Exports\ExportsRecetteByEventSession.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\Exports\SoftwareAGgetDatas.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\Exports\SoftwareAGupdateDatas.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\Exports\SoftwareAGupdateDatasV2.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\Exports\TestGetManifByGroupId.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\GestionPlaces\dispoBySeanceCategForOperateur.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\GestionPlaces\GetAllByEventIdAndSessionId.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\GestionPlaces\GetGestionPlaceById.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\GestionPlaces\getGpsByIds.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\GestionPlaces\getGrilleTarifs.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\GestionPlaces\getGrilleTarifs_BigOne.553.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\GestionPlaces\getGrilleTarifs_BigOne.146.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\GestionPlaces\---getGrilleTarifs_BigOne.218.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\GestionPlaces\getGrilleTarifs_BigOne.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\GestionPlaces\getIndivPricesCategs.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\GestionPlaces\getIndivSessions.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\GestionPlaces\getIndivSessionsByIdentitePA.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\GestionPlaces\GetInfosOfSessionsByIdentitePA.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\GestionPlaces\getLowerPriceOfEvent.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\GestionPlaces\getMObentionByGpsIds.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\GestionPlaces\getMObentionByProductsIds.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\Offers\getOffersByIdentitePA.146.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\Offers\getOffersByIdentitePA.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\GestionPlaces\loadGestionPlaceTypeEnvoi.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\GestionPlaces\loadTarifCommentaires.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\GestionPlaces\PanierTransformatorAdhesion.150.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\GestionPlaces\PanierTransformatorAdhesion.218.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\GestionPlaces\PanierTransformatorAdhesion.347.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\GestionPlaces\PanierTransformatorAdhesion.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\GestionPlaces\èèPanierTransformatorAdhesion.146.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\HomeModular\Clients\checkHomeModularBlockUserConfig.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\HomeModular\Clients\deleteHomeModularBlockEmplacement.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\HomeModular\Clients\GetAllEmplacementGroups.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\HomeModular\Clients\GetBlockEmplacementByBlockTypeId.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\HomeModular\Clients\GetHomeModularBlockEmplacementById.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\HomeModular\Clients\getHomeModularBlockEmplacementList.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\HomeModular\Clients\getHomeModularBlockUserConfigList.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\HomeModular\Clients\getHomeModularEmplacementGroupByIdWithDependencies.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\HomeModular\Clients\getHomeModularEmplacementGroupList.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\HomeModular\Clients\insertHomeModularBlockEmplacement.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\HomeModular\Clients\insertHomeModularBlockUserConfig.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\HomeModular\Clients\LoadProductsFeatures - Copier.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\HomeModular\Clients\LoadProductsFeatures.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\HomeModular\Clients\UpdateBlockUserConfigValuesTemp.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\HomeModular\getBlockFunctionByIdWithDependencies.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\HomeModular\GetBlockFunctionsByBlockTypeIdWithDependencies.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\HomeModular\GetFunctionsOfBlockTypeById.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\HomeModular\getHomeModularBlockTypeList.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\HomeModular\getHomeModularFunctionsList.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\Identity\AddConsumer.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\Identity\AddIdentiteComplement.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\Identity\AddIdentiteComplementWeb.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\Identity\AddIdentity - Copie.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\Identity\AddIdentity.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\Identity\AddInfoCompOnIdentite.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\Identity\GetConsommateurs.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\Identity\getIdentiteForPatHome.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\Identity\GetIdentity.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\Identity\GetIdentity_Unidy.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\Identity\GetIdIdentiteComplementWebPassword.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\Identity\GetLibelleTels.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\Identity\LinkConsumer.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\Identity\SearchConsumer.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\Identity\SelectCiblesFromListIdentite.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\Identity\UpdateIdentity.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\infoComp\DeleteInfoCompFromEmails.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\infoComp\DeleteInfoCompFromIdentiteIds.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\infoComp\InsertInfoComp.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\infoComp\InsertInfoCompForEmails.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\infoComp\PushInfoCompsOfIdentite.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\Insurance\ActivateInsurance.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\Insurance\createGlobalInsuranceOrder.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\Insurance\HasInsuranceProduct.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\Languages\getLanguage.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\Maquettes\GetById.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\Maquettes\getMaquetteCoupeFile.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\Maquettes\loadMaquette.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\OpinionOrder\--selectOrderIdOpen.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\OpinionOrder\Average\selectOpinionOrderAverage.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\OpinionOrder\Average\selectOpinionOrderAverageGraph.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\OpinionOrder\isOpinionConfigured.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\OpinionOrder\isOpinionManaged.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\OpinionOrder\Order\getOrdersFromIds.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\OpinionOrder\Questions\getOpinionOrderList.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\OpinionOrder\Responses\createBlankEvaluationsForm.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\OpinionOrder\Responses\createOpinionOrderResponses.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\OpinionOrder\Responses\getBlanksEvaluationsFormsList.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\OpinionOrder\Responses\getNonUpdatedEvaluationsFormsList.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\OpinionOrder\Responses\getOpinionOrderList.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\OpinionOrder\Responses\updateEvaluationsForm_setDemandeAvisEnvoye.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\OpinionOrder\Responses\updateEvaluationsForm_setInfos.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\OpinionOrder\Responses\updateEvaluationsForm_setOrderId.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\OpinionOrder\Responses\updateOpinionOrderForms.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\Order\CancelDossier.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\Order\CancelEntrees.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\Order\cancelOrder.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\Order\CancelOrderMiseEnAcompte.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\Order\cancelOrderOld.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\Order\CancelOrderReservation.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\Order\createCommandeLigne.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\Order\createSeatDossier.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\Order\createSeatDossierHisto.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\Order\createSeatDossierSvg.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\Order\DeleteEntreeComplement.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\Order\downgradeorder.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\Order\GetAllEntreesOfIdentiteById.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\Order\GetAllEntreesEditeesOfOrderById.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\Order\GetAllOrdersByIdentiteId.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\Order\GetAllOrderWithoutReservationByIdentiteId.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\Order\GetAllReservationByIdentiteId.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\Order\getCommandesLignes.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\Order\getDossiersProductFromOrderId.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\Order\getDossiersSeatsFromOrderId.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\Order\GetEntreeByCriteria.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\Order\getEventSessionPrice.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\Order\GetOrderById.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\Order\GetOrderByIdForInsurance.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\Order\getOrderDetailFromId.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\Order\getOrderFromId.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\Order\getOrderFromId_Buyer.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\Order\getOrdersFromIds.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\Order\getReservationsOfIdentity.224.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\Order\getReservationsOfIdentity.281.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\Order\getReservationsOfIdentity.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\Order\InsertEntreeComplement.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\Order\updatecommandeLigne_icone.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\Order\updateDossier_dossierC.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\Partner\createPartner.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\Partner\createPartnerStructureLink.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\Partner\deletePartnerStructureLink.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\Partner\getPartner.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\Partner\GetPartnerById.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\Partner\GetPartnerByName.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\Partner\getPartnerId.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\Partner\getPartnerRoles.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\Partner\getPartnersList.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\Partner\getStructuresOfPartner.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\Partner\updatePartner.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\Places\getFloorsFromFloorIds.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\Places\getPlacesFromSessionsIds.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\Places\getSectionsFromSectionIds.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\Places\getZonesFromZoneIds.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\planSalle\loadAllAlotissements.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\planSalle\loadAllCategs.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\planSalle\loadAllCategsBySession.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\planSalle\loadAllContingents.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\planSalle\loadAllDenominations.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\planSalle\loadAllFloors.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\planSalle\loadAllReserves.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\planSalle\loadAllSections.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\planSalle\loadAllTribunesGatesAcces.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\planSalle\loadAllZones.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\planSalle\loadPlanSalle.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\planSalle\loadPlanSalleParam.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\planSalle\loadPlanSalleParamIndispo.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\planSalle\loadPlanSalleTexts.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\planSalle\loadPlanSalleTextsParam.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\Products\GetAllProducts.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\Products\GetAllProductsInternet.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\Products\GetAssuranceProduct.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\Products\GetFraisDossierByFiliere.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\Products\getMaquettesProductsByMo.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\Products\GetProductById.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\Products\getProductForPatHome.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\Products\getProductForPatHomeCore.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\Products\LoadFamiliesSubFamiliesProductsEvent.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\Products\LoadFamiliesSubFamiliesProductsGlobaux.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\Products\LoadFamiliesSubFamiliesProductsSession.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\Recettes\getRecettes.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\Recettes\getRecettesFromBarCode.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\Recettes\getRecettesProducts.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\Recettes\insertLigneRecetteBillet.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\Recettes\setBarCodeRecette.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\ReservationOrder\createReservationOrder.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\ReservationOrder\createReservationScheduleReminder.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\ReservationOrder\getInfoResaForBasket.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\ReservationOrder\getReservationsOfIdentity.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\ReservationOrder\getReservationsReminder.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\ReservationOrder\isResa_alive.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\ReservationOrder\reservationEvents.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\ReservationOrder\updateReservationOrder.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\ReservationOrder\updateScheduleReservationOrder_setEnvoye.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\Reserves\reservesOfOperateur.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\SaleChannel\getSaleChannelList.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\SaleChannel\selectSaleChannelOpen.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\SaleRulesAndCo\getBuyerProfil.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\Seats\FlagAuto.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\Seats\flagSeat.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\Seats\GetAllEntreesEditeesBySeanceIdAndIdentiteId.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\Seats\getSeatForPatHome.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\Seats\getSeats.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\Seats\getSeats_lang.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\Seats\unflagSeats.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\Seats\UpdateEntreeReservation.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\Seats\UpdateEntreesPayees.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\Session\GetAllSessionsByEventIdOnSale.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\Session\GetAllSessionsTicketsByIdentiteId.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\Session\GetAllSessionsByEventId.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\Session\GetSessionById.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\Sponsors\GetCardsToSend.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\Sponsors\GetSponsoredAmount.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\Sponsors\GetSponsors.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\Sponsors\GetSponsorUsedRemaining.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\Sponsors\SetDateSent.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\Sponsors\SetDateSentNull.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\Structures\open_getStructureInfos.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\ThemisSupportTools\InsertTstAccess.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\Translations\Clients\--getSpecificTranslationsParent.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\Translations\Clients\GetSpecificClientTranslations.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\Translations\Clients\getSpecificTranslations.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\Translations\Clients\getSpecificTranslationsParent.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\Translations\Clients\GetTranslationByKeyword.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\Translations\Clients\GetTranslationsLikeKeyword.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\Translations\Clients\insertSpecificTranslations.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\Translations\Rodrigue\--updateTranslationsFieldVariable.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\Translations\Rodrigue\checkTranslationsFieldVariable.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\Translations\Rodrigue\deleteDefaultTranslation.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\Translations\Rodrigue\DeleteTranslateFieldsCodesList.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\Translations\Rodrigue\deleteTranslationsArea.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\Translations\Rodrigue\deleteTranslationsFieldVariable.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\Translations\Rodrigue\deleteTranslationsVariable.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\Translations\Rodrigue\GetGlobalFieldsTranslations.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\Translations\Rodrigue\GetGlobalTranslationById.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\Translations\Rodrigue\getGlobalTranslations.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\Translations\Rodrigue\GetTranslateFieldsCodesList.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\Translations\Rodrigue\GetTranslationByKeyword.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\Translations\Rodrigue\GetTranslationLikeKeyword.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\Translations\Rodrigue\getTranslationsAreasList.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\Translations\Rodrigue\getTranslationsFieldCodeList.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\Translations\Rodrigue\getTranslationsFields.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\Translations\Rodrigue\gettranslationsFieldsCode.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\Translations\Rodrigue\getTranslationsGlobalFields.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\Translations\Rodrigue\getTranslationsTermsAndValues.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\Translations\Rodrigue\getTranslationsVariablesList.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\Translations\Rodrigue\InsertTranslateArea.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\Translations\Rodrigue\InsertTranslateFieldsCodesList.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\Translations\Rodrigue\InsertTranslateVariables.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\Translations\Rodrigue\insertTranslations.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\Translations\Rodrigue\insertTranslationsArea.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\Translations\Rodrigue\insertTranslationsFieldCode.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\Translations\Rodrigue\insertTranslationsFieldVariable.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\Translations\Rodrigue\insertTranslationsVariable.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\Translations\Rodrigue\updateTranslations.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\Translations\Rodrigue\updateTranslationsArea.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\Translations\Rodrigue\updateTranslationsFieldCode.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\Translations\Rodrigue\updateTranslationsVariable.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\TST\CouponsPromo\getCouponsPromo.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\TST\Deflag\insertParamDeflag.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\TST\GenerateReferenceSalleImageXml\generateReferenceSalleImageXml.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\TST\GenerateReferenceSalleImageXml\generateReferenceSalleImageXmlByLieuPhysique.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\TST\GenerateReferenceWebVignetteXml\generateReferenceWebVignetteXml.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\TST\GenerateReferenceWebVignetteXml\generateReferenceWebVignetteXmlByLieuPhysique.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\TST\ListePlanAvecImage\listePlanAvecImage.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\TST\ListePlanSansImage\listePlanSansImage.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\TST\TransferePointagePhoto\transferePointagePhoto.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\TST\TransfereVignette\transfereVignetteAsync.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\ValeurTarifStock\GetBySessionAndCategsId.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\ValeurTarifStock\getValeurTarifStock.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\WT\Baskets\AddPaiementAction.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\WT\Baskets\AddProductResa.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\WT\Baskets\AddSeatsIndiv.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\WT\Baskets\CheckGpIdEnCours.150.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\WT\Baskets\CheckGpIdEnCours.218.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\WT\Baskets\CheckGpIdEnCours.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\WT\Baskets\CheckProductAdhesionEnCours.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\WT\Baskets\DeleteAllProductCarteAdhesion.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\WT\Baskets\DeleteAllProductEventSessionGlobal.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\WT\Baskets\DeleteAllSessionsPanierEntree.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\WT\Baskets\DeleteMObentionOnBasketId.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\WT\Baskets\DeletePanierProduit.412.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\WT\Baskets\DeletePanierProduit.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\WT\Baskets\DeletePanierProduit_prodId.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\WT\Baskets\deleteSeatInBasketLine.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\WT\Baskets\GetAllOrderIdByBuyerProfilIdAndSessionId.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\WT\ServicesAsynch\getLogsEtapesCreationActions.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\WT\ServicesAsynch\getLogsEtapesCreationCmd.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\WT\Baskets\getBasketsWebUsers.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\WT\Baskets\getBaskets.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\WT\Baskets\getBasketsFromOrdersId.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\WT\Baskets\GetCartesAdhesionEnCours.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\WT\Baskets\GetGpIdEnCours.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\WT\Baskets\getPaiementActions_baskets.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\WT\Baskets\getPaiementActions.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\WT\Baskets\GetPanierFromWebUser.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\WT\Baskets\InsertPanierProduit.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\WT\Baskets\InsertPanierProduitCarteAdhesionProps.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\WT\Baskets\UpdateConsumerEntrees.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\WT\Baskets\UpdateEtatPanier.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\WT\Baskets\UpdateEtatPanierStateToState.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\WT\Baskets\UpdateTypeEnvoiAndMaquette.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\WT\Baskets\UpdateTypeEnvoiAndMaquette_products.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\WT\SaleDatas\getSaleDatas.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\WT\ServicesAsynch\fillInfosWTDataBase.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\WT\ServicesAsynch\getPaniersToDo_createcmd.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\WT\ServicesAsynch\getPaniersToDo_createcmdBasketId.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\WT\ServicesAsynch\getPaniersToDo_reflag.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\WT\ServicesAsynch\getPaniersToDo_reflagBasketId.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\WT\ServicesAsynch\getPaniersToInform.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\WT\ServicesAsynch\getPaniersToSendEmailError.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\WT\ServicesAsynch\getPanierToDo_straight.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\WT\ServicesAsynch\insertLogEtapeCommande.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\WT\ServicesAsynch\insertLogEtapeInformEmail.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\WT\ServicesAsynch\RestartStepPanier.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\WT\ServicesAsynch\setEmailErrorEnvoye.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\WT\ServicesAsynch\updatePanierToDo_straight.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\WT\Sponsors\CheckSponsorPanierEntreesExist.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\WT\Sponsors\GetAllSponsorPanierEntrees.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\WT\Sponsors\GetSponsorReferenceOfMyBasket.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\WT\Sponsors\GetSponsorReferenceOfOtherBasket.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\WT\Sponsors\GetSponsorsPanierEntree.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\WT\Sponsors\InsertSponsorPanierEntree.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\WT\WebUsers\GetLogsWebUserId.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\WT\WebUsers\GetWebUsersCount.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\WT\WebUsers\GetWebUsers.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\WT\WebUsers\GetWebUser.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="libraries_SQLScripts\WT\WebUsers\InsertWebUser.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
  </ItemGroup>
  <ItemGroup>
    <None Update="libraries_SQLScripts\WT\WebUsers\UpdateCouponPromo.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
  </ItemGroup>
  <ItemGroup Condition="$(VisualStudioVersion) == '17.0'">
    <Reference Include="Microsoft.Data.Tools.Schema.Sql, Version=********, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>$(SSDTPath)\Microsoft.Data.Tools.Schema.Sql.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="Microsoft.Data.Tools.Schema.Sql.UnitTesting, Version=********, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>$(SSDTUnitTestPath)\Microsoft.Data.Tools.Schema.Sql.UnitTesting.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="Microsoft.Data.Tools.Schema.Sql.UnitTestingAdapter, Version=********, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>$(SSDTUnitTestPath)\Microsoft.Data.Tools.Schema.Sql.UnitTestingAdapter.dll</HintPath>
      <Private>True</Private>
    </Reference>
  </ItemGroup>
  <PropertyGroup>
    <SsdtUnitTestVersion>3.1</SsdtUnitTestVersion>
  </PropertyGroup>
  <Import Project="$(SQLDBExtensionsRefPath)\Microsoft.Data.Tools.Schema.Sql.UnitTesting.targets" Condition="$(VisualStudioVersion) != '15.0' And '$(SQLDBExtensionsRefPath)' != ''" />
  <Import Project="$(MSBuildExtensionsPath32)\Microsoft\VisualStudio\v$(VisualStudioVersion)\SSDT\Microsoft.Data.Tools.Schema.Sql.UnitTesting.targets" Condition="$(VisualStudioVersion) != '15.0' And '$(SQLDBExtensionsRefPath)' == ''" />
</Project>