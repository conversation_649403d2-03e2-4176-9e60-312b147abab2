﻿using Core.Themis.Libraries.BLL.adhesion_offres.Interfaces;
using Core.Themis.Libraries.BLL.Interfaces;
using Core.Themis.Libraries.BLL.WT.Interfaces;
using Core.Themis.Libraries.Data.Repositories.Open.EventsSessions.Interfaces;
using Core.Themis.Libraries.DTO;
using Core.Themis.Libraries.DTO.adhesion_offres;
using Core.Themis.Libraries.DTO.EventsSessions;
using Core.Themis.Libraries.DTO.Lookup;
using Core.Themis.Libraries.DTO.ReadyToPrint;
using Core.Themis.Libraries.Utilities.Logging;
using Microsoft.Extensions.Caching.Memory;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text.Json;
using System.Threading.Tasks;

namespace Core.Themis.Libraries.BLL
{
    public class PriceManager : IPriceManager
    {

        private readonly IMemoryCache _memoryCache;
        private readonly IBuyerProfilManager _buyerProfilManager;
        private readonly IBasketManager _basketManager;
        private readonly IEventManager _eventManager;
        private readonly IAdhesionCatalogManager _adhesionCatalogManager;
        private readonly IValeurTarifStockRepository _valeurTarifStockRepository;

        private static readonly RodrigueNLogger RodrigueLogger = new RodrigueNLogger();

        public PriceManager(IMemoryCache memoryCache,
            IBuyerProfilManager buyerProfilManager,
            IBasketManager basketManager,
            IEventManager eventManager,
            IAdhesionCatalogManager adhesionCatalogManager,
            IValeurTarifStockRepository valeurTarifStockRepository)
        {
            _memoryCache = memoryCache;
            _buyerProfilManager = buyerProfilManager;
            _basketManager = basketManager;
            _eventManager = eventManager;
            _adhesionCatalogManager = adhesionCatalogManager;
            _valeurTarifStockRepository = valeurTarifStockRepository;
        }




        public List<EventDTO> LoadPricesGrid(int structureId, string langCode, int eventId, int sessionId, int identityId, int webUserId, int buyerProfilId, string bpLogin, string bpPassword, int basketId)
        {
            int myBprofilId = 0;
            BuyerProfilDTO buyerProfil = _buyerProfilManager.GetBuyerProfilByIdORLoginPassword(structureId, buyerProfilId, bpLogin, bpPassword);
            if (buyerProfil != null)
            {
                myBprofilId = buyerProfil.Id;
            }

            string myGpInBasket = "";
            if (basketId > 0)
            {
                BasketDTO currentBasket = _basketManager.GetBasketById(structureId, basketId);
                var mySeatsThisEvent = (currentBasket.ListAllSeats().Where(s => s.EventId == eventId).ToList());
                if (mySeatsThisEvent.Count == 0) // si je n'ai aucune place de cette manif, pas besoin de checker les places du panier
                {
                    basketId = 0;
                }
                else
                {
                    List<int> listGps = mySeatsThisEvent.Select(s => s.RuleId).ToList();
                    myGpInBasket = string.Join(",", listGps);
                }
            }

            List<EventDTO> listRet = new();
            string cacheName = $"PriceManager.GetEventsSessionsCategsPrices.{structureId}.{langCode}.{identityId}.{eventId}.{sessionId}.{buyerProfilId}.{basketId}";
            if (!_memoryCache.TryGetValue(cacheName, out listRet))// Look for cache key.
            {

                listRet = _eventManager.GetEventsSessionsCategsPrices(langCode, structureId, identityId, eventId, sessionId, myBprofilId, myGpInBasket);


                if (listRet != null)
                {
                    var cacheEntryOptions = new MemoryCacheEntryOptions()
                                 //Priority on removing when reaching size limit (memory pressure)
                                 .SetPriority(CacheItemPriority.High)
                                // Keep in cache for this time, reset time if accessed.                           
                                // Remove from cache after this time, regardless of sliding expiration
                                .SetAbsoluteExpiration(TimeSpan.FromSeconds(60));
                    if (basketId > 0)
                    {
                        cacheEntryOptions = new MemoryCacheEntryOptions()
                                 //Priority on removing when reaching size limit (memory pressure)
                                 .SetPriority(CacheItemPriority.High)
                                // Keep in cache for this time, reset time if accessed.                           
                                // Remove from cache after this time, regardless of sliding expiration
                                .SetAbsoluteExpiration(TimeSpan.FromSeconds(4));
                    }


                    _memoryCache.Set(cacheName, listRet, cacheEntryOptions);
                }
            }




            foreach (var evt in listRet)
            {
                var sessions = evt.ListSessions.SelectMany(s => s.ListZones).ToList();
                var zones = sessions.SelectMany(z => z.ListFloors).ToList();
                var floors = zones.SelectMany(f => f.ListSections).ToList();
                var sections = floors.SelectMany(sect => sect.ListCategories).ToList();

                var gestionPlacesayantOffer = sections.SelectMany(gp => gp.ListGestionPlace.Where(o => o.Offer != null)).Select(gp => gp).ToList();

                if (gestionPlacesayantOffer.Count > 0)
                {

                    //var gestionPlaceWithOffer = gestionPlaces.SelectMany(gp => gp.Offer.AdhesionCatalog.Select(ad => ad.AdhesionCatalogId)).ToList();
                    var gestionPlaceWithOffer = gestionPlacesayantOffer.Where(gp => gp.Offer.AdhesionCatalog != null).Select(g => g).ToList();

                    List<AdhesionCatalogDTO> adhesions = _adhesionCatalogManager.GetAdhesionCatalogs(structureId, gestionPlaceWithOffer.Select(a => a.Offer.AdhesionCatalog.AdhesionCatalogId).Distinct().ToList());

                    foreach (var gp in gestionPlacesayantOffer)
                    {
                        foreach (AdhesionCatalogDTO adhesion in adhesions)
                        {
                            // var adhesionCatalogs = gp.Offer.AdhesionCatalogList.Where(a => a.AdhesionCatalogId == gp.Offer.AdhesionCatalogList.FirstOrDefault().AdhesionCatalogId).ToList();
                            //var adhesionCatalogs = gp.Offer.AdhesionCatalogList.Where(a => a.AdhesionCatalogId == adhesion.AdhesionCatalogId).ToList();
                            //var adhesionCatalogs = gp.Offer.AdhesionCatalog .Where(a => a.AdhesionCatalogId == adhesion.AdhesionCatalogId).ToList();
                            if (gp.Offer.AdhesionCatalog != null && adhesion.AdhesionCatalogId == gp.Offer.AdhesionCatalog.AdhesionCatalogId)
                            {

                                // if (adhesionCatalogs.Count > 0)
                                //{
                                // cette adhesion est la bonne

                                //gp.Offer.AdhesionCatalogList = new List<Libraries.DTO.adhesion_offres.AdhesionCatalogEntity>(); // on supp l'ancien objet adhesion qui n'etait pas completement remplie
                                gp.Offer.AdhesionCatalog = adhesion; // on y met la nouvelle qui a toutes les proprietes voulues
                            }
                        }
                    }

                }

            }

            return listRet;
        }


        
        public async Task<List<ReadyToPrintGrilleTarifDTO>?> GetTarifsNamesBySessionsAndCategsIdAsync(int structureId, string langCode, int eventId, int[] sessionsId, int[] categsId)
        {
            try
            {

                return await _valeurTarifStockRepository.GetTarifsNamesBySessionsAndCategsIdAsync(structureId, langCode, eventId, sessionsId, categsId).ConfigureAwait(false);
            }
            catch
            {
                throw;
            }


        }

    }
}
