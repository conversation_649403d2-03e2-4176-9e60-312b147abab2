﻿using Core.Themis.Libraries.BLL.OrderDetails.Interfaces;
using Core.Themis.Libraries.Data.Repositories.Open.Orders.Dossier.Interfaces;
using Core.Themis.Libraries.DTO.Orders.Details;
using System.Collections.Generic;
using Microsoft.Data.SqlClient;

namespace Core.Themis.Libraries.BLL.OrderDetails
{
    public class DossierManager : IDossierManager
    {
        private readonly IDossierRepository _dossierRepository;

        public DossierManager(IDossierRepository dossierRepository)
        {
            _dossierRepository = dossierRepository;
        }

        public bool UpdateDossierC(int structureId, int dossierId, int EventId, string dossierC)
        {
            try
            {
                return _dossierRepository.UpdateDossierC(structureId, dossierId, EventId, dossierC);
            }
            catch
            {
                throw;
            }
        }

        public List<DossierSeatDTO> GetDossierSeats(int structureId, int OrderId, int EventId)
        {
            try
            {
                return _dossierRepository.GetDossierSeats(structureId, OrderId, EventId);
            }
            catch
            {
                throw;
            }
        }

        public List<DossierProductDTO> GetDossierProducts(int structureId, int OrderId)
        {
            try
            {
                return _dossierRepository.GetDossierProducts(structureId, OrderId);
            }
            catch
            {
                throw;
            }
        }

        /// <summary>
        /// insert into dossier_xx et dossiersvg_xx
        /// </summary>
        /// <param name="structureId"></param>
        /// <param name="doss"></param>
        /// <returns></returns>
        public int CreateSeatDossier(int structureId, DossierSeatDTO doss, SqlConnection cnxOpen, SqlTransaction? mysqlts = null)
        {
            try
            {
                return _dossierRepository.CreateSeatDossier(cnxOpen, structureId, doss, mysqlts);
            }
            catch
            {
                throw;
            }
        }

        public int CreateSeatDossierSVG(int structureId, DossierSeatDTO doss, string typeOperation, SqlConnection cnxOpen, SqlTransaction? mysqlts = null)
        {
            try
            {
                return _dossierRepository.CreateSeatDossierSVG(cnxOpen, structureId, doss, typeOperation, mysqlts);
            }
            catch
            {
                throw;
            }
        }

        public int CreateSeatDossierHisto(int structureId, DossierSeatDTO doss, SqlConnection cnxOpen, SqlTransaction? mysqlts = null)
        {
            try
            {
                return _dossierRepository.CreateSeatDossierHisto(cnxOpen, structureId, doss, mysqlts);
            }
            catch
            {
                throw;
            }
        }

        public int UpdateDossierEnP(int structureId, DossierSeatDTO doss, int newVersion, long numPaiement, int operateurId, SqlConnection cnxOpen, SqlTransaction? mysqlts = null)
        {
            try
            {
                return _dossierRepository.UpdateDossierEnP(cnxOpen, structureId, doss, newVersion, numPaiement, operateurId, mysqlts);
            }
            catch
            {
                throw;
            }
        }

        public bool UpdateDossierEnB(int structureId, DossierSeatDTO doss, int newVersion, int operateurId, bool printAtHome)
        {
            try
            {
                return _dossierRepository.UpdateDossierEnB(structureId, doss, newVersion, operateurId, printAtHome);
            }
            catch
            {
                throw;
            }



        }

        /// <summary>
        /// Met à jour le compteur(cpt_dossier_XXX.compteur) si seance_id = 0, compteur global des dossiers, sinon compteur du dossier par séance
        /// </summary>
        /// <param name="manif_id"></param>
        /// <param name="seance_id"></param>
        /// <returns></returns>
        public int InstancieNumeroIdDossier(int structureId, int manif_id, int seance_id, SqlConnection cnxOpen, SqlTransaction? mysqlts = null)
        {
            try
            {
                return _dossierRepository.InstancieNumeroIdDossier(cnxOpen, structureId, manif_id, seance_id, mysqlts);
            }
            catch
            {
                throw;
            }
        }
    }
}
