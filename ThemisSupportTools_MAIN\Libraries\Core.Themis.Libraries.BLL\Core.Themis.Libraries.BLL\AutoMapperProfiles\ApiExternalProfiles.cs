﻿using AutoMapper;
using Core.Themis.Libraries.Data.Entities.Open.BuyerProfil;
using Core.Themis.Libraries.Data.Entities.Open.Devise;
using Core.Themis.Libraries.Data.Entities.Open.EventsSessions;
using Core.Themis.Libraries.Data.Entities.Open.Identite;
using Core.Themis.Libraries.Data.Entities.Open.Insurance;
using Core.Themis.Libraries.Data.Entities.Open.Structure;
using Core.Themis.Libraries.Data.Entities.WebTracing.DemandPasswordReset;
using Core.Themis.Libraries.DTO;
using Core.Themis.Libraries.DTO.AccessControl;
using Core.Themis.Libraries.DTO.EventsSessions;
using Core.Themis.Libraries.DTO.exposedObjects;
using Core.Themis.Libraries.DTO.exposedObjects.Insurance;
using Core.Themis.Libraries.DTO.Identity;
using Core.Themis.Libraries.DTO.Insurance;
using Core.Themis.Libraries.DTO.Orders;
using Core.Themis.Libraries.DTO.Seats;
using Core.Themis.Libraries.DTO.Structures;
using Core.Themis.Libraries.DTO.WebTracing;
using Core.Themis.Libraries.DTO.WSAdmin;
using System;
using System.Linq;
using ExposedWebUser = Core.Themis.Libraries.DTO.exposedObjects.WebUser;
using WTObjectsWebUser = Core.Themis.Libraries.DTO.WTObjects.WebUser;

namespace Core.Themis.Libraries.BLL.AutoMapperProfiles
{
    public class ApiExternalProfiles : Profile
    {
        public ApiExternalProfiles()
        {

            #region Event 
            //CreateMap<List<EventDTO>>, List<EventImages>>();


            CreateMap<EventDTO, EventImages>()
                .ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.EventId))
                .ForMember(dest => dest.Image1, opt => opt.MapFrom(src => src.Images.Image1))
                .ForMember(dest => dest.Image2, opt => opt.MapFrom(src => src.Images.Image2))
                .ForMember(dest => dest.ImageOnline, opt => opt.MapFrom(src => src.Images.ImageOnLine));

            CreateMap<EventsGroupsDTO, EventGroup>()
                .ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.EventGroupId))
                .ForMember(dest => dest.Name, opt => opt.MapFrom(src => src.EventGroupName));


            CreateMap<EventDTO, Event>()
                .ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.EventId))
                .ForMember(dest => dest.Name, opt => opt.MapFrom(src => src.EventName))
                .ForMember(dest => dest.ProducerName, opt => opt.MapFrom(src => src.ProducerName))
                .ForMember(dest => dest.ProducerLicenceNum1, opt => opt.MapFrom(src => src.ProducerLicenceNum1))
                .ForMember(dest => dest.ProducerLicenceNum2, opt => opt.MapFrom(src => src.ProducerLicenceNum2))
                .ForMember(dest => dest.Summary, opt => opt.MapFrom(src => src.Summary))
                .ForMember(dest => dest.Duration, opt => opt.MapFrom(src => src.Duration))
                .ForMember(dest => dest.DurationInfo, opt => opt.MapFrom(src => src.DurationText))
                .ForMember(dest => dest.Classification, opt => opt.MapFrom(src => src.Discipline.name))
                .ForMember(dest => dest.Genre, opt => opt.MapFrom(src => src.SousGenre.EventGenre.EventGenreName))
                .ForMember(dest => dest.SubGenre, opt => opt.MapFrom(src => src.SousGenre.SousGenreName))
                .ForMember(dest => dest.EventGroup, opt => opt.MapFrom(src => src.EventGroup))


                .ForMember(dest => dest.EventDescription1, opt => opt.MapFrom(src => src.EventDescription1))
                .ForMember(dest => dest.EventDescription2, opt => opt.MapFrom(src => src.EventDescription2))
                .ForMember(dest => dest.EventDescription3, opt => opt.MapFrom(src => src.EventDescription3))
                .ForMember(dest => dest.EventDescription4, opt => opt.MapFrom(src => src.EventDescription4))
                .ForMember(dest => dest.EventDescription5, opt => opt.MapFrom(src => src.EventDescription5))
                .ForMember(dest => dest.Sessions, opt => opt.MapFrom(src => src.ListSessions));

            CreateMap<EventsGroupsDTO, EventGroup>()
                .ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.EventGroupId))
                .ForMember(dest => dest.Name, opt => opt.MapFrom(src => src.EventGroupName))
            .ForMember(dest => dest.SuperGroup, opt => opt.MapFrom(src => src.SuperGroupe));

            CreateMap<EventSuperGroupDTO, EventSuperGroup>();
    

            #endregion

            #region Session

            CreateMap<SessionDTO, Session>()
                .ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.SessionId))
                .ForMember(dest => dest.EventId, opt => opt.MapFrom(src => src.EventId))
                .ForMember(dest => dest.StartDate, opt => opt.MapFrom(src => src.SessionStartDate))
                .ForMember(dest => dest.EndDate, opt => opt.MapFrom(src => src.SessionEndDate))

                .ForMember(dest => dest.StartDateSale, opt => opt.MapFrom(src => src.SessionStartSaleDate))
                .ForMember(dest => dest.EndDateSale, opt => opt.MapFrom(src => src.SessionEndSaleDate))

                .ForMember(dest => dest.EndDateSale, opt => opt.MapFrom(src => src.SessionEndSaleDate))

                .ForMember(dest => dest.Zones, opt => opt.MapFrom(src => src.ListZones))
                .ForMember(dest => dest.Place, opt => opt.MapFrom(src => src.Place))
                .ForMember(dest => dest.Version, opt => opt.MapFrom(src => src.targetDTOs.Count > 0 ? src.targetDTOs[0].Name : ""))
                .ForMember(dest => dest.ProjectionType, opt => opt.MapFrom(src => src.Projection.name));


                            //.ForMember(dest => dest.PlacementNumbered, opt => opt.MapFrom(src => src.ListGestionPlace.Count > 0 ? !src.ListGestionPlace[0].IsPlacementLibre : true))




            CreateMap<Session, SessionDTO>()
                .ForMember(dest => dest.SessionId, opt => opt.MapFrom(src => src.Id));

            #endregion

            #region WebUser => exposed

            CreateMap<ExposedWebUser, WTObjectsWebUser>()
                .ForMember(dest => dest.UserId, opt => opt.MapFrom(src => src.Id))
                .ForMember(dest => dest.Referer, opt => opt.MapFrom(src => src.Referer))
                .ForMember(dest => dest.IdentiteId, opt => opt.MapFrom(src => src.IdentityId))

                .ForMember(dest => dest.AddressIp, opt => opt.MapFrom(src => src.AddressIp));

            CreateMap<WTObjectsWebUser, ExposedWebUser>()
                .ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.UserId))
                .ForMember(dest => dest.IdentityId, opt => opt.MapFrom(src => src.IdentiteId))
                .ForMember(dest => dest.Referer, opt => opt.MapFrom(src => src.Referer))
                .ForMember(dest => dest.AddressIp, opt => opt.MapFrom(src => src.AddressIp));

            #endregion

            CreateMap<ProducteurDTO, Producer>()
                .ForMember(dest => dest.ProducerId, opt => opt.MapFrom(src => src.ProducteurId))
                .ForMember(dest => dest.ProducerName, opt => opt.MapFrom(src => src.ProducteurNom))
                .ForMember(dest => dest.Licence, opt => opt.MapFrom(src => src.ProducteurLicence));

            CreateMap<IdentityDTO, Identity>()
                .ForMember(dest => dest.ZipCode, opt => opt.MapFrom(src => src.PostalCode))
                .ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.IdentiteId))
                .ForMember(dest => dest.B64Password, opt => opt.MapFrom(src => src.Password))
                .ForMember(dest => dest.Salutation, opt => opt.MapFrom(src => src.Appellation))
              //.ForMember(dest => dest.Salutation.Id, opt => opt.MapFrom(src => src.Appellation.AppellationId))
              .ForMember(dest => dest.SubscriberNumber, opt => opt.MapFrom(src => src.AdherentNumber));

            CreateMap<Identity, IdentityDTOUpdate>()
                .ForMember(dest => dest.PostalCode, opt => opt.MapFrom(src => src.ZipCode))
                .ForMember(dest => dest.IdentiteId, opt => opt.MapFrom(src => src.Id))
                .ForMember(dest => dest.Password, opt => opt.MapFrom(src => src.B64Password))
                 .ForMember(dest => dest.Appellation, opt => opt.MapFrom(src => src.Salutation))
                .ForMember(dest => dest.AdherentNumber, opt => opt.MapFrom(src => src.SubscriberNumber));

            CreateMap<Identity, IdentityDTO>()
                .ForMember(dest => dest.PostalCode, opt => opt.MapFrom(src => src.ZipCode))
                .ForMember(dest => dest.IdentiteId, opt => opt.MapFrom(src => src.Id))
                .ForMember(dest => dest.Password, opt => opt.MapFrom(src => src.B64Password))
                .ForMember(dest => dest.Appellation, opt => opt.MapFrom(src => src.Salutation))
                .ForMember(dest => dest.AdherentNumber, opt => opt.MapFrom(src => src.SubscriberNumber));

            CreateMap<Salutation, GlobalAppellationDTO>()
                .ForMember(dest => dest.AppellationId, opt => opt.MapFrom(src => src.Id))
                .ForMember(dest => dest.AppellationName, opt => opt.MapFrom(src => src.Name));

            CreateMap<GlobalAppellationDTO, Salutation>()
                 .ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.AppellationId))
                .ForMember(dest => dest.Name, opt => opt.MapFrom(src => src.AppellationName));




            CreateMap<GlobalAppellationEntity, GlobalAppellationDTO>()
                .ForMember(dest => dest.AppellationName, opt => opt.MapFrom(src => src.AppellationNom));

            CreateMap<GlobalAppellationDTO, GlobalAppellationEntity>()
                .ForMember(dest => dest.AppellationNom, opt => opt.MapFrom(src => src.AppellationName));

            CreateMap<PlaceDTO, Place>()
                .ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.PlaceId))
                .ForMember(dest => dest.Name, opt => opt.MapFrom(src => src.PlaceName))
                .ForMember(dest => dest.Address1, opt => opt.MapFrom(src => src.Address_address1))
                .ForMember(dest => dest.Address2, opt => opt.MapFrom(src => src.Address_address2))
                .ForMember(dest => dest.Address3, opt => opt.MapFrom(src => src.Address_address3))
                .ForMember(dest => dest.Address4, opt => opt.MapFrom(src => src.Address_address4))
                .ForMember(dest => dest.City, opt => opt.MapFrom(src => src.Address_city))
                .ForMember(dest => dest.ZipCode, opt => opt.MapFrom(src => src.Address_postal_code))
                .ForMember(dest => dest.Region, opt => opt.MapFrom(src => src.Address_region))
                .ForMember(dest => dest.Country, opt => opt.MapFrom(src => src.Address_country));

            CreateMap<ZoneDTO, Zone>()
                .ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.ZoneId))
                .ForMember(dest => dest.Name, opt => opt.MapFrom(src => src.ZoneName))
                .ForMember(dest => dest.Floors, opt => opt.MapFrom(src => src.ListFloors));

            CreateMap<FloorDTO, Floor>()
                .ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.FloorId))
                .ForMember(dest => dest.Name, opt => opt.MapFrom(src => src.FloorName))
                .ForMember(dest => dest.Sections, opt => opt.MapFrom(src => src.ListSections));

            CreateMap<SectionDTO, Section>()
                .ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.SectionId))
                .ForMember(dest => dest.Name, opt => opt.MapFrom(src => src.SectionName))
                .ForMember(dest => dest.Categories, opt => opt.MapFrom(src => src.ListCategories));

            CreateMap<CategoryDTO, Category>()
                .ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.CategId))
                .ForMember(dest => dest.Name, opt => opt.MapFrom(src => src.CategoryName))
                .ForMember(dest => dest.Prices, opt => opt.MapFrom(src => src.ListPrices))
                .ForMember(dest => dest.SeatsCount, opt => opt.MapFrom(src => src.DispoTotal))
                .ForMember(dest => dest.SeatsAvailablesCount, opt => opt.MapFrom(src => src.Dispo));

            CreateMap<DeviseDTO, Currency>()
                .ForMember(dest => dest.CodeNIso, opt => opt.MapFrom(src => src.CodeNIso))
                .ForMember(dest => dest.Name, opt => opt.MapFrom(src => src.Name));



            CreateMap<PriceDTO, Price>()
                .ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.PriceId))
                .ForMember(dest => dest.Name, opt => opt.MapFrom(src => src.PriceName))
                .ForMember(dest => dest.UnitTTCAmount, opt => opt.MapFrom(src => src.UnitTTCAmount))
                .ForMember(dest => dest.UnitFeesAmount, opt => opt.MapFrom(src => src.UnitFeeAmount))
                .ForMember(dest => dest.RuleId, opt => opt.MapFrom(src => src.RuleId))
                .ForMember(dest => dest.MaxSeats, opt => opt.MapFrom(src => src.NbSeatMax))
                .ForMember(dest => dest.MinSeats, opt => opt.MapFrom(src => src.NbSeatMin))
                .ForMember(dest => dest.Seats, opt => opt.MapFrom(src => src.ListSeats))
                .ForMember(dest => dest.PlacementNumbered, opt => opt.MapFrom(src => src.ListGestionPlace.Count > 0 ? !src.ListGestionPlace[0].IsPlacementLibre : true))
                .ForMember(dest => dest.PlacementManual, opt => opt.MapFrom(src => src.ListGestionPlace.Count > 0 ? src.ListGestionPlace[0].IsSurPlan : false));

            CreateMap<SeatDTO, Seat>()
                .ForMember(dest => dest.BarCode, opt => opt.MapFrom(src => src.BarCode))
                .ForMember(dest => dest.TicketNumber, opt => opt.MapFrom(src => src.NumeroBillet))
                .ForMember(dest => dest.SeatN, opt => opt.MapFrom(src => src.Seat))
                .ForMember(dest => dest.Orientation, opt => opt.MapFrom(src => src.Orientation))
                .ForMember(dest => dest.Denomination, opt => opt.MapFrom(src => src.DenominationName))
                .ForMember(dest => dest.EventId, opt => opt.MapFrom(src => src.EventId))
                .ForMember(dest => dest.SessionId, opt => opt.MapFrom(src => src.SessionId))
                .ForMember(dest => dest.CategoryId, opt => opt.MapFrom(src => src.CategoryId))
                .ForMember(dest => dest.PosX, opt => opt.MapFrom(src => src.PosX))
                .ForMember(dest => dest.PosY, opt => opt.MapFrom(src => src.PosY))
                .ForMember(dest => dest.PhysicalSeatIndex, opt => opt.MapFrom(src => src.IIndex))
                .ForMember(dest => dest.State, opt => opt.MapFrom(src => src.State))

                .ForMember(dest => dest.Gate, opt => opt.MapFrom(src => src.Gate))
                .ForMember(dest => dest.Tribune, opt => opt.MapFrom(src => src.Tribune))
                .ForMember(dest => dest.Access, opt => opt.MapFrom(src => src.Acces));


            CreateMap<Seat, SeatDTO>()
                .ForMember(dest => dest.BarCode, opt => opt.MapFrom(src => src.BarCode))
                .ForMember(dest => dest.NumeroBillet, opt => opt.MapFrom(src => src.TicketNumber))
                .ForMember(dest => dest.Seat, opt => opt.MapFrom(src => src.SeatN))
                .ForMember(dest => dest.Orientation, opt => opt.MapFrom(src => src.Orientation))
                .ForMember(dest => dest.DenominationName, opt => opt.MapFrom(src => src.Denomination))
                .ForMember(dest => dest.EventId, opt => opt.MapFrom(src => src.EventId))
                .ForMember(dest => dest.SessionId, opt => opt.MapFrom(src => src.SessionId))
                .ForMember(dest => dest.CategoryId, opt => opt.MapFrom(src => src.CategoryId))
                .ForMember(dest => dest.PosX, opt => opt.MapFrom(src => src.PosX))
                .ForMember(dest => dest.PosY, opt => opt.MapFrom(src => src.PosY))
                .ForMember(dest => dest.IIndex, opt => opt.MapFrom(src => src.PhysicalSeatIndex))
                
                .ForMember(dest => dest.State, opt => opt.MapFrom(src => string.IsNullOrEmpty(src.State) ? (char?)null : src.State[0]))

                .ForMember(dest => dest.Gate, opt => opt.MapFrom(src => src.Gate))
                .ForMember(dest => dest.Tribune, opt => opt.MapFrom(src => src.Tribune))
                .ForMember(dest => dest.Acces, opt => opt.MapFrom(src => src.Access))
                ;
            CreateMap<EntreeEntity, SeatDTO>()
                .ForMember(dest => dest.SeatId, opt => opt.MapFrom(src => src.EntreeId))
                .ForMember(dest => dest.SessionId, opt => opt.MapFrom(src => src.SeanceId))
                .ForMember(dest => dest.Rank, opt => opt.MapFrom(src => src.ReferenceLieuPhysique.Rang))
                .ForMember(dest => dest.Seat, opt => opt.MapFrom(src => src.ReferenceLieuPhysique.Siege))
                .ForMember(dest => dest.Reference_Physic_Id, opt => opt.MapFrom(src => src.ReferenceLieuPhysique.Iindex))
                .ForMember(dest => dest.IIndex, opt => opt.MapFrom(src => src.ReferenceLieuPhysique.Iindex))
                .ForMember(dest => dest.PosX, opt => opt.MapFrom(src => src.ReferenceLieuPhysique.Pos_x))
                .ForMember(dest => dest.PosY, opt => opt.MapFrom(src => src.ReferenceLieuPhysique.Pos_y))
                .ForMember(dest => dest.Orientation, opt => opt.MapFrom(src => src.ReferenceLieuPhysique.Orientation))
                .ForMember(dest => dest.DenominationName, opt => opt.MapFrom(src => src.ReferenceLieuPhysique.Denomination.DenomNom))
                .ForMember(dest => dest.DenominationCode, opt => opt.MapFrom(src => src.ReferenceLieuPhysique.Denomination.DenomCode))
                .ForMember(dest => dest.State, opt => opt.MapFrom(src => src.EntreeEtat))
                .ForMember(dest => dest.EventId, opt => opt.MapFrom(src => src.ManifestationId))
                .ForMember(dest => dest.FloorId, opt => opt.MapFrom(src => src.ReferenceLieuPhysique.Etage.EtageId))
                .ForMember(dest => dest.ZoneId, opt => opt.MapFrom(src => src.ReferenceLieuPhysique.Zone.ZoneId))
                .ForMember(dest => dest.SectionId, opt => opt.MapFrom(src => src.ReferenceLieuPhysique.Section.SectionId))

                .ForMember(dest => dest.FloorName, opt => opt.MapFrom(src => src.ReferenceLieuPhysique.Etage.EtageNom))
                .ForMember(dest => dest.ZoneName, opt => opt.MapFrom(src => src.ReferenceLieuPhysique.Zone.ZoneNom))
                .ForMember(dest => dest.SectionName, opt => opt.MapFrom(src => src.ReferenceLieuPhysique.Section.SectionNom))

                .ForMember(dest => dest.TypeSeat, opt => opt.MapFrom(src => src.ReferenceLieuPhysique.TypeSiege))
                //.ForMember(dest => dest.Orientation, opt => opt.MapFrom(src => src.))

                .ForMember(dest => dest.DenominationName, opt => opt.MapFrom(src => src.ReferenceLieuPhysique.Denomination.DenomNom))


                .ForPath(dest => dest.ConsommateurIdentiteId, opt => opt.MapFrom(src => src.EntreeComplement.BeneficiaireIdentiteId))
                ;





            //CreateMap<List<EntreeEntity>, List<SeatDTO>>();

            //CreateMap<List<List<EntreeEntity>>, List<List<SeatDTO>>>();

            CreateMap<SeatDTO, SeatInsurance>()
            .ForMember(dest => dest.Id, opt => opt.MapFrom(src => $"{src.SessionId}_{src.DossierId}_{src.SeatId}"))
            ;


            CreateMap<TextOrLineSeatingPlanDTO, TextPlan>()
                .ForMember(dest => dest.PosX, opt => opt.MapFrom(src => src.PosX))
                .ForMember(dest => dest.PosY, opt => opt.MapFrom(src => src.PosY))
                .ForMember(dest => dest.Texte, opt => opt.MapFrom(src => src.Texte))

                ;


            CreateMap<BasketDTO, Basket>()
                .ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.BasketId))
                .ForMember(dest => dest.WebUserId, opt => opt.MapFrom(src => src.WebUser.UserId))
                .ForMember(dest => dest.CreationDate, opt => opt.MapFrom(src => src.DateOperation))
                .ForMember(dest => dest.EventsIndiv, opt => opt.MapFrom(src => src.ListEventsUnitSales));

            CreateMap<OrderDTO, OrderInsurance>()
                .ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.OrderId))
                .ForMember(dest => dest.PurchaseDate, opt => opt.MapFrom(src => src.DatePayment))
                ;

            CreateMap<OrderDTO, Order>()
                .ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.OrderId))
                .ForMember(dest => dest.PurchaseDate, opt => opt.MapFrom(src => src.DatePayment))
                ;


            CreateMap<SeatDTO, SeatInsurance>()
            .ForMember(dest => dest.Id, opt => opt.MapFrom(src => $"{src.SessionId}_{src.DossierId}_{src.SeatId}"))
            ;

            CreateMap<Ticket, BarCodeDTO>();



            CreateMap<BarCodeDTO, Ticket>()
                .ForMember(dest => dest.TicketNumber, opt => opt.MapFrom(src => src.NumBillet))
                .ForMember(dest => dest.OrderId, opt => opt.MapFrom(src => src.OrderId))
                .ForMember(dest => dest.IdentityId, opt => opt.MapFrom(src => src.Buyer.IdentiteId))

                .ForMember(dest => dest.BarCode, opt => opt.MapFrom(src => src.BarCode))
                .ForMember(dest => dest.Controlled, opt => opt.MapFrom(src => src.Controlled))
                .ForMember(dest => dest.ControlledDate, opt => opt.MapFrom(src => src.ControlledDate))
                .ForMember(dest => dest.BuyerEmail, opt => opt.MapFrom(src => src.Buyer.Email))
                .ForMember(dest => dest.ConsumerFirstName, opt => opt.MapFrom(src => src.Customer.FirstName))
                .ForMember(dest => dest.ConsumerLastName, opt => opt.MapFrom(src => src.Customer.SurName))
                .ForMember(dest => dest.SessionId, opt => opt.MapFrom(src => src.SessionId))
                .ForMember(dest => dest.SessionStartDate, opt => opt.MapFrom(src => src.SessionStartDate))
                .ForMember(dest => dest.CategoryName, opt => opt.MapFrom(src => src.Category.CategoryName))
                .ForMember(dest => dest.CategoryId, opt => opt.MapFrom(src => src.Category.CategId))
                .ForMember(dest => dest.PriceName, opt => opt.MapFrom(src => src.Price.PriceName))
                .ForMember(dest => dest.PriceId, opt => opt.MapFrom(src => src.Price.PriceId))
                .ForMember(dest => dest.SeatId, opt => opt.MapFrom(src => src.SeatId))

                .ForMember(dest => dest.Denomination, opt => opt.MapFrom(src => src.DenominationName))
            //.ForMember(dest => dest.TribuneName, opt => opt.MapFrom(src => src.TribuneName))
            //.ForMember(dest => dest.GateName, opt => opt.MapFrom(src => src.GateName))
            //.ForMember(dest => dest.AccessName, opt => opt.MapFrom(src => src.AccessName))

            .ForMember(dest => dest.Producer, opt => opt.MapFrom(src => src.Producteur));


            CreateMap<StructurePrefsEntity, StructurePrefsDTO>();
            CreateMap<StructurePrefsDTO, StructurePrefsEntity>();

            CreateMap<ProfilAcheteurEntity, BuyerProfilDTO>()
                .ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.Id))
                .ForMember(dest => dest.Name, opt => opt.MapFrom(src => src.Libelle))
                .ForMember(dest => dest.Password, opt => opt.MapFrom(src => src.Password))
                .ForMember(dest => dest.OperatorId, opt => opt.MapFrom(src => src.OperateurId))
                .ForMember(dest => dest.IsExclusiveMode, opt => opt.MapFrom(src => src.IsExclusiveMode))
                .ForMember(dest => dest.IsInsuranceActive, opt => opt.MapFrom(src => src.IsInsuranceActive))
                .ForMember(dest => dest.IsReseller, opt => opt.MapFrom(src => src.IsRevendeur))
                ;

            CreateMap<InsuranceContractEntity, InsuranceContractDTO>();
            CreateMap<InsuranceContractDTO, InsuranceContractEntity>();

            CreateMap<DossierProduitAssuranceEntity, DossierProduitAssuranceDTO>();
            CreateMap<DossierProduitAssuranceDTO, DossierProduitAssuranceEntity>();

            CreateMap<CategorieEntity, CategoryDTO>()
             .ForMember(dest => dest.CategId, opt => opt.MapFrom(src => src.CategId))
             .ForMember(dest => dest.CategoryName, opt => opt.MapFrom(src => src.CategNom))
             .ForMember(dest => dest.CategoryCode, opt => opt.MapFrom(src => src.CategCode))
             .ForMember(dest => dest.ColorRodrigue, opt => opt.MapFrom(src => src.CategCouleurId))
             .ForMember(dest => dest.DisplayRank, opt => opt.MapFrom(src => src.PrefAffichage));




            CreateMap<DeviseEntity, DeviseDTO>()
                .ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.DeviseId))
                .ForMember(dest => dest.Code, opt => opt.MapFrom(src => src.DeviseCode))
                .ForMember(dest => dest.Taux, opt => opt.MapFrom(src => src.DeviseTaux))
                .ForMember(dest => dest.IsReference, opt => opt.MapFrom(src => src.DeviseRef == "O" ? true : false))
                .ForMember(dest => dest.CodeNIso, opt => opt.MapFrom(src => src.TPEnumCode))
                .ForMember(dest => dest.Name, opt => opt.MapFrom(src => src.DeviseNom));

            //.ForMember(dest => dest.AmountTTCCents, opt => opt.MapFrom(src => src.AmountTTCCents))
            //.ForMember(dest => dest.FeesCents, opt => opt.MapFrom(src => src.Charge))
            //.ForMember(dest => dest.Count, opt => opt.MapFrom(src => src.Nbr))

            CreateMap<DemandPasswordResetEntity, DemandePassWordResetDTO>();
            





        }
    }
}
