﻿using AutoMapper;
using Core.Themis.Libraries.BLL.Helpers;
using Core.Themis.Libraries.BLL.Interfaces;
using Core.Themis.Libraries.BLL.Managers.Interfaces;
using Core.Themis.Libraries.BLL.Managers.Traduction.Interfaces;
using Core.Themis.Libraries.BLL.Translations.Interfaces;
using Core.Themis.Libraries.BLL.Services.RedisCache.Interfaces;
using Core.Themis.Libraries.Data.DBContext;
using Core.Themis.Libraries.Data.DBContext.Interfaces;
using Core.Themis.Libraries.Data.Entities.Consumer;
using Core.Themis.Libraries.Data.Entities.Open.EventsSessions;
using Core.Themis.Libraries.Data.Entities.Open.Traduction;
using Core.Themis.Libraries.Data.Repositories.Open.Consumer.Interfaces;
using Core.Themis.Libraries.Data.Repositories.Open.EventsSessions;
using Core.Themis.Libraries.Data.Repositories.Open.EventsSessions.Interfaces;
using Core.Themis.Libraries.Data.Repositories.Open.Identities.Interfaces;
using Core.Themis.Libraries.Data.Repositories.Open.Lieu.Interfaces;
using Core.Themis.Libraries.Data.Repositories.Open.Orders.Entree.Interfaces;
using Core.Themis.Libraries.Data.Repositories.Open.Orders.Interfaces;
using Core.Themis.Libraries.Data.Repositories.Open.Traduction.Interfaces;
using Core.Themis.Libraries.DTO;
using Core.Themis.Libraries.DTO.AdminSphere.VenteIndiv;
using Core.Themis.Libraries.DTO.CustomerArea.Tickets_Session.SessionCard;
using Core.Themis.Libraries.DTO.EventsSessions;
using Core.Themis.Libraries.DTO.Lookup;
using Core.Themis.Libraries.DTO.Seats;
using Core.Themis.Libraries.Utilities.Helpers;
using Core.Themis.Libraries.Utilities.Helpers.Cache;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.Configuration;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Data;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using System.Xml.Linq;
using Core.Themis.Libraries.DTO.Catalog.Models;

namespace Core.Themis.Libraries.BLL
{
    public class SessionManager : ISessionManager
    {
        private readonly ISessionRepository _sessionRepository;
        private readonly IOrderRepository _orderRepository;
        private readonly IEntreeRepository _entreeRepository;
        private readonly IIdentityRepository _identiteRepository;
        private readonly IConsumerRepository _consumerRepository;
        private readonly IMapper _mapper;
        private readonly IMemoryCache _memoryCache;
        private readonly IConfiguration _configuration;
        private readonly IGPSeanceRepository _gPSeanceRepository;
        private readonly ITraductionRepository _traductionRepository;
        private readonly ILanguageManager _languageManager;
        private readonly ILieuRepository _lieuRepository;
        private readonly ITraductionManager _traductionManager;
        private readonly ITranslateManager _translateManager;
        private readonly IDbContext _dbContext;
        private readonly IRedisCacheService _redisCacheService;

        public SessionManager(ISessionRepository sessionRepository,
            IIdentityRepository identiteRepository,
            IConsumerRepository consumersRepository,
            IOrderRepository orderRepository,
            IEntreeRepository entreeRepository,
            IMapper mapper, IMemoryCache memoryCache,
            IConfiguration configuration,
            IGPSeanceRepository gPSeanceRepository,
            ITraductionRepository traductionRepository,
            ILanguageManager languageManager,
            ILieuRepository lieuRepository,
            ITraductionManager traductionManager,
            ITranslateManager translateManager,
            IDbContext dbContext,
            IRedisCacheService redisCacheService)
        {
            _sessionRepository = sessionRepository;
            _identiteRepository = identiteRepository;
            _consumerRepository = consumersRepository;
            _orderRepository = orderRepository;
            _entreeRepository = entreeRepository;
            _mapper = mapper;
            _memoryCache = memoryCache;
            _configuration = configuration;
            _gPSeanceRepository = gPSeanceRepository;
            _traductionRepository = traductionRepository;
            _languageManager = languageManager;
            _lieuRepository = lieuRepository;
            _traductionManager = traductionManager;
            _translateManager = translateManager;
            _dbContext = dbContext;
            _redisCacheService = redisCacheService;
        }


        /// <summary>
        /// renseigne les seances de la liste listSessionsToFill avec les infos de la liste ListSessions (qui est chargée depuis Open)
        /// </summary>
        /// <param name="listSessionsToFill"></param>
        /// <param name="ListEvents"></param>
        public void Fill(List<SessionDTO> listSessionsToFill, List<SessionDTO> ListSessions)
        {
            foreach (SessionDTO sess in listSessionsToFill)
            {
                var thisSessionEnt = ListSessions.Where(e => e.SessionId == sess.SessionId).FirstOrDefault();

                sess.EventId = thisSessionEnt.EventId;

                sess.SessionStartDate = thisSessionEnt.SessionStartDate;
                sess.TvaTaux = thisSessionEnt.TvaTaux;
                sess.TvaName = thisSessionEnt.TvaName;
                sess.IsShowSessionDate = thisSessionEnt.IsShowSessionDate;
                sess.IsShowSessionHour = thisSessionEnt.IsShowSessionHour;
                sess.Place = thisSessionEnt.Place;
            }
        }

        public SessionDTO GetById(int structureId, int sessionId)
        {
            var resultQuery = _sessionRepository.GetById(structureId, sessionId);

            return _mapper.Map<SessionDTO>(resultQuery);
        }

        public IEnumerable<SessionDTO> GetAllSessionsByEventId(int structureId, int eventId)
        {
            var resultQuery = _sessionRepository.GetAllSessionsByEventId(structureId, eventId);
            return _mapper.Map<IEnumerable<SessionDTO>>(resultQuery);
        }

        /// <summary>
        /// load toutes les infos des séances
        /// </summary>
        /// <param name="structureId"></param>
        /// <param name="listSessionId"></param>
        /// <param name="langCode"></param>
        /// <returns></returns>
        public List<SessionDTO> Load(int structureId, List<int> listSessionId, string langCode)
        {
            try
            {
                return _sessionRepository.Load(structureId, listSessionId, langCode);
            }
            catch
            {
                throw;
            }
        }

        /// <summary>
        /// dictionnaire colonnes / valeur pour utilisation dans les maquettes
        /// </summary>
        /// <param name="structureId"></param>
        /// <param name="langCode"></param>
        /// <param name="identiteId"></param>
        /// <param name="orderId"></param>
        /// <param name="eventId"></param>
        /// <param name="sessionId"></param>
        /// <returns></returns>
        public DictionaryExtented GetDictionnaryForPdf(int structureId, string langCode, int identiteId, int orderId, int eventId, int sessionId)
        {
            return _sessionRepository.GetDictionnaryForPdf(structureId, langCode, identiteId, orderId, sessionId);
        }


        #region plan de salle

        public List<SeatDTO> LoadSeatPlan(int structureId, int operatorId, int eventId, int sessionId, int zoneId, int floorId, int sectionId, int identiteId, string myFlag, string langCode, int webUserId)
        {


            string seatsListCache = $"seatsListCache_{structureId}_{eventId}_{sessionId}_{zoneId}_{floorId}_{sectionId}_{langCode}_{webUserId}";
            List<SeatDTO> seatsList = null;
            try
            {
                _memoryCache.TryGetValue(seatsListCache, out seatsList);

                if (seatsList == null)
                {
                    seatsList = _sessionRepository.LoadSeatPlan(structureId, operatorId, eventId, sessionId, zoneId, floorId, sectionId, identiteId, myFlag, langCode, webUserId);

                    //Mets en cache 
                    var cacheExpiryOptions = new MemoryCacheEntryOptions
                    {
                        AbsoluteExpiration = DateTime.Now.AddSeconds(int.Parse(_configuration["Cache:SeatsAbsoluteExpiration"]!)),
                        Priority = CacheItemPriority.High,
                        SlidingExpiration = TimeSpan.FromSeconds(int.Parse(_configuration["Cache:SeatsSlidingExpiration"]!))
                    };
                    _memoryCache.Set(seatsListCache, seatsList, cacheExpiryOptions);

                }
            }
            catch (Exception ex)
            {
                throw;
            }


            return seatsList;
        }

        public List<SeatDTO> LoadSeatPlanAvailables(int structureId, int operatorId, int eventId, int sessionId, int zoneId, int floorId, int sectionId, int identiteId, string langCode, int profilAcheteurId, int webUserId)
        {

            string seatsListCache = $"LoadSeatPlanAvailables_{structureId}_{eventId}_{sessionId}_{zoneId}_{floorId}_{sectionId}_{identiteId}_{langCode}_{profilAcheteurId}_{webUserId}";
            List<SeatDTO> seatsList = null;
            _memoryCache.TryGetValue(seatsListCache, out seatsList);
            if (seatsList == null)
            {

                seatsList = _sessionRepository.LoadSeatPlanAvailables(structureId, operatorId, eventId, sessionId, zoneId, floorId, sectionId, identiteId, langCode, profilAcheteurId, webUserId);
                var cacheExpiryOptions = new MemoryCacheEntryOptions
                {
                    AbsoluteExpiration = DateTime.Now.AddSeconds(int.Parse(_configuration["Cache:SeatsAbsoluteExpiration"]!)),
                    Priority = CacheItemPriority.High,
                    SlidingExpiration = TimeSpan.FromSeconds(int.Parse(_configuration["Cache:SeatsSlidingExpiration"]!))
                };
                _memoryCache.Set(seatsListCache, seatsList, cacheExpiryOptions);


            }



            return seatsList;
        }

        /// <summary>
        /// liste des textes de salle 
        /// </summary>
        /// <param name="structureId"></param>
        /// <param name="operatorId"></param>
        /// <param name="eventId"></param>
        /// <param name="sessionId"></param>
        /// <returns></returns>
        public List<TextOrLineSeatingPlanDTO> LoadTextsPlan(int structureId, int operatorId, int eventId, int sessionId, List<int> listZonesId, List<int> listFloorsId, List<int> listSectionsId)
        {

            string cacheKey = $"{structureId}_LoadTextsPlan{eventId}_{sessionId}_{string.Join(",", listZonesId == null ? new List<int> { 0 } : listZonesId)}_" +
                $"{string.Join(",", listFloorsId == null ? new List<int> { 0 } : listFloorsId)}_{string.Join(",", listSectionsId == null ? new List<int> { 0 } : listSectionsId)}";

            List<TextOrLineSeatingPlanDTO> seatsTexts = new List<TextOrLineSeatingPlanDTO>();
            try
            {

                var cacheResult = _redisCacheService.GetRecord<List<TextOrLineSeatingPlanDTO>>(cacheKey);

                if (cacheResult is not null)
                    return cacheResult;

                seatsTexts = _sessionRepository.LoadTextsPlan(structureId, operatorId, eventId, sessionId, listZonesId, listFloorsId, listSectionsId);

                _redisCacheService.SetRecord(cacheKey, seatsTexts, TimeSpan.FromSeconds(int.Parse(_configuration["Cache:SeatsTextAbsoluteExpiration"]!)));
  
                return seatsTexts;
            }
            catch (Exception ex)
            {
                throw;
            }

            //try
            //{
            //    return _sessionRepository.LoadTextsPlan(structureId, operatorId, eventId, sessionId, listZonesId, listFloorsId, listSectionsId);   
            //}
            //catch
            //{
            //    throw;
            //}
        }

        /// <summary>
        /// liste des sieges indispo (etat=X)
        /// </summary>
        /// <returns></returns>
        public List<SeatDTO> LoadSeatPlanIndispo(int structureId, int operatorId, int eventId, int sessionId, List<int> listZonesId, List<int> listFloorsId, List<int> listSectionsId, string langCode)
        {
            string sListZonesId = string.Join(";", listZonesId);
            string sListFloorsId = string.Join(";", listFloorsId);
            string sListSesctionId = string.Join(";", listSectionsId);


            if (listZonesId.Count == 0) listZonesId.Add(0);
            if (listFloorsId.Count == 0) listFloorsId.Add(0);
            if (listSectionsId.Count == 0) listSectionsId.Add(0);


            string seatsListCache = $"seatsListCache_{structureId}_{eventId}_{sessionId}_{sListZonesId}_{sListFloorsId}_{sListSesctionId}_{langCode}";
            List<SeatDTO> seatsList = null;
            try
            {
                _memoryCache.TryGetValue(seatsListCache, out seatsList);

                if (seatsList == null)
                {
                    seatsList = _sessionRepository.LoadSeatPlanIndispo(structureId, operatorId, eventId, sessionId, listZonesId, listFloorsId, listSectionsId, langCode);

                    //Mets en cache 
                    var cacheExpiryOptions = new MemoryCacheEntryOptions
                    {
                        AbsoluteExpiration = DateTime.Now.AddSeconds(int.Parse(_configuration["Cache:SeatsAbsoluteExpiration"]!)),
                        Priority = CacheItemPriority.High,
                        SlidingExpiration = TimeSpan.FromSeconds(int.Parse(_configuration["Cache:SeatsSlidingExpiration"]!))
                    };
                    _memoryCache.Set(seatsListCache, seatsList, cacheExpiryOptions);

                }

                return seatsList;
            }
            catch (Exception ex)
            {
                throw;
            }

        }

        /// <summary>
        /// liste des textes de salle 
        /// </summary>
        /// <param name="structureId"></param>
        /// <param name="operatorId"></param>
        /// <param name="eventId"></param>
        /// <param name="sessionId"></param>
        /// <param name="zoneId"></param>
        /// <param name="floorId"></param>
        /// <param name="sectionId"></param>
        /// <returns></returns>
        public List<TextOrLineSeatingPlanDTO> LoadTextsPlan(int structureId, int operatorId, int eventId, int sessionId, int zoneId, int floorId, int sectionId)
        {
            List<TextOrLineSeatingPlanDTO> ob = null;

            string seatsListCache = $"TextsList{structureId}_{eventId}_{sessionId}_{zoneId}_{floorId}_{sectionId}";

            try
            {
                _memoryCache.TryGetValue(seatsListCache, out ob);

                if (ob == null)
                {
                    ob = _sessionRepository.LoadTextsPlan(structureId, operatorId, eventId, sessionId, zoneId, floorId, sectionId);

                    //Mets en cache 
                    var cacheExpiryOptions = new MemoryCacheEntryOptions
                    {
                        AbsoluteExpiration = DateTime.Now.AddSeconds(int.Parse(_configuration["Cache:SeatsTextAbsoluteExpiration"]!)),
                        Priority = CacheItemPriority.High,
                        SlidingExpiration = TimeSpan.FromSeconds(int.Parse(_configuration["Cache:SeatsTextSlidingExpiration"]!))
                    };
                    _memoryCache.Set(seatsListCache, ob, cacheExpiryOptions);

                }
            }
            catch (Exception ex)
            {
                throw;
            }

            return ob;
        }

        /// <summary>
        /// Liste des sieges de la séance sur plusieurs zones, etages, sections
        /// </summary>
        /// <param name="structureId"></param>
        /// <param name="operatorId"></param>
        /// <param name="eventId"></param>
        /// <param name="sessionId"></param>
        /// <param name="listZonesId"></param>
        /// <param name="listFloorsId"></param>
        /// <param name="listSectionsId"></param>
        /// <param name="identiteId"></param>
        /// <param name="myFlag"></param>
        /// <param name="langCode"></param>
        /// <returns></returns>
        public List<SeatDTO> LoadSeatPlan(int structureId, int operatorId, int eventId, int sessionId, List<int> listZonesId, List<int> listFloorsId, List<int> listSectionsId, int identiteId, string myFlag, string langCode, int webUserId)
        {
            try
            {
                return _sessionRepository.LoadSeatPlan(structureId, operatorId, eventId, sessionId, listZonesId, listFloorsId, listSectionsId, identiteId, myFlag, langCode, webUserId);
            }
            catch
            {
                throw;
            }
        }

        #endregion




        /// <summary>
        /// get session of orders of users (fake)
        /// </summary>
        /// <param name="structureId"></param>
        /// <param name="identiteId"></param>
        /// <returns></returns>
        public List<SessionCardInfo>? GetAllSessionCardInfoByIdentiteIdFake(int structureId, int identiteId) //, int? skipElement = null, int? takeElement = null)
        {
            List<SessionCardInfo> ret = new List<SessionCardInfo>();
            for (int i = -5; i < 5; i++)
            {
                Random random = new Random();
                ret.Add(new SessionCardInfo()
                {
                    SessionDateDeb = DateTime.Now.AddDays(i),
                    SessionId = random.Next(10, 100),
                    TicketsCount = random.Next(1, 15),
                });
            }



            return ret;
        }

        /// <summary>
        /// get session of orders of users
        /// </summary>
        /// <param name="structureId"></param>
        /// <param name="identiteId"></param>
        /// <returns></returns>
        public List<SessionCardInfo>? GetAllSessionCardInfoByIdentiteId(int structureId, int identiteId, int orderId, bool useCache = true) //, int? skipElement = null, int? takeElement = null)
        {
            // var ret = _orderRepository.GetAllOrdersByIdentiteId(structureId, identiteId);


            string cacheKey = $"GetAllSessionCardInfoByIdentiteId_{structureId}_{identiteId}_{orderId}";




            var listSessionsCardInfo = new List<SessionCardInfo>();
            if (useCache)
            {
                listSessionsCardInfo = _memoryCache.Get<List<SessionCardInfo>>(cacheKey);
            }

            if (listSessionsCardInfo is null || listSessionsCardInfo.Count == 0)
            {
                listSessionsCardInfo = new List<SessionCardInfo>();
                var listEntreesEntites = new List<EntreeEntity>();
                try
                {
                    // var sessionsEntities = _orderRepository.GetAllOrdersByIdentiteId(structureId, identiteId);
                    listEntreesEntites = _entreeRepository.GetAllTicketsByIdentityId(structureId, identiteId, orderId).ToList();
                }
                catch (Exception ex)
                {
                    if (ex.Message.ToLower().Contains("beneficiaire_identite_id"))
                    {
                        _entreeRepository.CreateColumnBeneficiaireId(structureId);
                    }
                    //throw ex;
                }

                List<int> listSeancesIds = listEntreesEntites.Select(s => s.SeanceId).Distinct().ToList();

                List<int> listIdentitesRelieesCmdLignes = listEntreesEntites.Select(e => e.IdentiteIdCommandeLigne).Distinct().ToList();
                List<int> listIdentitesPayeur = listEntreesEntites.Select(e => e.IdentiteIdPayeur).Distinct().ToList();
                List<int> listIdentitesEComplementIdentitesId = listEntreesEntites.Where(e => e.EntreeComplement != null).Select(e => e.EntreeComplement.BeneficiaireIdentiteId).Distinct().ToList();


                List<int> listIdentitesPayeurEComplementIdentiteId = listEntreesEntites.Where(e => e.EntreeComplement != null).Select(e => e.EntreeComplement.IdentiteId).Distinct().ToList();

                List<int> listAllIdentites = listIdentitesRelieesCmdLignes.Concat(listIdentitesPayeur).Concat(listIdentitesEComplementIdentitesId).Concat(listIdentitesPayeurEComplementIdentiteId).Distinct().ToList();

                var listIdentiteEntities = _identiteRepository.Load(structureId, listAllIdentites, "", 0, "en", "");

                List<int> listConsumersEComplementConsommId = listEntreesEntites.Where(e => e.EntreeComplement != null).Select(e => e.EntreeComplement.ConsommateurID).Distinct().ToList();

                var listConsumersEntities = new List<ConsumerEntity>();

                if (listConsumersEComplementConsommId.Count > 0)
                {
                    listConsumersEntities = _consumerRepository.Load(structureId, listConsumersEComplementConsommId);
                }

                var listSeancesEntitites = _sessionRepository.LoadE(structureId, listSeancesIds);

                foreach (var sessionE in listSeancesEntitites)
                {
                    sessionE.ListEntrees = listEntreesEntites.Where(e => e.SeanceId == sessionE.SeanceId).ToList();
                }

                listSessionsCardInfo = _mapper.Map<List<SessionCardInfo>>(listSeancesEntitites);

                foreach (var sessionE in listSessionsCardInfo)
                {
                    foreach (var seat in sessionE.ListSeats)
                    {

                        if (seat.ConsommateurConsumerId > 0)
                        {
                            var cons = listConsumersEntities.Where(c => c.consumerid == seat.ConsommateurConsumerId).FirstOrDefault();
                            if (cons != null)
                            {
                                seat.consumerConsumer = _mapper.Map<ConsumerDTO>(cons);
                            }
                            else
                            {
                                // ne retrouve pas le consumer dans la table ?!?
                                seat.ConsommateurConsumerId = 0;
                                seat.consumerConsumer = new ConsumerDTO();
                            }
                        }

                        if (seat.ConsommateurIdentiteId > 0)
                        {
                            seat.consumerIdentite = listIdentiteEntities.Where(i => i.IdentiteId == seat.ConsommateurIdentiteId).FirstOrDefault();
                        }
                        else
                        {
                            seat.consumerIdentite = listIdentiteEntities.Where(i => i.IdentiteId == seat.IdentiteIdCommandeLigne).FirstOrDefault();

                        }
                    }
                }

                if (listSessionsCardInfo is not null && listSessionsCardInfo.Count > 0 && useCache)
                    _memoryCache.Set(cacheKey, listSessionsCardInfo, TimeSpan.FromSeconds(10));
            }


            return listSessionsCardInfo.OrderBy(s => s.SessionDateDeb).ToList();
        }





        public string FillSessionMessageLockByLangCode(int structureId, int sessionId, string langCode)
        {

            StructureInfosHelper.SetStructure(structureId);

            var traductions = _traductionRepository.FindBy(a => a.TableId == sessionId, structureId);
            var traductionOfGpManifestation = traductions.FirstOrDefault(t => t.TraductionTable.ToUpper().Contains("GP_SEANCE") && t.TraductionChamps.ToUpper().Contains("LOCK_SEANCE_COMMENT"));

            string defaultSessionTraduction = _translateManager.GetTranslationByKeyWord(structureId, "Widget_Catalog_MsgSessionLocked", langCode);
            if (traductionOfGpManifestation is not null)
            {
                var language = StructureInfosHelper.GetLanguageByLangCode(langCode);
                string sessiontraduction = traductionOfGpManifestation.GetTraductionByLangueId(language.LanguageId);

                if (string.IsNullOrEmpty(sessiontraduction))
                    return defaultSessionTraduction;
                else
                    return sessiontraduction;

            }

            return defaultSessionTraduction;

        }



        /// <summary>
        /// Retourne la liste des séances bloquées pour une manifestation 
        /// </summary>
        /// <param name="structureId"></param>
        /// <returns></returns>
        public async Task<List<SessionLockDTO>> GetLocksSessionsByEventIdAsync(int structureId, int eventId)
        {
            try
            {
                IEnumerable<SeanceEntity> seanceEntities = await _sessionRepository.GetAllSessionsByEventIdOnSaleAsync(structureId, eventId).ConfigureAwait(false);

                foreach (var seance in seanceEntities)
                {
                    seance.Lieu = _lieuRepository.GetById(structureId, seance.LieuId);

                    seance.GPSeance = _gPSeanceRepository.GetById(structureId, seance.SeanceId);

                    if (seance.GPSeance.GpSeanceLockSeanceCommentId != null)
                    {

                        var traduction = await _traductionRepository.GetByIdAsync(structureId, seance.GPSeance.GpSeanceLockSeanceCommentId.Value)
                                                                   .ConfigureAwait(false);

                        seance.GPSeance.TraductionGpManifestationsForLock = traduction;

                    }
                }

                var resultSessionDTO = _mapper.Map<List<SessionLockDTO>>(seanceEntities);

                var traductionsLockSessions = resultSessionDTO.SelectMany(sess => sess.TraductionGpLockSessions).ToList();

                _languageManager.SetLangCode(structureId, traductionsLockSessions);

                return resultSessionDTO;
            }
            catch (Exception ex)
            {

                throw;
            }

        }


        public async Task<bool> UpdateTraductionGpSeanceLockAsync(int structureId, List<SessionLockDTO> sessionsLocks)
        {
            bool isListeAttenteColumnExist = await _gPSeanceRepository.CheckExistColumnAsync(structureId, "gp_seance_lock_seance_comment_id");

            if (!isListeAttenteColumnExist)
                _gPSeanceRepository.CreateColumnTraductionForLock(structureId);


            //lit le fichier XML et enregistre les manifestations bloquées en base de données
            string customerFileIndivPath = _configuration["CustomerfilesIndivPhysicalPath"]!
                .Replace("{structureId}", structureId.ToString("0000"));

            string indivIhmManifsPath = Path.Combine(customerFileIndivPath, "IHM", "messages_seances.xml");


            IEnumerable<SeanceEntity> seanceEntities = _mapper.Map<List<SeanceEntity>>(sessionsLocks);

            using IDbContext dbContextTransactionScope = _dbContext.BeginTransaction(ContextType.Open, structureId);

            try
            {
                foreach (var seance in seanceEntities)
                {
                    if (seance.GPSeance != null)
                    {
                        if (seance.GPSeance.TraductionGpManifestationsForLock != null)
                        {
                            var traduction = seance.GPSeance.TraductionGpManifestationsForLock;

                            if (traduction.TraductionId == 0)
                            {
                                //Récupère le dernier ID traduction dans la base de données
                                var traductionsIdDB = await _traductionRepository.GetAllAsync(structureId).ConfigureAwait(false);

                                //Existe t-il une traduction avec cette manifestation
                                var traductionAvecCetteSeanceInDB = traductionsIdDB.FirstOrDefault(t => t.TableId == seance.SeanceId && t.TraductionTable.ToUpper().Equals("GP_SEANCE"));
                                if (traductionAvecCetteSeanceInDB is null)
                                {
                                    int newTraductionId = traductionsIdDB.OrderByDescending(t => t.TraductionId).First().TraductionId + 1;

                                    traduction.TraductionId += newTraductionId;
                                    traduction.TraductionTable = "GP_SEANCE";
                                    traduction.TraductionChamps = "LOCK_SEANCE_COMMENT";
                                    traduction.TableId = seance.SeanceId;

                                    bool isInserted = _traductionRepository.InsertWithoutIdReturn(structureId, traduction, isAutoIncremented: false);

                                    if (!isInserted)
                                        throw new Exception("Traduction not insert in bdd");
                                }
                                else
                                {
                                    bool isUpdated = _traductionManager.UpdateTraduction(structureId, traductionAvecCetteSeanceInDB, traduction);

                                    if (!isUpdated)
                                        throw new Exception($"Traduction not updated in bdd {traduction.TraductionId}");
                                }
                            }
                            else
                            {

                                TraductionEntity traductionInDB = await _traductionRepository.GetByIdAsync(structureId, traduction.TraductionId)
                                     ?? throw new ArgumentNullException($"Traduction Not Found {traduction.TraductionId}");

                                bool isUpdated = _traductionManager.UpdateTraduction(structureId, traductionInDB, traduction);

                                if (!isUpdated)
                                    throw new Exception($"Traduction not updated in bdd {traduction.TraductionId}");

                            }

                            seance.GPSeance.GpSeanceLockSeanceCommentId = traduction.TraductionId;
                        }

                        var gPSeance = _gPSeanceRepository.GetById(structureId, seance.SeanceId)
                                    ?? throw new ArgumentNullException($"gPSeance Not Found {seance.SeanceId}");

                        if (gPSeance != null)
                        {
                            gPSeance.IsLock = seance.GPSeance.IsLock;
                            gPSeance.GpSeanceLockSeanceCommentId = seance.GPSeance.GpSeanceLockSeanceCommentId;

                            bool gpSeanceIsUpdated = _gPSeanceRepository.Update(structureId, gPSeance);

                            if (!gpSeanceIsUpdated)
                                throw new Exception($"gPSeance not update in bdd {seance.GPSeance.SeanceId}");
                        }

                    }
                    seance.GPSeance.SeanceId = seance.SeanceId;
                    WriteToMessagesSeancesXmlFile(indivIhmManifsPath, seance.GPSeance);
                }

                dbContextTransactionScope.Commit();

                _redisCacheService.RemoveKeysContainPartOfKey($"{structureId}_Catalog");

            }
            catch (Exception ex)
            {
                dbContextTransactionScope.Rollback();
                throw;
            }

            return true;
        }
        private void WriteToMessagesSeancesXmlFile(string xmlFilePath, GPSeanceEntity gpSeance)
        {
            if (File.Exists(xmlFilePath))
            {

                XDocument doc = XDocument.Load(xmlFilePath);

                // Rechercher l'élément <islock> dont le sous-élément <seance_id> correspond à l'ID
                XElement xelementEvent = doc.Root.Elements("islock")
                    .FirstOrDefault(m => m.Element("seance_id") != null && m.Element("seance_id").Value == gpSeance.SeanceId.ToString());

                // S'il existe, le supprimer
                if (xelementEvent != null)
                {
                    xelementEvent.Remove();
                }

                if (gpSeance.IsLock)
                {
                    // Créer le nouvel élément <islock> avec l'attribut et les sous-éléments requis
                    XElement newNode = new XElement("islock",
                       new XAttribute("seance_id", gpSeance.SeanceId),
                       new XElement("seance_id", gpSeance.SeanceId),
                       new XElement("message", gpSeance.TraductionGpManifestationsForLock.Langue1) // Remplacer par le message souhaité
                    );

                    // Ajouter le nouvel élément à la racine
                    doc.Root.Add(newNode);

                }

                // Sauvegarder les modifications dans le fichier XML
                doc.Save(xmlFilePath);

            }
        }

        #region SelectLookup

        /// <summary>
        /// Récupère la liste des séances à venir d'une manifestation 
        /// </summary>
        /// <param name="structureId"></param>
        /// <param name="eventId"></param>
        /// <returns></returns>
        public async Task<List<SelectLookup>> SelectLookupSessionsAsync(int structureId, int eventId)
        {
            IEnumerable<SeanceEntity> sessions = await _sessionRepository.FindCustomEntityBy(s => s.ManifestationId == eventId && s.SeanceDateDeb > DateTime.Now, structureId)
                                                                         .OrderBy(s => s.SeanceDateDeb)
                                                                         .ToEntityListAsync()
                                                                         .ConfigureAwait(false);

            return sessions.Select(s => new SelectLookup()
            {
                Value = s.SeanceId.ToString(),
                Libelle = $"{s.SeanceId} - {s.SeanceDateDeb} - {s.SeanceDateFin}",
                AdditionalInfo = JsonConvert.SerializeObject(new
                {
                    s.LieuId,
                    s.SeanceDateDeb,
                    s.SeanceDateFin,
                }),
            })
            .ToList();
        }


        #endregion
    }
}
