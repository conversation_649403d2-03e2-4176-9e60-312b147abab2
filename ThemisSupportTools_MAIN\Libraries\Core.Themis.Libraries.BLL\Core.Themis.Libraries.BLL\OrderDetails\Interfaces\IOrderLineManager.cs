﻿using Core.Themis.Libraries.DTO.Orders.Details;
using System.Collections.Generic;
using Microsoft.Data.SqlClient;

namespace Core.Themis.Libraries.BLL.OrderDetails.Interfaces
{
    /// <summary>
    /// commande_ligne
    /// </summary>
    public interface IOrderLineManager
    {
        /// <summary>
        /// get compteur 
        /// </summary>
        /// <param name="structureId"></param>
        /// <returns></returns>
        int InstancieOrderLineId(int structureId, SqlConnection cnxOpen, SqlTransaction? mysqlts = null);

        /// <summary>
        /// insert into [commande_ligne]
        /// </summary>
        /// <returns></returns>
        int CreateOrderLine(int structureId, OrderLineDTO cmdLigne, SqlConnection cnxOpen, SqlTransaction? mysqlts = null);

        List<OrderLineDTO> Get(int structureId, int OrderId);

        bool UpdateCommandeLigneIcone(int structureId, int dossierId, int EventId, int OrderId, int icone);

    }
}
