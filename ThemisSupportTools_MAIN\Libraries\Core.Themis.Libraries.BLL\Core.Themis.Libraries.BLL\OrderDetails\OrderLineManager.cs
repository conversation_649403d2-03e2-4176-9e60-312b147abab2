﻿using Core.Themis.Libraries.BLL.OrderDetails.Interfaces;
using Core.Themis.Libraries.Data.Repositories.Open.Orders.OrderDetails.Interfaces;
using Core.Themis.Libraries.DTO.Orders.Details;
using System.Collections.Generic;
using Microsoft.Data.SqlClient;

namespace Core.Themis.Libraries.BLL.OrderDetails
{
    /// <summary>
    /// commande_ligne
    /// </summary>
    public class OrderLineManager : IOrderLineManager
    {

        private readonly IOrderLineRepository _orderlineRepository;

        public OrderLineManager(IOrderLineRepository barCodeRepository)
        {
            _orderlineRepository = barCodeRepository;
        }

        public List<OrderLineDTO> Get(int structureId, int OrderId)
        {
            try
            {
                return _orderlineRepository.Get(structureId, OrderId);
            }
            catch
            {
                throw;
            }

        }

        /// <summary>
        /// insert into [commande_ligne]
        /// </summary>
        /// <param name="structureId"></param>
        /// <param name="cmdLigne"></param>
        /// <returns></returns>
        public int CreateOrderLine(int structureId, OrderLineDTO cmdLigne, SqlConnection cnxOpen, SqlTransaction? mysqlts = null)
        {
            try
            {
                return _orderlineRepository.CreateOrderLine(structureId, cmdLigne, cnxOpen, mysqlts);
            }
            catch
            {
                throw;
            }
        }

        public bool UpdateCommandeLigneIcone(int structureId, int dossierId, int eventId, int orderId, int icone)
        {
            return icone == _orderlineRepository.UpdateCommandeLigneIcone(structureId, dossierId, eventId, orderId, icone);
        }

        /// <summary>
        /// Met à jour le compteur(CPT_COMMANDE_LIGNE.compteur) pour la table Commande.commande_id 
        /// </summary>
        /// <param name="structureId"></param>
        /// <returns></returns>
        public int InstancieOrderLineId(int structureId, SqlConnection cnxOpen, SqlTransaction? mysqlts = null)
        {

            try
            {

                return _orderlineRepository.InstancieOrderLineId(structureId, cnxOpen, mysqlts);
            }
            catch
            {
                throw;
            }

        }

        public static OrderLineDTO InitCommandeLigne(DossierSeatDTO m_dossier)
        {
            OrderLineDTO m_CommandeLigne = new()
            {
                OrderId = m_dossier.OrderId,
                EventId = m_dossier.Event.EventId,
                SessionId = m_dossier.Session.SessionId,
                IdentityId = m_dossier.IdentityId,
                DossierId = m_dossier.DossierId,
                GroupId = 0,
                TypeLine = "DOS",
                FormulaId = 0,
                AboId = 0,
                ConstraintId = 0,
                AcompteNum = 0,

                //spécifique commande_ligne_comp
                State = m_dossier.State,
                Count = m_dossier.Coefficient,
                Amount1 = m_dossier.Amount1,
                Amount2 = m_dossier.Amount2,
                Amount3 = m_dossier.Amount3,
                Amount4 = m_dossier.Amount4,
                Amount5 = m_dossier.Amount5,
                Amount6 = m_dossier.Amount6,
                Amount7 = m_dossier.Amount7,
                Amount8 = m_dossier.Amount8,
                Amount9 = m_dossier.Amount9,
                Amount10 = m_dossier.Amount10,
                FiliereId = m_dossier.FiliereId,
                ClientName = m_dossier.IdentNomPrenomConsomm,
                FanCard = "",
                DossierC = m_dossier.DossierC,
                IconeId = m_dossier.IconeId,
                NumDossier = m_dossier.NumDossier,
                OperatorId = m_dossier.OperatorId
            };

            return m_CommandeLigne;
        }
    }
}
