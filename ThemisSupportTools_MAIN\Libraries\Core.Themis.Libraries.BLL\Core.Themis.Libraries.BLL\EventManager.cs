﻿using AutoMapper;
using Core.Themis.Libraries.BLL.Helpers;
using Core.Themis.Libraries.BLL.Interfaces;
using Core.Themis.Libraries.BLL.Managers.Interfaces;
using Core.Themis.Libraries.BLL.Managers.Traduction.Interfaces;
using Core.Themis.Libraries.BLL.Translations.Interfaces;
using Core.Themis.Libraries.BLL.Services.RedisCache.Interfaces;

using Core.Themis.Libraries.Data.DBContext;
using Core.Themis.Libraries.Data.DBContext.Interfaces;
using Core.Themis.Libraries.Data.Entities.Open.EventsSessions;
using Core.Themis.Libraries.Data.Entities.Open.Traduction;
using Core.Themis.Libraries.Data.Repositories.Open.Cinema.Interfaces;
using Core.Themis.Libraries.Data.Repositories.Open.EventsSessions;
using Core.Themis.Libraries.Data.Repositories.Open.EventsSessions.Interfaces;
using Core.Themis.Libraries.Data.Repositories.Open.GestionPlaces.Interfaces;
using Core.Themis.Libraries.Data.Repositories.Open.HomeModular;
using Core.Themis.Libraries.Data.Repositories.Open.HomeModular.Interfaces;
using Core.Themis.Libraries.Data.Repositories.Open.Traduction.Interfaces;
using Core.Themis.Libraries.DTO.AdminSphere.VenteIndiv;
using Core.Themis.Libraries.DTO.Enums.HomeModular;
using Core.Themis.Libraries.DTO.EventsSessions;
using Core.Themis.Libraries.DTO.HomeModular.Clients;
using Core.Themis.Libraries.DTO.HomeModular.Clients.ViewModels;
using Core.Themis.Libraries.DTO.Insurance.Meetch.Subscription;
using Core.Themis.Libraries.DTO.Lookup;
using Core.Themis.Libraries.Utilities.Extensions;
using Core.Themis.Libraries.Utilities.Helpers;
using Core.Themis.Libraries.Utilities.Helpers.Cache;
using Core.Themis.Libraries.Utilities.Helpers.Images;
using Core.Themis.Libraries.Utilities.Logging;
using Microsoft.Extensions.Caching.Memory;
using Mono.Cecil;
using SixLabors.ImageSharp.PixelFormats;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Data;
using Microsoft.Data.SqlClient;
using System.Drawing;
using System.Drawing.Imaging;
using System.IO;
using System.Linq;
using System.Runtime.InteropServices;
using System.Threading.Tasks;
using System.Xml.Linq;
using static Core.Themis.Libraries.DTO.EventsSessions.EventDTO;
using Core.Themis.Libraries.Utilities.Helpers.GestionPlace;
using static Core.Themis.Libraries.Utilities.Helpers.GestionPlace.GestionPlaceHelper;
using Core.Themis.Libraries.DTO.exposedObjects;


namespace Core.Themis.Libraries.BLL
{
    public class EventManager : IEventManager
    {
        private readonly IEventRepository _eventRepository;
        private readonly ISessionRepository _sessionRepository;
        private readonly IHomeModularRepository _homeModularRepository;
        private readonly IGPManifestationRepository _gPManifestationRepository;
        private readonly IMapper _mapper;
        private readonly IMemoryCache _memoryCache;
        private readonly ILowerPriceEventRepository _lowerPriceEventRepository;
        private readonly ITraductionRepository _traductionRepository;
        private readonly ITranslateManager _translateManager;
        private readonly ILanguageManager _languageManager;
        private readonly ITraductionManager _traductionManager;
        private readonly IManifestationInfosRepository _eventInfosRepository;
        private readonly IManifestationImageRepository _eventImageRepository;
        private readonly IManifestationGroupeRepository _manifestationGroupeRepository;
        private readonly IManifestationSuperGroupeRepository _manifestationSuperGroupeRepository;



        private readonly IEventGenreRepository _eventGenreRepository;
        private readonly IEventSousGenreRepository _eventSousGenreRepository;
        private readonly IManifestationInfosRepository _manifestationInfosRepository;
        private readonly IProducteurRepository _producteurRepository;
        private readonly ITraductionManifestationInfosRepository _traductionManifestationInfosRepository;
        private readonly IDisciplineRepository _disciplineRepository;
        private readonly ISessionManager _sessionManager;
        private readonly IGestionPlaceRepository _gestionPlaceRepository;

        private readonly IDbContext _dbContext;
        private readonly IConfiguration _configuration;

        private readonly IRedisCacheService _redisCacheService;

        public EventManager(IEventRepository eventRepository,
            IHomeModularRepository homeModularRepository,
            IMapper mapper,
            IMemoryCache memoryCache,
            ISessionRepository sessionRepository,
            IGPManifestationRepository gPManifestationRepository,
            ILowerPriceEventRepository lowerPriceEventRepository,
            ITraductionRepository traductionRepository,
            ITranslateManager translateManager,
            ILanguageManager languageManager,
            ITraductionManager traductionManager,
            ISessionManager sessionManager,
            IGestionPlaceRepository gestionPlaceRepository,
            IManifestationInfosRepository eventInfosRepository,
            IManifestationImageRepository eventImageRepository,
            IEventGenreRepository eventGenreRepository,
            IEventSousGenreRepository eventSousGenreRepository,
            IManifestationInfosRepository manifestationInfosRepository,

            IManifestationGroupeRepository manifestationGroupeRepository,
            IManifestationSuperGroupeRepository manifestationSuperGroupeRepository,
            //IManifestationSuperGroupe


            IProducteurRepository producteurRepository,
            ITraductionManifestationInfosRepository traductionManifestationInfosRepository,
            IDisciplineRepository disciplineRepository,
            IDbContext dbContext,
            IConfiguration configuration,
            IRedisCacheService redisCacheService)
        {
            _eventRepository = eventRepository;
            _homeModularRepository = homeModularRepository;
            _mapper = mapper;
            _memoryCache = memoryCache;
            _sessionRepository = sessionRepository;
            _gPManifestationRepository = gPManifestationRepository;
            _lowerPriceEventRepository = lowerPriceEventRepository;
            _traductionRepository = traductionRepository;
            _translateManager = translateManager;
            _languageManager = languageManager;
            _traductionManager = traductionManager;
            _gestionPlaceRepository = gestionPlaceRepository;
            _eventInfosRepository = eventInfosRepository;
            _eventImageRepository = eventImageRepository;
            _eventGenreRepository = eventGenreRepository;
            _manifestationGroupeRepository  = manifestationGroupeRepository;
            _manifestationSuperGroupeRepository  = manifestationSuperGroupeRepository;

            _eventSousGenreRepository = eventSousGenreRepository;
            _manifestationInfosRepository = manifestationInfosRepository;
            _producteurRepository = producteurRepository;
            _traductionManifestationInfosRepository = traductionManifestationInfosRepository;
            _disciplineRepository = disciplineRepository;


            _sessionManager = sessionManager;
            _dbContext = dbContext;
            _configuration = configuration;
            _redisCacheService = redisCacheService;
        }
        private static readonly RodrigueNLogger RodrigueLogger = new();

        public async Task<bool> CheckLowerPriceExist(int structureId)
        {
            return await _lowerPriceEventRepository.CheckExistTableAsync(structureId);
        }

        public async Task<IEnumerable<EventDTO>> GetAllEventsOnSales(int structureId)
        {
            IEnumerable<ManifestationEntity> events = await _eventRepository.GetEventsOnSales(structureId);

            return _mapper.Map<IEnumerable<EventDTO>>(events);
        }

        public  IEnumerable<EventDTO> GetAllEventsAfterDate(int structureId, DateTime? afterDate)
        {
            DateTime pafterDate = afterDate ?? DateTime.Now;
          
            IEnumerable<ManifestationEntity> events =  _eventRepository.GetAllEventsAfterADate(structureId, "", pafterDate);
            return _mapper.Map<IEnumerable<EventDTO>>(events);
        }



        /// <summary>
        /// utilisation dans le carrousel home
        /// </summary>
        /// <param name="structureId"></param>
        /// <param name="langCode"></param>
        /// <param name="isHomeTeasing"></param>
        /// <returns>List( emplacement Id, List<manifs>) )) </returns>

        public Dictionary<int, List<EventFeaturesDTO>> GetEventsFeatures(int structureId, string langCode, HomeModularEmplacementGroupDTO emplacementGroup, int buyerProfilId, bool isHomeTeasing = false)
        {
            var userConfigEventFeature = emplacementGroup.HomeModularBlockEmplacements.Where(be => be.BlockTypeId == (int)HomeModularBlockTypeEnum.EventsFeatured).FirstOrDefault();

            if (userConfigEventFeature != null)
            {
                return LoadEventsFeatures(structureId, langCode, isHomeTeasing, buyerProfilId);
            }
            return null;
        }


        /// <summary>
        /// obtenir la liste des séances et grille tarif disponibles de la manif (% aux regles de ventes)
        /// </summary>
        /// <param name="structureId"></param>
        /// <param name="langCode"></param>
        /// <param name="eventId"></param>
        /// <param name="identityId"></param>
        /// <param name="profilAcheteurId"></param>
        /// <param name="dateFrom"></param>
        /// <param name="dateTo"></param>
        /// <returns></returns>
        /// <exception cref="Exception"></exception>
        public EventDTO? GetEventSessions(int structureId, string langCode, int eventId, int identityId, int profilAcheteurId, DateTime dateFrom, DateTime dateTo)
        {
            try
            {
                var resultQuery = _eventRepository.GetEventsSessionByFilter(structureId, eventId, identityId, profilAcheteurId, dateFrom, dateTo);
                var evtDTO = _mapper.Map<EventDTO>(resultQuery);
                if (evtDTO.EventIsLock)
                {
                    evtDTO.EventIsLockMessage = FillEventMessageLockByLangCode(structureId, eventId, langCode);
                    evtDTO.ListSessions.ForEach(sess => sess.SessionIsLock = true);

                }

                foreach (var session in evtDTO.ListSessions)
                {
                    if (session.SessionIsLock)
                    {
                        session.SessionIsLockMessage = _sessionManager.FillSessionMessageLockByLangCode(structureId, session.SessionId, langCode);
                    }
                }
                return evtDTO;

            }
            catch (Exception ex)
            {
                throw;
            }

        }


        /// <summary>
        /// obtenir la liste des séances et grille tarif disponibles de la manif (% aux regles de ventes)
        /// </summary>
        /// <returns></returns>
        public EventDTO GetEventSessionsDTO(int structureId, string langCode, int eventId, int identityId, int profilAcheteurId, DateTime dateFrom, DateTime dateTo)
        {
            try
            {
                var evt = _eventRepository.GetEventsSessionsDTO(structureId, langCode, eventId, identityId, profilAcheteurId, dateFrom, dateTo);

                if (evt != null)
                {
                    if (evt.EventIsLock)
                    {
                        evt.EventIsLockMessage = FillEventMessageLockByLangCode(structureId, eventId, langCode);

                        evt.ListSessions.ForEach(sess => sess.SessionIsLock = true);
                    }

                    foreach (var session in evt.ListSessions)
                    {
                        if (session.SessionIsLock)
                        {
                            session.SessionIsLockMessage = _sessionManager.FillSessionMessageLockByLangCode(structureId, session.SessionId, langCode);
                        }
                    }
                }

                return evt;
            }
            catch
            {
                throw;
            }
        }


        private string FillEventMessageLockByLangCode(int structureId, int eventId, string langCode)
        {


            string tableName = "'GP_MANIFESTATION'";
            var traductions = _traductionRepository.FindBy(a => a.TableId == eventId && a.TraductionTable == tableName, structureId);

            var traductionOfGpManifestation = traductions.FirstOrDefault(t => t.TraductionTable.ToUpper().Contains("GP_MANIFESTATION") && t.TraductionChamps.ToUpper().Contains("LOCK_MANIF_COMMENT"));

            string defaultEventTraduction = _translateManager.GetTranslationByKeyWord(structureId, "Widget_Catalog_MsgEventLockedList", langCode);
            if (traductionOfGpManifestation is not null)
            {
                StructureInfosHelper.SetStructure(structureId);
                var language = StructureInfosHelper.GetLanguageByLangCode(langCode);
                string eventTraduction = traductionOfGpManifestation.GetTraductionByLangueId(language.LanguageId);

                if (string.IsNullOrEmpty(eventTraduction))
                    return defaultEventTraduction;
                else
                    return eventTraduction;

            }

            return defaultEventTraduction;

        }

        /// <summary>
        /// utilisation dans le carrousel home
        /// </summary>
        /// <param name="structureId"></param>
        /// <param name="langCode"></param>
        /// <param name="isHomeTeasing"></param>
        /// <returns>List( emplacement Id, List<manifs>) )) </returns>
        public Dictionary<int, List<EventFeaturesDTO>> LoadEventsFeatures(int structureId, string langCode, bool isHomeTeasing, int buyerProfilId)
        {
            try
            {
                Dictionary<int, List<EventFeaturesDTO>> listReturn = new Dictionary<int, List<EventFeaturesDTO>>();
                var blocksEmplcementOfThisBlockTypeId = _homeModularRepository.GetBlockEmplacementByBlockTypeId(structureId, (int)HomeModularBlockTypeEnum.EventsFeatured);

                foreach (var blockEmplacement in blocksEmplcementOfThisBlockTypeId)
                {
                    var functionsOfThisBlockType = _homeModularRepository.GetFunctionsOfBlockTypeById(structureId, (int)HomeModularBlockTypeEnum.EventsFeatured);
                    if (functionsOfThisBlockType is not null)
                    {
                        string sqlTableFunction = "";
                        foreach (var function in functionsOfThisBlockType)
                        {
                            var blockFunction = function.HomeModularBlockFunctions.Where(f => f.FunctionsID == function.FunctionsId).FirstOrDefault();
                            sqlTableFunction += $"INSERT INTO @TblFunctions VALUES ({blockFunction.BlockFunctionId}, {function.FunctionsId}, '{function.FunctionsNAME}')" + Environment.NewLine;
                        }

                        var resultEventsFeaturesQuery = _eventRepository.GetEventsFeaturesForHomeModular(structureId, langCode, blockEmplacement.BlockEmplacementId, isHomeTeasing, sqlTableFunction, buyerProfilId);
                        resultEventsFeaturesQuery.ToList().ForEach(e => e.EventImageUrl = ImagesHelper.GetEventImagePath(structureId, e.EventId));

                        listReturn.Add(blockEmplacement.BlockEmplacementId, resultEventsFeaturesQuery.ToList());
                    }
                }
                return listReturn;

            }
            catch (Exception ex)
            {
                throw;
            }
        }

        /// <summary>
        /// /!\ ca filtre sur les séance en ventes !
        /// </summary>
        /// <param name="structureId"></param>
        /// <param name="manifestation"></param>
        /// <param name="filterFormModel"></param>
        /// <param name="offersEntities"></param>
        /// <returns></returns>
        private async Task<ManifestationEntity> SetSessionsAndInformationsDependanciesAsync(int structureId, ManifestationEntity manifestation, string langCode)
        {
            try
            {

                manifestation.Producteur = await _producteurRepository.GetByIdAsync(structureId, manifestation.ProducteurId).ConfigureAwait(false);


                manifestation.ManifestationInfos = await _manifestationInfosRepository.GetByEventIdAsync(structureId, manifestation.ManifestationId).ConfigureAwait(false);

                bool checkExistTraductionManifInfosTable = await _traductionManifestationInfosRepository.CheckExistTableAsync(structureId).ConfigureAwait(false);
                if (manifestation.ManifestationInfos != null && checkExistTraductionManifInfosTable)
                {
                    if (manifestation.ManifestationInfos.TraductionManifestationInfos == null)
                        manifestation.ManifestationInfos.TraductionManifestationInfos = new List<TraductionManifestationInfosEntity>();

                    var traductionManifestationInfos = await _traductionManifestationInfosRepository.GetTraductionByEventIdAndLangCodeAsync(structureId, manifestation.ManifestationId, langCode);

                    if (traductionManifestationInfos != null)
                        manifestation.ManifestationInfos.TraductionManifestationInfos.Add(traductionManifestationInfos);
                }


                manifestation.Discipline = await _disciplineRepository.GetDisciplineByEventIdAsync(structureId, manifestation.ManifestationId).ConfigureAwait(false);

                if (manifestation.Discipline != null)
                {
                    var traductionsDiscipline = await _disciplineRepository.GetTranslationOfDisciplineByIdAndLangCodeAsync(structureId, manifestation.Discipline.Id, langCode)
                                              .ConfigureAwait(false);

                    if (manifestation.Discipline.TraductionsDiscipline == null)
                        manifestation.Discipline.TraductionsDiscipline = new List<TraductionDisciplineEntity>();

                    manifestation.Discipline.TraductionsDiscipline.Add(traductionsDiscipline);

                }

                var sousGenre = await _eventSousGenreRepository.GetByIdAsync(structureId, manifestation.IdGenre, langCode)
                                             .ConfigureAwait(false);

                if (sousGenre != null)
                {
                    if (sousGenre.GroupeId > 0)
                    {
                        var genreE = _eventGenreRepository.GetById(structureId, sousGenre.GroupeId, langCode);
                        //var genre = aaa.Where(x => x.Id == sousGenre.GroupeId).FirstOrDefault();
                        sousGenre.Genre = genreE;
                    }

                    manifestation.SousGenre = sousGenre;

                }

                var groupe = _manifestationGroupeRepository.FindCustomEntityBy(g => g.ManifGroupeId == manifestation.ManifestationGroupeId, structureId).Include(gr => gr.ManifestationSuperGroupe).ToEntity();

                manifestation.ManifestationGroupe = groupe;

                return manifestation;
            }
            catch
            {

                throw;
            }


        }


        /// <summary>
        /// Ramène les prochaines manifs à venir sur un temps donné
        /// </summary>
        /// <param name="structureId"></param>
        /// <param name="duration">en minutes</param>
        /// <param name="langCode"></param>
        /// <returns></returns>
        public async Task<List<EventDTO>> LoadEventsSessionsIntervalAsync(int structureId, int fromminutes, int tominutes, string langCode)
        {
            try
            {
                List<EventDTO> listEvents = new();
                string cacheName = $"EventManager.LoadEventsSessionsInterval.{structureId}.{langCode}.{fromminutes}.{tominutes}";
                if (!_memoryCache.TryGetValue(cacheName, out listEvents))// Look for cache key.
                {
                    //listEvents = _eventRepository.LoadEventsSessionsInterval(structureId, langCode, fromminutes: fromminutes, tominutes: tominutes);
                    var listSessionsEntities = _sessionRepository.LoadSessionsInterval(structureId, langCode, fromminutes: fromminutes, tominutes: tominutes);
                    var listSessionsEntitiesWithEvents = listSessionsEntities.Include(s => s.Manifestation).Include(s => s.Lieu).ToEntityList(); // sessions avec chacune leur manif remplies

                    //var ListManifs = listSessionsEntitiesWithEvents.DistinctBy(s => s.ManifestationId).Select(s => s.Manifestation).ToList();

                    var ListManifsId = listSessionsEntitiesWithEvents.DistinctBy(s => s.ManifestationId).Select(s => s.ManifestationId).ToList();

                    List<ManifestationEntity> listEventsEnts = new();
                    if (ListManifsId.Count > 0)
                    {
                        //_eventRepository.FindBy(e => ids.Contains(e.ManifestationId), 991);
                        listEventsEnts = _eventRepository.FindBy(e => ListManifsId.Contains(e.ManifestationId), structureId).ToList();


                        var tasks = listEventsEnts.Select(evt => SetSessionsAndInformationsDependanciesAsync(structureId, evt, langCode));
                        var result = await Task.WhenAll(tasks).ConfigureAwait(false);

                        //var list

                        listEventsEnts.ForEach(m =>
                        {
                            m.Seances = listSessionsEntitiesWithEvents.Where(s => s.ManifestationId == m.ManifestationId).ToList();
                        }
                        );

                        //ListManifs.ForEach(m =>
                        //{
                        //    m.Seances = listSessionsEntitiesWithEvents.Where(s => s.ManifestationId == m.ManifestationId).ToList();
                        //}
                        //);
                    }

                    listEvents = _mapper.Map<List<EventDTO>>(listEventsEnts);

                    var cacheExpiryOptions = new MemoryCacheEntryOptions
                    {
                        AbsoluteExpiration = DateTime.Now.AddSeconds(60),
                        Priority = CacheItemPriority.Low,
                    };
                    _memoryCache.Set(cacheName, listEvents, cacheExpiryOptions);
                }

                return listEvents;
            }
            catch
            {
                throw;
            }
        }

        /// <summary>
        /// Ramène les prochaines manifs à venir sur un temps donné
        /// </summary>
        /// <param name="structureId"></param>
        /// <param name="duration">en minutes</param>
        /// <param name="langCode"></param>
        /// <returns></returns>
        public async Task<List<EventDTO>> LoadFuturesEventsSessionsAsync(int structureId, int tominutes, string langCode)
        {
            try
            {
                return await LoadEventsSessionsIntervalAsync(structureId, 0, tominutes, langCode); // _eventRepository.LoadEventsSessionsInterval(structureId, langCode, tominutes: tominutes);
            }
            catch
            {
                throw;
            }
        }

        /// <summary>
        /// renseigne les proprietes des evenements (description, etc)
        /// </summary>
        /// <param name="structureId"></param>
        /// <param name="ListEvents"></param>
        /// <param name="langCode"></param>
        public List<EventDTO> Load(int structureId, List<int> ListEvents, string langCode)
        {
            try
            {
                return _eventRepository.Load(structureId, ListEvents, langCode);
            }
            catch
            {
                throw;
            }
        }

        private string GetImageBytes(string imagesRep, int eventId)
        {
            try
            {
                string imageEventPhysical = $"{imagesRep}{eventId}.gif";

                if (File.Exists(imageEventPhysical))
                {
                    byte[] imageBytes = File.ReadAllBytes(imageEventPhysical);
                    return Convert.ToBase64String(imageBytes);
                }
            }
            catch (Exception ex)
            {
                RodrigueLogger.Error(0, $"_eventManager.GetImageBytes({imagesRep}, {eventId} {ex.Message} {ex.StackTrace}");
            }

            return null;
        }
        //string imageEventPhysical = $"{imageEventPhysicalRoot}{manifestationId.Value}.gif";

        /// <summary>
        /// renseigne les vignettes 1 et 2, + l'image indiv des manifs
        /// </summary>
        /// <param name="structureId"></param>
        /// <param name="ListEvents"></param>
        /// <param name="langCode"></param>
        public async Task<List<EventDTO>> GetImagesAsync(int structureId, int eventId = 0)
        {
            try
            {
                List<EventDTO> listEvents = new();
                string cacheName = $"EventManager.GetImagesAsync.{structureId}.{eventId}";
                if (!_memoryCache.TryGetValue(cacheName, out listEvents))// Look for cache key.
                {


                    if (eventId == 0)
                    {
                        var minfos = _eventInfosRepository.GetAllAsync(structureId);
                        var mimage2 = _eventImageRepository.GetAllAsync(structureId);


                        Task.WaitAll(minfos, mimage2);

                        List<int> listEventsId = minfos.Result.Select(i => i.ManifestationId).Distinct().ToList();

                        string imageEventPhysicalRoot = _configuration["Images:EventsImagesPhysicalPath"]!
    .Replace("{structureId}", structureId.ToString("0000"));

                        listEvents = listEventsId.Select(c => new EventDTO()
                        {
                            EventId = c,
                            Images = new DTO.EventsSessions.EventDTO.EventImages()
                            {
                                ImageOnLine = GetImageBytes(imageEventPhysicalRoot, c),

                                Image1 = (minfos.Result.FirstOrDefault(i => i.ManifestationId == c)?.ImageAffiche == null) ? "" : Convert.ToBase64String(
                                    minfos.Result.FirstOrDefault(i => i.ManifestationId == c)?.ImageAffiche
                                    )
                            ,
                                Image2 = (mimage2.Result.FirstOrDefault(i => i.ManifestationId == c)?.ImageAffiche2 == null) ? "" : Convert.ToBase64String(mimage2.Result.FirstOrDefault(i => i.ManifestationId == c)?.ImageAffiche2)
                            }
                        }).ToList();

                        //return listRet;

                    }
                    else
                    {
                        var tminfos = _eventInfosRepository.GetByEventIdAsync(structureId, eventId);
                        var tmimage2 = _eventImageRepository.GetByEventIdAsync(structureId, eventId);

                        Task.WaitAll(tminfos, tmimage2);

                        EventDTO eventDTO = new EventDTO()
                        {
                            EventId = eventId,
     
                        };

                        if (tminfos.Result != null)
                        {
                            eventDTO.Images.Image1 = Convert.ToBase64String(tminfos.Result?.ImageAffiche);
                        }
                        if (tmimage2.Result != null)
                        {

                            eventDTO.Images.Image2 = Convert.ToBase64String(tmimage2.Result?.ImageAffiche2);
                        }


                        listEvents = new List<EventDTO> { eventDTO };

                    }


                    foreach (var item in listEvents)
                    {
                        if (item.Images.Image1 != null)
                        {
                            if (item.Images.Image1.IsBlankImage())
                            {  
                                item.Images.Image1 = "";
                            }
                            if (item.Images.Image2.IsBlankImage())
                            {
                                item.Images.Image2 = "";
                            }
                        }
                    }


                    var cacheExpiryOptions = new MemoryCacheEntryOptions
                    {
                        AbsoluteExpiration = DateTime.Now.AddSeconds(60),
                        Priority = CacheItemPriority.Low,
                    };
                    _memoryCache.Set(cacheName, listEvents, cacheExpiryOptions);
                }
                return listEvents;



            }
            catch
            {
                throw;
            }
        }
        //public static Bitmap ByteToImage(byte[] blob)
        //{
        //    using (MemoryStream mStream = new MemoryStream())
        //    {
        //        mStream.Write(blob, 0, blob.Length);
        //        mStream.Seek(0, SeekOrigin.Begin);

        //        Bitmap bm = new Bitmap(mStream);
        //        return bm;
        //    }
        //}

        //public static bool IsBlankImage(string base64image)
        //{
        //    if (base64image != "")
        //    {
        //        int threshold = 255;
        //        byte[] pixels = Convert.FromBase64String(base64image);

        //        Bitmap img = ByteToImage(pixels);
        //        BitmapData data = img.LockBits(new Rectangle(0, 0, img.Width, img.Height), ImageLockMode.ReadOnly, PixelFormat.Format32bppArgb);
        //        byte[] buffer = new byte[data.Height * data.Stride];
        //        Marshal.Copy(data.Scan0, buffer, 0, buffer.Length);
        //        Int32 stride = data.Stride;
        //        img.UnlockBits(data);
        //        Int32 height = img.Height;
        //        Int32 width = img.Width;

        //        Int32 lineStart = 0;


        //        bool thereIsColoredPixel = false;

        //        //for (Int32 y = 0; y < height; ++y)
        //        Int32 y = 0;
        //        while (y < height && !thereIsColoredPixel)
        //        {
        //            Int32 offset = lineStart;
        //            Int32 x = 0;
        //            //for (Int32 x = 0; x < width; ++x)
        //            while (x < width && !thereIsColoredPixel)
        //            {
        //                bool isWhitePixel = true;
        //                byte blue = buffer[offset + 0];
        //                byte green = buffer[offset + 1];
        //                byte red = buffer[offset + 2];
        //                //byte alpha = buffer[offset + 3];
        //                if (blue != threshold || green != threshold && red != threshold)
        //                {
        //                    thereIsColoredPixel = true;
        //                    // is white pixel.
        //                    //isWhitePixel = true;
        //                }
        //                offset += 4;
        //                x++;
        //            }
        //            lineStart += stride;
        //            y++;
        //        }
        //        return !thereIsColoredPixel;
        //    }
        //    else
        //        return true;

        //    //byte[] buffer = new byte[data.Height * data.Stride];
        //}





        /// <summary>
        /// events / sessions (sans les tarifs)
        /// </summary>
        /// <param name="langCode"></param>
        /// <param name="structureId"></param>
        /// <param name="identityId"></param>fSessions
        /// <param name="profilAcheteurId"></param>
        /// <param name="getDispo">avec la dispo ou non</param>
        /// <returns></returns>
        public List<EventDTO> GetSchedule(string langCode, int structureId, int identityId, int profilAcheteurId, bool getDispo = false)
        {
            try
            {
                var ret = _eventRepository.GetSchedule(langCode, structureId, identityId, profilAcheteurId);

                if (getDispo && profilAcheteurId == 0 && identityId == 0)
                {
                    foreach (var evt in ret)
                    {
                        var gps = _gestionPlaceRepository.FindBy(o =>
                        o.ManifId == evt.EventId
                        , structureId).Where(o => o.CategId == null && o.IsContrainteIdentite == false && o.FormuleId == null).ToList(); // les regles meres indiv


                        Console.WriteLine($"dispo m {evt.EventId} gp count= {gps.Count}?? ");

                        foreach (var s in evt.ListSessions)
                        {
                            Console.WriteLine($"dispo m {evt.EventId}, s {s.SessionId} ??");

                            var thisGpSessionMere = gps.Where(gpss => gpss.SeanceId == s.SessionId).FirstOrDefault();

                            var dispo = thisGpSessionMere?.Dispo;
                            Console.WriteLine($"dispo {dispo}, m {evt.EventId}, s {s.SessionId} => {dispo}");
                            if (dispo > 0)
                            {
                                Console.WriteLine($"dispo {dispo}, m {evt.EventId}, s {s.SessionId}");
                            }

                            if (thisGpSessionMere.DateDebValiditeType == null) // null = date fixe
                                thisGpSessionMere.DateDebValiditeType = (int)CalculateDateType.DATEFIXE;
                            if (thisGpSessionMere.DateFinValiditeType == null)
                                thisGpSessionMere.DateFinValiditeType = (int)CalculateDateType.DATEFIXE;

                            CalculateDateType validTypeD = CalculateDateType.DATEFIXE;
                                if (Enum.IsDefined(typeof(CalculateDateType), thisGpSessionMere?.DateDebValiditeType))
                                    validTypeD = (CalculateDateType)thisGpSessionMere.DateDebValiditeType;
                                CalculateDateType validTypeF = CalculateDateType.DATEFIXE;
                                if (Enum.IsDefined(typeof(CalculateDateType), thisGpSessionMere?.DateFinValiditeType))
                                    validTypeF = (CalculateDateType)thisGpSessionMere.DateFinValiditeType;

                                s.SessionStartSaleDate = GestionPlaceHelper.CalculateDateVente(s.SessionStartDate, thisGpSessionMere.DateDebValidite, validTypeD, thisGpSessionMere.DateDebValiditeInt ?? 0);
                                s.SessionEndSaleDate = GestionPlaceHelper.CalculateDateVente(s.SessionStartDate, thisGpSessionMere.DateFinValidite, validTypeF, thisGpSessionMere.DateFinValiditeInt ?? 0);
                            
                            s.TotalSeatsCount = thisGpSessionMere?.PlacesTotal;
                            s.AvailablesSeatsCount = (dispo == null ? 0 : (int)dispo);
                        }
                    }
                }
                return ret;
            }
            catch
            {
                throw;
            }
        }



        public List<EventDTO> GetCrossSelling(int structureid, List<int> eventIds, List<int> sessionIds, int buyerProfilId, int poidsGenre, int poidsSousGenre, int poidsCible, int poidsMois, int differenceDate, int poidsGroupe, int nombreARemonter, string langCode)
        {
            try
            {
                return _eventRepository.GetCrossSelling(structureid, eventIds, sessionIds, buyerProfilId, poidsGenre, poidsSousGenre, poidsCible, poidsMois, differenceDate, poidsGroupe, nombreARemonter, langCode);
            }
            catch
            {
                throw;
            }
        }

        /// <summary>
        /// Plus bas tarif pour les tarifs tarofIdRef /!\ ne tiens pas compte des regles de vente (cf Kiosk)
        /// </summary>
        /// <param name="structureId"></param>
        /// <param name="langCode"></param>
        /// <param name="filiereId"></param>
        /// <param name="operateurId"></param>
        /// <param name="tarifIdRef"></param>
        /// <param name="listEvents"></param>
        /// <returns></returns>
        public List<SessionDTO> GetLowerPriceOfEvents(int structureId, string langCode, List<int> filiereId, List<int> operateurId, List<int> tarifIdRef, List<int> listEvents)
        {
            try
            {
                return _eventRepository.GetLowerPriceOfEvents(structureId, langCode, filiereId, operateurId, tarifIdRef, listEvents);
            }
            catch
            {
                throw;
            }
        }

        /// <summary>
        /// event / session et grille de tarif filtrés par filiere et operateur (ne tient pas compte des regles de ventes internet)
        /// </summary>
        /// <param name="langCode"></param>
        /// <param name="structureId"></param>
        /// <param name="listEventsId"></param>
        /// <param name="listOperateursId"></param>
        /// <param name="listFilieresId"></param>
        /// <returns></returns>
        public List<EventDTO> GetEventsSessionsZoneFloorSectionCategPrices_full(string langCode, int structureId, List<int> listOperateursId, List<int> listFilieresId, List<int> listEventsId)
        {
            try
            {
                return _eventRepository.GetEventsSessionsZoneFloorSectionCategPrices_full(langCode, structureId, listOperateursId, listFilieresId, listEventsId);
            }
            catch
            {
                throw;
            }
        }

        /// <summary>
        /// event / session et grille de tarif filtrés par filiere et operateur (ne tient pas compte des regles de ventes internet)
        /// </summary>
        /// <param name="langCode"></param>
        /// <param name="structureId"></param>
        /// <param name="listEventsId"></param>
        /// <param name="listOperateursId"></param>
        /// <param name="listFilieresId"></param>
        /// <returns></returns>
        public List<EventDTO> GetEventsSessionsPrices_full(string langCode, int structureId, List<int> listOperateursId, List<int> listFilieresId, List<int> listEventsId)
        {
            try
            {
                return _eventRepository.GetEventsSessionsPrices_full(langCode, structureId, listOperateursId, listFilieresId, listEventsId);
            }
            catch (Exception ex)
            {
                RodrigueLogger.Error(structureId, $"_eventRepository.GetEventsSessionsPrices_full({langCode}, {structureId}, {String.Join(".", listOperateursId)},{String.Join(".", listFilieresId)},{String.Join(".", listEventsId)}) {ex.Message}\n{ex.StackTrace}");
                throw ex;
            }
        }

        public List<SessionDTO> LoadDispoSeancesOfEventForOperateur(int structureId, string langCode, List<int> listEvents, List<int> operateurId, List<int> filiereId)
        {
            try
            {
                return _eventRepository.LoadDispoSeancesOfEventForOperateur(structureId, langCode, listEvents, operateurId, filiereId);
            }
            catch (Exception ex)
            {
                RodrigueLogger.Error(structureId, $"_eventRepository.LoadDispoSeancesOfEventForOperateur({langCode}, {structureId}, {String.Join(".", listEvents)}, {operateurId},{filiereId}) {ex.Message}\n{ex.StackTrace}");
                throw ex;
            }
        }



        //                      listEvent = _eventManager.GetEventsSessionsCategsPrices("", structureId, webUser.IdentiteId, 0, flg.SessionId, myBprofilId, "", listGpAsked);

                    //        List<EventDTO> listEvent = new();
                    //string cacheName = $"EventManager.GetEventsSessionsCategsPrices.{structureId}.{webUser.IdentiteId}.0.{flg.SessionId}.{myBprofilId}.{string.Join('_', listGpAsked)}";

                    //Logger.Debug(structureId, $"GetEventsSessionsCategsPrices cacheName={cacheName} ?");

                    //if (!_memoryCache.TryGetValue(cacheName, out listEvent))// Look for cache key.
                    //{
                    //    Logger.Debug(structureId, $"GetEventsSessionsCategsPrices ... ");
                    //    listEvent = _eventManager.GetEventsSessionsCategsPrices("", structureId, webUser.IdentiteId,
                    //     0, flg.SessionId, myBprofilId, "", listGpAsked);

                    //    if (listEvent != null)
                    //    {
                    //        var cacheEntryOptions = new MemoryCacheEntryOptions()
                    //                     //Priority on removing when reaching size limit (memory pressure)
                    //                     .SetPriority(CacheItemPriority.High)
                    //                    // Keep in cache for this time, reset time if accessed.                           
                    //                    // Remove from cache after this time, regardless of sliding expiration
                    //                    .SetAbsoluteExpiration(TimeSpan.FromSeconds(60));
                    //        _memoryCache.Set(cacheName, listEvent, cacheEntryOptions);
                    //    }
                    //}



        public List<EventDTO> GetEventsSessionsCategsPrices(string langCode, int structureId, int identityId, int eventId, int sessionId, int buyerprofilId, string listGPInBasket, List<int> listgpasked )
        {
            try
            {
                if (listgpasked == null)
                {
                    listgpasked = new List<int>();
                }
                List<EventDTO> listEvent = new();
                string cacheName = $"EventManager.GetEventsSessionsCategsPrices.{structureId}.{identityId}.{eventId}.{sessionId}.{buyerprofilId}.{string.Join('_', listgpasked)}";
                if (!_memoryCache.TryGetValue(cacheName, out listEvent))// Look for cache key.
                {

                    listEvent = _eventRepository.GetEventsSessionsCategsPrices(langCode, structureId, identityId, eventId, sessionId, buyerprofilId, listGPInBasket, listgpasked);

                    if (listEvent != null)
                    {
                        var cacheEntryOptions = new MemoryCacheEntryOptions()
                                     //Priority on removing when reaching size limit (memory pressure)
                                     .SetPriority(CacheItemPriority.High)
                                    // Keep in cache for this time, reset time if accessed.                           
                                    // Remove from cache after this time, regardless of sliding expiration
                                    .SetAbsoluteExpiration(TimeSpan.FromSeconds(60));
                        _memoryCache.Set(cacheName, listEvent, cacheEntryOptions);
                    }

                    //return _eventRepository.GetEventsSessionsCategsPrices(langCode, structureId, identityId, eventId, sessionId, buyerprofilId, listGPInBasket, listgpasked);

                }
                return listEvent;
   
            }
            catch (SqlException ex)
            {
                RodrigueLogger.Error(structureId, $"_eventRepository.GetEventsSessionsCategsPrices({langCode}, {structureId}, {identityId}, {eventId},{sessionId}, {buyerprofilId}, {listGPInBasket}) {ex.Message}\n{ex.StackTrace}");
                throw ex;
            }
            catch (Exception ex)
            {
                RodrigueLogger.Error(structureId, $"_eventRepository.GetEventsSessionsCategsPrices({langCode}, {structureId}, {identityId}, {eventId},{sessionId}, {buyerprofilId}, {listGPInBasket}) {ex.Message}\n{ex.StackTrace}");
                throw ex;
            }
        }

        /// <summary>
        /// renseigne les evenements de la liste ListEventsToFill avec les infos de la liste ListEvents (qui est chargée depuis Open)
        /// </summary>
        /// <param name="ListEventsToFill"></param>
        /// <param name="ListEvents"></param>
        public static void Fill(List<EventDTO> ListEventsToFill, List<EventDTO> ListEvents)
        {
            foreach (EventDTO evt in ListEventsToFill)
            {
                var thisEventLoaded = ListEvents.Where(e => e.EventId == evt.EventId).FirstOrDefault();

                if (thisEventLoaded != null)
                {
                    evt.EventId = thisEventLoaded.EventId;

                    evt.EventDescription1 = thisEventLoaded.EventDescription1;
                    evt.EventDescription2 = thisEventLoaded.EventDescription2;
                    evt.EventDescription3 = thisEventLoaded.EventDescription3;
                    evt.EventDescription4 = thisEventLoaded.EventDescription4;
                    evt.EventDescription5 = thisEventLoaded.EventDescription5;

                    evt.EventName = thisEventLoaded.EventName;

                    evt.IsZapperZoneEtageSectionIndiv = thisEventLoaded.IsZapperZoneEtageSectionIndiv;
                    evt.IsZapperZoneEtageIndiv = thisEventLoaded.IsZapperZoneEtageIndiv;

                }
                else
                {
                    Exception ex = new Exception($"EventManager.Fill: can't reload prop of Event {evt.EventId} !");

                    throw ex;
                }
            }
        }


        /// <summary>
        /// Charge le Carousel sur les manifestations
        /// </summary>
        /// <param name="structureId"></param>
        /// <param name="langCode"></param>
        /// <param name="userConfigEventFeature"></param>
        /// <returns></returns>
        public Dictionary<int, List<EventDTO>> LoadEventsFeatures(int structureId, string langCode, List<HomeModularViewModel> userConfigEventFeature)
        {
            try
            {
                return _eventRepository.LoadEventsFeatures(structureId, langCode, userConfigEventFeature);
            }
            catch
            {
                throw;
            }
        }

        /// <summary>
        /// dictionnaire colonnes / valeur pour utilisation dans les maquettes
        /// </summary>
        public DictionaryExtented GetDictionnaryForPdf(int structureId, string langCode, int eventId)
        {
            try
            {
                return _eventRepository.GetDictionnaryForPdf(structureId, langCode, eventId);
            }
            catch
            {
                throw;
            }
        }



        #region Liste d'attente Manifestations
        /// <summary>
        /// Liste d'attente des manifestations
        /// </summary>
        /// <param name="structureId"></param>
        /// <returns></returns>
        public async Task<List<EventWaitingDTO>> GetWaitingListOfEventsAsync(int structureId)
        {
            IEnumerable<ManifestationEntity> events = await _eventRepository.GetEventsOnSales(structureId).ConfigureAwait(false);

            foreach (var item in events)
            {
                item.GPManifestation = await _gPManifestationRepository.GetByIdAsync(structureId, item.ManifestationId).ConfigureAwait(false);
                if (item.GPManifestation != null && item.GPManifestation.GpManifestationListeAttenteManifCommentId != null)
                {
                    var traduction = await _traductionRepository.GetByIdAsync(structureId, item.GPManifestation.GpManifestationListeAttenteManifCommentId.Value)
                                                               .ConfigureAwait(false);

                    item.GPManifestation.TraductionGpManifestationsForWaiting = traduction;
                }
            }

            var resultEventDTO = _mapper.Map<List<EventWaitingDTO>>(events);
            var traductionsEventWaiting = resultEventDTO.SelectMany(evt => evt.TraductionGpWaitingEvents).ToList();
            _languageManager.SetLangCode(structureId, traductionsEventWaiting);

            return resultEventDTO.OrderBy(m => m.EventName).ToList();
        }

        public async Task<bool> UpdateTraductionGpManifestationWaitingAsync(int structureId, List<EventWaitingDTO> eventsWaiting)
        {
            bool isListeAttenteColumnExist = await _gPManifestationRepository.CheckExistColumnAsync(structureId, "is_liste_attente");

            if (!isListeAttenteColumnExist)
                _gPManifestationRepository.CreateColumnsForWaitingListAndTraduction(structureId);


            IEnumerable<ManifestationEntity> events = _mapper.Map<List<ManifestationEntity>>(eventsWaiting);

            using IDbContext dbContextTransactionScope = _dbContext.BeginTransaction(ContextType.Open, structureId);

            try
            {
                foreach (var evt in events)
                {
                    if (evt.GPManifestation != null)
                    {
                        if (evt.GPManifestation.GpManifestationListeAttenteManifCommentId != null)
                        {
                            var traduction = evt.GPManifestation.TraductionGpManifestationsForWaiting;

                            if (traduction.TraductionId == 0)
                            {

                                //Récupère le dernier ID traduction dans la base de données
                                var traductionsIdDB = await _traductionRepository.GetAllAsync(structureId).ConfigureAwait(false);

                                //Existe t-il une traduction avec cette manifestation
                                var traductionAvecCetteManifestationInDB = traductionsIdDB.FirstOrDefault(t => t.TableId == evt.ManifestationId && t.TraductionTable.ToUpper().Equals("GP_MANIFESTATION"));
                                if (traductionAvecCetteManifestationInDB is null)
                                {
                                    int newTraductionId = traductionsIdDB.OrderByDescending(t => t.TraductionId).First().TraductionId + 1;

                                    traduction.TraductionId += newTraductionId;
                                    traduction.TraductionTable = "GP_MANIFESTATION";
                                    traduction.TraductionChamps = "LISTE_ATTENTE_MANIF_COMMENT";
                                    traduction.TableId = evt.ManifestationId;

                                    bool isInserted = _traductionRepository.InsertWithoutIdReturn(structureId, traduction, isAutoIncremented: false);

                                    if (!isInserted)
                                        throw new Exception("Traduction not insert in bdd");
                                }
                                else
                                {
                                    _traductionManager.UpdateTraduction(structureId, traductionAvecCetteManifestationInDB, traduction);

                                }

                            }
                            else
                            {

                                TraductionEntity traductionInDB = await _traductionRepository.GetByIdAsync(structureId, traduction.TraductionId)
                                     ?? throw new ArgumentNullException($"Traduction Not Found {traduction.TraductionId}");

                                _traductionManager.UpdateTraduction(structureId, traductionInDB, traduction);
                            }

                            evt.GPManifestation.GpManifestationListeAttenteManifCommentId = traduction.TraductionId;

                        }

                        var gpManifestation = _gPManifestationRepository.GetById(structureId, evt.ManifestationId)
                                ?? throw new ArgumentNullException($"GpManifestation Not Found {evt.ManifestationId}");


                        if (gpManifestation != null)
                        {
                            gpManifestation.IsListeAttente = evt.GPManifestation.IsListeAttente;
                            gpManifestation.GpManifestationListeAttenteManifCommentId = evt.GPManifestation.GpManifestationListeAttenteManifCommentId;

                            bool gpManifestationIsUpdated = _gPManifestationRepository.Update(structureId, gpManifestation);

                            if (!gpManifestationIsUpdated)
                                throw new Exception($"GpManifestation not update in bdd {evt.GPManifestation.ManifestationId}");
                        }

                    }

                }

                dbContextTransactionScope.Commit();

                _redisCacheService.RemoveKeysContainPartOfKey($"{structureId}_Catalog");
            }
            catch (Exception)
            {
                dbContextTransactionScope.Rollback();
                throw;
            }

            return true;
        }


        #endregion

        #region Gestion Manifestations bloquées


        public async Task<EventLockDTO> GetEventLockByIdAsync(int structureId, int eventId)
        {

            var manifestation = await _eventRepository.GetByIdAsync(structureId, eventId).ConfigureAwait(false);

            if (manifestation is not null)
            {
                manifestation.GPManifestation = await _gPManifestationRepository.GetByIdAsync(structureId, manifestation.ManifestationId).ConfigureAwait(false);

                if (manifestation.GPManifestation != null && manifestation.GPManifestation.GpManifestationLockManifCommentId != null)
                {

                    var traduction = await _traductionRepository.GetByIdAsync(structureId, manifestation.GPManifestation.GpManifestationLockManifCommentId.Value)
                                                               .ConfigureAwait(false);

                    manifestation.GPManifestation.TraductionGpManifestationsForLock = traduction;
                }

            }

            var resultEventDTO = _mapper.Map<EventLockDTO>(manifestation);

            _languageManager.SetLangCode(structureId, resultEventDTO.TraductionGpLockEvents.ToList());

            return resultEventDTO;

        }


        public async Task<bool> CheckGpManifestationLockAsync(int structureId, int eventId)
        {
            return await _gPManifestationRepository.GetByIdAsync(structureId, eventId).ConfigureAwait(false) != null;
        }


        public async Task<bool> InsertGpManifestationLockAsync(int structureId, EventLockDTO eventLock)
        {
            //Pour enregistrer les manifestation bloquée, on a besoin de la colonne is_liste_attente car c'est la même table (GP_manifestation)
            bool isListeAttenteColumnExist = await _gPManifestationRepository.CheckExistColumnAsync(structureId, "is_liste_attente");

            if (!isListeAttenteColumnExist)
                _gPManifestationRepository.CreateColumnsForWaitingListAndTraduction(structureId);

            GPManifestationEntity gpEventMapper = _mapper.Map<GPManifestationEntity>(eventLock);

            using IDbContext dbContextTransactionScope = _dbContext.BeginTransaction(ContextType.Open, structureId);
            try
            {
                if (gpEventMapper is not null)
                {
                    gpEventMapper.DateOperation = DateTime.Now;
                    await _gPManifestationRepository.InsertWithoutIdReturnAsync(structureId, gpEventMapper, isAutoIncremented: false).ConfigureAwait(false);

                    dbContextTransactionScope.Commit();

                    _redisCacheService.RemoveKeysContainPartOfKey($"{structureId}_Catalog");
                }
            }
            catch (Exception)
            {
                dbContextTransactionScope.Rollback();
                throw;
            }

            return true;

        }




        public async Task<bool> UpdateTraductionGpManifestationLockAsync(int structureId, EventLockDTO eventLock)
        {
            //Pour enregistrer les manifestation bloquée, on a besoin de la colonne is_liste_attente car c'est la même table (GP_manifestation)
            bool isListeAttenteColumnExist = await _gPManifestationRepository.CheckExistColumnAsync(structureId, "is_liste_attente");

            if (!isListeAttenteColumnExist)
                _gPManifestationRepository.CreateColumnsForWaitingListAndTraduction(structureId);


            //lit le fichier XML et enregistre les manifestations bloquées en base de données
            string customerFileIndivPath = _configuration["CustomerfilesIndivPhysicalPath"]!
                .Replace("{structureId}", structureId.ToString("0000"));

            string indivIhmManifsPath = Path.Combine(customerFileIndivPath, "IHM", "messages_manifs.xml");



            ManifestationEntity eventMapper = _mapper.Map<ManifestationEntity>(eventLock);

            using IDbContext dbContextTransactionScope = _dbContext.BeginTransaction(ContextType.Open, structureId);

            try
            {
                if (eventMapper is not null)
                {
                    if (eventMapper.GPManifestation != null)
                    {

                        var traduction = eventMapper.GPManifestation.TraductionGpManifestationsForLock ?? new();

                        if (traduction.TraductionId == 0)
                        {

                            //Récupère le dernier ID traduction dans la base de données
                            var traductionsIdDB = await _traductionRepository.GetAllAsync(structureId).ConfigureAwait(false);

                            //Existe t-il une traduction avec cette manifestation
                            var traductionAvecCetteManifestationInDB = traductionsIdDB.FirstOrDefault(t => t.TableId == eventMapper.ManifestationId && t.TraductionTable.ToUpper().Equals("GP_MANIFESTATION"));
                            if (traductionAvecCetteManifestationInDB is null)
                            {
                                int newTraductionId = traductionsIdDB.OrderByDescending(t => t.TraductionId).First().TraductionId + 1;

                                traduction.TraductionId = newTraductionId;
                                traduction.TraductionTable = "GP_MANIFESTATION";
                                traduction.TraductionChamps = "LOCK_MANIF_COMMENT";
                                traduction.TableId = eventMapper.ManifestationId;

                                bool isInserted = _traductionRepository.InsertWithoutIdReturn(structureId, traduction, isAutoIncremented: false);

                                if (!isInserted)
                                    throw new Exception("Traduction not insert in bdd");



                            }
                            else
                            {
                                _traductionManager.UpdateTraduction(structureId, traductionAvecCetteManifestationInDB, traduction);
                            }

                        }
                        else
                        {
                            TraductionEntity traductionInDB = await _traductionRepository.GetByIdAsync(structureId, traduction.TraductionId)
                                 ?? throw new ArgumentNullException($"Traduction Not Found {traduction.TraductionId}");

                            _traductionManager.UpdateTraduction(structureId, traductionInDB, traduction);
                        }

                        eventMapper.GPManifestation.GpManifestationLockManifCommentId = traduction.TraductionId;

                        var gpManifestation = _gPManifestationRepository.GetById(structureId, eventMapper.ManifestationId)
                                    ?? throw new ArgumentNullException($"GpManifestation Not Found {traduction.TraductionId}");

                        if (gpManifestation != null)
                        {
                            gpManifestation.IsLock = eventMapper.GPManifestation.IsLock;
                            gpManifestation.GpManifestationLockManifCommentId = eventMapper.GPManifestation.GpManifestationLockManifCommentId;

                            bool gpManifestationIsUpdated = _gPManifestationRepository.Update(structureId, gpManifestation);

                            if (!gpManifestationIsUpdated)
                                throw new Exception($"GpManifestation not update in bdd {eventMapper.GPManifestation.ManifestationId}");

                        }
                    }

                    WriteToMessagesManifsXmlFile(indivIhmManifsPath, eventMapper.GPManifestation);
                }

                dbContextTransactionScope.Commit();

                _redisCacheService.RemoveKeysContainPartOfKey($"{structureId}_Catalog");
            }
            catch (Exception)
            {
                dbContextTransactionScope.Rollback();
                throw;
            }

            return true;
        }

        private void WriteToMessagesManifsXmlFile(string xmlFilePath, GPManifestationEntity gpManifestation)
        {
            if (File.Exists(xmlFilePath))
            {

                XDocument doc = XDocument.Load(xmlFilePath);

                // Rechercher l'élément <islock> dont le sous-élément <manifestation_id> correspond à l'ID
                XElement xelementEvent = doc.Root.Elements("islock")
                    .FirstOrDefault(m => m.Element("manifestation_id") != null && m.Element("manifestation_id").Value == gpManifestation.ManifestationId.ToString());

                // S'il existe, le supprimer
                if (xelementEvent != null)
                {
                    xelementEvent.Remove();
                }

                if (gpManifestation.IsLock)
                {
                    // Créer le nouvel élément <islock> avec l'attribut et les sous-éléments requis
                    XElement newNode = new XElement("islock",
                       new XAttribute("manifestation_id", gpManifestation.ManifestationId),
                       new XElement("manifestation_id", gpManifestation.ManifestationId),
                       new XElement("message", gpManifestation.TraductionGpManifestationsForLock.Langue1) // Remplacer par le message souhaité
                    );

                    // Ajouter le nouvel élément à la racine
                    doc.Root.Add(newNode);

                }

                // Sauvegarder les modifications dans le fichier XML
                doc.Save(xmlFilePath);

            }
        }
        #endregion



        #region Gp Manifestation


        #endregion


        #region Lookup
        /// <summary>
        /// ?????????
        /// </summary>
        /// <param name="structureId"></param>
        /// <param name="selectEventOption"></param>
        /// <returns></returns>
        public async Task<List<SelectLookup>> GetSelectLookupOfEventsAsync(int structureId, SelectEventOption selectEventOption)
        {
            IEnumerable<ManifestationEntity> events = await _eventRepository.GetEventsOnSales(structureId).ConfigureAwait(false);

            if (selectEventOption.EventsGroupId != null)
                events = events.Where(evt => selectEventOption.EventsGroupId.Contains(evt.ManifestationGroupeId));

            return events.Select(e => new SelectLookup
            {
                Value = e.ManifestationId.ToString(),
                Libelle = e.ManifestationNom

            }).ToList();
        }

        public async Task<List<SelectLookup>> SelectLookupEventsAsync(int structureId)
        {
            IEnumerable<ManifestationEntity> events = await _eventRepository.GetAllCustomEntities(structureId)
                                                                            .Include(m => m.Seances)
                                                                            .Where(m => m.Seances != null && m.Seances.Any(s => s.SeanceDateDeb > DateTime.Now))
                                                                            .ToEntityListAsync()
                                                                            .ConfigureAwait(false);

            return events.Select(e => new SelectLookup()
            {
                Value = e.ManifestationId.ToString(),
                Libelle = e.ManifestationNom
            })
            .ToList();
        }


        public async Task<List<SelectLookup>> GetSelectLookupOfAllEventsAsync(int structureId, SelectEventOption selectEventOption)
        {
            IEnumerable<ManifestationEntity>? events = await _eventRepository.GetAllAsync(structureId).ConfigureAwait(false);

            if (events is not null && events.Any())
            {
                if (selectEventOption.EventsGroupId != null)
                {
                    events = events.Where(evt => selectEventOption.EventsGroupId.Contains(evt.ManifestationGroupeId));
                }
            }


            return events.Select(e => new SelectLookup
            {
                Value = e.ManifestationId.ToString(),
                Libelle = $"{e.ManifestationId} - {e.ManifestationNom}"

            }).ToList();
        }



        public List<SelectLookup> GetSelectLookupOfAllEventOfThisFormulaWithTranslation(int structureId, string langCode, int formulaId)
        {
            var events = _eventRepository.GetAllEventsOfThisFormulaWithTranslation(structureId, langCode, formulaId);

            return events?
                    .Select(e => new SelectLookup
                    {
                        Value = e.ManifestationId.ToString(),
                        Libelle = e.ManifestationNom

                    }).ToList() ?? new List<SelectLookup>();

        }
        #endregion



    }


}
