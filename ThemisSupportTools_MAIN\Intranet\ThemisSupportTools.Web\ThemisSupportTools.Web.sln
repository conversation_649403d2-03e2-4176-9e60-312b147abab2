﻿
Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 17
VisualStudioVersion = 17.11.35327.3
MinimumVisualStudioVersion = 10.0.40219.1
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "ThemisSupportTools.Web", "ThemisSupportTools.Web\ThemisSupportTools.Web.csproj", "{DCD2C40A-9CF6-4B62-9FEE-7C82650B4B68}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Core.Themis.Libraries.BLL", "..\..\Libraries\Core.Themis.Libraries.BLL\Core.Themis.Libraries.BLL\Core.Themis.Libraries.BLL.csproj", "{95E6348E-C5AA-4836-BAC6-EF0845F452F8}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Core.Themis.Libraries.BLLTests", "..\..\Libraries\Core.Themis.Libraries.BLL\Core.Themis.Libraries.BLLTests\Core.Themis.Libraries.BLLTests.csproj", "{2038EA54-2478-4F4B-BFF2-8C1D2B16EE58}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Core.Themis.Libraries.Data", "..\..\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\Core.Themis.Libraries.Data.csproj", "{5A450D3A-4E7F-431C-8DAA-97F1D02D79C0}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Core.Themis.Libraries.DataTests", "..\..\Libraries\Core.Themis.Libraries.Data\Core.Themis.Libraries.DataTests\Core.Themis.Libraries.DataTests.csproj", "{FADA7B83-7A06-4CA8-BCC5-F3511876B80F}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Core.Themis.Libraries.DTO", "..\..\Libraries\Core.Themis.Libraries.DTO\Core.Themis.Libraries.DTO.csproj", "{3FCB00B9-3AC3-4E3C-A0C4-D63B67213968}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Core.Themis.Libraries.Razor", "..\..\Libraries\Core.Themis.Libraries.Razor\Core.Themis.Libraries.Razor\Core.Themis.Libraries.Razor.csproj", "{629BDF81-E618-42FF-A7ED-1217D2B26C14}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Core.Themis.Libraries.Utilities", "..\..\Libraries\Core.Themis.Libraries.Utilities\Core.Themis.Libraries.Utilities\Core.Themis.Libraries.Utilities.csproj", "{2054573F-7434-4457-ACCE-63FD0B0A912E}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Core.Themis.Libraries.UtilitiesTests", "..\..\Libraries\Core.Themis.Libraries.Utilities\Core.Themis.Libraries.UtilitiesTests\Core.Themis.Libraries.UtilitiesTests.csproj", "{6DA58B8B-4C74-4C84-B787-E7934B0C336B}"
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|Any CPU = Debug|Any CPU
		Release|Any CPU = Release|Any CPU
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{DCD2C40A-9CF6-4B62-9FEE-7C82650B4B68}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{DCD2C40A-9CF6-4B62-9FEE-7C82650B4B68}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{DCD2C40A-9CF6-4B62-9FEE-7C82650B4B68}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{DCD2C40A-9CF6-4B62-9FEE-7C82650B4B68}.Release|Any CPU.Build.0 = Release|Any CPU
		{95E6348E-C5AA-4836-BAC6-EF0845F452F8}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{95E6348E-C5AA-4836-BAC6-EF0845F452F8}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{95E6348E-C5AA-4836-BAC6-EF0845F452F8}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{95E6348E-C5AA-4836-BAC6-EF0845F452F8}.Release|Any CPU.Build.0 = Release|Any CPU
		{2038EA54-2478-4F4B-BFF2-8C1D2B16EE58}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{2038EA54-2478-4F4B-BFF2-8C1D2B16EE58}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{2038EA54-2478-4F4B-BFF2-8C1D2B16EE58}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{2038EA54-2478-4F4B-BFF2-8C1D2B16EE58}.Release|Any CPU.Build.0 = Release|Any CPU
		{5A450D3A-4E7F-431C-8DAA-97F1D02D79C0}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{5A450D3A-4E7F-431C-8DAA-97F1D02D79C0}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{5A450D3A-4E7F-431C-8DAA-97F1D02D79C0}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{5A450D3A-4E7F-431C-8DAA-97F1D02D79C0}.Release|Any CPU.Build.0 = Release|Any CPU
		{FADA7B83-7A06-4CA8-BCC5-F3511876B80F}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{FADA7B83-7A06-4CA8-BCC5-F3511876B80F}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{FADA7B83-7A06-4CA8-BCC5-F3511876B80F}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{FADA7B83-7A06-4CA8-BCC5-F3511876B80F}.Release|Any CPU.Build.0 = Release|Any CPU
		{3FCB00B9-3AC3-4E3C-A0C4-D63B67213968}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{3FCB00B9-3AC3-4E3C-A0C4-D63B67213968}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{3FCB00B9-3AC3-4E3C-A0C4-D63B67213968}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{3FCB00B9-3AC3-4E3C-A0C4-D63B67213968}.Release|Any CPU.Build.0 = Release|Any CPU
		{629BDF81-E618-42FF-A7ED-1217D2B26C14}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{629BDF81-E618-42FF-A7ED-1217D2B26C14}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{629BDF81-E618-42FF-A7ED-1217D2B26C14}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{629BDF81-E618-42FF-A7ED-1217D2B26C14}.Release|Any CPU.Build.0 = Release|Any CPU
		{2054573F-7434-4457-ACCE-63FD0B0A912E}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{2054573F-7434-4457-ACCE-63FD0B0A912E}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{2054573F-7434-4457-ACCE-63FD0B0A912E}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{2054573F-7434-4457-ACCE-63FD0B0A912E}.Release|Any CPU.Build.0 = Release|Any CPU
		{6DA58B8B-4C74-4C84-B787-E7934B0C336B}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{6DA58B8B-4C74-4C84-B787-E7934B0C336B}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{6DA58B8B-4C74-4C84-B787-E7934B0C336B}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{6DA58B8B-4C74-4C84-B787-E7934B0C336B}.Release|Any CPU.Build.0 = Release|Any CPU
	EndGlobalSection
	GlobalSection(SolutionProperties) = preSolution
		HideSolutionNode = FALSE
	EndGlobalSection
	GlobalSection(ExtensibilityGlobals) = postSolution
		SolutionGuid = {501A3C9B-C8F6-4BF4-9330-52408E4D1581}
	EndGlobalSection
	GlobalSection(TeamFoundationVersionControl) = preSolution
		SccNumberOfProjects = 10
		SccEnterpriseProvider = {4CA58AB2-18FA-4F8D-95D4-32DDF27D184C}
		SccTeamFoundationServer = http://v-dev-tfs:8080/tfs/appscollection
		SccLocalPath0 = .
		SccProjectUniqueName1 = ThemisSupportTools.Web\\ThemisSupportTools.Web.csproj
		SccProjectName1 = ThemisSupportTools.Web
		SccLocalPath1 = ThemisSupportTools.Web
		SccProjectUniqueName2 = ..\\..\\Libraries\\Core.Themis.Libraries.BLL\\Core.Themis.Libraries.BLL\\Core.Themis.Libraries.BLL.csproj
		SccProjectName2 = ../../Libraries/Core.Themis.Libraries.BLL/Core.Themis.Libraries.BLL
		SccLocalPath2 = ..\\..\\Libraries\\Core.Themis.Libraries.BLL\\Core.Themis.Libraries.BLL
		SccProjectUniqueName3 = ..\\..\\Libraries\\Core.Themis.Libraries.BLL\\Core.Themis.Libraries.BLLTests\\Core.Themis.Libraries.BLLTests.csproj
		SccProjectName3 = ../../Libraries/Core.Themis.Libraries.BLL/Core.Themis.Libraries.BLLTests
		SccLocalPath3 = ..\\..\\Libraries\\Core.Themis.Libraries.BLL\\Core.Themis.Libraries.BLLTests
		SccProjectUniqueName4 = ..\\..\\Libraries\\Core.Themis.Libraries.Data\\Core.Themis.Librairies.Data\\Core.Themis.Libraries.Data.csproj
		SccProjectName4 = ../../Libraries/Core.Themis.Libraries.Data/Core.Themis.Librairies.Data
		SccLocalPath4 = ..\\..\\Libraries\\Core.Themis.Libraries.Data\\Core.Themis.Librairies.Data
		SccProjectUniqueName5 = ..\\..\\Libraries\\Core.Themis.Libraries.Data\\Core.Themis.Libraries.DataTests\\Core.Themis.Libraries.DataTests.csproj
		SccProjectName5 = ../../Libraries/Core.Themis.Libraries.Data/Core.Themis.Libraries.DataTests
		SccLocalPath5 = ..\\..\\Libraries\\Core.Themis.Libraries.Data\\Core.Themis.Libraries.DataTests
		SccProjectUniqueName6 = ..\\..\\Libraries\\Core.Themis.Libraries.DTO\\Core.Themis.Libraries.DTO.csproj
		SccProjectName6 = ../../Libraries/Core.Themis.Libraries.DTO
		SccLocalPath6 = ..\\..\\Libraries\\Core.Themis.Libraries.DTO
		SccProjectUniqueName7 = ..\\..\\Libraries\\Core.Themis.Libraries.Razor\\Core.Themis.Libraries.Razor\\Core.Themis.Libraries.Razor.csproj
		SccProjectName7 = ../../Libraries/Core.Themis.Libraries.Razor/Core.Themis.Libraries.Razor
		SccLocalPath7 = ..\\..\\Libraries\\Core.Themis.Libraries.Razor\\Core.Themis.Libraries.Razor
		SccProjectUniqueName8 = ..\\..\\Libraries\\Core.Themis.Libraries.Utilities\\Core.Themis.Libraries.Utilities\\Core.Themis.Libraries.Utilities.csproj
		SccProjectName8 = ../../Libraries/Core.Themis.Libraries.Utilities/Core.Themis.Libraries.Utilities
		SccLocalPath8 = ..\\..\\Libraries\\Core.Themis.Libraries.Utilities\\Core.Themis.Libraries.Utilities
		SccProjectUniqueName9 = ..\\..\\Libraries\\Core.Themis.Libraries.Utilities\\Core.Themis.Libraries.UtilitiesTests\\Core.Themis.Libraries.UtilitiesTests.csproj
		SccProjectName9 = ../../Libraries/Core.Themis.Libraries.Utilities/Core.Themis.Libraries.UtilitiesTests
		SccLocalPath9 = ..\\..\\Libraries\\Core.Themis.Libraries.Utilities\\Core.Themis.Libraries.UtilitiesTests
	EndGlobalSection
EndGlobal
