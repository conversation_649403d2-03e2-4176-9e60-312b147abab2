﻿
using AspNetCoreRateLimit;
using Core.Themis.Api.External;
using Core.Themis.Api.External.Helpers;
using Core.Themis.Api.External.Helpers.Interfaces;
using Core.Themis.API.External.Handlers;
using Core.Themis.Libraries.BLL.Extentions.ServicesBuilder;
using Core.Themis.Libraries.BLL.Services.RedisCache.Interfaces;
using Core.Themis.Libraries.BLL.Services.RedisCache;
using Core.Themis.Libraries.Utilities.Helpers;
using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Controllers;
using Microsoft.Extensions.Caching.Distributed;
using Microsoft.IdentityModel.Tokens;
using Microsoft.OpenApi.Any;
using Microsoft.OpenApi.Interfaces;
using Microsoft.OpenApi.Models;
using StackExchange.Redis;
using Swashbuckle.AspNetCore.Filters;
using System.Diagnostics;
using System.Reflection;
using System.Text;



var builder = WebApplication.CreateBuilder(args);

// Lire la configuration CORS depuis appsettings.json
var allowedOrigins = builder.Configuration
    .GetSection("AllowedCorsOrigins")
    .Get<string[]>();

// Add services to the container.
builder.Services.AddCors(options =>
{
    options.AddDefaultPolicy(
        policy =>
        {
            policy.WithOrigins(allowedOrigins)
                .AllowAnyHeader()
                .AllowAnyMethod();
        });
});

builder.Services.AddControllers().AddJsonOptions(options =>
{
    options.JsonSerializerOptions.DefaultIgnoreCondition = System.Text.Json.Serialization.JsonIgnoreCondition.WhenWritingNull;
});



// Learn more about configuring Swagger/OpenAPI at https://aka.ms/aspnetcore/swashbuckle
builder.Services.AddEndpointsApiExplorer();
builder.Services.AddSwaggerGen();

builder.Services.AddScoped<IAuthorizationHandler, RolesAuthorizationHandler>();

// Rodrigue injection 

builder.Services.AddRodrigueDataServices();
builder.Services.AddRodrigueManager();
builder.Services.AddRodrigueMapper();

builder.Services.AddPassCultureServices(); 

builder.Services.AddSingleton<IApiOffersClient>(
    new ApiOffersClient(builder.Configuration["ApiOfferUrl"]!)
);

builder.Services.AddSingleton<IHttpContextAccessor, HttpContextAccessor>();

builder.Services.AddRazorPages();


builder.Services.AddAuthentication(options =>
{
    options.DefaultScheme = JwtBearerDefaults.AuthenticationScheme;
})
.AddJwtBearer(options =>
{
    options.SaveToken = true;
    options.TokenValidationParameters = new TokenValidationParameters()
    {
        ValidateIssuer = true,
        ValidateAudience = true,
        ClockSkew = Debugger.IsAttached ? TimeSpan.Zero : TimeSpan.FromMinutes(10),
        ValidateLifetime = true,
        ValidateIssuerSigningKey = true,
        ValidIssuer = "rodrigue",
        ValidAudience = "ThemisAPI",
        IssuerSigningKey = new SymmetricSecurityKey(Encoding.UTF8.GetBytes(builder.Configuration["ApiSecretKey"]!))
    };
});

builder.Services.Configure<IpRateLimitOptions>(options =>
{
    options.EnableEndpointRateLimiting = true;
    options.StackBlockedRequests = false;
    options.HttpStatusCode = 429;
    options.RealIpHeader = "X-Real-IP";
    options.ClientIdHeader = "X-ClientId";

    

    options.GeneralRules = new List<RateLimitRule>
        {
            new RateLimitRule
            {
                Endpoint = "*",
                Period = "1s",
                Limit = 20,
            }
        };
});



ConfigurationHelper.Initialize(builder.Configuration);

builder.Services.AddResponseCaching();

builder.Services.AddSingleton<IIpPolicyStore, MemoryCacheIpPolicyStore>();
builder.Services.AddSingleton<IRateLimitCounterStore, MemoryCacheRateLimitCounterStore>();
builder.Services.AddSingleton<IRateLimitConfiguration, RateLimitConfiguration>();
builder.Services.AddSingleton<IProcessingStrategy, AsyncKeyLockProcessingStrategy>();
builder.Services.AddInMemoryRateLimiting();

// In-Memory Caching

#region Redis cache
string instanceName = "Rodrigue_cache_" + Environment.GetEnvironmentVariable("ASPNETCORE_ENVIRONMENT") + "_";
builder.Services.AddRodrigueRedis(builder.Configuration.GetConnectionString("Redis")!, instanceName);

builder.Services.AddSingleton<IRedisCacheService>(sp =>
    new RedisCacheService(
        instanceName,
        sp.GetRequiredService<IDistributedCache>(),
        sp.GetRequiredService<IConnectionMultiplexer>()
    )
);
#endregion



builder.Services.AddMemoryCache();
builder.Services.AddResponseCaching();

builder.Services.ConfigureHelpers();
builder.Services.InitHelpers();

//builder.Services.AddAutoMapper(AppDomain.CurrentDomain.GetAssemblies());
builder.Services.AddSwaggerGen(opts => opts.EnableAnnotations());
builder.Services.AddSwaggerGen(options =>
{
    options.SwaggerDoc("ext", new OpenApiInfo
    {
        Version = "ext",
        Title = "Themis Integration API",
        Description = @"### ✨ This API is to consume Themis datas.",
        
        //TermsOfService = new Uri("https://example.com/terms"),
        Contact = new OpenApiContact
        {    
            Url = new Uri("https://www.rodrigue-solutions.com/")
        },   


        //License = new OpenApiLicense
        //{
        //    Name = "Example License",
        //    Url = new Uri("https://example.com/license")
        //}
    });


    options.SwaggerDoc("internal", new OpenApiInfo
    {
        Version = "interne",
        Title = "Themis Integration API interne",
        Description = @"### ✨ An API to consume Themis datas.",
        //TermsOfService = new Uri("https://example.com/terms"),
        Contact = new OpenApiContact
        {

            Url = new Uri("https://www.rodrigue-solutions.com/")
        },


        //License = new OpenApiLicense
        //{
        //    Name = "Example License",
        //    Url = new Uri("https://example.com/license")
        //}
    });
    //options.DocumentFilter<ExcludeVersionFromSwagger>();

    options.DocInclusionPredicate((docName, apiDesc) =>
    {
        if (docName == "internal")
            return true; // Inclure tous les endpoints dans la doc complète

        // Récupérer les tags du controller
        var tags = apiDesc.ActionDescriptor.EndpointMetadata
            .OfType<ApiExplorerSettingsAttribute>()
            .SelectMany(attr => attr.GroupName?.Split(',') ?? Array.Empty<string>())
            .Union(apiDesc.ActionDescriptor.EndpointMetadata
                .OfType<TagsAttribute>()
                .SelectMany(attr => attr.Tags))
            .ToList();

        // Si c'est vide, utiliser le nom du controller comme tag par défaut
        if (!tags.Any() && apiDesc.ActionDescriptor is ControllerActionDescriptor controllerActionDescriptor)
        {
            var controllerName = controllerActionDescriptor.ControllerName;
            tags.Add(controllerName);
        }

        return tags.Any(t => t.Equals(docName, StringComparison.OrdinalIgnoreCase));
    });


    //options.DocumentFilter<PublicApiFilter>();

    //options.SwaggerDoc("school", new OpenApiInfo
    //{
    //    Title = "School",
    //    Version = "v1",
    //    Description = "School stuff",
    //    Contact = new OpenApiContact
    //    {
    //        Name = "itsfinniii"
    //    }
    //});


    var xmlFilename = $"{Assembly.GetExecutingAssembly().GetName().Name}.xml";
    options.IncludeXmlComments(Path.Combine(AppContext.BaseDirectory, xmlFilename), includeControllerXmlComments: true);

    //var xmlPath = Path.Combine(AppContext.BaseDirectory, xmlFilename);
    //options.IncludeXmlComments(xmlPath);

    options.UseAllOfToExtendReferenceSchemas();

    //options.OrderActionsBy((apiDesc) => $"{apiDesc.ActionDescriptor.RouteValues["controller"]}");

    //options.SupportNonNullableReferenceTypes();

    //Assert.False(schema.Properties["StringWithRequiredDisallowNull"].Nullable);

    List<string> xmlFiles = Directory.GetFiles(AppContext.BaseDirectory, "*.xml", SearchOption.TopDirectoryOnly).ToList();
    xmlFiles.ForEach(xmlFile => options.IncludeXmlComments(xmlFile));


    options.ExampleFilters();

    options.OperationFilter<SecurityRequirementsOperationFilter>(true, "Bearer");
    options.AddSecurityDefinition("Bearer", new OpenApiSecurityScheme
    {
        Description = "Standard Authorization header using the Bearer scheme (JWT). Example: \"bearer {token}\"",
        Name = "Authorization",
        In = ParameterLocation.Header,
        Type = SecuritySchemeType.ApiKey,
        Scheme = "Bearer"
    });
    options.AddSecurityRequirement(new OpenApiSecurityRequirement {
        {
            new OpenApiSecurityScheme {
                Reference = new OpenApiReference {
                    Type = ReferenceType.SecurityScheme,
                        Id = "Bearer"
                }
            },
            new string[] {}
        }
    });

    //var apiGroups = new Dictionary<string, int>
    //{
    //    { "WebUser", 1 },
    //    { "Products", 2 },
    //    { "Orders", 3 },
    //    { "internal", 99 },
    //    { "ext", 1 }
    //};

    


});

builder.Services.AddSwaggerExamplesFromAssemblies(Assembly.GetEntryAssembly());


builder.Logging.ClearProviders();
builder.Logging.SetMinimumLevel(Microsoft.Extensions.Logging.LogLevel.Trace);

string environn = Environment.GetEnvironmentVariable("ASPNETCORE_ENVIRONMENT");

NLog.LogManager.Configuration.Variables.Add("environment", environn);

string vers = Directory.GetParent(AppContext.BaseDirectory).Name;
NLog.LogManager.Configuration.Variables.Add("version", vers);



//services.AddSwaggerGen(c =>
//{
//    c.SwaggerDoc("v1", new OpenApiInfo { Title = "Core.Themis.API.Offers", Version = "v1" });
//    var xmlFilename = $"{Assembly.GetExecutingAssembly().GetName().Name}.xml";
//    c.IncludeXmlComments(Path.Combine(AppContext.BaseDirectory, xmlFilename));
//});


var app = builder.Build();

// Configure the HTTP request pipeline.
//if (app.Environment.IsDevelopment())
{
    app.UseSwagger();
    app.UseSwaggerUI(c =>
    {
        c.SwaggerEndpoint("../swagger/ext/swagger.json", "ext");
        c.SwaggerEndpoint("../swagger/internal/swagger.json", "internal");
        c.EnableDeepLinking();
    });
}

app.UseReDoc((c =>
{
    c.DocumentTitle = $"Themis Integration API {vers} / {environn}";
    c.SpecUrl = "../swagger/ext/swagger.json";
    c.RequiredPropsFirst();
   
    //c.SpecUrl = "/redoc";
    //c.IndexStream = () => Assembly.GetExecutingAssembly()
    //       .GetManifestResourceStream("Core.Themis.Api.External.index.html"); // requires file to be added as an embedded resource


}));

app.UseHttpsRedirection();

app.UseIpRateLimiting();

app.UseCors();

app.UseResponseCaching();

app.UseAuthentication();

app.UseAuthorization();

app.MapControllers();

app.Run();



