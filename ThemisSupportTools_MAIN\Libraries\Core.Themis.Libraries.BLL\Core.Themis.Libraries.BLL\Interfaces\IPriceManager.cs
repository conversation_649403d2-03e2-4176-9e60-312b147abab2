﻿using Core.Themis.Libraries.DTO.EventsSessions;
using Parlot.Fluent;
using Core.Themis.Libraries.DTO.ReadyToPrint;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace Core.Themis.Libraries.BLL.Interfaces
{
    public interface IPriceManager
    {
        /// <summary>
        /// Get grilleTarif via GrilletarifBigOne
        /// </summary>
        /// <param name="structureId"></param>
        /// <param name="langCode">lang iso 2c: fr, de, en, it. fr and de by default</param>
        /// <param name="eventId">The event identifier</param>
        /// <param name="sessionId">The sessions identifier</param>
        /// <param name="identityId">The customer identifier</param>
        /// <param name="webUserId">The internet user identifier</param>
        /// <param name="buyerProfilId"></param>
        /// <param name="bpLogin">Buyer profil / Reseller login</param>
        /// <param name="bpPassword">Buyer profil /Reseller password</param>
        /// <param name="basketId"></param>
        /// <returns></returns>
        List<EventDTO> LoadPricesGrid(int structureId, string langCode, int eventId, int sessionId, int identityId, int webUserId, int buyerProfilId, string bpLogin, string bpPassword, int basketId);


        Task<List<ReadyToPrintGrilleTarifDTO>?> GetTarifsNamesBySessionsAndCategsIdAsync(int structureId, string langCode, int eventId, int[] sessionsId, int[] categsId);

    }
}
