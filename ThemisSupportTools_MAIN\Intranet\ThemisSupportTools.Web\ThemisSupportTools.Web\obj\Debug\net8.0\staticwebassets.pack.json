{"Files": [{"Id": "D:\\WORK\\TST_MAIN\\ThemisSupportTools_MAIN\\Intranet\\ThemisSupportTools.Web\\ThemisSupportTools.Web\\obj\\Debug\\net8.0\\scopedcss\\projectbundle\\ThemisSupportTools.Web.bundle.scp.css", "PackagePath": "staticwebassets\\ThemisSupportTools.Web.k3y8vupvmb.bundle.scp.css"}, {"Id": "D:\\WORK\\TST_MAIN\\ThemisSupportTools_MAIN\\Intranet\\ThemisSupportTools.Web\\ThemisSupportTools.Web\\wwwroot\\app.css", "PackagePath": "staticwebassets\\app.css"}, {"Id": "D:\\WORK\\TST_MAIN\\ThemisSupportTools_MAIN\\Intranet\\ThemisSupportTools.Web\\ThemisSupportTools.Web\\wwwroot\\bootstrap\\bootstrap.min.css", "PackagePath": "staticwebassets\\bootstrap\\bootstrap.min.css"}, {"Id": "D:\\WORK\\TST_MAIN\\ThemisSupportTools_MAIN\\Intranet\\ThemisSupportTools.Web\\ThemisSupportTools.Web\\wwwroot\\bootstrap\\bootstrap.min.css.map", "PackagePath": "staticwebassets\\bootstrap\\bootstrap.min.css.map"}, {"Id": "D:\\WORK\\TST_MAIN\\ThemisSupportTools_MAIN\\Intranet\\ThemisSupportTools.Web\\ThemisSupportTools.Web\\wwwroot\\css\\CheckWaitingPages.css", "PackagePath": "staticwebassets\\css\\CheckWaitingPages.css"}, {"Id": "D:\\WORK\\TST_MAIN\\ThemisSupportTools_MAIN\\Intranet\\ThemisSupportTools.Web\\ThemisSupportTools.Web\\wwwroot\\favicon.png", "PackagePath": "staticwebassets\\favicon.png"}, {"Id": "D:\\WORK\\TST_MAIN\\ThemisSupportTools_MAIN\\Intranet\\ThemisSupportTools.Web\\ThemisSupportTools.Web\\wwwroot\\js\\script.js", "PackagePath": "staticwebassets\\js\\script.js"}, {"Id": "obj\\Debug\\net8.0\\staticwebassets\\msbuild.ThemisSupportTools.Web.Microsoft.AspNetCore.StaticWebAssetEndpoints.props", "PackagePath": "build\\Microsoft.AspNetCore.StaticWebAssetEndpoints.props"}, {"Id": "obj\\Debug\\net8.0\\staticwebassets\\msbuild.ThemisSupportTools.Web.Microsoft.AspNetCore.StaticWebAssets.props", "PackagePath": "build\\Microsoft.AspNetCore.StaticWebAssets.props"}, {"Id": "obj\\Debug\\net8.0\\staticwebassets\\msbuild.build.ThemisSupportTools.Web.props", "PackagePath": "build\\ThemisSupportTools.Web.props"}, {"Id": "obj\\Debug\\net8.0\\staticwebassets\\msbuild.buildMultiTargeting.ThemisSupportTools.Web.props", "PackagePath": "buildMultiTargeting\\ThemisSupportTools.Web.props"}, {"Id": "obj\\Debug\\net8.0\\staticwebassets\\msbuild.buildTransitive.ThemisSupportTools.Web.props", "PackagePath": "buildTransitive\\ThemisSupportTools.Web.props"}], "ElementsToRemove": []}