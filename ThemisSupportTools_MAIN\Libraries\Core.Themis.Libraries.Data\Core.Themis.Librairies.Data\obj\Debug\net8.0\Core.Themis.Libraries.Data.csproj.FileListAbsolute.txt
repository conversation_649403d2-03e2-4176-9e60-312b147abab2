D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\EventsSessions\ProprertiesOfManifs\GetPropertieOfManifsByManifIdAndRefCode.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\Exports\ExportLieusSeancesFutures.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\planSalle\loadPlanSalleAvailabilitiesParam.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\Repositories\Open\ParamsDeflag\Interfaces\IParamsDeflagRepository.cs
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\Abonnement\GetAllFormulasWithTranslations.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\Abonnement\GetCatalogFormuleAboByFilter.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\Abonnement\GetFormuleAbonnement.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\Adhesions\checkAdhesionToConsumer.146.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\Adhesions\checkAdhesionToConsumer.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\Adhesions\checkAdhesionToConsumerProduct.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\Adhesions\getAdherentIdByIdentityId.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\Adhesions\getAdhesionCatalogsAndProperties.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\Adhesions\getAdhesionCatalogsOfIdentity.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\Adhesions\getAdhesionFromGpId.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\Adhesions\getAdhesionOfIdentity.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\Adhesions\getDetailAdhesionOfIdentity.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\Adhesions\insertAdhesionDossierEntree.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\BarCode\GetBarCodeByBarCodeAndColEmail.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\BarCode\GetBarCodeManif.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\BarCode\SetBarCode.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\Boutique\GetAllFamilliesProducts.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\Boutique\GetBoutiqueProductById.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\Boutique\GetFamilleById.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\Boutique\GetSousFamilleById.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\Boutique\LoadFamiliesSubFamiliesProducts.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\BuyerProfil\getBuyerProfilById.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\BuyerProfil\getBuyerProfilByLoginPassw.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\Categories\GetCategoriesForSessions.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\Cinema\GetTranslationOfDisciplineByIdAndLangCode.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\Cinema\GetTranslationOfLocalisationByIdAndLangCode.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\Cinema\LoadDisciplineByEventId.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\Cinema\LoadLocalisationsBySessionId.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\Consumers\createConsumer.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\CoupeFile\loadOrdersOfInternetUser.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\CrossSelling\getCrossSelling.146.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\CrossSelling\getCrossSelling.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\DepotVente\depotVente_Get.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\DepotVente\depotVente_getMontantReprise.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\DepotVente\depotVente_IsAutorise.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\DepotVente\depotVente_push.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\Distanciation\getDistanciation.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\Distanciation\updateSeatsDistanciation.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\EventsSessions\EventsGroups\GetAllEventsGroupsByEventTypeId.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\EventsSessions\EventsGroups\GetAllEventsGroupWithEvents.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\EventsSessions\EventsGroups\GetAllEventsGroupWithEventsForSelect.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\EventsSessions\EventsGroups\GetEventGroupTranslationById.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\EventsSessions\EventsGroups\GetEventsGroupsByEventTypesId.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\EventsSessions\EventsGroups\GetEventsGroupsByEventTypesIdAndGenresId.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\EventsSessions\EventsGroups\GetEventsGroupsByEventTypesIdAndTargetsId.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\EventsSessions\EventsGroups\GetEventsGroupsOnSaleByEventsTypes.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\EventsSessions\EventsGroups\GetEventsTypeOnSale.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\EventsSessions\Genres\GetAllGenresByEventTypeId.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\EventsSessions\Genres\GetEventsGenreByEventSubGenreId.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\EventsSessions\Genres\GetEventsGenreOnSaleByEventsTypes.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\EventsSessions\Genres\GetGenresByEventTypesId.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\EventsSessions\Genres\GetGenresByEventTypesIdAndEventGroupsId.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\EventsSessions\Genres\GetGenresByEventTypesIdAndTargetsId.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\EventsSessions\GetAllEventsAfterADate.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\EventsSessions\GetCatalogEventsByFilter.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\EventsSessions\getEventGenreById.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\EventsSessions\getEventGroupById.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\EventsSessions\getEventSessionOfSession.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\EventsSessions\GetEventsGeneralsAndAdhesionsOfferByFilter.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\EventsSessions\getEventsGroupsOfEventsSales.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\EventsSessions\getEventsInAnInterval.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\EventsSessions\getEventsInfos.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\EventsSessions\GetEventsOnSale.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\EventsSessions\GetEventsOnSaleIntoAdhesionOffer.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\EventsSessions\getIndivScheduleByIdentitePA.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\EventsSessions\GetInfosByEventId.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\EventsSessions\GetInfosEventForHomeModular.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\EventsSessions\GetInfosOfEventByIdAndLangCode.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\EventsSessions\GetInfosOfEventByIdAndLangCodeWithDependencies.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\EventsSessions\GetManifestationImageByEventId.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\EventsSessions\getSessionsEntitiesInAnInterval.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\EventsSessions\getSessionsInfos.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\EventsSessions\getSubGenreById.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\EventsSessions\GetTranslationEventInfos.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\EventsSessions\GetTranslationOfEventByIdAndLangCode.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\EventsSessions\GetTranslationOfLockByEventIdAndLangCode.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\EventsSessions\GetTranslationOfLockManifByEventId.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\EventsSessions\GetTranslationOfWaitingListByEventIdAndLangCode.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\EventsSessions\GetTranslationOfWaitingListById.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\EventsSessions\InsertLowestPrice.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\EventsSessions\InsertLowestPriceAbo.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\EventsSessions\InsertLowestPriceManif.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\EventsSessions\loadEventsFeatures.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\EventsSessions\loadEventsGroups.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\EventsSessions\loadEventsOfThisFormulaWithTranslation.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\EventsSessions\loadGenreSousGenre.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\EventsSessions\loadPricesGridOfEvents.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\EventsSessions\loadSessionsPricesOfEvents.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\EventsSessions\loadSessionsZoneFloorSectionCategPricesOfEvents.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\EventsSessions\ProprertiesOfManifs\DeletePropertieOfManif.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\EventsSessions\ProprertiesOfManifs\InsertPropertieOfManif.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\EventsSessions\ProprertiesOfManifs\UpdatePropertieOfManif.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\EventsSessions\ProprertiesReferences\GetPropertieReferenceOfManifsByRefCode.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\EventsSessions\SousGenres\GetSousGenresOnSaleByGenre.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\EventsSessions\SousGenres\GetSubGenresByEventsTypesId.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\EventsSessions\SousGenres\GetSubGenresByEventTypesIdAndEventGroupsId.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\EventsSessions\SousGenres\GetSubGenresByEventTypesIdAndGenresId.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\EventsSessions\SousGenres\GetSubGenresByEventTypesIdAndTargetsId.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\EventsSessions\SousGenres\GetTranslationOfSousGenreAndLangCode.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\Exports\exportCustomersChanges.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\Exports\exportCustomersInfoCompChanges.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\Exports\ExportDetailBilletsByEvent.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\Exports\ExportEtatBilletterie.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\Exports\ExportEtatBilletterieEmis.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\Exports\exportEventsGauges.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\Exports\ExportEventsGaugev0.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\Exports\ExportEventsPriceGridBCubix.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\Exports\ExportEventsPriceGridReelax.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\Exports\exportGlobalMoves.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\Exports\ExportMouvementsProducts.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\Exports\ExportMouvementsSeats.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\Exports\ExportRestantBySeanceCategReserve.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\Exports\ExportsRecetteByEventSession.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\Exports\exportsVardar.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\Exports\ExportsVardarAccessControl.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\Exports\ExportVardarAllEventsSessionsPlaces.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\Exports\SoftwareAGgetDatas.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\Exports\SoftwareAGupdateDatas.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\Exports\SoftwareAGupdateDatasV2.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\Exports\TestGetManifByGroupId.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\GestionPlaces\---getGrilleTarifs_BigOne.218.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\GestionPlaces\dispoBySeanceCategForOperateur.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\GestionPlaces\GetAllAdhesionsByEventIdAndSessionId.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\GestionPlaces\GetAllByEventIdAndSessionId.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\GestionPlaces\GetAllByEventIdAndSessionIdAndOffersId.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\GestionPlaces\GetDispoFromGestionPlacesWithOffer.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\GestionPlaces\GetGestionPlaceById.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\GestionPlaces\GetGestionPlaceTypeEnvoiByEventSeddionCategTypeTarif.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\GestionPlaces\GetGestionPlaceWithOfferBySessionIdAndCategoryId.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\GestionPlaces\getGpsByIds.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\GestionPlaces\getGrilleTarifs.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\GestionPlaces\getGrilleTarifs_BigOne.146.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\GestionPlaces\getGrilleTarifs_BigOne.553.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\GestionPlaces\getGrilleTarifs_BigOne.614.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\GestionPlaces\getGrilleTarifs_BigOne.744.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\GestionPlaces\getGrilleTarifs_BigOne.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\GestionPlaces\getIndivPricesCategs.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\GestionPlaces\getIndivSessions.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\GestionPlaces\getIndivSessionsByIdentitePA.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\GestionPlaces\GetInfosOfSessionsByIdentitePA.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\GestionPlaces\getLowerPriceOfEvent.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\GestionPlaces\getMObentionByGpsIds.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\GestionPlaces\getMObentionByProductsIds.412.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\GestionPlaces\getMObentionByProductsIds.655.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\GestionPlaces\getMObentionByProductsIds.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\GestionPlaces\GetReserveIdsFromGestionPlacesWithOffer.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\GestionPlaces\loadGestionPlaceTypeEnvoi.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\GestionPlaces\loadTarifCommentaires.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\GestionPlaces\PanierTransformatorAddSupp.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\GestionPlaces\PanierTransformatorAdhesion.150.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\GestionPlaces\PanierTransformatorAdhesion.218.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\GestionPlaces\PanierTransformatorAdhesion.347.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\GestionPlaces\PanierTransformatorAdhesion.347_saison2324.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\GestionPlaces\PanierTransformatorAdhesion.661.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\GestionPlaces\PanierTransformatorAdhesion.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\GestionPlaces\èèPanierTransformatorAdhesion.146.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\GpManifestation\CreateColumnsForWaitingListAndTraduction.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\GpSeance\CreateColumnTraductionForLock.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\HomeModular\Clients\checkHomeModularBlockUserConfig.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\HomeModular\Clients\deleteHomeModularBlockEmplacement.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\HomeModular\Clients\GetAllEmplacementGroups.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\HomeModular\Clients\GetBlockEmplacementByBlockTypeId.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\HomeModular\Clients\GetHomeModularBlockEmplacementById.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\HomeModular\Clients\getHomeModularBlockEmplacementList.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\HomeModular\Clients\getHomeModularBlockUserConfigList.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\HomeModular\Clients\getHomeModularEmplacementGroupByIdWithDependencies.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\HomeModular\Clients\getHomeModularEmplacementGroupList.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\HomeModular\Clients\insertHomeModularBlockEmplacement.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\HomeModular\Clients\insertHomeModularBlockUserConfig.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\HomeModular\Clients\LoadProductsFeatures - Copier.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\HomeModular\Clients\LoadProductsFeatures.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\HomeModular\Clients\UpdateBlockUserConfigValuesTemp.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\HomeModular\getBlockFunctionByIdWithDependencies.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\HomeModular\GetBlockFunctionsByBlockTypeIdWithDependencies.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\HomeModular\GetFunctionsOfBlockTypeById.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\HomeModular\getHomeModularBlockTypeList.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\HomeModular\getHomeModularFunctionsList.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\Identity\AddConsumer.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\Identity\AddIdentiteComplement.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\Identity\AddIdentiteComplementWeb.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\Identity\AddIdentity - Copie.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\Identity\AddIdentity.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\Identity\AddInfoCompOnIdentite.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\Identity\GetConsommateurs.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\Identity\getIdentiteForPatHome.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\Identity\GetIdentity.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\Identity\GetIdentity_Unidy.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\Identity\GetIdIdentiteComplementWebPassword.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\Identity\GetLibelleTels.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\Identity\LinkConsumer.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\Identity\SearchConsumer.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\Identity\SelectCiblesFromListIdentite.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\Identity\UpdateIdentity.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\Identity\UpdatePasswordIdentity.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\infoComp\DeleteInfoCompFromEmails.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\infoComp\DeleteInfoCompFromIdentiteIds.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\infoComp\InsertInfoComp.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\infoComp\InsertInfoCompForEmails.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\infoComp\PushInfoCompsOfIdentite.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\Insurance\ActivateInsurance.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\Insurance\createGlobalInsuranceOrder.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\Insurance\HasInsuranceProduct.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\Languages\getLanguage.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\LogsPartenaires\getLogPartnerById.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\Maquettes\GetById.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\Maquettes\GetDefaultMaquette.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\Maquettes\GetFontName.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\Maquettes\getMaquetteCoupeFile.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\Maquettes\GetVisibleWebMaquettes.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\Maquettes\loadMaquette.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\Offers\getOffersByIdentitePA.146.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\Offers\getOffersByIdentitePA.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\Offers\GetOffersLinkedToBuyerProfilRevendeur.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\Offers\GetOffresProfilAcheteur.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\OpenPartner\CheckPartnerIsValidRevender.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\OpinionOrder\--selectOrderIdOpen.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\OpinionOrder\Average\selectOpinionOrderAverage.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\OpinionOrder\Average\selectOpinionOrderAverageGraph.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\OpinionOrder\isOpinionConfigured.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\OpinionOrder\isOpinionManaged.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\OpinionOrder\Order\getOrdersFromIds.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\OpinionOrder\Questions\getOpinionOrderList.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\OpinionOrder\Responses\createBlankEvaluationsForm.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\OpinionOrder\Responses\createOpinionOrderResponses.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\OpinionOrder\Responses\getBlanksEvaluationsFormsList.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\OpinionOrder\Responses\getNonUpdatedEvaluationsFormsList.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\OpinionOrder\Responses\getOpinionOrderList.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\OpinionOrder\Responses\updateEvaluationsForm_setDemandeAvisEnvoye.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\OpinionOrder\Responses\updateEvaluationsForm_setInfos.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\OpinionOrder\Responses\updateEvaluationsForm_setOrderId.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\OpinionOrder\Responses\updateOpinionOrderForms.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\Order\CancelDossier.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\Order\CancelEntrees.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\Order\cancelOrder.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\Order\CancelOrderMiseEnAcompte.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\Order\cancelOrderOld.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\Order\CancelOrderReservation.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\Order\createCommandeLigne.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\Order\createSeatDossier.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\Order\createSeatDossierHisto.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\Order\createSeatDossierSvg.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\Order\DeleteEntreeComplement.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\Order\downgradeorder.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\Order\GetAllEntreesEditeesOfOrderById.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\Order\GetAllEntreesOfIdentiteById.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\Order\GetAllOrdersByIdentiteId.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\Order\GetAllOrderWithoutReservationByIdentiteId.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\Order\GetAllReservationByIdentiteId.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\Order\GetAllReservationOrderAvailableForPassCultureCollectiveOffer.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\Order\GetAllReservationUpdatablePassCultureCollectiveOffer.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\Order\getCommandesLignes.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\Order\getDossiersProductFromOrderId.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\Order\getDossiersSeatsFromOrderId.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\Order\GetEntreeByCriteria.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\Order\getEventSessionPrice.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\Order\GetOrderById.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\Order\GetOrderByIdForInsurance.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\Order\getOrderDetailFromId.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\Order\getOrderFromId.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\Order\getOrderFromId_Buyer.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\Order\GetOrderIdsByBarcodes.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\Order\getOrdersFromIds.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\Order\getReservationsOfIdentity.224.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\Order\getReservationsOfIdentity.281.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\Order\getReservationsOfIdentity.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\Order\InsertEntreeComplement.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\Order\updatecommandeLigne_icone.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\Order\updateDossier_dossierC.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\Partner\createPartner.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\Partner\createPartnerStructureLink.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\Partner\deletePartnerStructureLink.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\Partner\getPartner.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\Partner\GetPartnerById.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\Partner\GetPartnerByName.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\Partner\getPartnerId.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\Partner\getPartnerRoles.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\Partner\getPartnersList.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\Partner\getStructuresOfPartner.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\Partner\updatePartner.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\Partner\updatePartnerRoleLink.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\Partner\updatePartnerStructuresLink.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\Places\getFloorsFromFloorIds.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\Places\getPlacesFromSessionsIds.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\Places\getSectionsFromSectionIds.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\Places\getZonesFromZoneIds.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\planSalle\loadAllAlotissements.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\planSalle\loadAllCategs.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\planSalle\loadAllCategsBySession.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\planSalle\loadAllContingents.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\planSalle\loadAllDenominations.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\planSalle\loadAllFloors.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\planSalle\loadAllReserves.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\planSalle\loadAllSections.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\planSalle\loadAllTribunesGatesAcces.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\planSalle\loadAllZones.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\planSalle\loadPlanSalle.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\planSalle\loadPlanSalleParam.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\planSalle\loadPlanSalleParamIndispo.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\planSalle\loadPlanSalleTexts.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\planSalle\loadPlanSalleTextsParam.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\Products\GetAllFamily.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\Products\GetAllProductFamily.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\Products\GetAllProductFamilyKeyValues.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\Products\GetAllProducts.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\Products\GetAllProductsInternet.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\Products\GetAllProductsWithTranslations.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\Products\GetAllSubFamilyByProductFamilyId.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\Products\GetAssuranceProduct.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\Products\GetFraisDossierByFiliere.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\Products\getMaquettesProductsByMo.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\Products\GetProductById.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\Products\getProductForPatHome.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\Products\getProductForPatHomeCore.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\Products\LoadFamiliesSubFamiliesProductsEvent.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\Products\LoadFamiliesSubFamiliesProductsGlobaux.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\Products\LoadFamiliesSubFamiliesProductsSession.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\Recettes\getRecettes.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\Recettes\getRecettesFromBarCode.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\Recettes\getRecettesProducts.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\Recettes\insertLigneRecetteBillet.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\Recettes\setBarCodeRecette.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\ReservationOrder\createReservationOrder.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\ReservationOrder\createReservationScheduleReminder.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\ReservationOrder\getInfoResaForBasket.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\ReservationOrder\getReservationsOfIdentity.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\ReservationOrder\getReservationsReminder.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\ReservationOrder\isResa_alive.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\ReservationOrder\reservationEvents.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\ReservationOrder\updateReservationOrder.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\ReservationOrder\updateScheduleReservationOrder_setEnvoye.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\Reserves\reservesOfOperateur.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\SaleChannel\getSaleChannelList.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\SaleChannel\selectSaleChannelOpen.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\SaleRulesAndCo\getBuyerProfil.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\Seats\FlagAuto.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\Seats\flagSeat.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\Seats\GetAllEntreesEditeesBySeanceIdAndIdentiteId.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\Seats\getSeatForPatHome.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\Seats\getSeats.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\Seats\getSeats_lang.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\Seats\unflagSeats.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\Seats\UpdateEntreeControleAccess.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\Seats\UpdateEntreeReservation.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\Seats\UpdateEntreesPayees.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\Session\GetAllSessionsByEventId.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\Session\GetAllSessionsByEventIdOnSale.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\Session\GetAllSessionsTicketsByIdentiteId.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\Session\GetSessionById.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\Session\GetSessionsByFormulaId.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\Sponsors\GetCardsToSend.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\Sponsors\GetSponsoredAmount.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\Sponsors\GetSponsors.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\Sponsors\GetSponsorUsedRemaining.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\Sponsors\SetDateSent.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\Sponsors\SetDateSentNull.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\Structures\open_getStructureInfos.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\Targets\GetAllTargetsByEventTypeId.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\Targets\GetTargetsByEventTypesId.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\Targets\GetTargetsByEventTypesIdAndEventGroupsId.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\Targets\GetTargetsByEventTypesIdAndGenresId.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\Targets\GetTranslationOfTargetByIdAndLangCode.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\Targets\LoadTargetsBySessionId.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\Targets\LoadTargetsOnSaleByEventsTypes.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\ThemisSupportTools\InsertTstAccess.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\Translations\Clients\--getSpecificTranslationsParent.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\Translations\Clients\GetSpecificClientTranslations.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\Translations\Clients\getSpecificTranslations.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\Translations\Clients\getSpecificTranslationsParent.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\Translations\Clients\GetTranslationByKeyword.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\Translations\Clients\GetTranslationsLikeKeyword.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\Translations\Clients\insertSpecificTranslations.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\Translations\Rodrigue\--updateTranslationsFieldVariable.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\Translations\Rodrigue\checkTranslationsFieldVariable.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\Translations\Rodrigue\deleteDefaultTranslation.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\Translations\Rodrigue\DeleteTranslateFieldsCodesList.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\Translations\Rodrigue\deleteTranslationsArea.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\Translations\Rodrigue\deleteTranslationsFieldVariable.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\Translations\Rodrigue\deleteTranslationsVariable.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\Translations\Rodrigue\GetGlobalFieldsTranslations.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\Translations\Rodrigue\GetGlobalTranslationById.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\Translations\Rodrigue\getGlobalTranslations.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\Translations\Rodrigue\GetTranslateFieldsCodesList.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\Translations\Rodrigue\GetTranslationByKeyword.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\Translations\Rodrigue\GetTranslationLikeKeyword.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\Translations\Rodrigue\getTranslationsAreasList.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\Translations\Rodrigue\getTranslationsFieldCodeList.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\Translations\Rodrigue\getTranslationsFields.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\Translations\Rodrigue\gettranslationsFieldsCode.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\Translations\Rodrigue\getTranslationsGlobalFields.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\Translations\Rodrigue\getTranslationsTermsAndValues.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\Translations\Rodrigue\getTranslationsVariablesList.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\Translations\Rodrigue\InsertTranslateArea.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\Translations\Rodrigue\InsertTranslateFieldsCodesList.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\Translations\Rodrigue\InsertTranslateVariables.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\Translations\Rodrigue\insertTranslations.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\Translations\Rodrigue\insertTranslationsArea.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\Translations\Rodrigue\insertTranslationsFieldCode.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\Translations\Rodrigue\insertTranslationsFieldVariable.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\Translations\Rodrigue\insertTranslationsVariable.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\Translations\Rodrigue\updateTranslations.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\Translations\Rodrigue\updateTranslationsArea.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\Translations\Rodrigue\updateTranslationsFieldCode.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\Translations\Rodrigue\updateTranslationsVariable.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\TST\Deflag\insertParamDeflag.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\ValeurTarifStock\GetBySessionAndCategsId.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\ValeurTarifStock\getValeurTarifStock.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\WT\Baskets\AddPaiementAction.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\WT\Baskets\AddProductResa.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\WT\Baskets\AddSeatsIndiv.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\WT\Baskets\CheckGpIdEnCours.150.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\WT\Baskets\CheckGpIdEnCours.218.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\WT\Baskets\CheckGpIdEnCours.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\WT\Baskets\CheckProductAdhesionEnCours.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\WT\Baskets\DeleteAllProductCarteAdhesion.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\WT\Baskets\DeleteAllProductEventSessionGlobal.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\WT\Baskets\DeleteAllSessionsPanierEntree.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\WT\Baskets\DeleteMObentionOnBasketId.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\WT\Baskets\DeletePanierProduit.412.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\WT\Baskets\DeletePanierProduit.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\WT\Baskets\DeletePanierProduit_prodId.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\WT\Baskets\deleteSeatInBasketLine.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\WT\Baskets\GetAllOrderIdByBuyerProfilIdAndSessionId.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\WT\Baskets\getBaskets.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\WT\Baskets\getBasketsFromOrdersId.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\WT\Baskets\getBasketsWebUsers.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\WT\Baskets\GetCartesAdhesionEnCours.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\WT\Baskets\GetGpIdEnCours.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\WT\Baskets\getPaiementActions.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\WT\Baskets\getPaiementActions_baskets.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\WT\Baskets\GetPanierFromWebUser.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\WT\Baskets\InsertPanierProduit.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\WT\Baskets\InsertPanierProduitCarteAdhesionProps.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\WT\Baskets\UpdateConsumerEntrees.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\WT\Baskets\UpdateEtatPanier.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\WT\Baskets\UpdateEtatPanierStateToState.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\WT\Baskets\UpdateTypeEnvoiAndMaquette.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\WT\Baskets\UpdateTypeEnvoiAndMaquette_products.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\WT\SaleDatas\getSaleDatas.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\WT\ServicesAsynch\fillInfosWTDataBase.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\WT\ServicesAsynch\getLogsEtapesCreationActions.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\WT\ServicesAsynch\getLogsEtapesCreationCmd.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\WT\ServicesAsynch\getPaniersToDo_createcmd.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\WT\ServicesAsynch\getPaniersToDo_createcmdBasketId.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\WT\ServicesAsynch\getPaniersToDo_reflag.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\WT\ServicesAsynch\getPaniersToDo_reflagBasketId.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\WT\ServicesAsynch\getPaniersToInform.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\WT\ServicesAsynch\getPaniersToSendEmailError.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\WT\ServicesAsynch\getPanierToDo_straight.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\WT\ServicesAsynch\insertLogEtapeCommande.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\WT\ServicesAsynch\insertLogEtapeInformEmail.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\WT\ServicesAsynch\RestartStepPanier.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\WT\ServicesAsynch\setEmailErrorEnvoye.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\WT\ServicesAsynch\updatePanierToDo_straight.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\WT\Sponsors\CheckSponsorPanierEntreesExist.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\WT\Sponsors\GetAllSponsorPanierEntrees.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\WT\Sponsors\GetSponsorReferenceOfMyBasket.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\WT\Sponsors\GetSponsorReferenceOfOtherBasket.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\WT\Sponsors\GetSponsorsPanierEntree.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\WT\Sponsors\InsertSponsorPanierEntree.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\WT\WebUsers\GetLogsWebUserId.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\WT\WebUsers\GetWebUser.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\WT\WebUsers\GetWebUsers.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\WT\WebUsers\GetWebUsersCount.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\WT\WebUsers\InsertWebUser.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\WT\WebUsers\UpdateCouponPromo.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\Core.Themis.Libraries.Data.deps.json
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\Core.Themis.Libraries.Data.dll
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\Core.Themis.Libraries.Data.pdb
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\Core.Themis.Libraries.DTO.dll
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\Core.Themis.Libraries.Utilities.dll
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\Microsoft.Data.Tools.Schema.Sql.UnitTesting.dll
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\Microsoft.Data.Tools.Schema.Sql.UnitTestingAdapter.dll
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\Core.Themis.Libraries.DTO.pdb
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\Core.Themis.Libraries.Utilities.pdb
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\fr\Microsoft.Data.Tools.Schema.Sql.UnitTesting.resources.dll
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\obj\Debug\net8.0\Core.Themis.Libraries.Data.csproj.AssemblyReference.cache
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\obj\Debug\net8.0\Core.Themis.Libraries.Data.GeneratedMSBuildEditorConfig.editorconfig
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\obj\Debug\net8.0\Core.Themis.Libraries.Data.AssemblyInfoInputs.cache
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\obj\Debug\net8.0\Core.Themis.Libraries.Data.AssemblyInfo.cs
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\obj\Debug\net8.0\Core.Themis.Libraries.Data.csproj.CoreCompileInputs.cache
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\obj\Debug\net8.0\Core.The.CE68FCE1.Up2Date
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\obj\Debug\net8.0\Core.Themis.Libraries.Data.dll
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\obj\Debug\net8.0\refint\Core.Themis.Libraries.Data.dll
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\obj\Debug\net8.0\Core.Themis.Libraries.Data.pdb
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\obj\Debug\net8.0\ref\Core.Themis.Libraries.Data.dll
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\TST\CouponsPromo\getCouponsPromo.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\TST\GenerateReferenceSalleImageXml\generateReferenceSalleImageXml.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\TST\GenerateReferenceWebVignetteXml\generateReferenceWebVignetteXml.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\TST\ListePlanAvecImage\listePlanAvecImage.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\TST\ListePlanSansImage\listePlanSansImage.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\TST\TransferePointagePhoto\transferePointagePhoto.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\TST\TransfereVignette\transfereVignetteAsync.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\infoComp\InsertInfoCompForEmailsGroup.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\GestionPlaces\getGrilleTarifs_BigOneV2.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\GestionPlaces\GetTTPublicByEventIdAndSessionId.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\infoComp\GetVisibleInfoCompGroupsOrderedByPrefAffichage.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\Products\GetAllProductColorsAvailable.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\Products\GetAllProductSizesAvailable.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\Products\GetCatalogProductsByFilter.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\Products\GetProductColorsAvailableByProductFamilyIds.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\Products\GetProductColorsAvailableByProductSubFamilyIds.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\Products\GetProductSizesAvailableByProductFamilyIds.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\Products\GetProductSizesAvailableByProductSubFamilyIds.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\TST\GenerateReferenceSalleImageXml\generateReferenceSalleImageXmlByLieuPhysique.sql
D:\WORK\TST_MAIN\ThemisSupportTools_MAIN\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\bin\Debug\net8.0\libraries_SQLScripts\TST\GenerateReferenceWebVignetteXml\generateReferenceWebVignetteXmlByLieuPhysique.sql
