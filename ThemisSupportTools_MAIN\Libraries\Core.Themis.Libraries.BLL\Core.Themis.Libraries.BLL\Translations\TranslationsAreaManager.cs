﻿using Core.Themis.Libraries.DTO.Translations;
using Core.Themis.Libraries.Utilities.Logging;
using Core.Themis.Libraries.Utilities.ThemisSql;
using System;
using System.Collections.Generic;
using System.Data;
using Microsoft.Data.SqlClient;

namespace Core.Themis.Libraries.BLL.Translations
{
    public class TranslationsAreaManager
    {

        private static readonly RodrigueNLogger RodrigueLogger = new RodrigueNLogger();


        /// <summary>
        /// Liste des sections sur les termes 
        /// </summary>
        /// <param name="structureId"></param>
        /// <returns></returns>
        public static List<TranslationAreaDTO> GetTranslationsAreasList(int structureId, SqlConnection cnxWebLibrary, string scriptPathSqlCommons)
        {

            List<TranslationAreaDTO> lstAreas = new List<TranslationAreaDTO>();
            try
            {
                if (cnxWebLibrary.State == ConnectionState.Closed)
                    cnxWebLibrary.Open();
                //liste contenant le nom des sections
                string translationsAreasListSql = FilesForSqlRequestsManager.GetScript(structureId, "Translations\\Rodrigue\\", "getTranslationsAreasList", scriptPathSqlCommons);

                using (SqlCommand cmd = new SqlCommand())
                {
                    cmd.Connection = cnxWebLibrary;
                    #region Rempli la liste des sections
                    cmd.CommandText = translationsAreasListSql;

                    using (SqlDataReader reader = cmd.ExecuteReader())
                    {
                        while (reader.Read())
                        {
                            TranslationAreaDTO area = new TranslationAreaDTO()
                            {
                                TranslationAreaId = reader.GetInt32(reader.GetOrdinal("id")),
                                TranslationAreaName = reader.GetString(reader.GetOrdinal("name")),
                                TranslationAreaDescription = reader.GetString(reader.GetOrdinal("description"))
                            };

                            lstAreas.Add(area);
                        }
                    }
                    #endregion
                }


                cnxWebLibrary.Close();
            }
            catch (Exception ex)
            {
                throw new Exception(ex.Message);
            }
            finally
            {

                if (cnxWebLibrary.State == ConnectionState.Open)
                    cnxWebLibrary.Close();
            }
            return lstAreas;

        }
        public static bool InsertTranslationArea(int structureId, TranslationAreaDTO translationArea, SqlConnection cnxWebLibrary, string scriptPathSqlCommons)
        {
            bool result = false;
            try
            {


                if (cnxWebLibrary.State == ConnectionState.Closed)
                    cnxWebLibrary.Open();


                //liste contenant les traductions par défault dans Rodrigue
                string translationsSpecificsListSql = FilesForSqlRequestsManager.GetScript(structureId, "Translations\\Rodrigue\\", "insertTranslationsArea", scriptPathSqlCommons);

                using (SqlCommand cmd = new SqlCommand())
                {
                    cmd.Parameters.Add(new SqlParameter("@pName", translationArea.TranslationAreaName));
                    cmd.Parameters.Add(new SqlParameter("@pDescription", translationArea.TranslationAreaDescription));

                    cmd.Connection = cnxWebLibrary;
                    cmd.CommandText = translationsSpecificsListSql;

                    //return (int)cmd.ExecuteScalar();
                    result = cmd.ExecuteNonQuery() == 1;
                }


                cnxWebLibrary.Close();

            }
            catch (Exception ex)
            {
                throw new Exception(ex.Message);
            }
            finally
            {
                if (cnxWebLibrary.State == ConnectionState.Open)
                    cnxWebLibrary.Close();

            }


            return result;

        }


        public static bool UpdateTranslationArea(int structureId, TranslationAreaDTO translationArea, SqlConnection cnxWebLibrary, string scriptPathSqlCommons)
        {
            bool result = false;
            try
            {


                if (cnxWebLibrary.State == ConnectionState.Closed)
                    cnxWebLibrary.Open();


                //liste contenant les traductions par défault dans Rodrigue
                string translationsSectionSql = FilesForSqlRequestsManager.GetScript(structureId, "Translations\\Rodrigue\\", "updateTranslationsArea", scriptPathSqlCommons);

                using (SqlCommand cmd = new SqlCommand())
                {
                    cmd.Parameters.Add(new SqlParameter("@pAreaId", translationArea.TranslationAreaId));
                    cmd.Parameters.Add(new SqlParameter("@pName", translationArea.TranslationAreaName));
                    cmd.Parameters.Add(new SqlParameter("@pDescription", translationArea.TranslationAreaDescription));

                    cmd.Connection = cnxWebLibrary;
                    cmd.CommandText = translationsSectionSql;

                    //return (int)cmd.ExecuteScalar();
                    result = cmd.ExecuteNonQuery() == 1;
                }


                cnxWebLibrary.Close();

            }
            catch (Exception ex)
            {
                throw new Exception(ex.Message);
            }
            finally
            {
                if (cnxWebLibrary.State == ConnectionState.Open)
                    cnxWebLibrary.Close();

            }


            return result;

        }

        public static bool DeleteTranslationArea(int structureId, int translationAreaId, SqlConnection cnxWebLibrary, string scriptPathSqlCommons)
        {
            bool result = false;
            try
            {


                if (cnxWebLibrary.State == ConnectionState.Closed)
                    cnxWebLibrary.Open();


                //liste contenant les traductions par défault dans Rodrigue
                string translationsSpecificsListSql = FilesForSqlRequestsManager.GetScript(structureId, "Translations\\Rodrigue\\", "deleteTranslationsArea", scriptPathSqlCommons);

                using (SqlCommand cmd = new SqlCommand())
                {
                    cmd.Parameters.Add(new SqlParameter("@pAreaId", translationAreaId));

                    cmd.Connection = cnxWebLibrary;
                    cmd.CommandText = translationsSpecificsListSql;

                    //return (int)cmd.ExecuteScalar();
                    result = cmd.ExecuteNonQuery() == 1;
                }


                cnxWebLibrary.Close();

            }
            catch (Exception ex)
            {
                throw new Exception(ex.Message);
            }
            finally
            {
                if (cnxWebLibrary.State == ConnectionState.Open)
                    cnxWebLibrary.Close();

            }


            return result;

        }




    }
}
