﻿using Core.Themis.Libraries.DTO.Enums.Catalog;
using Core.Themis.Libraries.DTO.Catalog.Event;
using Core.Themis.Libraries.DTO.Catalog.Filter;
using Core.Themis.Libraries.DTO.Catalog.Models;
using Core.Themis.Libraries.DTO.Lookup;
using System.Collections.Generic;
using System.Threading.Tasks;
using System;
using Core.Themis.Libraries.DTO.Catalog.Product;


namespace Core.Themis.Libraries.BLL.Managers.Catalog.Interfaces
{
    public interface ICatalogManager
    {
        /// <summary>
        /// Pagination des cards des manifestations filtées
        /// </summary>
        /// <param name="structureId"></param>
        /// <param name="filter"></param>
        /// <param name="pageNumber"></param>
        /// <param name="pageSize"></param>
        /// <returns></returns>
        Task<PaginationLookup<CardEventInfoBase>?> GetEventsCardsInfoPaginationAsync(int structureId, SearchForm filter, int pageNumber = 0, int pageSize = 9);

        /// <summary>
        /// Pagination des cards d'abonnements fermés filtés
        /// </summary>
        /// <param name="structureId"></param>
        /// <param name="filter"></param>
        /// <param name="pageNumber"></param>
        /// <param name="pageSize"></param>
        /// <returns></returns>
        Task<PaginationLookup<CardEventInfoBase>?> GetAbosCardsInfoPaginationAsync(int structureId, SearchForm filter, int pageNumber = 0, int pageSize = 9);

        /// <summary>
        /// Pagination des cards de produits filtés
        /// </summary>
        /// <param name="structureId"></param>
        /// <param name="filter"></param>
        /// <param name="pageNumber"></param>
        /// <param name="pageSize"></param>
        /// <returns></returns>
        Task<PaginationLookup<CardProductInfoBase>?> GetProductsCardsInfoPaginationAsync(int structureId, SearchForm filter, int pageNumber = 0, int pageSize = 9);

        Task<List<FilterItem>> GetDynamicFiltersAsync(int structureId, string langCode, int[] mainMenuIds, int[] subMenuIds, EnumFilterItemType mainFilterItemType, EnumFilterItemType? subFilterItemType, int identiteId, int buyerProfilId);

        /// <summary>
        ///  Insert les prix les plus bas dans la table LowerPrice
        /// </summary>
        /// <param name="structureId"></param>
        /// <param name="offersId"></param>
        /// <param name="manifestationsId"></param>
        /// <param name="deleteAllRows"></param>
        /// <returns></returns>
        Task InsertLowerPrice(int structureId, List<int> offersId, List<int> manifestationsId, bool deleteAllRows = false);

        /// <summary>
        ///  Insert les prix les plus bas dans la table LowerPriceAbo
        /// </summary>
        /// <param name="structureId"></param>
        /// <param name="formulasIds"></param>
        /// <param name="deleteAllRows"></param>
        /// <returns></returns>
        Task InsertLowestPricesAboAsync(int structureId, List<int> formulasIds, bool deleteAllRows = false);

        /// <summary>
        /// Get  offer catalog linked to an destination page (ex: events page).
        /// return default catalog if not exist
        /// </summary>
        /// <param name="structureId"></param>
        /// <param name="destinationPageType"></param>
        /// <param name="langCode"></param>
        /// <returns></returns>
        /// <exception cref="NullReferenceException"></exception>
        Task<OfferCatalogDTO> GetOfferCatalogForDestinationPageAsync(int structureId, EnumDestinationPageType destinationPageType, string langCode, int buyerProfilId);

        Task<OfferCatalogDTO> GetOfferCatalogById(int structureId, int catalogId, string langCode, int buyerProfilId);

        Task<CatalogDTO> GetCatalogSettingsByIdAsync(int catalogId, int structureId, string langCode);

        Task<CatalogDTO> GetNewCatalogSettingsAsync(int structureId, string langCode, EnumDestinationPageType destinationPage, int? modelCatalogId = null);

        AreaDTO GetNewCatalogArea(int structureId, string langCode);

        List<CatalogDTO> GetAllCatalogsByStructureId(int structureId);

        int AddNewCatalog(CatalogDTO catalog);

        bool UpdateCatalog(CatalogDTO catalog);

        bool DeleteCatalog(CatalogDTO catalog);

        int AddDestinationPage(DestinationPageDTO destinationPage);

        bool UpdateDestinationPage(DestinationPageDTO destinationPage);

        bool DeleteDestinationPage(DestinationPageDTO destinationPage);

        List<MainMenuTypeDTO> GetAllMainMenuType();

        EnumMainMenuType[] GetAllParentMainMenuTypes();

        List<EnumSubMenuType> GetAllSubMenuTypeByMainMenuType(EnumMainMenuType mainMenuType);

        Task<List<SubMenuItemDTO>> GetSubMenuItemsAsync(int structureId, string langCode, EnumSubMenuType subMenuType, int[] linkedIds);

        Task<string[]> GetProductColorChoicesAvalaibleAsync(int structureId);

        Task<string[]> GetProductSizeChoicesAvalaibleAsync(int structureId);
    }
}
