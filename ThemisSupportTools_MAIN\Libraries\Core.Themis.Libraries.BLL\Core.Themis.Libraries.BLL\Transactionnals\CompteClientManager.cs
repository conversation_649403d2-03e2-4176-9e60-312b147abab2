﻿using AutoMapper;
using Core.Themis.Libraries.BLL.Transactionnals.Interfaces;
using Core.Themis.Libraries.Data.Repositories.Open.Transactionnals.Interfaces;
using Core.Themis.Libraries.DTO.EventsSessions;
using Core.Themis.Libraries.DTO.Transactionnals;
using Fluid.Ast;
using System;
using System.Collections.Generic;
using Microsoft.Data.SqlClient;

namespace Core.Themis.Libraries.BLL.Transactionnals
{
    public class CompteClientManager : ICompteClientManager
    {
        private readonly ICompteClientRepository _compteClientRepository;
        private readonly IMapper _mapper;


        public CompteClientManager(ICompteClientRepository compteClientRepository, IMapper mapper)
        {
            _compteClientRepository = compteClientRepository;
            _mapper = mapper;
        }

        /// <summary>
        /// INSERT INTO Compte_Client2
        /// </summary>
        /// <param name="structureId"></param>
        /// <param name="posteId"></param>
        /// <param name="poste"></param>
        /// <param name="cc_operationId"></param>
        /// <returns></returns>
        public int CreateCompteClient2(SqlConnection cnxOpen, int structureId, int posteId, string poste, long cc_operationId)
        {
            try
            {
                return _compteClientRepository.CreateCompteClient2(cnxOpen, structureId, posteId, poste, cc_operationId);
            }
            catch
            {
                throw;
            }
        }

        /// <summary>
        /// INSERT INTO Compte_Client
        /// </summary>
        /// <param name="structureId"></param>
        /// <param name="cc"></param>
        /// <returns></returns>
        public int Create(SqlConnection cnxOpen, int structureId, CompteClientLineDTO cc)
        {
            try
            {
                return _compteClientRepository.Create(cnxOpen, structureId, cc);
            }
            catch
            {
                throw;
            }
        }

        public int CreateCompteTransaction(SqlConnection cnxOpen, int structureId, int OperatorId, long operation1_id, long operation2_id, string typeOperation)
        {
            try
            {
                return _compteClientRepository.CreateCompteTransaction(cnxOpen, structureId, OperatorId, operation1_id, operation2_id, typeOperation);
            }
            catch
            {
                throw;
            }
        }

        public List<CompteClientLineDTO> Get(int structureId, int identiteId =0, int orderId = 0)
        {
            try
            {
                if (identiteId > 0 && orderId > 0)
                {
                    var listCCE = _compteClientRepository.FindBy(cc => cc.IdentiteId == identiteId && cc.CommandeId == orderId, structureId);
                    var listCC = _mapper.Map<List<CompteClientLineDTO>>(listCCE);
                    return listCC;
                }
                else if (identiteId > 0)
                {
                    var listCCEi = _compteClientRepository.FindBy(cc => cc.IdentiteId == identiteId, structureId);
                    var listCCi = _mapper.Map<List<CompteClientLineDTO>>(listCCEi);
                    return listCCi;
                }
                else if (orderId > 0)
                {
                    var listCCEo = _compteClientRepository.FindBy(cc => cc.CommandeId == orderId, structureId);
                    var listCCo = _mapper.Map<List<CompteClientLineDTO>>(listCCEo);
                    return listCCo;
                }
                else
                {
                    Exception ex = new Exception("compteclientmanager: provide identiteid or orderid");
                    throw ex;
                }
            }
            catch
            {
                throw;
            }
        }

        /// <summary>
        /// update and get compteur cpt_transaction
        /// </summary>
        public long GetCompteurOperationId(int structureId)
        {
            try
            {
                return _compteClientRepository.GetCompteurOperationId(structureId);
            }
            catch
            {
                throw;
            }
        }

        /// <summary>
        /// update and get compteur cpt_paiement
        /// </summary>
        public int GetNumPaiement(int structureId)
        {
            try
            {
                return _compteClientRepository.GetNumPaiement(structureId);
            }
            catch
            {
                throw;
            }
        }
    }
}
