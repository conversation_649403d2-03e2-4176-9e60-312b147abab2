﻿using AutoMapper;
using Core.Themis.Libraries.BLL.Interfaces;
using Core.Themis.Libraries.BLL.WT.Interfaces;
using Core.Themis.Libraries.Data.Entities.Open.EventsSessions;
using Core.Themis.Libraries.Data.Entities.Open.Maquette;
using Core.Themis.Libraries.Data.Entities.Open.Order;
using Core.Themis.Libraries.Data.Repositories.Open.EventsSessions.Interfaces;
using Core.Themis.Libraries.Data.Repositories.Open.Identities.Interfaces;
using Core.Themis.Libraries.Data.Repositories.Open.Maquette.Interfaces;
using Core.Themis.Libraries.Data.Repositories.Open.Orders.Entree.Interfaces;
using Core.Themis.Libraries.Data.Repositories.Open.Orders.Interfaces;
using Core.Themis.Libraries.Data.Repositories.Open.Orders.OrderDetails;
using Core.Themis.Libraries.Data.Repositories.Open.Orders.OrderDetails.Interfaces;
using Core.Themis.Libraries.Data.Repositories.Open.Places.Interfaces;
using Core.Themis.Libraries.Data.Repositories.Open.Produit.Interfaces;
using Core.Themis.Libraries.Data.Repositories.Open.Structure.Interfaces;
using Core.Themis.Libraries.Data.Repositories.WebTracing.Interfaces;
using Core.Themis.Libraries.DTO;
using Core.Themis.Libraries.DTO.Enums;
using Core.Themis.Libraries.DTO.ExportEditionModel;
using Core.Themis.Libraries.DTO.Orders;
using Core.Themis.Libraries.DTO.Orders.Details;
using Core.Themis.Libraries.DTO.Products;
using Core.Themis.Libraries.DTO.Seats;
using Core.Themis.Libraries.Utilities.Extensions;
using Core.Themis.Libraries.Utilities.Helpers;
using Core.Themis.Libraries.Utilities.Helpers.Files;
using Core.Themis.Libraries.Utilities.Helpers.Interfaces;
using Core.Themis.Libraries.Utilities.Helpers.Wallets;
using Core.Themis.Libraries.Utilities.Logging;
using CoreHtmlToImage;
using Google.Apis.Walletobjects.v1.Data;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Passbook.Generator;
using PdfSharpCore.Drawing;
using PdfSharpCore.Fonts;
using PdfSharpCore.Pdf;
using PdfSharpCore.Pdf.Security;
using QRCoder.Exceptions;
using SixLabors.ImageSharp;
using SkiaSharp;
using System;
using System.Collections.Generic;
using System.Globalization;
using System.IO;
using System.Linq;
using System.Net.Http;
using System.Text.RegularExpressions;
using System.Threading;
using System.Threading.Tasks;

//using
using TheArtOfDev.HtmlRenderer.PdfSharp;
using DateTime = System.DateTime;

namespace Core.Themis.Libraries.BLL
{

    /// <summary>
    /// Classe principale de gestion du Print@Home (impression de billets à domicile).
    /// Permet la génération de fichiers PDF de billets, de tickets électroniques (Apple/Google/Samsung Wallet), 
    /// et la gestion des maquettes associées.
    /// </summary>
    public class PrintHomeManager : IPrintHomeManager
    {
        private readonly IMemoryCache _memoryCache;
        private readonly IConfiguration _config;
        private readonly IMapper _mapper;
        private readonly IRodrigueConfigIniDictionnary _rodrigueConfig;
        private readonly IBasketManager _basketManager;
        private readonly ISessionManager _sessionManager;
        private readonly IEventManager _eventManager;

        private readonly IMaquetteManager _maquetteManager;
        private readonly IOrderRepository _orderRepository;
        private readonly IOrderLineRepository _orderLineRepository;
        private readonly IStructureRepository _structureRepository;
        private readonly IEventRepository _eventRepository;
        private readonly ICategoryRepository _categRepository;

        private readonly IManifestationInfosRepository _eventInfosRepository;
        private readonly IManifestationImageRepository _eventImageRepository;

        private readonly IManifestationImagesWebRepository _eventImagesWebRepository;
        private readonly IImagesWebRepository _imagesWebRepository;


        private readonly ISessionRepository _sessionRepository;
        private readonly IPlaceRepository _placeRepository;
        private readonly IIdentityRepository _identityRepository;
        private readonly ISeatRepository _seatRepository;
        private readonly IProduitRepository _produitRepository;
        private readonly IMaquetteRepository _maquetteRepository;
        private readonly IRecetteRepository _recetteRepository;
        private readonly IBasketRepository _basketRepository;
        private readonly IEntreeRepository _entreeRepository;
        private readonly IGestionTraceManager _gestionTrace;


        private static readonly RodrigueNLogger Logger = new();

        /// <summary>
        /// Constructeur principal de la classe PrintHomeManager.
        /// </summary>
        /// <param name="memoryCache">Cache mémoire pour les images et données temporaires.</param>
        /// <param name="mapper">Mapper AutoMapper pour la conversion d'entités.</param>
        /// <param name="rodrigueConfig">Accès à la configuration Rodrigue (ini).</param>
        /// <param name="basketManager">Gestionnaire de panier.</param>
        /// <param name="sessionManager">Gestionnaire de séances.</param>
        /// <param name="eventsManager">Gestionnaire d'événements.</param>
        /// <param name="maquetteManager">Gestionnaire de maquettes.</param>
        /// <param name="config">Configuration de l'application.</param>
        /// <param name="structureRepository">Repository des structures.</param>
        /// <param name="eventRepository">Repository des événements.</param>
        /// <param name="orderRepository">Repository des commandes.</param>
        /// <param name="sessionRepository">Repository des séances.</param>
        /// <param name="categoryRepository">Repository des catégories.</param>
        /// <param name="placeRepository">Repository des lieux.</param>
        /// <param name="identityRepository">Repository des identités.</param>
        /// <param name="seatRepository">Repository des sièges.</param>
        /// <param name="produitRepository">Repository des produits.</param>
        /// <param name="maquetteRepository">Repository des maquettes.</param>
        /// <param name="recetteRepository">Repository des recettes.</param>
        /// <param name="basketRepository">Repository des paniers.</param>
        /// <param name="entreeRepository">Repository des entrées.</param>
        /// <param name="gestionTrace">Gestionnaire de logs et traces.</param>
        /// <param name="eventInfosRepository">Repository des infos de manifestation.</param>
        /// <param name="eventImageRepository">Repository des images de manifestation.</param>
        /// <param name="eventImagesWebRepository">Repository des images web de manifestation.</param>
        /// <param name="imagesRepository">Repository des images web.</param>

        public PrintHomeManager(
            IMemoryCache memoryCache,
            IMapper mapper,
            IRodrigueConfigIniDictionnary rodrigueConfig,
            IBasketManager basketManager,
            ISessionManager sessionManager,
            IEventManager eventsManager,
            IMaquetteManager maquetteManager,
            IConfiguration config,
            IStructureRepository structureRepository,
            IEventRepository eventRepository,
            IOrderRepository orderRepository,
            IOrderLineRepository orderLigneRepository,
            ISessionRepository sessionRepository,
            ICategoryRepository categoryRepository,

            IPlaceRepository placeRepository,
            IIdentityRepository identityRepository,
            ISeatRepository seatRepository,
            IProduitRepository produitRepository,
            IMaquetteRepository maquetteRepository,
            IRecetteRepository recetteRepository,
            IBasketRepository basketRepository,
            IEntreeRepository entreeRepository,
            IGestionTraceManager gestionTrace,


            IManifestationInfosRepository eventInfosRepository,
            IManifestationImageRepository eventImageRepository,

            IManifestationImagesWebRepository eventImagesWebRepository,
            IImagesWebRepository imagesRepository


            )
        {          
            _memoryCache = memoryCache;
            _mapper = mapper;
            _rodrigueConfig = rodrigueConfig;
            _config = config;

            _basketManager = basketManager;
            _sessionManager = sessionManager;
            _eventManager = eventsManager;
            _maquetteManager = maquetteManager;

            _orderRepository = orderRepository;
            _orderLineRepository = orderLigneRepository;


            _structureRepository = structureRepository;
            _eventRepository = eventRepository;
            _sessionRepository = sessionRepository;
            _categRepository = categoryRepository;
            _placeRepository = placeRepository;
            _identityRepository = identityRepository;
            _seatRepository = seatRepository;
            _produitRepository = produitRepository;
            _maquetteRepository = maquetteRepository;
            _recetteRepository = recetteRepository;
            _basketRepository = basketRepository;
            _entreeRepository = entreeRepository;
            _gestionTrace = gestionTrace;

            _eventInfosRepository = eventInfosRepository;
            _eventImageRepository = eventImageRepository;
            _imagesWebRepository = imagesRepository;
            _eventImagesWebRepository = eventImagesWebRepository;

        }

        #region Pdf





        /// <summary>
        /// cree le pdf web : la maquette coupe file est obtenue via les maquettes du panier
        /// </summary>
        /// <param name="structureId"></param>
        /// <param name="langCode"></param>
        /// <param name="orderId"></param>
        /// <returns>file path complet du pdf</returns>
        public string DoPdfWebThemis(int structureId, string langCode, int orderId, List<int>? lstDossierId, int paramId = 0)
        {
            try
            {
                string pathPdfSaved = _config["PathPdf"] ?? throw new ArgumentNullException("PathPdf n'existe pas dans la config");
                pathPdfSaved = pathPdfSaved.Replace("[structureId]", structureId.ToString("0000"));

                List<string> listPdfCrees = new();

                // Création du répertoire si nécessaire
                Directory.CreateDirectory(pathPdfSaved);

                string lstDossInName = lstDossierId?.Count > 0
                    ? string.Join("", lstDossierId)
                    : "doss";

                string typePdf = "webT";



                //string filename = $"{structureId}.{orderId}.{langCode}.{lstDossInName}.{paramId}.{typePdf}.{DateTime.Now.Ticks}.pdf";
                string invariantName = $"{structureId}.{orderId}.{langCode}.{lstDossInName}.{paramId}.webT.";
                string filename = $"{invariantName}{DateTime.Now.Ticks}.pdf";
                string pdfNameLike = $"{invariantName}*.pdf";

                #region verifier s'il n'y a pas déjà un fichier créé < 10mn

                //bool verifOldFile = false;
                //if (verifOldFile)
                //{
                //    string oldF = GetPreviousFileIfExists(structureId, pathPdfSaved, pdfNameLike);
                //    if (oldF != "")
                //        return oldF;
                //}

                #endregion

                PdfDocument pdfDoc = GenerateBilletPdf(structureId, langCode, orderId);

                if (pdfDoc == null || pdfDoc.PageCount == 0)
                {
                    throw new InvalidOperationException($"pdfDoc ({structureId},{langCode},{orderId}) est null ou vide");
                }

                // Sauvegarde du fichier
                string completePath = Path.Combine(pathPdfSaved, filename);
                pdfDoc.Save(completePath);

                return completePath;
            }
            catch
            {
                throw;
            }
        }

        /// <summary>
        /// cree le pdf web : la maquette coupe file est obtenue via les maquettes du panier
        /// </summary>
        /// <param name="structureId"></param>
        /// <param name="langCode"></param>
        /// <param name="orderId"></param>
        /// <returns>file path complet du pdf</returns>
        public string DoPdfWebThemis(int structureId, string langCode, int orderId, List<SeatDTO>? lstSeatDTO, List<DossierProductDTO>? listDossProduits)
        {
            try
            {
                string pathPdfSaved = _config["PathPdf"] ?? throw new ArgumentNullException("PathPdf n'existe pas dans la config");
                pathPdfSaved = pathPdfSaved.Replace("[structureId]", structureId.ToString("0000"));

                List<string> listPdfCrees = new();

                if (!Directory.Exists(pathPdfSaved))
                    Directory.CreateDirectory(pathPdfSaved);

                string lstDossInName = "seats";
                string typeF = "webT";

                if (lstSeatDTO != null && lstSeatDTO.Count > 0)
                {
                    foreach (var item in lstSeatDTO)
                    {
                        lstDossInName += item.SessionId + "." + item.SeatId;
                    }
                }
                List<ProductDTO> lstProds = [];
                if (listDossProduits != null && listDossProduits.Count > 0) // si des produits => passer la maquette à utiliser au pdf : à finir dans GenerateBilletPdf
                {
                    foreach (var item in listDossProduits)
                    {
                        lstProds.Add(new ProductDTO()
                        {
                            DossierProduitId = item.DossierId,
                            MaquetteId = item.Product.MaquetteId,
                        });
                    }
                }

                string invariantName = $"{structureId}.{orderId}.{langCode}.{lstDossInName}.{typeF}.";
                string filename = $"{invariantName}{DateTime.Now.Ticks}.pdf";
                string pdfNameLike = $"{invariantName}*.pdf";

                #region verifier s'il n'y a pas déjà un fichier créé < 10mn

                bool verifOldFile = true;
                if (verifOldFile)
                {
                    string oldF = GetPreviousFileIfExists(structureId, pathPdfSaved, pdfNameLike, 10);
                    if (oldF != "")
                        return oldF;
                }

                #endregion



                PdfDocument pdfDoc = GenerateBilletPdf(structureId, langCode, orderId, lstSeatDTO, lstProds);

                if (pdfDoc != null && pdfDoc.PageCount > 0)
                {
                    string completePath = Path.Combine(pathPdfSaved, filename);
                    pdfDoc.Save(completePath);
                    return completePath;
                }
                else
                {
                    Exception exception = new($"pdfDoc ({structureId},{langCode},{orderId}) is null");
                    throw exception;
                }
            }
            catch
            {
                throw;
            }
        }


        /// <summary>
        /// cree le pdf web : la maquette est obtenue via les maquettes du panier
        /// </summary>
        /// <param name="structureId"></param>
        /// <param name="langCode"></param>
        /// <param name="orderId"></param>
        /// <returns>byte[] du pdf</returns>
        public byte[] DoPdfWebThemis_GetBytes(int structureId, string langCode, int orderId, List<int>? lstDossierId, int paramId = 0)
        {
            try
            {
                string fileName = DoPdfWebThemis(structureId, langCode, orderId, lstDossierId);

                return File.ReadAllBytes(fileName);

                var stream = new FileStream(fileName, FileMode.Open);

                using MemoryStream ms = new();
                stream.CopyTo(ms);
                byte[] pdf = ms.ToArray();

                return pdf;

            }
            catch
            {
                throw;
            }
        }

        /// <summary>
        /// cree le pdf web, les seats sont précisés : la maquette est obtenue via les maquettes du panier
        /// </summary>
        /// <param name="structureId"></param>
        /// <param name="langCode"></param>
        /// <param name="orderId"></param>
        /// <returns>byte[] du pdf</returns>
        public byte[] DoPdfWebThemisSeats_GetBytes(int structureId, string langCode, int orderId, List<SeatDTO>? lstSeats, List<DossierProductDTO>? listDossProduits)
        {
            try
            {
                string fileName = DoPdfWebThemis(structureId, langCode, orderId, lstSeats, listDossProduits);

                return File.ReadAllBytes(fileName);

                //var stream = new FileStream(fileName, FileMode.Open);

                //using MemoryStream ms = new();
                //stream.CopyTo(ms);
                //byte[] pdf = ms.ToArray();
                //stream.Close();

                //return pdf;

            }
            catch (Exception ex)
            {
                Logger.Error(structureId, $"{ex.Message}\n{ex.StackTrace}");
                throw new Exception("error");
            }
        }

        /// <summary>
        /// cree le pdf Coupe File : la maquette coupe file est obtenue en sql 
        /// </summary>
        /// <returns></returns>
        public string DoPdfCoupeFile(int structureId, string langCode, int orderId, List<int> lstDossierId)
        {
            try
            {

                var pathPdfSaved = _config["PathPdfSkipQueue"];
                var pathImagesLogo = _config["PathImagesLogos"];

                if (pathPdfSaved is null)
                    throw new NullReferenceException(nameof(pathPdfSaved));

                if (pathImagesLogo is null)
                    throw new NullReferenceException(nameof(pathImagesLogo));

                pathPdfSaved = pathPdfSaved.Replace("[structureId]", structureId.ToString("0000"));
                pathImagesLogo = pathImagesLogo.Replace("[structureId]", structureId.ToString("0000"));

                List<string> listPdfCrees = new();

                if (!Directory.Exists(pathPdfSaved))
                    Directory.CreateDirectory(pathPdfSaved);

                string lstDossInName = "doss";

                if (lstDossierId != null && lstDossierId.Count > 0)
                    lstDossInName = string.Join("", lstDossierId);

                string typeF = "skipqueue";

                string invariantName = $"{structureId}.{orderId}.{langCode}.{lstDossInName}.{typeF}.";
                string filename = $"{invariantName}{DateTime.Now.Ticks}.pdf";
                string pdfNameLike = $"{invariantName}*.pdf";

                #region verifier s'il n'y a pas déjà un fichier créé < 10mn

                bool verifOldFile = false;
                if (verifOldFile)
                {
                    string oldF = GetPreviousFileIfExists(structureId, pathPdfSaved, pdfNameLike);
                    if (oldF != "")
                        return oldF;

                }
                #endregion

                OrderDTO order = _orderRepository.Load(structureId, orderId, 0);

                int maqCoupeFile = _maquetteRepository.GetMaquetteCoupeFileId(structureId);

                if (maqCoupeFile == 0)
                {
                    Exception ex = new Exception("maquette coupe file not found !");
                    throw ex;
                }

                foreach (var d in order.ListDossiersSeats.Where(d => d.State == "B").ToList())
                {
                    foreach (var s in d.ListSeats)
                    {
                        s.MaquetteId = maqCoupeFile;
                    }
                }

                PdfDocument pdfDoc = new PdfDocument();

                if (pdfDoc.PageCount > 0)
                {

                    string completePath = Path.Combine(pathPdfSaved, filename);
                    pdfDoc.Save(completePath);

                    return completePath;
                }
                else
                {
                    Exception ex = new Exception("there is no page in this pdf !");
                    throw ex;
                }
            }
            catch (Exception ex)
            {
                throw ex;
            }
        }



        // Méthode pour préparer les données du dictionnaire pour le PDF
        private DictionaryExtented PrepareDictionnaryForPdf(int structureId, string langCode, OrderDTO order, int billetNombre)
        {
            var now = DateTime.Now;
            var dico = new DictionaryExtented
            {
                {"COMMANDE_NUMERO", order.OrderId.ToString() },
                {"DOSSIER_MONTANT", string.Empty },
                {"DOSSIER_VALEUR", string.Empty },
                {"EDITION_DATE", $"{now.ToShortDateString()} {now.ToString("T", DateTimeFormatInfo.InvariantInfo)}" },
                {"EDITION_TIME", now.ToShortTimeString() },
                {"DOSSIER_SEANCE_DATE_*", now.AddDays(30).ToShortDateString() },
                {"SEANCE_DATE_VALIDITE", now.AddDays(30).ToShortDateString() },
                {"EDITION_OPERATEUR", "THEMIS" },
                {"BILLET_NOMBRE", billetNombre.ToString() }
            };

            dico += _structureRepository.GetDictionnaryForPdf(structureId, langCode);

            return dico;
        }
        private int CountBillets(OrderDTO order, List<SeatDTO> listSeatsToWrite, List<ProductDTO> listProductsToWrite)
        {
            // Comptage des sièges
            int seatsCount = order.ListDossiersSeats
                .SelectMany(d => d.ListSeats)
                .Count(s => ShouldWriteThisSeat(s, listSeatsToWrite, listProductsToWrite));

            // Comptage des produits
            int productsCount = order.ListDossiersProduct
                .Count(p => p.State == "B" && ShouldWriteThisProduct(p, listSeatsToWrite, listProductsToWrite));

            return seatsCount + productsCount;
        }
        private bool ShouldWriteThisSeat(SeatDTO seat, List<SeatDTO> listSeatsToWrite, List<ProductDTO> listProductsToWrite)
        {
            // Si la liste est vide ou nulle, on écrit toujours le siège
            if (listSeatsToWrite is null || listSeatsToWrite.Count == 0)
                return true;

            // Sinon, on vérifie la présence du siège avec les critères donnés
            return listSeatsToWrite.Any(se =>
                se.SeatId == seat.SeatId &&
                (se.SessionId == seat.SessionId || se.EventId == seat.EventId)
            );
        }

        // Méthode pour déterminer si un produit doit être écrit
        private bool ShouldWriteThisProduct(DossierProductDTO dos, List<SeatDTO> listSeatsToWrite, List<ProductDTO> listProductsToWrite)
        {

            // Si la liste est vide ou nulle, on écrit toujours le produit
            if (listProductsToWrite is null || listProductsToWrite.Count == 0)
                return true;

            // Sinon, on vérifie la présence du produit dans la liste
            return listProductsToWrite.Any(se => se.ProductId == dos.Product.ProductId);
        }


        /// <summary>
        /// retrouve s'il y a un fichier créé avec ce nom il y a moins de 10 mn
        /// </summary>
        /// <param name="structureId"></param>
        /// <param name="pathPdfSaved"></param>
        /// <param name="pdFNameLike"></param>
        /// <returns></returns>

        private string GetPreviousFileIfExists(int structureId,
            string pathPdfSaved,
            string pdfNameLike, int depuisQuand = 10)
        {

            if (!Directory.Exists(pathPdfSaved)) return "";

            var directory = new DirectoryInfo(pathPdfSaved);

            // Filtrer d'abord les fichiers correspondant au motif et créés dans les 10 dernières minutes
            var recentFile = directory.EnumerateFiles(pdfNameLike)
                .Where(f => DateTimeOffset.UtcNow - f.CreationTimeUtc < TimeSpan.FromMinutes(depuisQuand))
                .OrderByDescending(f => f.CreationTimeUtc)
                .FirstOrDefault();

            return recentFile != null
                ? Path.Combine(pathPdfSaved, recentFile.Name)
                : "";
        }

        /// <summary>
        /// renseigne les maquettes id des sieges where maquette=0 à la manière de GetMaquette() en fichier sql
        /// </summary>
        /// <param name="structureId"></param>
        /// <param name="order"></param>
        public void FillMaquettesSeats(int structureId, OrderDTO order)
        {
            foreach (var d in order.ListDossiersSeats)
            {
                // Sélectionne d'abord tous les sièges sans MaquetteId
                var seatsWithoutMaquette = d.ListSeats.Where(s => s.MaquetteId == 0).ToList();
                if (seatsWithoutMaquette.Count > 0)
                {
                    int maqId = _maquetteRepository.GetMaquetteDefaultId(structureId, d.Session.SessionId, d.DossierId, 0);
                    // Attribue le MaquetteId à tous les sièges concernés
                    foreach (var s in seatsWithoutMaquette)
                        s.MaquetteId = maqId;
                }

            }
        }

        /// <summary>
        /// get la maquette à partir de la commande
        /// </summary>
        /// <param name="ticket"></param>
        /// <param name="order"></param>
        /// <returns></returns>
        private int? fillMaquetteFromPanier(PdfTicketModel ticket, OrderDTO order)
        {
            var maqId = order.listAllSeats
                .FirstOrDefault(s => s.EventId == ticket.EventId && s.SeatId == ticket.SeatId)
                ?.MaquetteId;

            return (maqId > 0) ? maqId : null;
        }


        public Task<byte[]> GenerateBATproductsAsync(int structureId, string langCode, int productId, int maquetteId)
        {
            return Task.Run(() =>
            {
                //PdfTicketModelBAT[] ticketList = Array.Empty<PdfTicketModelBAT>();
                List<PdfTicketModelBAT> ticketList = new List<PdfTicketModelBAT>();


            ticketList.Add(new PdfTicketModelBAT()
            {
                ProductId = productId,
                OrderId = Random.Shared.Next(100000, 900000), // Simule un order ID pour le BAT
                NumTicket = $"BAT-{productId}",
                OperationDate = DateTime.Now,
                Barcode = new Random().NextInt64(1_000_000_000L, 10_000_000_000L).ToString(),
                MaquetteId = maquetteId, // Simulate a maquette ID for the BAT
            });

            var batDoc = GenerateTicketPdfDocument(structureId, langCode, true, ticketList.ToArray());

            return ConvertPdfDocumentToByteArray(batDoc);
            });
        }



        public Task<byte[]> GenerateBATseatsAsync(int structureId, string langCode, int eventId, int[] listSessionId, int[] listCategorieId, int[] listTypeTarifId, int maquetteId)
        {

            var random = Random.Shared;
            return Task.Run(() =>
            {
                List<PdfTicketModelBAT> ticketList = new List<PdfTicketModelBAT>();

            foreach (var sessionId in listSessionId)
            {
                foreach (var categorieId in listCategorieId)
                {
                    foreach (var typeTarifId in listTypeTarifId)
                    {
                        long barcode = random.NextInt64(1_000_000_000L, 10_000_000_000L);
                        int orderId = random.Next(10000, 1000000); // Simulate an order ID for the BAT
                        int numbillet = random.Next(10000, 100000); // Simulate a ticket number for the BAT
                        PdfTicketModelBAT pdfTicket = new PdfTicketModelBAT()
                        {
                            //StructureId = structureId,
                            //   LangCode = langCode,
                            EventId = eventId,
                            SessionId = sessionId,
                            OrderId = orderId,
                            NumTicket = numbillet.ToString(),
                            OperationDate = DateTime.Now,
                            //CategorieId = categorieId,
                            //TypeTarifId = typeTarifId,
                            Barcode = barcode.ToString(),
                            MaquetteId = maquetteId,
                            CategId = categorieId,
                            PriceId = typeTarifId,

                        };
                        ticketList.Add(pdfTicket);
                    }
                }
            }

            Logger.Info(structureId, $"Generated {ticketList.Count} tickets for BAT with maquette {maquetteId}.");

            var batDoc = GenerateTicketPdfDocument(structureId, langCode, true, ticketList.ToArray());

            Logger.Info(structureId, $"Generated BAT PDF document with {batDoc.PageCount} pages.");

            return ConvertPdfDocumentToByteArray(batDoc);

            });

        }


        /// <summary>
        /// methode 1, creation du pdf document, order et seatsDto en parametres => !! do edition des places
        /// </summary>
        /// <param name="structureId"></param>
        /// <param name="langCode"></param>
        /// <param name="orderId"></param>
        /// <returns></returns>
        public PdfDocument GenerateBilletPdf(int structureId, string langCode, int orderId,
            List<SeatDTO> listSeatsToWrite = null, List<ProductDTO> listProductsToWrite = null)
        {
            BasketDTO bask = _basketRepository.GetBasketInfoByOrderId(structureId, orderId);

            bool LoadFontsPerso = false;
            var DicoConfigIni = _rodrigueConfig.GetDictionaryFromCache(structureId); // font perso dans le config.ini : ca pete les paragraphe !!!

            //string UseFontPersoKey = "PARAMPDFUSEFONTPERSO";
            //if (DicoConfigIni != null && DicoConfigIni.TryGetValue(UseFontPersoKey, out string sloadFontPerso))
            //{
            //    try
            //    {
            //        LoadFontsPerso = Convert.ToBoolean(int.Parse(sloadFontPerso));
            //        if (LoadFontsPerso)
            //        {
            //            EZFontResolver fontResolver = EZFontResolver.Get;
            //            LoadAllFonts(fontResolver);
            //        }
            //    }
            //    catch { }
            //}



            try
            {
                var culture = CultureInfo.GetCultureInfo(langCode); // Valide l'existence de la culture
                Thread.CurrentThread.CurrentUICulture = culture;
                Thread.CurrentThread.CurrentCulture = CultureInfo.CreateSpecificCulture(langCode);
            }
            catch (CultureNotFoundException)
            {
                // Fallback sur une culture par défaut (ex: "en-US")
                var defaultCulture = CultureInfo.GetCultureInfo("en-GB");
                Thread.CurrentThread.CurrentUICulture = defaultCulture;
                Thread.CurrentThread.CurrentCulture = defaultCulture;

                // Loguer l'erreur si nécessaire

            }



            EZFontResolver fontResolver = EZFontResolver.Get;
            LoadAllFonts(structureId, fontResolver);

            //var DicoConfigIni = _rodrigueConfig.GetDictionaryFromCache(structureId);

            int operatorId = 0;
            operatorId = OperatorManager.GetOperateurPaiement(DicoConfigIni);

            _orderRepository.DoEtapeEditionPartielle(structureId, operatorId, orderId, listSeatsToWrite, listProductsToWrite);

            var order = _orderRepository.GetOrderInfoForEdition(structureId, orderId)
                ?? throw new ArgumentNullException("order is null");

            if (order.OrderId != orderId)
            {
                throw new Exception($"order {orderId} is not found");
            }

            FillMaquettesSeats(structureId, order);

            PdfDocument document = new();
            SetSecuritySetting(document);

            string pathImagesLogo = _config["PathImagesLogos"]!;
            pathImagesLogo = pathImagesLogo.Replace("[structureId]", structureId.ToString("0000"));


            List<MaquetteBillet> listMaquetteChargee = new();
            Dictionary<string, XImage> dicImagesPathImageId = new();

            int billetNombre = CountBillets(order, listSeatsToWrite, listProductsToWrite);
            // Dictionnaire des données pour le PDF
            DictionaryExtented dico = PrepareDictionnaryForPdf(structureId, langCode, order, billetNombre);

            int i = 0;
            int currentY = 0;

            #region List seats

            bool FirstPage = true;

            PdfPage page = new();

            var allMaquettesEntities = _maquetteRepository.GetAll(structureId);

            List<string> listCodesBarresWrited = new();



            foreach (var dos in order.ListDossiersSeats)
            {
                PdfOutline outline;

                dico += _eventRepository.GetDictionnaryForPdf(structureId, langCode, dos.Event.EventId);
                dico += _sessionRepository.GetDictionnaryForPdf(structureId, langCode, order.IdentityId, order.OrderId, dos.Session.SessionId);
                dico += _placeRepository.GetDictionnaryForPdf(structureId, langCode, dos.Event.EventId, dos.Session.SessionId);

                foreach (var s in dos.ListSeats)
                {
                    bool writeThisSeat = ShouldWriteThisSeat(s, listSeatsToWrite, listProductsToWrite);

                    if (writeThisSeat)
                    {
                        var maquetteId = s.MaquetteId;

                        var matchingSeat = listSeatsToWrite?.FirstOrDefault(se =>
                                se.SeatId == s.SeatId && (se.SessionId == s.SessionId || se.EventId == s.EventId));

                        if (matchingSeat?.MaquetteId > 0) // la maquette a été forcée (pdf depuis Rodrigue)
                        {
                            maquetteId = matchingSeat.MaquetteId;
                        }

                        if (maquetteId == 0)
                        {
                            maquetteId = _maquetteRepository.GetMaquetteDefaultId(structureId, s.SessionId, s.DossierId, s.SeatId);
                            if (maquetteId == 0)
                            {
                                // on peut plus rien faire au bout d'un moment là
                                throw new ArgumentOutOfRangeException($"{nameof(maquetteId)} du siège est défini à 0");
                            }
                        }

                        maquetteId = switchMaquetteByLang(allMaquettesEntities, maquetteId, langCode).MaquetteBilletId;

                        MaquetteBillet? maqFound = listMaquetteChargee.Where(m => m.MaquetteId == maquetteId).FirstOrDefault();

                        if (maqFound == null)
                        {
                            MaquetteBillet maqLoad = _maquetteRepository.Load(structureId, maquetteId, "WIN");
                            listMaquetteChargee.Add(maqLoad);
                        }


                        MaquetteBillet maq = listMaquetteChargee.Where(m => m.MaquetteId == maquetteId).FirstOrDefault();


                        if (maq.TypeMaquette.ToUpper() == "CARTEWEB" && listCodesBarresWrited.Contains(s.BarCode))
                        {
                            writeThisSeat = false; // carteWeb et on a déjà imprimé ce code barre, zapp ce siege
                        }
                        else
                        {
                            RecetteEntity? myRecetteEntity = _recetteRepository.GetSeatRecetteByCriteria(structureId, dos.Event.EventId, dos.Session.SessionId, dos.DossierId, s.SeatId);


                            if (myRecetteEntity != null)
                            {
                                RecetteLineDTO myRecetteLine = _mapper.Map<RecetteLineDTO>(myRecetteEntity);

                                i++;

                                dico.AjouterValeurBillet(dico, "BILLET_POSITION", $"{i}");
                                dico.AjouterValeurBillet(dico, "BILLET_NUMERO", myRecetteLine.NumBillet);
                                dico.AjouterValeurBillet(dico, "EDITION_DATE", myRecetteLine.DateOperation.ToShortDateString() + " " + myRecetteLine.DateOperation.ToString("T", DateTimeFormatInfo.InvariantInfo));
                                dico.AjouterValeurBillet(dico, "EDITION_TIME", myRecetteLine.DateOperation.ToShortTimeString());
                                dico.AjouterValeurBillet(dico, "CRYPT_BILLET_NUMERO", myRecetteLine.CodeBarre);
                                dico.AjouterValeurBillet(dico, "WEB_TICKET_NUMBER", myRecetteLine.CodeBarre);

                                dico += _identityRepository.GetDictionnaryForPdf(structureId, langCode, order.IdentityId, order.OrderId, dos.Event.EventId, dos.Session.SessionId, dos.DossierId, s.SeatId);
                                dico += _seatRepository.GetDictionnaryForPdf(structureId, langCode, order.IdentityId, order.OrderId, dos.Event.EventId, dos.Session.SessionId, dos.DossierId, s.SeatId);

                                int billet_taille_y = maq.Height;

                                if (currentY + billet_taille_y > page.Height.Millimeter)
                                {
                                    // nextPage
                                    page.Size = PdfSharpCore.PageSize.A4;
                                    page = document.AddPage();

                                    currentY = 0;
                                }
                                else if (document.PageCount > 0)
                                {
                                    //trace la fin de billet
                                    var x = XUnit.FromMillimeter(10);
                                    var y = XUnit.FromMillimeter(currentY);
                                    var w = XUnit.FromMillimeter(page.Width.Millimeter - 20);
                                    double lineWidth = 1;

                                    XGraphics gfx2 = XGraphics.FromPdfPage(page);

                                    for (int ii = 0; ii < 14; ii++)
                                    {
                                        int ix = 10 + ii * 40;
                                        int fx = ix + 30;
                                        gfx2.DrawLine(new XPen(XColor.FromKnownColor(XKnownColor.DarkGray)), ix, y, fx, y);
                                    }

                                    gfx2.Dispose();

                                }

                                if (FirstPage)
                                {
                                    currentY = 0;
                                    page = document.AddPage();
                                    //outline = document.Outlines.Add("tickets", page, true, PdfOutlineStyle.Bold, XColors.Red);
                                    FirstPage = false;
                                }

                                XGraphics gfx = XGraphics.FromPdfPage(page);

                                //outline = document.Outlines.Add("tick " + myRecetteLine.CodeBarre, page, true);
                                try
                                {
                                    DrawImagesEtLogos(maq.ListImagesLogo, gfx, currentY, pathImagesLogo, dicImagesPathImageId);

                                    //var imagesweb = _imagesWebRepository.GetAll(structureId);
                                    //var imageswebManif = _eventImagesWebRepository.GetAll(structureId);
                                    // DrawImageWeb(maq.ListImagesWeb, gfx, currentY, dos.Event.EventId, imagesweb, imageswebManif, dicImagesPathImageId);
                                    DrawAndGetImagesWeb(structureId, maq.MaquetteId, maq.ListImagesWeb, gfx, currentY, dos.Event.EventId, dicImagesPathImageId);

                                    //DrawImageWeb(maq.ListImagesWeb, gfx,  currentY, dos.Event.EventId, dicImagesPathImageId);

                                    if (maq.ListVignettesEvent.Count > 0)
                                    {
                                        var evts = _eventManager.GetImagesAsync(structureId, dos.Event.EventId).Result;
                                        var Image1 = evts.FirstOrDefault().Images.Image1;
                                        var Image2 = evts.FirstOrDefault().Images.Image2;

                                        try
                                        {
                                            DrawVignette(maq.ListVignettesEvent, gfx, currentY, dicImagesPathImageId, dos.Event.EventId,
                                                Convert.FromBase64String(Image1), Convert.FromBase64String(Image2));
                                        }
                                        catch (Exception ex)
                                        {
                                            Logger.Warn(structureId, $"draw vignette error ! {maq.MaquetteId}, event {dos.Event.EventId}\n{ex.Message}\n{ex.StackTrace}");
                                        }
                                    }

                                    DrawBarsCodes(maq.ListBarCodes, gfx, dico, currentY);
                                    DrawQRCodes(maq.ListQrCodes, gfx, dico, currentY);
                                    DrawTexts(structureId, maq.ListTexts, gfx, dico, currentY);
                                    DrawParagraphs(structureId, maq.ListParagraphes, gfx, currentY);
                                    DrawLines(maq.ListLines, gfx, currentY);

                                    if (_config["TypeRun"] != "PROD")
                                        DrawWatermark(gfx, billet_taille_y, currentY, $"p@h bil {langCode}, {maq.MaquetteName} ({maq.MaquetteId})");

                                    listCodesBarresWrited.Add(s.BarCode);

                                }
                                catch (DataTooLongException ex)
                                {

                                    string errorMessage = $"La version et la correction dans Rodrigue du QrCode sont incompatibles  \n La taille de chaine est trop grande.";

                                    _gestionTrace.WriteLogErrorMessage(structureId, bask.WebUser.UserId, errorMessage);
                                    _gestionTrace.WriteLogErrorMessage(structureId, bask.WebUser.UserId, $"Generation du PDF : {ex.Message}");
                                    _gestionTrace.WriteLogErrorMessage(structureId, bask.WebUser.UserId, $"Generation du PDF : {ex.StackTrace}");
                                    throw;
                                }
                                catch (Exception ex)
                                {

                                    _gestionTrace.WriteLogErrorMessage(structureId, bask.WebUser.UserId, $"Generation du PDF : {ex.Message}");
                                    _gestionTrace.WriteLogErrorMessage(structureId, bask.WebUser.UserId, $"Generation du PDF : {ex.StackTrace}");
                                    throw;
                                }

                                gfx.Dispose();

                                currentY += billet_taille_y;
                            }
                        }
                    }
                }
            }

            #endregion

            #region List products


            List<int> ListDossProdTraites = new();
            foreach (var dos in order.ListDossiersProduct.Where(p => p.State == "B"))
            {
                bool writeThisProduct = ShouldWriteThisProduct(dos, listSeatsToWrite, listProductsToWrite);

                if (writeThisProduct)
                {

                    page = new();
                    PdfOutline outline = new();

                    int maquetteId = dos.Product.MaquetteId;


                    maquetteId = switchMaquetteByLang(allMaquettesEntities, maquetteId, langCode).MaquetteBilletId;


                    if (maquetteId > 0)
                    {
                        MaquetteBillet maqFound = listMaquetteChargee.Where(m => m.MaquetteId == maquetteId).FirstOrDefault();

                        if (maqFound == null)
                        {
                            MaquetteBillet maqLoad = _maquetteRepository.Load(structureId, maquetteId, "WIN");
                            listMaquetteChargee.Add(maqLoad);
                        }

                        MaquetteBillet maq = listMaquetteChargee.Where(m => m.MaquetteId == maquetteId).FirstOrDefault();

                        int billet_taille_y = maq.Height;

                        RecetteLineDTO? myRecetteLigne = _recetteRepository.LoadRecettesProducts(structureId, "", order.OrderId, dos.EventIdLinked,
                            dos.SessionIdLinked, dos.DossierId, dos.Product.ProductId, 0, new List<int>() { 0 }).FirstOrDefault();

                        int nbrPageForThisDossier = 1;
                        if (dos.Product.Pdf_OneTicketParProduit)
                        {
                            nbrPageForThisDossier = dos.Count;
                        }
                        for (int p = 0; p < nbrPageForThisDossier; p++)
                        {
                            i++;

                            if (currentY + billet_taille_y > page.Height.Millimeter)
                            {
                                // nextPage
                                page = document.AddPage();
                                page.Size = PdfSharpCore.PageSize.A4;
                                currentY = 0;
                            }
                            else
                            {
                                if (!FirstPage)
                                {
                                    //trace la fin de billet
                                    var x = XUnit.FromMillimeter(10);
                                    var y = XUnit.FromMillimeter(currentY);
                                    var w = XUnit.FromMillimeter(page.Width.Millimeter - 20);
                                    double lineWidth = 1;

                                    XGraphics gfx2 = XGraphics.FromPdfPage(page);

                                    for (int ii = 0; ii < 14; ii++)
                                    {
                                        int ix = 10 + ii * 40;
                                        int fx = ix + 30;
                                        gfx2.DrawLine(new XPen(XColor.FromKnownColor(XKnownColor.DarkGray)), ix, y, fx, y);
                                    }

                                    gfx2.Dispose();
                                }
                            }

                            if (FirstPage)
                            {
                                currentY = 0;
                                page = document.AddPage();
                                //outline = document.Outlines.Add("tickets", page, true, PdfOutlineStyle.Bold, XColors.Red);
                                FirstPage = false;
                            }


                            dico += _identityRepository.GetDictionnaryForPdf(structureId, langCode, order.IdentityId, order.OrderId, dos.EventIdLinked, dos.SessionIdLinked, dos.DossierId, 0);
                            
                          

                            dico.AjouterValeurBillet(dico, "PRODUIT_NOMBRE", dos.Count);
                            dico.AjouterValeurBillet(dico, "BILLET_POSITION", i.ToString());
                        //https://localhost:44354/Edition/GetTicketPdfFileByOrderWithHash/85/145925/fr?hash=a2tNNW83UWJEbEk4OUhCdUJudlFWRHFjeVVySkhmL1lIYzI2bjJucHNZMD0=&queryHash=SVNlMUhJd0FxYURnb2tUZVJDZWpQYUptZU1sbUdLb0h3NEVVL1VaSDZ2eXp1d2UvRHpXaFdBOVcxYkZ5MTBYWStVU1dMMW8vQWN0Q2FJNmlnbS9OTXVRbzJtQmxsOS9pYklLTFlLa3d3dUFxUjJFUlk4cFdnZEVOdHU1cFNIOUI=&orderHash=wHb6rtCojjsL5H10hKrJoEYQHHIoJtp%2f5Qbl%2fbefN3I%3d
                            if (myRecetteLigne is not null)
                            {
                                dico += _produitRepository.GetDictionnaryForPdf(structureId, langCode, order.OrderId, dos.Product.ProductId, myRecetteLigne.RecetteId, dos.DossierId);
                                dico.AjouterValeurBillet(dico, "BILLET_NUMERO", myRecetteLigne.NumBillet);
                                dico.AjouterValeurBillet(dico, "CRYPT_BILLET_NUMERO", myRecetteLigne.CodeBarre);
                                dico.AjouterValeurBillet(dico, "WEB_TICKET_NUMBER", myRecetteLigne.CodeBarre);
                            }
                            else
                            {
                                dico += _produitRepository.GetDictionnaryForPdf(structureId, langCode, order.OrderId, dos.Product.ProductId, myRecetteLigne.RecetteId, dos.DossierId);
                            }

                            if (dos.EventIdLinked > 0)
                                dico += _eventRepository.GetDictionnaryForPdf(structureId, langCode, dos.EventIdLinked);

                            if (dos.SessionIdLinked > 0)
                                dico += _sessionRepository.GetDictionnaryForPdf(structureId, langCode, order.IdentityId, order.OrderId, dos.SessionIdLinked);

                            XGraphics gfx = XGraphics.FromPdfPage(page);

                            //outline = document.Outlines.Add("prod " + i, page, true);

                            DrawImagesEtLogos(maq.ListImagesLogo, gfx, currentY, pathImagesLogo, dicImagesPathImageId);
                            DrawLines(maq.ListLines, gfx, currentY);
                            DrawTexts(structureId, maq.ListTexts, gfx, dico, currentY);
                            DrawParagraphs(structureId, maq.ListParagraphes, gfx, currentY);
                            DrawBarsCodes(maq.ListBarCodes, gfx, dico, currentY);
                            DrawQRCodes(maq.ListQrCodes, gfx, dico, currentY);

                            if (_config["TypeRun"] != "PROD")
                                DrawWatermark(gfx, billet_taille_y, currentY, $"p@h prod {langCode} {maq.MaquetteName} ({maq.MaquetteId})");

                            gfx.Dispose();

                            currentY += billet_taille_y;
                        }
                    }
                }

            }
            #endregion
            return document;
        }






        /// <summary>
        /// methode 2 : tickets en parametres, pas d'edition !!
        /// </summary>
        /// <param name="structureId"></param>
        /// <param name="langCode"></param>
        /// <param name="tickets"></param>
        /// <returns></returns>
        /// <exception cref="ArgumentOutOfRangeException"></exception>
        /// <exception cref="Exception"></exception>
        private PdfDocument GenerateTicketPdfDocument(int structureId, string langCode, bool isBat = false, params PdfTicketModel[] tickets)
        {
            Logger.Info(structureId, $"GenerateTicketPdfDocument for {tickets.Length} tickets with lang {langCode}.");

            if (tickets.Length == 0)
            {
                Logger.Error(structureId, "No tickets provided for PDF generation.");
                throw new Exception("tickets count =0");
            }


            PdfDocument document = new();
            SetSecuritySetting(document);

            string pathImagesLogo = _config["PathImagesLogos"]!;
            pathImagesLogo = pathImagesLogo.Replace("[structureId]", $"{structureId:0000}");

            bool FirstPage = true;

            EZFontResolver fontResolver = EZFontResolver.Get;
            //GlobalFontSettings.FontResolver = fontResolver;

            LoadAllFonts(structureId, fontResolver);

            List<MaquetteBillet> listMaquetteChargee = new();
            Dictionary<string, XImage> dicImagesPathImageId = new();
            DictionaryExtented dico = _structureRepository.GetDictionnaryForPdf(structureId, langCode);

            int currentY = 0;

            
            PdfPage page = new();
            //PdfOutline outline;


            var allMaquettesEntities = _maquetteRepository.GetAll(structureId);

            foreach (var (ticket, i) in tickets.Select((t, i) => (t, i + 1)))
            {
                /// remplir dico à partir du ticket

                SetTicketInfoDico(structureId, langCode, dico, ticket, tickets.Length, 0, i, isBat);


                if (ticket.MaquetteId == 0)
                    throw new ArgumentOutOfRangeException($"{nameof(ticket.MaquetteId)} du siège est défini à 0");

                ticket.MaquetteId = switchMaquetteByLang(allMaquettesEntities, ticket.MaquetteId, langCode).MaquetteBilletId;


                MaquetteBillet? maqFound = listMaquetteChargee.Where(m => m.MaquetteId == ticket.MaquetteId).FirstOrDefault();

                if (maqFound == null)
                {
                    MaquetteBillet maqLoad = _maquetteRepository.Load(structureId, ticket.MaquetteId, "WIN");
                    listMaquetteChargee.Add(maqLoad);
                }

                MaquetteBillet maq = listMaquetteChargee.Where(m => m.MaquetteId == ticket.MaquetteId).First();

                int billet_taille_y = maq.Height;

                if (currentY + billet_taille_y > page.Height.Millimeter)
                {
                    // nextPage
                    page.Size = PdfSharpCore.PageSize.A4;
                    page = document.AddPage();

                    currentY = 0;
                }
                else
                {
                    if (document.PageCount > 0)
                    {
                        //trace la fin de billet
                        var x = XUnit.FromMillimeter(10);
                        var y = XUnit.FromMillimeter(currentY);
                        var w = XUnit.FromMillimeter(page.Width.Millimeter - 20);
                        double lineWidth = 1;

                        XGraphics gfx2 = XGraphics.FromPdfPage(page);

                        for (int ii = 0; ii < 14; ii++)
                        {
                            int ix = 10 + ii * 40;
                            int fx = ix + 30;
                            gfx2.DrawLine(new XPen(XColor.FromKnownColor(XKnownColor.DarkGray)), ix, y, fx, y);
                        }

                        gfx2.Dispose();
                    }
                }

                if (FirstPage)
                {
                    currentY = 0;
                    page = document.AddPage();
                    //outline = document.Outlines.Add("tickets", page, true, PdfOutlineStyle.Bold, XColors.Red);
                    FirstPage = false;
                }

                XGraphics gfx = XGraphics.FromPdfPage(page);

                //outline = document.Outlines.Add("tick " + ticket.Barcode, page, true);

                try
                {
                    if (!isBat && _config["TypeRun"] != "PROD")
                    {
                        // Si l'environnement n'est pas la production, on ajoute un filigrane "p@h doc ..."        
                        DrawWatermark(gfx, billet_taille_y, currentY, $"p@h doc {langCode}, {maq.MaquetteName} ({maq.MaquetteId})");
                    }
                    else
                    {
                        if (isBat)
                        {
                            // Si c'est un BAT, on ajoute un filigrane spécifique "Printer's proof ..."
                            DrawWatermarkBAT(gfx, billet_taille_y, currentY, $"Printer's proof, {langCode}, {maq.MaquetteName} ({maq.MaquetteId})");
                        }                                                      
                    }


                    DrawImagesEtLogos(maq.ListImagesLogo, gfx, currentY, pathImagesLogo, dicImagesPathImageId);

                    if (maq.ListImagesWeb.Count > 0)
                    {
                        // fill urls :
                        DrawAndGetImagesWeb(structureId, maq.MaquetteId, maq.ListImagesWeb, gfx, currentY, ticket.EventId, dicImagesPathImageId);
                    }
                    if (maq.ListVignettesEvent.Count > 0)
                    {

                        var evts = _eventManager.GetImagesAsync(structureId, ticket.EventId).Result;
                        var Image1 = evts.FirstOrDefault().Images.Image1;
                        var Image2 = evts.FirstOrDefault().Images.Image2;

                        try
                        {
                            DrawVignette(maq.ListVignettesEvent, gfx, currentY, dicImagesPathImageId, ticket.EventId,
                                Convert.FromBase64String(Image1), Convert.FromBase64String(Image2));
                        }
                        catch (Exception ex)
                        {
                            Logger.Warn(structureId, $"draw vignette error ! {maq.MaquetteId}, event {ticket.EventId}\n{ex.Message}\n{ex.StackTrace}");
                        }
                    }
                    DrawBarsCodes(maq.ListBarCodes, gfx, dico, currentY);
                    DrawQRCodes(maq.ListQrCodes, gfx, dico, currentY);
                    DrawParagraphs(structureId, maq.ListParagraphes, gfx, currentY);
                    DrawTexts(structureId, maq.ListTexts, gfx, dico, currentY);

                    DrawLines(maq.ListLines, gfx, currentY);


                } // end for each ticket seat
                catch (DataTooLongException ex)
                {
                    Logger.Error(structureId, $"La version et la correction dans Rodrigue du QrCode sont incompatibles  \n La taille de chaine est trop grande. {ex.Message}");
                    throw new Exception($"La version et la correction dans Rodrigue du QrCode sont incompatibles  \n La taille de chaine est trop grande.");
                }               
                catch (Exception ex)
                {
                    throw;
                }
                gfx.Dispose();

                currentY += billet_taille_y;
            }





            return document;
        }

        /// <summary>
        /// ramene maquetteoriginalecode_LANGCODE si elle existe (et type4 et n'est pas masquée)
        /// </summary>
        /// <param name="allmaquettes"></param>
        /// <param name="originMaquetteId"></param>
        /// <param name="langCode"></param>
        /// <returns></returns>
        private MaquetteBilletEntity switchMaquetteByLang(IEnumerable<MaquetteBilletEntity> allmaquettes, int originMaquetteId, string langCode)
        {
            //var thisMaqEnt = allmaquettes.Where(m => m.MaquetteBilletId == originMaquetteId).FirstOrDefault();

            var thisMaqEnt = allmaquettes.FirstOrDefault(m => m.MaquetteBilletId == originMaquetteId)
                    ?? throw new KeyNotFoundException($"can't find maquette {originMaquetteId} !");


            MaquetteBilletEntity maqReturn = thisMaqEnt;
            var thisMaqEntLang = allmaquettes.FirstOrDefault(
                m => m.Code.Trim().ToUpper() == thisMaqEnt.Code.Trim().ToUpper() + "_" + langCode.ToUpper()
                && m.Masquer != 'O'
                && m.TypeSupport == 4
            );

            return thisMaqEntLang ?? maqReturn;
        }



        public string GenerateMaquettePdfForTest(int structureId, string langCode, int maquetteId, Dictionary<string, string> dict)
        {
            try
            {
                string pathImagesLogo = _config["PathImagesLogos"]!;
                pathImagesLogo = pathImagesLogo.Replace("[structureId]", structureId.ToString("0000"));

                var allMaquettesEntities = _maquetteRepository.GetAll(structureId);

                maquetteId = switchMaquetteByLang(allMaquettesEntities, maquetteId, langCode).MaquetteBilletId;

                MaquetteBillet maquette = _maquetteRepository.Load(structureId, maquetteId, "WIN");


                bool LoadFontsPerso = false;
                var DicoConfigIni = _rodrigueConfig.GetDictionaryFromCache(structureId);
                //string UseFontPersoKey = "PARAMPDFUSEFONTPERSO";

                EZFontResolver fontResolver = EZFontResolver.Get;
                LoadAllFonts(structureId, fontResolver);


                PdfDocument document = new();
                Dictionary<string, XImage> dicImagesPathImageId = new();

                int currentY = 0;
                int billet_taille_y = maquette.Height;
                PdfPage page = document.AddPage();

                DictionaryExtented dico = DictionaryExtented.ConvertToDictionaryExtented(dict);

                XGraphics gfx = XGraphics.FromPdfPage(page);

                DrawImagesEtLogos(maquette.ListImagesLogo, gfx, currentY, pathImagesLogo, dicImagesPathImageId);


                var evts = _eventManager.GetImagesAsync(structureId, 1).Result;
                var Image1 = evts.FirstOrDefault().Images.Image1;
                var Image2 = evts.FirstOrDefault().Images.Image2;

                if (maquette.ListImagesWeb.Count > 0)
                {
                    DrawAndGetImagesWeb(structureId, maquette.MaquetteId, maquette.ListImagesWeb, gfx, currentY, 1, dicImagesPathImageId);
                }

                DrawVignette(maquette.ListVignettesEvent, gfx, currentY, dicImagesPathImageId, 1, null, null);

                DrawBarsCodes(maquette.ListBarCodes, gfx, dico, currentY);
                DrawQRCodes(maquette.ListQrCodes, gfx, dico, currentY);
                DrawTexts(structureId, maquette.ListTexts, gfx, dico, currentY);
                DrawParagraphs(structureId, maquette.ListParagraphes, gfx, currentY);
                DrawLines(maquette.ListLines, gfx, currentY);
                DrawWatermark(gfx, billet_taille_y, currentY, $"p@h fortest {langCode}, {maquette.MaquetteName} ({maquetteId.ToString()})");

                gfx.Dispose();

                string filename = $"{structureId}.{maquetteId}.{langCode}.webT.{DateTime.Now.Ticks}.pdf";

                string pathPdfSaved = _config["PathPdfForViewMaquette"] ?? throw new ArgumentNullException("PathPdf n'existe pas dans la config");
                pathPdfSaved = pathPdfSaved.Replace("[structureId]", structureId.ToString("0000"));

                if (!Directory.Exists(pathPdfSaved))
                    Directory.CreateDirectory(pathPdfSaved);

                if (document != null && document.PageCount > 0)
                {
                    string completePath = Path.Combine(pathPdfSaved, filename);
                    document.Save(completePath);

                    return filename;
                }

                FilesForRequestHelper.ClearFilesOlderThanPeriodToDays(pathPdfSaved, 30);
                throw new ArgumentNullException(nameof(document), $"Pdf document is null or empty");


                //return ConvertPdfDocumentToByteArray(structureId, document, filename);
            }
            catch
            {
                throw;
            }
        }


        /// <summary>
        /// ticket 1 seat
        /// </summary>
        /// <param name="structureId"></param>
        /// <param name="eventId"></param>
        /// <param name="seatId"></param>
        /// <param name="langCode"></param>
        /// <returns></returns>
        public byte[] GenerateTicketPdfById(int structureId, int eventId, int seatId, string langCode)
        {
            var ticket = GetPdfTicketById(structureId, eventId, seatId);


            var orderdto = _orderRepository.GetOrderInfoForEdition(structureId, ticket.OrderId, eventId, seatId);

            FillMaquettesSeats(structureId, orderdto);

            int? maqFromBasket = fillMaquetteFromPanier(ticket, orderdto);

            ticket.MaquetteId = maqFromBasket == null ? ticket.MaquetteId : maqFromBasket.Value;

            // .ForMember(dest => dest.MaquetteId, opt => opt.MapFrom(src => src.Seance.MaqBilletId))

            var pdfDocument = GenerateTicketPdfDocument(structureId, langCode, isBat: false, ticket);

            string fileName = $"{structureId}.{eventId}.{seatId}.{langCode}.{DateTime.Now.Ticks}.pdf";

            return ConvertPdfDocumentToByteArray(structureId, pdfDocument, fileName);
        }

        public byte[] GenerateTicketPdfByOrderId(int structureId, int orderId, string langCode)
        {
            var tickets = GetPdfTicketByOrderId(structureId, orderId);

            var pdfDocument = GenerateTicketPdfDocument(structureId, langCode, isBat: false, tickets.ToArray());

            string fileName = $"{structureId}.{orderId}.{langCode}.{DateTime.Now.Ticks}.pdf";

            return ConvertPdfDocumentToByteArray(structureId, pdfDocument, fileName);
        }

        /// <summary>
        /// pdf contenant toutes les places de l'identité sur telle séance
        /// </summary>
        /// <param name="structureId"></param>
        /// <param name="sessionId"></param>
        /// <param name="identityId"></param>
        /// <param name="langCode"></param>
        /// <returns></returns>
        public async Task<byte[]> GenerateTicketPdfBySessionIdAsync(int structureId, int sessionId, int identityId, string langCode, int OrderId = 0)
        {
            var tickets = await GetPdfTicketBySeanceAsync(structureId, sessionId, identityId, OrderId).ConfigureAwait(false);


            List<int> listOrders = tickets.Select(x => x.OrderId).Distinct().ToList();
            List<OrderDTO> orders = new List<OrderDTO>();
            foreach (var orderId in listOrders)
            {
                var thisOrder = _orderRepository.GetOrderInfoForEdition(structureId, orderId);

                FillMaquettesSeats(structureId, thisOrder);

                orders.Add(thisOrder);
            }

            foreach (var ticket in tickets)
            {
                int? maqFromBasket = fillMaquetteFromPanier(ticket, orders.Where(c => c.OrderId == ticket.OrderId).FirstOrDefault());

                ticket.MaquetteId = maqFromBasket == null ? ticket.MaquetteId : maqFromBasket.Value;
            }


            var pdfDocument = GenerateTicketPdfDocument(structureId, langCode, isBat: false, tickets.ToArray());

            string filename = $"{structureId}.{sessionId}.{identityId}.{langCode}.{DateTime.Now.Ticks}.pdf";

            return ConvertPdfDocumentToByteArray(structureId, pdfDocument, filename);
        }

        public byte[] ConvertPdfDocumentToByteArray(PdfDocument pdfDocument)
        {
            using (var memoryStream = new MemoryStream())
            {
                pdfDocument.Save(memoryStream);
                return memoryStream.ToArray();
            }
        }

        private byte[] ConvertPdfDocumentToByteArray(int structureId, PdfDocument document, string filename)
        {
            string pathPdfSaved = _config["PathPdf"] ?? throw new ArgumentNullException("PathPdf n'existe pas dans la config");
            pathPdfSaved = pathPdfSaved.Replace(oldValue: "[structureId]", structureId.ToString("0000"));

            Directory.CreateDirectory(pathPdfSaved);

            if (document != null && document.PageCount > 0)
            {
                string completePath = Path.Combine(pathPdfSaved, filename);
                document.Save(completePath);
                return File.ReadAllBytes(completePath);
   
            }

            throw new ArgumentNullException(nameof(document), $"Pdf document si null or empty");
        }

        private PdfTicketModel? GetPdfTicketById(int structureId, int eventId, int seatId)
        {
            try 
            {              
                var entree = _entreeRepository.GetCustomEntityById(structureId, seatId, eventId)
                                            .Where(e => e.State == TypeStateEnum.Editer)
                                            .Include(e => e.Recette)
                                            .Include(e => e.Seance)                                            
                                            .Include(e => e.Seance.Manifestation)                                         
                                            .ToEntity();
                if (entree != null)
                {
                    // il peut y avoir 2 commandes ligne pour la recette via dossierid =xx, manif = xx et seance = xx : une en "DOS", l'autre en "PROD" 
                    // ======> on obtient la bonne commande_ligne dans un second temps, via le filtre typeLigne ="DOS"
                    var cmdLigne = _orderLineRepository.FindBy(c =>
                       c.DossierId == entree.DossierId &&
                       c.SeanceId == entree.SeanceId &&
                       c.ManifestationId == entree.ManifestationId &&
                       c.TypeLigne == "DOS", structureId)
                   .FirstOrDefault();

                    if (entree?.Recette != null)
                    {
                        entree.Recette.CommandeLigne = cmdLigne;
                    }
                }
  
                return _mapper.Map<PdfTicketModel>(entree);
            }
            catch
            {
                throw;
            }
        }

        private async Task<List<PdfTicketModel>> GetPdfTicketBySeanceAsync(int structureId, int seanceId, int identiteId, int OrderId)
        {
            var query = _entreeRepository.GetAllEntreesEditeesBySeanceIdAndIdentiteId(structureId, seanceId, identiteId)
                                         .Where(e => e.State == TypeStateEnum.Editer);

            Task[] tasks = new Task[]
            {
                query.IncludeAsync(e => e.Seance),
                query.IncludeAsync(e => e.Recette),
                query.IncludeAsync(e => e.CommandeLigne),
            };

            await Task.WhenAll(tasks).ConfigureAwait(false);

            Task[] tasks2 = new Task[]
            {
                query.IncludeAsync(e => e.Seance.Manifestation),
                query.IncludeAsync(e => e.Recette.CommandeLigne)
            };

            await Task.WhenAll(tasks2).ConfigureAwait(false);

            var entrees = await query.ToEntityListAsync().ConfigureAwait(false);

            var r = _mapper.Map<List<PdfTicketModel>>(entrees);
            if (OrderId > 0)
            {
                return r.Where(m => m.OrderId == OrderId).ToList();
            }
            return r;

        }

        private List<PdfTicketModel> GetPdfTicketByOrderId(int structureId, int orderId)
        {
            try
            {
                var entrees = _entreeRepository.GetAllEntreesEditeesOfOrderById(structureId, orderId).ToList();

                List<PdfTicketModel> listToReturn = _mapper.Map<List<PdfTicketModel>>(entrees);

                var orderdto = _orderRepository.GetOrderInfoForEdition(structureId, orderId);

                foreach (var ticket in listToReturn)
                {
                    var thisSeatDTO = orderdto.listAllSeats.Where(s => s.EventId == ticket.EventId && s.SeatId == ticket.SeatId).FirstOrDefault();
                    if (thisSeatDTO?.MaquetteId > 0)
                    {
                        ticket.MaquetteId = thisSeatDTO.MaquetteId;
                    }
                }
                return listToReturn;
            }
            catch
            {
                throw;
            }
        }

        #endregion

        private void SetTicketInfoDico(int structureId, string langCode, DictionaryExtented dico, PdfTicketModel ticket, int ticketsCount, int productCount, int index, bool isBat = false)
        {

            try
            {
                var culture = CultureInfo.GetCultureInfo(langCode); // Valide l'existence de la culture
                Thread.CurrentThread.CurrentUICulture = culture;
                Thread.CurrentThread.CurrentCulture = CultureInfo.CreateSpecificCulture(langCode);
            }
            catch (CultureNotFoundException)
            {
                // Fallback sur une culture par défaut (ex: "en-GB")
                var defaultCulture = CultureInfo.GetCultureInfo("en-GB");
                Thread.CurrentThread.CurrentUICulture = defaultCulture;
                Thread.CurrentThread.CurrentCulture = defaultCulture;

            }




            dico.AjouterValeurBillet(dico, "COMMANDE_NUMERO", $"{ticket.OrderId}");
            dico.AjouterValeurBillet(dico, "DOSSIER_MONTANT", string.Empty);
            dico.AjouterValeurBillet(dico, "DOSSIER_VALEUR", string.Empty);
            //dico.AjouterValeurBillet(dico, "EDITION_DATE", $"{DateTime.Now.ToShortDateString()} {DateTime.Now.ToString("T", DateTimeFormatInfo.InvariantInfo)}");
            dico.AjouterValeurBillet(dico, "EDITION_TIME", DateTime.Now.ToShortTimeString());
            dico.AjouterValeurBillet(dico, "DOSSIER_SEANCE_DATE_*", DateTime.Now.AddDays(30).ToShortDateString());
            dico.AjouterValeurBillet(dico, "SEANCE_DATE_VALIDITE", DateTime.Now.AddDays(30).ToShortDateString());
            dico.AjouterValeurBillet(dico, "EDITION_OPERATEUR", "THEMIS");
            dico.AjouterValeurBillet(dico, "BILLET_NOMBRE", ticketsCount);
            dico.AjouterValeurBillet(dico, "PRODUIT_NOMBRE", productCount);
            dico.AjouterValeurBillet(dico, "BILLET_POSITION", index);
            dico.AjouterValeurBillet(dico, "BILLET_NUMERO", ticket.NumTicket);
            dico.AjouterValeurBillet(dico, "EDITION_DATE", ticket.OperationDate.ToShortDateString() + " " + ticket.OperationDate.ToString("T", DateTimeFormatInfo.InvariantInfo));
            dico.AjouterValeurBillet(dico, "EDITION_TIME", ticket.OperationDate.ToShortTimeString());
            dico.AjouterValeurBillet(dico, "CRYPT_BILLET_NUMERO", ticket.Barcode);
            dico.AjouterValeurBillet(dico, "WEB_TICKET_NUMBER", ticket.Barcode);




            dico += _eventRepository.GetDictionnaryForPdf(structureId, langCode, ticket.EventId);
            dico += _sessionRepository.GetDictionnaryForPdf(structureId, langCode, ticket.IdentityId, ticket.OrderId, ticket.SessionId);
            dico += _placeRepository.GetDictionnaryForPdf(structureId, langCode, ticket.EventId, ticket.SessionId);
            dico += _identityRepository.GetDictionnaryForPdf(structureId, langCode, ticket.IdentityId, ticket.OrderId, ticket.EventId, ticket.SessionId, ticket.DossierId, ticket.SeatId, isBat);

            if (ticket is PdfTicketModelBAT batTicket)
            {
                Console.WriteLine($"CategId: {batTicket.CategId}, PriceId: {batTicket.PriceId}");
                dico += _seatRepository.GetDictionnaryForPdf(structureId, langCode, ticket.IdentityId, ticket.OrderId, ticket.EventId, ticket.SessionId, ticket.DossierId, ticket.SeatId, isBat, batTicket.CategId, batTicket.PriceId);
            }
            else
            {
                Console.WriteLine("Ticket standard sans catégorie ni prix spécifique.");
                if (ticket.SeatId > 0)
                    dico += _seatRepository.GetDictionnaryForPdf(structureId, langCode, ticket.IdentityId, ticket.OrderId, ticket.EventId, ticket.SessionId, ticket.DossierId, ticket.SeatId, isBat);
            }



            if (ticket.ProductId > 0)
            {
                dico += _produitRepository.GetDictionnaryForPdf(structureId, langCode, ticket.OrderId, ticket.ProductId, 0, ticket.DossierId, isBat);
            }

            if (ticket.EventId > 0)
                dico += _eventRepository.GetDictionnaryForPdf(structureId, langCode, ticket.EventId);

            if (ticket.SessionId > 0)
                dico += _sessionRepository.GetDictionnaryForPdf(structureId, langCode, ticket.IdentityId, ticket.OrderId, ticket.SessionId);

        }

        /// <summary>
        /// charger toutes les polices potentielles (appsettings + fichier ttf)
        /// </summary>
        /// <param name="fontResolver"></param>
        private void LoadAllFonts(int structureId, EZFontResolver fontResolver)
        {
            Logger.Debug(structureId, "LoadAllFonts...");

            string pathFonts = _config["pathPdfFonts"];

            //var listFonts2 = _config.GetSection("pdfFonts");
            var listFonts = _config.GetSection("pdfFonts").Get<string[]>();

            //List<string> listFonts = new List<string>()
            //{
            //    "Arial",
            //    "Verdana211",
            //    "Comic sans MS",
            //    "Broadway Normal",
            //    "insatiable display condensed"
            //};


            if (listFonts != null)
            {
                Logger.Info(structureId, $"LoadAllFonts listFonts.Count = {listFonts.Length}");

                foreach (string fontName in listFonts)
                {
                    try
                    {
                        var fontStyle = GetFontStyleFromName(fontName);
                        string fontPath = Path.Combine(pathFonts, $"{fontName}.ttf");
                        if (!File.Exists(fontPath))
                        {
                            Logger.Warn(structureId, $"Font file not found: {fontPath}");
                            continue;
                        }
                        fontResolver.AddFont(fontName, fontStyle.style, fontPath, fontStyle.isBold, fontStyle.isItalic);
                        Logger.Info(structureId, $"Font loaded: {fontName} ({fontStyle.style})");


                //        if (fontName.Contains("Bold Italic"))
                //        {
                //            fontResolver.AddFont(fontName, XFontStyle.BoldItalic,
                //            @$"{pathFonts}\{fontName}.ttf", true, true);
                //        }
                //        else if (fontName.Contains("Bold"))
                //        {
                //            fontResolver.AddFont(fontName, XFontStyle.Bold,
                //            @$"{pathFonts}\{fontName}.ttf", true, false);
                //        }
                //        else if (fontName.Contains("Italic"))
                //        {
                //            fontResolver.AddFont(fontName, XFontStyle.Italic,
                //            @$"{pathFonts}\{fontName}.ttf", false, false);
                //        }
                //        else
                //        {
                //            fontResolver.AddFont(fontName, XFontStyle.Regular,
                //@$"{pathFonts}\{fontName}.ttf", false, false);
                //        }
                    }
                    catch (Exception ex)
                    {
                        Logger.Error(structureId, $"LoadAllFonts {ex.Message} {ex.StackTrace}");
                    }
                }
                GlobalFontSettings.FontResolver = fontResolver;
            }
            else
            {
                Logger.Error(structureId, $"LoadAllFonts listFonts null");
            }
            //Logger.Error(structureId, $"LoadAllFonts {fontResolver.fonts.length} null");
        }

        // Méthode utilitaire pour déterminer le style de police
        (XFontStyle style, bool isBold, bool isItalic) GetFontStyleFromName(string fontName)
        {
            string name = fontName.ToLowerInvariant();
            if (name.Contains("bold italic"))
                return (XFontStyle.BoldItalic, true, true);
            if (name.Contains("bold"))
                return (XFontStyle.Bold, true, false);
            if (name.Contains("italic"))
                return (XFontStyle.Italic, false, true);
            return (XFontStyle.Regular, false, false);
        }

        #region dessiner les differents champs

        /// <summary>
        /// dessine les textes simples
        /// </summary>
        /// <param name="listTexts"></param>
        /// <param name="gfx"></param>
        /// <param name="myDicoVariablesValeurs"></param>
        /// <param name="currentY"></param>
        private void DrawTexts(int structureId, List<MaquetteField_Texte> listTexts, XGraphics gfx, DictionaryExtented myDicoVariablesValeurs, int currentY)
        {
            foreach (var texte in listTexts)
            {
                if (!texte.TransfereVersZoneDeTexte)
                {
                    string TextToWrite = MaquetteManager.TAH_TexteVariableToTexteValue(texte.Texte, myDicoVariablesValeurs);
                    bool TestCondition = true;
                    if (texte.Condition != "")
                    {
                        TestCondition = TAH_TestCondition(texte.Condition, myDicoVariablesValeurs);
                    }
                    if (TestCondition && TextToWrite != "")
                    {
                        var x = XUnit.FromMillimeter(texte.PosX);
                        var y = XUnit.FromMillimeter(texte.PosY) + XUnit.FromMillimeter(currentY);
                        y += texte.TailleX + 3;  // petit décalage 
                        var size = texte.TailleX;

                        int colorRed = 0, colorGreen = 0, colorBlue = 0;
                        if (texte.ForeColor != 0)
                        {
                            double dcolorRed = ((texte.ForeColor % 65536) % 256);
                            double dcolorGreen = ((texte.ForeColor % 65536) / 256);
                            double dcolorBlue = (texte.ForeColor / 65536);
                            colorRed = Convert.ToInt32(dcolorRed);
                            colorGreen = Convert.ToInt32(dcolorGreen);
                            colorBlue = Convert.ToInt32(dcolorBlue);
                        }

                        XSolidBrush xSolidBrush = new(XColor.FromArgb(colorRed, colorGreen, colorBlue));

                        string fontName = _maquetteManager.GetFontName(structureId, texte.PoliceNum);

                        Logger.Trace(structureId, $"{TextToWrite}, policeNum={texte.PoliceNum} => fontName={fontName}");

                        XFont fontT = new XFont("Arial", size, XFontStyle.Regular);

                        try

                        {
                            XFontStyle fontStyle = _maquetteManager.GetFontStyle(texte.PoliceNum);
                            // fontName = "Arial Bold";
                            fontT = new XFont(fontName, size, fontStyle);
                            if (fontT.Name.Contains("Agency") && (fontName.Contains("Bold") || fontName.Contains("Italic")))
                            {
                                // Agency = n'a pas trouvé cette police : si on n'a pas utilisé fontperso, donc pas EZResolver, il ne va pas trouver les "Arial Bold", "Arial Italic", etc
                                string fontNameEpuree = fontName.Replace("Bold", "").Replace("Italic", "").Trim();
                                fontT = new XFont(fontNameEpuree, size, _maquetteManager.GetFontStyle(texte.PoliceNum));

                            }
                        }
                        catch (Exception ex)
                        {
                            Logger.Error(structureId, $"error fontT = {fontName} : {ex.Message} {ex.StackTrace}");
                        }

                        Logger.Debug(structureId, $"{TextToWrite}, policeNum={texte.PoliceNum} => fontT={fontT.Name}, b={fontT.Bold}, i={fontT.Italic}, {fontT.Underline}");

                        // fontName = "insatiable display condensed";

                        ///XFont fontT = new(fontName, size, MaquetteManager.GetFontStyle(texte.PoliceNum));

                        int rotation = 0;
                        switch (texte.Rotation)
                        {
                            case "N":
                                rotation = -90;
                                break;
                            case "S":
                                rotation = 90;
                                break;
                            default:
                                break;
                        }

                        gfx.RotateAtTransform(rotation, new XPoint(x, y));
                        gfx.DrawString(TextToWrite, fontT, xSolidBrush, new XPoint(x, y));
                        gfx.RotateAtTransform(-rotation, new XPoint(x, y));
                    }
                }
            }
        }

        /// <summary>
        /// dessine les paragraphes (= textes simples regroupés en paragraphes)
        /// </summary>
        /// <param name="listTexts"></param>
        /// <param name="gfx"></param>
        /// <param name="currentY"></param>
        /// <param name="page"></param>
        private static void DrawParagraphs(int structureId, List<MaquetteField_Texte> listTexts, XGraphics gfx, int currentY)
        {
            foreach (var parag in listTexts)
            {
                var pageX = gfx.PdfPage.Width.Point;
                var pageY = gfx.PdfPage.Height.Point;
                var pageSize = new XSize(pageX, pageY);

                var x = XUnit.FromMillimeter(parag.PosX).Point;
                var y = XUnit.FromMillimeter(parag.PosY).Point + XUnit.FromMillimeter(currentY).Point;

                string workedText = parag.Texte;
                // workedText = "billet blabla";

                string WidthToSearch = "width=\"[0-9]*\"";

                string widthExtracted = Regex.Match(workedText, WidthToSearch).Value;
                string widthDivValue = Regex.Match(widthExtracted, @"\d+").Value;

                //var fontResolver = EZFontResolver.Get;
                //  GlobalFontSettings.FontResolver = fontResolver;

                //var farial = resov.GetFont("Arial");
                //  XFont fontT = new XFont("Arial", 5, XFontStyle.Regular);

                int divWidth = 180;
                if (!string.IsNullOrWhiteSpace(widthExtracted))
                    divWidth = Convert.ToInt32(widthDivValue);
                workedText = workedText.Replace("<p align=>", "<p>");
                workedText = workedText.Replace("<b>", "<h3>");
                workedText = workedText.Replace("</b>", "</h3>");
                workedText = workedText.Replace($"<font size=\"{parag.TailleX}pt\">", "<font>");
                workedText = workedText.Replace("pt\"", "px\"");


                //workedText = workedText.Replace("</b>", "</i>");
                workedText = workedText.Replace("pt\"", "px\"");

                bool textJustified = workedText.Contains("align=\"justified\"");
                //textJustified = true;
                var regexDiv = new Regex("[<](/)?div[^>]*[>]", RegexOptions.IgnoreCase);

                workedText = regexDiv.Replace(workedText, string.Empty);

                string newHtml = $"<div style='{(textJustified ? "text-align: justify;" : null)} font-size: {parag.TailleX}px;'>{workedText}</div>";

                //newHtml = workedText;
                var maxSize = new XSize(XUnit.FromMillimeter(divWidth).Point, pageY - y);

                HtmlContainer container = new()
                {
                    Location = new XPoint(x, y),
                    MaxSize = maxSize,
                    PageSize = pageSize,
                };

                //<h1 style="font-weight: bold"> your text here </h1>

                container.SetHtml(newHtml);
                //container.SetHtml("This is a <b>HTML</b> string <u>written</u> to the <font color=\"red\">PDF</font>.<br><br><a href=\"http://www.google.nl\">www.google.nl</a>");


                using XGraphics measure = XGraphics.CreateMeasureContext(maxSize, XGraphicsUnit.Point, XPageDirection.Downwards);
                container.PerformLayout(measure);

                // gfx.IntersectClip(new XRect(0, 0, pageX, pageY));

                container.PerformPaint(gfx);

                if (container.ActualSize.Width == 0)
                {
                    Logger.Warn(structureId, $"pb paragraphe {parag.Texte}");
                    //////// pb !!!!
                }
            }
        }


        /// <summary>
        /// dessine les paragraphes (= textes simples regroupés en paragraphes)
        /// </summary>
        /// <param name="listTexts"></param>
        /// <param name="gfx"></param>
        /// <param name="currentY"></param>
        /// <param name="page"></param>
        private static void DrawParagraphs_marchepas(List<MaquetteField_Texte> listTexts, XGraphics gfx, int currentY)
        {
            foreach (var parag in listTexts)
            {
                var pageX = gfx.PdfPage.Width.Point;
                var pageY = gfx.PdfPage.Height.Point;
                var pageSize = new XSize(pageX, pageY);

                var x = XUnit.FromMillimeter(parag.PosX).Point;
                var y = XUnit.FromMillimeter(parag.PosY).Point + XUnit.FromMillimeter(currentY).Point;

                string workedText = parag.Texte;
                //workedText = "billet blabla";

                string WidthToSearch = "width=\"[0-9]*\"";

                string widthExtracted = Regex.Match(workedText, WidthToSearch).Value;
                string widthDivValue = Regex.Match(widthExtracted, @"\d+").Value;

                //var fontResolver = EZFontResolver.GetViaExterne;
                //GlobalFontSettings.FontResolver = fontResolver;
                //var farial = resov.GetFont("Arial");
                XFont fontT = new XFont("Arial", 20, XFontStyle.Regular);

                int divWidth = 180;
                if (!string.IsNullOrWhiteSpace(widthExtracted))
                    divWidth = Convert.ToInt32(widthDivValue);

                workedText = workedText.Replace("<b>", "<i>");
                workedText = workedText.Replace("</b>", "</i>");
                workedText = workedText.Replace("pt\"", "px\"");

                bool textJustified = workedText.Contains("align=\"justified\"");

                var regexDiv = new Regex("[<](/)?div[^>]*[>]", RegexOptions.IgnoreCase);

                workedText = regexDiv.Replace(workedText, string.Empty);

                string newHtml = $"<div style='{(textJustified ? "text-align: justify;" : null)} font-size: {parag.TailleX}px;'>{workedText}</div>";

                var maxSize = new XSize(XUnit.FromMillimeter(divWidth).Point, pageY - y);

                HtmlContainer container = new()
                {
                    Location = new XPoint(x, y),
                    MaxSize = maxSize,
                    PageSize = pageSize,
                };

                container.SetHtml(newHtml);

                using XGraphics measure = XGraphics.CreateMeasureContext(maxSize, XGraphicsUnit.Point, XPageDirection.Downwards);
                container.PerformLayout(measure);

                var converter = new HtmlConverter();
                var html = "<div><strong>Hello</strong> World!</div>";
                string encoded = System.Net.WebUtility.HtmlEncode(newHtml);
                var bytes = converter.FromHtmlString(newHtml, 350, ImageFormat.Jpg, 100);
                //File.WriteAllBytes("image.jpg", bytes);

                using (MemoryStream stream = new MemoryStream(bytes))
                {
                    XImage imageqr = XImage.FromStream(() => stream);

                    // gfx.DrawImage(imageqr, x, y, divWidth * 2, divWidth * 2);
                    gfx.DrawImage(imageqr, x, y, imageqr.Size.Width / 2, imageqr.Size.Height / 2);

                }



                // gfx.IntersectClip(new XRect(0, 0, pageX, pageY));

                container.PerformPaint(gfx);

                if (container.ActualSize.Width == 0)
                {
                    Logger.Warn(0, "pb paragraphe");
                    //////// pb !!!!
                }
            }
        }

        /// <summary>
        /// dessine les QRCode
        /// </summary>
        /// <param name="listQrCodes"></param>
        /// <param name="gfx"></param>
        /// <param name="myDicoVariablesValeurs"></param>
        /// <param name="currentY"></param>
        private static void DrawQRCodes(List<MaquetteField_QrCode> listQrCodes, XGraphics gfx, DictionaryExtented myDicoVariablesValeurs, int currentY)
        {
            foreach (var tahQrCode in listQrCodes)
            {
                string TextToWrite = MaquetteManager.TAH_TexteVariableToTexteValue(tahQrCode.Texte, myDicoVariablesValeurs);

                if (string.IsNullOrWhiteSpace(TextToWrite))
                    throw new ArgumentNullException(nameof(TextToWrite));

                try
                {
                    byte[] qrCodeAsBitmapByteArr = QRCodeHelper.GenerateByteQrCode(TextToWrite, tahQrCode.ErrorCorrection);

                    var posX = XUnit.FromMillimeter(tahQrCode.PosX).Point;
                    var posY = XUnit.FromMillimeter(tahQrCode.PosY).Point + XUnit.FromMillimeter(currentY).Point;
                    XPoint xpoint1 = new(posX, posY);
                    XPoint xpoint2 = new(posX + XUnit.FromMillimeter(tahQrCode.Hauteur).Point, posY + XUnit.FromMillimeter(tahQrCode.Hauteur).Point);
                    XRect xRect = new(xpoint1, xpoint2);

                    using Stream stream = new MemoryStream(qrCodeAsBitmapByteArr);
                    stream.Position = 0;

                    XImage imageqr = XImage.FromStream(() => stream);

                    gfx.DrawImage(imageqr, xRect);
                }
                catch
                {
                    throw;
                }


            }
        }

        /// <summary>
        /// dessine les lignes
        /// </summary>
        /// <param name="listLines"></param>
        /// <param name="gfx"></param>
        /// <param name="currentY"></param>
        private static void DrawLines(List<MaquetteField_Ligne> listLines, XGraphics gfx, int currentY)
        {
            foreach (var ligne in listLines)
            {
                var x = XUnit.FromMillimeter(ligne.PosX);
                var y = XUnit.FromMillimeter(ligne.PosY) + XUnit.FromMillimeter(currentY);
                var h = XUnit.FromMillimeter(ligne.TailleX);
                var w = XUnit.FromMillimeter(ligne.TailleY + XUnit.FromMillimeter(currentY));
                double lineWidth = 1;

                if (ligne.CoeffElargissement > 1)
                    lineWidth = 1.2;

                gfx.DrawRectangle(new XPen(XColor.FromKnownColor(XKnownColor.Black), lineWidth), x, y, h, w);
            }
        }



        /// <summary>
        /// dessine les vignette de manif
        /// </summary>
        /// <param name="listImages"></param>
        /// <param name="gfx"></param>
        /// <param name="currentY"></param>
        /// <param name="pathImagesLogos"></param>
        /// <param name="dicImagesPathImageId"></param>
        private static void DrawVignette(List<MaquetteField_Vignette> listImages, XGraphics gfx, int currentY,
            Dictionary<string, XImage> dicImagesPathImageId, int eventId, byte[] image1, byte[] image2)
        {
            try
            {
                foreach (var tahImage in listImages.Where(i => !string.IsNullOrWhiteSpace(i.ImageName)))
                {
                    if (tahImage.ImageName is null)
                        throw new ArgumentNullException(nameof(tahImage.ImageName), "L'image name est null");

                    var posX = XUnit.FromMillimeter(tahImage.PosX);
                    var posY = XUnit.FromMillimeter(tahImage.PosY) + XUnit.FromMillimeter(currentY);
                    var myH = XUnit.FromMillimeter(tahImage.Height / 12);
                    var myW = XUnit.FromMillimeter(tahImage.Width / 12);

                    string keyImage = tahImage.ImageName + "." + eventId;
                    if (dicImagesPathImageId.Keys.Contains(keyImage) && dicImagesPathImageId[keyImage] != null)
                    {
                        gfx.DrawImage(dicImagesPathImageId[keyImage], posX, posY, myW, myH);
                    }
                    else
                    {
                        byte[]? imageToDraw = null;
                        switch (tahImage.ImageName)
                        {
                            case "VIGNETTE1":
                                imageToDraw = image1; break;
                            case "VIGNETTE2":
                                imageToDraw = image2; break;
                            default:
                                break;
                        }
                        if (imageToDraw != null)
                        {

                            //string pngPath = Path.Combine(Path.GetTempPath(), "image_convertie.png");


                            using (MemoryStream ms = new MemoryStream(imageToDraw))
                            using (System.Drawing.Image sysImage = System.Drawing.Image.FromStream(ms))
                            using (MemoryStream msPng = new MemoryStream())
                            {
                                // Enregistre l'image en PNG dans un nouveau MemoryStream
                                //sysImage.Save(msng, ImageFormat.Png);
                                //sysImage.Save(pngPath, System.Drawing.Imaging.ImageFormat.Png);

                                sysImage.Save(msPng, System.Drawing.Imaging.ImageFormat.Png);

                                //msPng.Position = 0;

                                //File.WriteAllBytes(pngPath, msPng.ToArray()); // <- après le Save en mémoire

                                msPng.Position = 0;

                                // Charge l'image PNG via PDFsharp
                                XImage thisimage = XImage.FromStream(() => msPng);

                                gfx.DrawImage(thisimage, posX, posY, myW, myH);
                                //gfx.DrawImage(thisimage, posX, posY);
                                dicImagesPathImageId.Add(keyImage, thisimage);

                            }
                        }

                    }
                }
            }
            catch
            {
                throw;
            }
        }


        /// <summary>
        /// dessine les images
        /// </summary>
        /// <param name="listImages"></param>
        /// <param name="gfx"></param>
        /// <param name="currentY"></param>
        /// <param name="pathImagesLogos"></param>
        /// <param name="dicImagesPathImageId"></param>
        private static void DrawImagesEtLogos(List<MaquetteField_ImageLogo> listImages, XGraphics gfx, int currentY, string pathImagesLogos, Dictionary<string, XImage> dicImagesPathImageId)
        {
            try
            {
                foreach (var tahImage in listImages.Where(i => !string.IsNullOrWhiteSpace(i.ImageName)))
                {
                    if (tahImage.ImageName is null)
                        throw new ArgumentNullException(nameof(tahImage.ImageName), "L'image name est null");

                    var posX = XUnit.FromMillimeter(tahImage.PosX);
                    var posY = XUnit.FromMillimeter(tahImage.PosY) + XUnit.FromMillimeter(currentY);
                    var myH = XUnit.FromMillimeter(tahImage.Height / 12);
                    var myW = XUnit.FromMillimeter(tahImage.Width / 12);

                    string urlImage = pathImagesLogos + tahImage.ImageName;
                    if (dicImagesPathImageId.Keys.Contains(urlImage) && dicImagesPathImageId[urlImage] != null)
                    {
                        gfx.DrawImage(dicImagesPathImageId[urlImage], posX, posY, myW, myH);
                    }
                    else if (File.Exists(pathImagesLogos + tahImage.ImageName))
                    {
                        XImage thisimage = XImage.FromFile(pathImagesLogos + tahImage.ImageName);
                        gfx.DrawImage(thisimage, posX, posY, myW, myH);
                        dicImagesPathImageId.Add(urlImage, thisimage);
                    }
                }
            }
            catch (ImageFormatException ex)
            {
                Logger.Error(0, $"Image format error: {ex.Message}");
            }
            catch
            {
                throw;
            }
        }


        private void DrawAndGetImagesWeb(int structureId, int maquetteId, List<MaquetteField_ImageWeb> listImages, XGraphics gfx, int currentY, int eventId,
                Dictionary<string, XImage> dicImagesPathImageId)
        {
            try
            {
                string imagesWebCacheKey = $"ImagesWeb_{structureId}";
                string imagesWebManifCacheKey = $"ImagesWebManif_{structureId}";
                var imagesweb = _memoryCache.Get(imagesWebCacheKey) as IEnumerable<ImageWebEntity>;
                if (imagesweb == null)
                {
                    imagesweb = _imagesWebRepository.GetAll(structureId);
                    _memoryCache.Set(imagesWebCacheKey, imagesweb, DateTimeOffset.Now.AddMinutes(5));
                }

                var imageswebManif = _memoryCache.Get(imagesWebManifCacheKey) as IEnumerable<ManifestationImageWebEntity>;
                if (imageswebManif == null)
                {
                    imageswebManif = _eventImagesWebRepository.GetAll(structureId);
                    _memoryCache.Set(imagesWebManifCacheKey, imageswebManif, DateTimeOffset.Now.AddMinutes(5));
                }

                DrawImageWeb(listImages, gfx, currentY, eventId, imagesweb, imageswebManif, dicImagesPathImageId);
            }
            catch (Exception ex)
            {
                Logger.Warn(structureId, $"draw image web error ! {maquetteId}, event {eventId}\n{ex.Message}\n{ex.StackTrace}");
            }

        }


        /// <summary>
        /// dessine les images
        /// </summary>
        /// <param name="listImages"></param>
        /// <param name="gfx"></param>
        /// <param name="currentY"></param>
        /// <param name="pathImagesLogos"></param>
        /// <param name="dicImagesPathImageId"></param>
        private static void DrawImageWeb(List<MaquetteField_ImageWeb> listImages, XGraphics gfx,
            int currentY, int eventId,
            IEnumerable<ImageWebEntity> images,
            IEnumerable<ManifestationImageWebEntity> manifestationsImages,

            Dictionary<string, XImage> dicImagesPathImageId)
        {
            try
            {
                foreach (var tahImage in listImages.Where(i => !string.IsNullOrWhiteSpace(i.ImageName)))
                {
                    if (tahImage.ImageName is null)
                        throw new ArgumentNullException(nameof(tahImage.ImageName), "L'image name est null");

                    var posX = XUnit.FromMillimeter(tahImage.PosX);
                    var posY = XUnit.FromMillimeter(tahImage.PosY) + XUnit.FromMillimeter(currentY);
                    var myH = XUnit.FromMillimeter(tahImage.Height / 12);
                    var myW = XUnit.FromMillimeter(tahImage.Width / 12);

                    /// obtient les urls de l'image x pour la manif y
                    var thisImageId = images.Where(i => i.Code == tahImage.ImageName).FirstOrDefault().Id;
                    var theseImagesUrl = manifestationsImages.Where(m => m.ManifestationId == eventId && m.Id == thisImageId);

                    if (theseImagesUrl.Count() == 0)
                    {
                        return;
                    }

                    //Random rnd = new Random(); 
                    int index = Random.Shared.Next(theseImagesUrl.ToList().Count); // s'il y en plusieurs, on en prend 1 au pif

                    string urlImage = theseImagesUrl.ToList()[index].url;


                    if (dicImagesPathImageId.Keys.Contains(urlImage) && dicImagesPathImageId[urlImage] != null)
                    {
                        gfx.DrawImage(dicImagesPathImageId[urlImage], posX, posY, myW, myH);
                    }
                    else
                    {
                        byte[] imageBytes;
                        using (HttpClient client = new HttpClient())
                        {
                            client.DefaultRequestHeaders.Add("User-Agent", "Mozilla/5.0");
                            HttpResponseMessage response = client.GetAsync(urlImage).Result;

                            if (!response.IsSuccessStatusCode)
                            {
                                Console.WriteLine($"❌ HTTP Error: {response.StatusCode}");
                                return;
                            }
                            string contentType = response.Content.Headers.ContentType.MediaType.ToLower();
                            if (!contentType.StartsWith("image/"))
                            {
                                Console.WriteLine($"❌ Le contenu n'est pas une image : {contentType}");
                                return;
                            }

                            imageBytes = response.Content.ReadAsByteArrayAsync().Result;

                            //string pngPath = Path.Combine(Path.GetTempPath(), "image_downloaded.png");

                            //File.WriteAllBytes(pngPath, imageBytes);
                        }


                        using (MemoryStream inputStream = new MemoryStream(imageBytes))
                        using (System.Drawing.Image sysImage = System.Drawing.Image.FromStream(inputStream))
                        using (MemoryStream msPng = new MemoryStream())
                        {
                            // Convertit en PNG pour compatibilité
                            sysImage.Save(msPng, System.Drawing.Imaging.ImageFormat.Jpeg);
                            msPng.Position = 0;

                            // Crée le XImage
                            XImage thisimage = XImage.FromStream(() => msPng);

                            gfx.DrawImage(thisimage, posX, posY, myW, myH);
                            dicImagesPathImageId.Add(urlImage, thisimage);

                        }
                    }
                }
            }
            catch
            {
                throw;
            }
        }



        /// <summary>
        /// dessine les codes barres
        /// </summary>
        /// <param name="listCodesBars"></param>
        /// <param name="gfx"></param>
        /// <param name="myDicoVariablesValeurs"></param>
        /// <param name="currentY"></param>
        private static void DrawBarsCodes(List<MaquetteField_BarCode> listCodesBars, XGraphics gfx, DictionaryExtented myDicoVariablesValeurs, int currentY)
        {
            try
            {
                foreach (var tahCodeBarre in listCodesBars)
                {
                    string TextToWrite = MaquetteManager.TAH_TexteVariableToTexteValue(tahCodeBarre.Texte, myDicoVariablesValeurs);

                    if (string.IsNullOrWhiteSpace(TextToWrite))
                        throw new ArgumentNullException(nameof(TextToWrite));

                    if (long.TryParse(TextToWrite, out _))
                    {
                        BarcodeStandard.Barcode barcode = new()
                        {
                            IncludeLabel = tahCodeBarre.AvecLibelle,
                            LabelFont = new SKFont(SKTypeface.FromFamilyName("Arial"), 8),
                            Alignment = BarcodeStandard.AlignmentPositions.Center,
                        };

                        var myTypeCB = tahCodeBarre.TypeCodeBarre switch
                        {
                            1 => BarcodeStandard.Type.Code128,
                            2 => BarcodeStandard.Type.Code39,
                            3 => BarcodeStandard.Type.Standard2Of5,
                            _ => BarcodeStandard.Type.Code128,
                        };

                        int rotation = tahCodeBarre.Rotation switch
                        {
                            "S" => 90,
                            "N" => 90,
                            _ => 0,
                        };

                        var posX = XUnit.FromMillimeter(tahCodeBarre.PosX).Point;
                        var posY = XUnit.FromMillimeter(tahCodeBarre.PosY).Point + XUnit.FromMillimeter(currentY).Point;

                        int barcodeWidth = (int)XUnit.FromMillimeter(tahCodeBarre.Hauteur * 6).Point;
                        int barcodeHeight = (int)XUnit.FromMillimeter(tahCodeBarre.Hauteur).Point;

                        SKImage img = barcode.Encode(myTypeCB, TextToWrite, SKColors.Black, SKColors.Transparent, barcodeWidth, barcodeHeight);
                        SKData imgData = img.Encode(SKEncodedImageFormat.Png, 50);
                        MemoryStream ms = new();
                        imgData.SaveTo(ms);
                        ms.Position = 0;

                        XImage xImg = XImage.FromStream(() => ms);

                        var x = posX + xImg.PointHeight;

                        gfx.RotateAtTransform(rotation, new XPoint(x, posY));
                        gfx.DrawImage(xImg, posX, posY);
                        gfx.RotateAtTransform(-rotation, new XPoint(x, posY));

                    }
                }
            }
            catch
            {
                throw;
            }
        }


        /// <summary>
        /// dessine watermark
        /// </summary>
        /// <param name="gfx"></param>
        /// <param name="ticketSize"></param>
        /// <param name="currentY"></param>
        /// <param name="watermark"></param>
        private void DrawWatermark(XGraphics gfx, int ticketSize, int currentY, string watermark = "test print@home")
        {
            PdfPage page = gfx.PdfPage;

            var ticketSizeInPoint = XUnit.FromMillimeter(ticketSize).Point;
            var currentYInPoint = XUnit.FromMillimeter(currentY).Point;

            int nbWatermarkPerPage = 10;
            int nbWatermarkToDraw = nbWatermarkPerPage * (int)ticketSizeInPoint / (int)page.Height.Point;
            if (nbWatermarkToDraw < 5)
                nbWatermarkToDraw = 5;

            XFont font = new("arial", 7 + nbWatermarkToDraw);

            double radians = Math.Atan2(ticketSizeInPoint, page.Width);
            double angle = radians * (180 / Math.PI);

            double rotation = -angle;

            XBrush brush = new XSolidBrush(XColor.FromKnownColor(XKnownColor.Red));

            for (int i = 0; i < nbWatermarkToDraw; i++)
            {
                Random rnd = new();
                double x = rnd.Next(0, (int)page.Width.Point);
                double y = rnd.Next((int)currentYInPoint, (int)ticketSizeInPoint + (int)currentYInPoint);

                var format = new XStringFormat
                {
                    Alignment = XStringAlignment.Near,
                    LineAlignment = XLineAlignment.Near
                };

                gfx.RotateAtTransform(rotation, new XPoint(x, y));
                gfx.DrawString(watermark, font, brush, new XPoint(x, y), format);
                gfx.RotateAtTransform(-rotation, new XPoint(x, y));
            }

            gfx.DrawLine(new XPen(XColor.FromKnownColor(XKnownColor.Red)), 0, currentYInPoint, page.Width, ticketSizeInPoint + currentYInPoint);
            gfx.DrawLine(new XPen(XColor.FromKnownColor(XKnownColor.Red)), page.Width, currentYInPoint, 0, ticketSizeInPoint + currentYInPoint);

        }
        private void DrawWatermarkBAT(XGraphics gfx, int ticketSize, int currentY, string watermark = "test print@home")
        {
            PdfPage page = gfx.PdfPage;

            var ticketSizeInPoint = XUnit.FromMillimeter(ticketSize).Point;
            var currentYInPoint = XUnit.FromMillimeter(currentY).Point;

            int nbWatermarkToDraw = 2;

            XFont font = new("arial", 10);

            double rotation = 90;

            XBrush brush = new XSolidBrush(XColor.FromKnownColor(XKnownColor.LightGray));

            for (int i = 0; i < nbWatermarkToDraw; i++)
            {
                Random rnd = new();
                double x = rnd.Next(0, (int)page.Width.Point);
                double y = rnd.Next((int)currentYInPoint, (int)ticketSizeInPoint + (int)currentYInPoint);

                var format = new XStringFormat
                {
                    Alignment = XStringAlignment.Near,
                    LineAlignment = XLineAlignment.Near
                };

                gfx.RotateAtTransform(rotation, new XPoint(x, y));
                gfx.DrawString(watermark, font, brush, new XPoint(x, y), format);
                gfx.RotateAtTransform(-rotation, new XPoint(x, y));
            }

            gfx.DrawLine(new XPen(XColor.FromKnownColor(XKnownColor.LightGray)), 0, currentYInPoint, page.Width, ticketSizeInPoint + currentYInPoint);
            gfx.DrawLine(new XPen(XColor.FromKnownColor(XKnownColor.LightGray)), page.Width, currentYInPoint, 0, ticketSizeInPoint + currentYInPoint);
        }

        #endregion

        #region mouliner les conditions (faire apparaitre ou non le champs texte suivant le tarif ou autre)
        private bool petitebrik(string Condition, ref int nbConditionRemplies, DictionaryExtented Dico, ConditionProtype mycondition)
        {
            bool myReturn = false;
            if (mycondition.TestPreCondition(Condition))
            {
                string VarVal = "";
                if (Dico.TryGetValue(mycondition.valeur1, out VarVal))
                    mycondition.valeur1 = VarVal;

                if (mycondition.TestCondition())
                {
                    nbConditionRemplies++;
                }
                myReturn = true;
            }
            return myReturn;
        }

        private bool TAH_TestCondition(string TheCondition, DictionaryExtented Dico)
        {
            bool Myreturn = true;

            string[] ListCondition = TheCondition.Split(';');
            if (ListCondition.Length > 1)
            {
                int nbCondition = ListCondition.Length;
                string typeCondition = ListCondition[nbCondition - 1];
                int nbMinCondition = nbCondition - 1;
                if (typeCondition == "OU")
                    nbMinCondition = 1;

                int nbConditionRemplies = 0;
                //définir quelle methode 
                ConditionProtype mycondition;
                for (int i = 0; i < nbCondition - 1; i++)
                {
                    string Condition = ListCondition[i];

                    if (nbConditionRemplies >= nbMinCondition) break;

                    mycondition = new ConditionSuperieurEgale();
                    if (petitebrik(Condition, ref nbConditionRemplies, Dico, mycondition))
                    {
                        continue;
                    }
                    mycondition = new ConditionInferieurEgale();
                    if (petitebrik(Condition, ref nbConditionRemplies, Dico, mycondition))
                    {
                        continue;
                    }
                    mycondition = new ConditionDifferent();
                    if (petitebrik(Condition, ref nbConditionRemplies, Dico, mycondition))
                    {
                        continue;
                    }

                    mycondition = new ConditionEgale();
                    if (petitebrik(Condition, ref nbConditionRemplies, Dico, mycondition))
                    {
                        continue;
                    }
                    mycondition = new ConditionSuperieur();
                    if (petitebrik(Condition, ref nbConditionRemplies, Dico, mycondition))
                    {
                        continue;
                    }
                    mycondition = new ConditionInferieur();
                    if (petitebrik(Condition, ref nbConditionRemplies, Dico, mycondition))
                    {
                        continue;
                    }

                }

                //condition du ET et du OU
                if (nbConditionRemplies >= nbMinCondition)
                    Myreturn = true;
                else
                    Myreturn = false;
            }

            return Myreturn;
        }

        public bool ConditionSup(string valeur1, string valeur2)
        {
            return true;
        }



        public class ConditionProtype
        {
            public string valeur1;
            public string valeur2;
            //retourn le signe de l'operation exmple ">"
            public virtual bool TestPreCondition(string condition)
            {
                return true;
            }
            protected void Decortique(string strOperateur, string condition)
            {
                valeur1 = condition.Remove(condition.LastIndexOf(strOperateur));
                valeur2 = condition.Substring(condition.LastIndexOf(strOperateur) + strOperateur.Length);

            }
            public virtual bool TestCondition()
            {
                valeur1 = valeur1.Replace(".", ",");
                valeur2 = valeur2.Replace(".", ",");

                return true;
            }

        }

        public class ConditionEgale : ConditionProtype
        {
            //retourn le signe de l'operation exmple "="
            public override bool TestPreCondition(string condition)
            {
                bool myreturn = false;
                if (condition.Contains("="))
                {
                    Decortique("=", condition);
                    myreturn = true;
                }

                return myreturn;
            }
            public override bool TestCondition()
            {
                base.TestCondition();
                bool myreturn = false;
                DateTime dtvaleur1 = DateTime.MinValue, dtvaleur2 = DateTime.MinValue;
                double dbvaleur1 = 0, dbvaleur2 = 0;
                long lvaleur1 = 0, lvaleur2 = 0;
                if (long.TryParse(valeur1, out lvaleur1) && long.TryParse(valeur2, out lvaleur2))
                {
                    if (lvaleur1 == lvaleur2)
                        myreturn = true;
                }
                else if (double.TryParse(valeur1, out dbvaleur1) && double.TryParse(valeur2, out dbvaleur2))
                {
                    if (dbvaleur1 == dbvaleur2)
                        myreturn = true;
                }

                else if (DateTime.TryParse(valeur1, out dtvaleur1) && DateTime.TryParse(valeur2, out dtvaleur2))
                {
                    if (dtvaleur1 == dtvaleur2)
                        myreturn = true;
                }
                else
                {
                    if (valeur1 == valeur2)
                        myreturn = true;
                }
                return myreturn;

            }
        }

        public class ConditionSuperieurEgale : ConditionProtype
        {
            //retourn le signe de l'operation exmple "="
            public override bool TestPreCondition(string condition)
            {
                bool myreturn = false;
                if (condition.Contains(">="))
                {
                    Decortique(">=", condition);
                    myreturn = true;
                }

                return myreturn;
            }
            public override bool TestCondition()
            {
                base.TestCondition();

                bool myreturn = false;
                DateTime dtvaleur1 = DateTime.MinValue, dtvaleur2 = DateTime.MinValue;
                double dbvaleur1 = 0, dbvaleur2 = 0;
                long lvaleur1 = 0, lvaleur2 = 0;
                if (long.TryParse(valeur1, out lvaleur1) && long.TryParse(valeur2, out lvaleur2))
                {
                    if (lvaleur1 >= lvaleur2)
                        myreturn = true;
                }
                else if (double.TryParse(valeur1, out dbvaleur1) && double.TryParse(valeur2, out dbvaleur2))
                {
                    if (dbvaleur1 >= dbvaleur2)
                        myreturn = true;
                }

                else if (DateTime.TryParse(valeur1, out dtvaleur1) && DateTime.TryParse(valeur2, out dtvaleur2))
                {
                    if (dtvaleur1 >= dtvaleur2)
                        myreturn = true;
                }

                return myreturn;
            }


        }
        public class ConditionInferieurEgale : ConditionProtype
        {
            //retourn le signe de l'operation exmple "="
            public override bool TestPreCondition(string condition)
            {
                bool myreturn = false;
                if (condition.Contains("<="))
                {
                    Decortique("<=", condition);

                    myreturn = true;
                }

                return myreturn;
            }
            public override bool TestCondition()
            {
                base.TestCondition();
                bool myreturn = false;
                DateTime dtvaleur1 = DateTime.MinValue, dtvaleur2 = DateTime.MinValue;
                double dbvaleur1 = 0, dbvaleur2 = 0;
                long lvaleur1 = 0, lvaleur2 = 0;
                if (long.TryParse(valeur1, out lvaleur1) && long.TryParse(valeur2, out lvaleur2))
                {
                    if (lvaleur1 <= lvaleur2)
                        myreturn = true;
                }
                else if (double.TryParse(valeur1, out dbvaleur1) && double.TryParse(valeur2, out dbvaleur2))
                {
                    if (dbvaleur1 <= dbvaleur2)
                        myreturn = true;
                }
                else if (DateTime.TryParse(valeur1, out dtvaleur1) && DateTime.TryParse(valeur2, out dtvaleur2))
                {
                    if (dtvaleur1 <= dtvaleur2)
                        myreturn = true;
                }
                return myreturn;

            }
        }
        public class ConditionSuperieur : ConditionProtype
        {
            //retourn le signe de l'operation exmple "="
            public override bool TestPreCondition(string condition)
            {
                bool myreturn = false;
                if (condition.Contains(">"))
                {
                    Decortique(">", condition);
                    myreturn = true;
                }

                return myreturn;
            }
            public override bool TestCondition()
            {
                base.TestCondition();
                bool myreturn = false;
                DateTime dtvaleur1 = DateTime.MinValue, dtvaleur2 = DateTime.MinValue;
                double dbvaleur1 = 0, dbvaleur2 = 0;
                long lvaleur1 = 0, lvaleur2 = 0;
                if (long.TryParse(valeur1, out lvaleur1) && long.TryParse(valeur2, out lvaleur2))
                {
                    if (lvaleur1 > lvaleur2)
                        myreturn = true;
                }
                else if (double.TryParse(valeur1, out dbvaleur1) && double.TryParse(valeur2, out dbvaleur2))
                {
                    if (dbvaleur1 > dbvaleur2)
                        myreturn = true;
                }
                else if (DateTime.TryParse(valeur1, out dtvaleur1) && DateTime.TryParse(valeur2, out dtvaleur2))
                {
                    if (dtvaleur1 > dtvaleur2)
                        myreturn = true;
                }


                return myreturn;
            }
        }
        public class ConditionInferieur : ConditionProtype
        {
            //retourn le signe de l'operation exmple "="
            public override bool TestPreCondition(string condition)
            {
                bool myreturn = false;
                if (condition.Contains("<"))
                {
                    Decortique("<", condition);
                    myreturn = true;
                }

                return myreturn;
            }
            public override bool TestCondition()
            {
                base.TestCondition();
                bool myreturn = false;
                DateTime dtvaleur1 = DateTime.MinValue, dtvaleur2 = DateTime.MinValue;

                double dbvaleur1 = 0, dbvaleur2 = 0;
                long lvaleur1 = 0, lvaleur2 = 0;
                if (long.TryParse(valeur1, out lvaleur1) && long.TryParse(valeur2, out lvaleur2))
                {
                    if (lvaleur1 < lvaleur2)
                        myreturn = true;
                }
                else if (double.TryParse(valeur1, out dbvaleur1) && double.TryParse(valeur2, out dbvaleur2))
                {
                    if (dbvaleur1 < dbvaleur2)
                        myreturn = true;
                }
                else if (DateTime.TryParse(valeur1, out dtvaleur1) && DateTime.TryParse(valeur2, out dtvaleur2))
                {
                    if (dtvaleur1 < dtvaleur2)
                        myreturn = true;
                }


                return myreturn;
            }
        }
        public class ConditionDifferent : ConditionProtype
        {
            //retourn le signe de l'operation exmple "="
            public override bool TestPreCondition(string condition)
            {
                bool myreturn = false;
                if (condition.Contains("<>"))
                {
                    Decortique("<>", condition);
                    myreturn = true;
                }

                return myreturn;
            }
            public override bool TestCondition()
            {
                base.TestCondition();
                bool myreturn = false;
                DateTime dtvaleur1 = DateTime.MinValue, dtvaleur2 = DateTime.MinValue;
                double dbvaleur1 = 0, dbvaleur2 = 0;
                long lvaleur1 = 0, lvaleur2 = 0;
                if (long.TryParse(valeur1, out lvaleur1) && long.TryParse(valeur2, out lvaleur2))
                {
                    if (lvaleur1 != lvaleur2)
                        myreturn = true;
                }
                else if (double.TryParse(valeur1, out dbvaleur1) && double.TryParse(valeur2, out dbvaleur2))
                {
                    if (dbvaleur1 != dbvaleur2)
                        myreturn = true;
                }

                else if (DateTime.TryParse(valeur1, out dtvaleur1) && DateTime.TryParse(valeur2, out dtvaleur2))
                {
                    if (dtvaleur1 != dtvaleur2)
                        myreturn = true;
                }
                else
                {
                    if (valeur1 != valeur2)
                        myreturn = true;
                }
                return myreturn;
            }
        }

        #endregion

        #region MTicket

        public byte[] GenerateAppleTicket(int structureId, MTicketModel ticketInfos)
        {
            try
            {
                PassGeneratorRequest request = _mapper.Map<PassGeneratorRequest>(ticketInfos);

                return AppleWalletHelper.GenerateAppleTicketByte(request);
            }
            catch (Exception ex)
            {
                Logger.Error(structureId, $"Message = {ex.Message} - stackTrace = {ex.StackTrace}");
                throw;
            }
        }

        public byte[] GenerateMultiAppleTickets(int structureId, List<MTicketModel> mtickets)
        {
            try
            {
                List<PassGeneratorRequest> requests = _mapper.Map<List<PassGeneratorRequest>>(mtickets);

                return AppleWalletHelper.GenerateMultiAppleTicketsByte(requests);
            }
            catch (Exception ex)
            {
                Logger.Error(structureId, $"Message = {ex.Message} - stackTrace = {ex.StackTrace}");
                throw;
            }
        }

        public string GenerateGoogleTicketLink(int structureId, MTicketModel ticketInfos)
        {
            try
            {
                string typeRun = _config["typeRun"] ?? "";
                dynamic? googleConfig = _config.GetDynamicSection("MTicket:Google");
                List<string> listUrlOrigins = _config.GetSection("MTicket:Google:ListUrlOrigins").Get<List<string>>();

                if (googleConfig is null)
                    throw new ArgumentNullException(nameof(googleConfig), $"{nameof(googleConfig)} is null (check appsetting section MTicket exist)");

                if (googleConfig.IssuerId is null)
                    throw new ArgumentNullException(nameof(googleConfig.IssuerId), $"{nameof(googleConfig.IssuerId)} is null (check appsetting section MTicket IssuerId exist)");

                if (googleConfig.UrlPay is null)
                    throw new ArgumentNullException(nameof(googleConfig.UrlPay), $"{nameof(googleConfig.UrlPay)} is null (check appsetting section MTicket UrlPay exist)");

                if (listUrlOrigins is null)
                    throw new ArgumentNullException(nameof(googleConfig.ListUrlOrigins), $"{nameof(googleConfig.ListUrlOrigins)} is null (check appsetting section MTicket ListUrlOrigins exist)");


                ticketInfos.GoogleClassId = $"{googleConfig.IssuerId}.{typeRun}_{ticketInfos.GroupTicketId}";
                ticketInfos.GoogleObjectId = $"{googleConfig.IssuerId}.{typeRun}_{ticketInfos.TicketId}";

                EventTicketClass eventTicketClass = _mapper.Map<EventTicketClass>(ticketInfos);
                EventTicketObject eventTicketObject = _mapper.Map<EventTicketObject>(ticketInfos);

                if (GoogleWalletHelper.CheckClassExist(eventTicketClass.Id))
                    GoogleWalletHelper.UpdateClass(eventTicketClass);

                return GoogleWalletHelper.CreateJWTNewObjects(eventTicketClass, eventTicketObject, listUrlOrigins);
            }
            catch (Exception ex)
            {
                Logger.Error(structureId, $"Message = {ex.Message} - stackTrace = {ex.StackTrace}");
                throw;
            }
        }

        public string GenerateMultiGoogleTicketsLink(int structureId, List<MTicketModel> mtickets)
        {
            try
            {
                string typeRun = _config["typeRun"] ?? "";
                dynamic? googleConfig = _config.GetDynamicSection("MTicket:Google");

                var listUrlOrigins = _config.GetSection("MTicket:Google:ListUrlOrigins").Get<List<string>>();

                if (googleConfig is null)
                    throw new ArgumentNullException(nameof(googleConfig), $"{nameof(googleConfig)} is null (check appsetting section MTicket exist)");

                if (googleConfig.IssuerId is null)
                    throw new ArgumentNullException(nameof(googleConfig.IssuerId), $"{nameof(googleConfig.IssuerId)} is null (check appsetting section MTicket exist)");

                if (listUrlOrigins is null)
                    throw new ArgumentNullException(nameof(googleConfig), $"{nameof(googleConfig.ListUrlOrigins)} is null (check appsetting section MTicket ListUrlOrigins exist)");

                mtickets.ForEach(t =>
                {
                    t.GoogleClassId = $"{googleConfig.IssuerId}.{typeRun}_{t.GroupTicketId}";
                    t.GoogleObjectId = $"{googleConfig.IssuerId}.{typeRun}_{t.TicketId}";
                });

                List<EventTicketClass> eventTicketClasses = _mapper.Map<List<EventTicketClass>>(mtickets.DistinctBy(t => t.GoogleClassId));
                List<EventTicketObject> eventTicketObjects = _mapper.Map<List<EventTicketObject>>(mtickets);

                eventTicketClasses.ForEach(c =>
                {
                    if (GoogleWalletHelper.CheckClassExist(c.Id))
                        GoogleWalletHelper.UpdateClass(c);
                });

                return GoogleWalletHelper.CreateJWTMultiNewObjects(eventTicketClasses, eventTicketObjects, listUrlOrigins);
            }
            catch (Exception ex)
            {
                Logger.Error(structureId, $"Message = {ex.Message} - stackTrace = {ex.StackTrace}");
                throw;
            }
        }

        public string GenerateSamsungTicketLink(int structureId, MTicketModel ticketInfos)
        {
            try
            {
                SamsungCartData cardData = _mapper.Map<SamsungCartData>(ticketInfos);
                SetSamsungWalletImages(structureId, cardData);

                SamsungEventTicket samsungTicket = new();
                samsungTicket.Card.Data.Add(cardData);

                return SamsungWalletHelper.CreateJWTTicketLink(samsungTicket);
            }
            catch (Exception ex)
            {
                Logger.Error(structureId, $"Message = {ex.Message} - stackTrace = {ex.StackTrace}");
                throw;
            }
        }

        public string GenerateMultiSamsungTicketsLink(int structureId, List<MTicketModel> mtickets)
        {
            try
            {
                List<SamsungCartData> cardDatas = _mapper.Map<List<SamsungCartData>>(mtickets);

                cardDatas.ForEach(cd => SetSamsungWalletImages(structureId, cd));

                SamsungEventTicket samsungTicket = new();
                samsungTicket.Card.Data = cardDatas;

                return SamsungWalletHelper.CreateJWTTicketLink(samsungTicket);
            }
            catch (Exception ex)
            {
                Logger.Error(structureId, $"Message = {ex.Message} - stackTrace = {ex.StackTrace}");
                throw;
            }
        }

        private void SetSamsungWalletImages(int structureId, SamsungCartData cardData)
        {
            string baseImagesPhysicalPath = _config["Images:BaseImagesPhysicalPath"]
                ?? throw new NullReferenceException("section Images:BaseImagesPhysicalPath not set in appsettings");

            baseImagesPhysicalPath = baseImagesPhysicalPath.Replace("{structureId}", $"{structureId:0000}");

            if (File.Exists($@"{baseImagesPhysicalPath}banner_mticket.png"))
            {
                string baseImagesUrlPath = _config["Images:BaseImagesUrlPath"]
                    ?? throw new NullReferenceException("section Images:BaseImagesUrlPath not set in appsettings");

                baseImagesUrlPath = baseImagesUrlPath.Replace("{structureId}", $"{structureId:0000}");

                cardData.Attributes.MainImg = $@"{baseImagesUrlPath}banner_mticket.png";
            }
            else
            {
                cardData.Attributes.MainImg = $@"{_config["AssetsUrlPath"]}IMAGES\customer\mticket\banner_mticket_noimg.png";
            }

        }

        #endregion

        private static void SetSecuritySetting(PdfDocument document)
        {
            PdfSecuritySettings securitySettings = document.SecuritySettings;

            securitySettings.OwnerPassword = "PWOwner";
            securitySettings.PermitAccessibilityExtractContent = true;
            securitySettings.PermitAnnotations = false;
            securitySettings.PermitAssembleDocument = false;
            securitySettings.PermitExtractContent = false;
            securitySettings.PermitFormsFill = true;
            securitySettings.PermitFullQualityPrint = false;
            securitySettings.PermitModifyDocument = false;
            securitySettings.PermitPrint = true;
        }

    }
}
