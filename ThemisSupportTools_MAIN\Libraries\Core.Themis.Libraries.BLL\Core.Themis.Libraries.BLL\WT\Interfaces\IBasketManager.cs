﻿using Core.Themis.Libraries.DTO;
using Core.Themis.Libraries.DTO.Enums;
using Core.Themis.Libraries.DTO.EventsSessions;
using Core.Themis.Libraries.DTO.Products;
using Core.Themis.Libraries.DTO.Seats;
using Core.Themis.Libraries.DTO.WTObjects;
using System.Collections.Generic;
using System.Data;
using System.Threading.Tasks;

namespace Core.Themis.Libraries.BLL.WT.Interfaces
{



    public interface IBasketManager
    {
        int? GetCalculProductByStoredProcedure(int structureId, int basketId, int identiteId, int productId);

        List<BasketLineDTO> GetWrongBasketLinesForAdhesion(int structureId, int basketId);

        bool CheckPriceAdhesions(int structureId, int basketId);

        bool CheckWebUserCanContinue(int structureId, BasketDTO basket);

        BasketDTO? GetBasketByWebUserIdAndBasketState(int structureId, int webUserId, string basketState);

        ICollection<BasketDTO> GetBasketsToCreateCmd(int structureId, string status, string etape, int sincesecondes, int tosecondes, string? before = null);
        BasketDTO GetBasketById(int structureId, int basketId);

        DataTable GetEntriesDataTable(List<SeatDTO> seats);

        DataTable GetProductsDataTable(BasketDTO basket);

        int AddSponsorSeatLine(int structureId, int basketLineId, string sponsorReference, int sponsorId);

        /// <summary>
        /// Add panier_entree
        /// </summary>
        /// <param name="structureId"></param>
        /// <param name="basketId"></param>
        /// <param name="evt"></param>
        /// <param name="sess"></param>
        /// <param name="s"></param>
        /// <param name="gp"></param>
        /// <param name="identiteConsommateurId"></param>
        /// <returns>panier_entree_id inseré</returns>
        int AddSeatInBasketLine(int structureId, int basketId, EventDTO evt, SessionDTO sess, SeatDTO s, GestionPlaceDTO gp, int identiteConsommateurId, string feedBookTokenIdentifiant = null);

        void InsertMultipleLignesForTheSameProduct(int structureId, int basketId, ProductDTO oproduct);

        BasketDTO GetBasketInfo(int structureId, int orderId);

        List<BasketDTO> GetAllBasketInfo(int structureId, int basketId, int webUserId);

        Dictionary<int, List<int>> CheckBasketAdhesions(int structureId, List<int> listCustomerIds);

        void FillFromOpen(int structureId, BasketDTO bask, string langCode);

        /// <summary>
        /// call sp panier_transformator + sql panier_transform adhesion
        /// </summary>
        /// <param name="structureId"></param>
        /// <param name="basketId"></param>
        /// <returns></returns>
        BasketDTO BasketTransformator(int structureId, int basketId, string langCode);

        BasketDTO CreateBasketIfNotExists(int structureId, int webUserId, int identiteId, string basketEtat, int? orderId, string langCode = null);

        BasketDTO GetActiveBasketByUserAndIdentity(int structureId, int webUserId, int identiteId);

        int AddSeat(int structureId, int basketId, EventDTO evt, SessionDTO sess, SeatDTO s, GestionPlaceDTO gp, int identiteConsommateurId);

        /// <summary>
        /// update panier set identite_id = xxx
        /// </summary>
        /// <param name="structureId"></param>
        /// <param name="basketId"></param>
        /// <param name="identiteId"></param>
        /// <returns></returns>
        int UpdateIdentiteId(int structureId, int basketId, int identiteId);

        int UpdateOrderId(int structureId, int basketId, int orderId);



        bool UpdateConsumerPanierEntree(int structureId, int basketId, int consumId);

        /// <summary>
        /// update typeenvoiid & maquette de chaque place du panier
        /// </summary>
        /// <param name="structureId"></param>
        /// <param name="basketId"></param>
        /// <param name="listGestionPlaceId"></param>
        /// <param name="maquetteId"></param>
        /// <param name="typeEnvoiId"></param>
        /// <returns></returns>
        bool UpdateTypeEnvoiAndMaquette(int structureId, int basketId, List<int> listGestionPlaceId, int maquetteId, int typeEnvoiId);

        bool UpdateTypeEnvoiAndMaquetteProducts(int structureId, int basketId, List<int> ProductIds, int maquetteId, int typeEnvoiId);

        int InsertPanierProduit(int structureId, int basketId, ProductDTO product);

        bool AddFraisDossier(int structureId, int basketId, List<ProductDTO> listToAdd);

        bool DeleteAllCartesAdhesion(int structureId, int basketId);

        bool DeleteAllObtentionModes(int structureId, int basketId);

        /// <summary>
        /// s'il n'y a qu'un mode d'obtention on le force, insert panier_produit MO, update maquette des sieges
        /// </summary>
        /// <param name="structureId"></param>
        /// <param name="basketId"></param>
        /// <returns></returns>
        public bool ForceModeObtention(int structureId, int basketId);

        bool DelFraisDossier(int structureId, int basketId);


        /// <summary>
        /// delete des produits manifs & séance qui ne sont plus liés, delete des produits global au panier, s'il n'y a plus de panier
        /// </summary>
        /// <param name="structureId"></param>
        /// <param name="basketId"></param>
        /// <returns></returns>
        bool DeleteProduitsSeancesEventsGlobaux(int structureId, int basketId);


        //Supprime une liste de panier_produit_id pour un panier donné
        bool DeleteProduct(int structureId, int basketId, List<int> listToBasketProductsId);

        //Supprime une liste de produit_id pour un panier donné
        bool DeleteProductId(int structureId, int basketId, List<int> listProductsId);

        /// <summary>
        /// delete panier_entree where basketId and seanceId and entree_id in ({pSeatsId}) and etat in (C,I)
        /// </summary>
        /// <param name="structureId"></param>
        /// <param name="sessionId"></param>
        /// <param name="basketId"></param>
        /// <param name="seatsId"></param>
        /// <returns>True si les places ne sont plus/pas dans le panier</returns>
        bool DeleteSeatsOfSessionBasket(int structureId, int sessionId, int basketId, List<int> seatsId);

        bool DeleteWrongBasketLineForAdhesion(int structureId, int basketId);

        /// <summary>
        /// Liste des produits du panier
        /// </summary>
        /// <param name="structureId"></param>
        /// <param name="basketId"></param>
        /// <returns></returns>
        Task<List<int>> GetProductIdsWithoutInsertionAuto(int structureId, int basketId, List<int> productsId);


        #region etapes et etats panier
        bool UpdatePanierStraightPriseEnCompte(int structureId, BasketDTO bask);

        /// <summary>
        /// update panier de C ou I en etatBasket
        /// </summary>
        /// <param name="structureId"></param>
        /// <param name="basketId"></param>
        /// <param name="etatBasket"></param>
        /// <returns></returns>
        int UpdateEtatPanier(int structureId, int basketId, string etatBasket);

        /// <summary>
        /// update panier de etatBasketInit en etatBasket
        /// </summary>
        /// <param name="structureId"></param>
        /// <param name="basketId"></param>
        /// <param name="etatBasketInit"></param>
        /// <param name="etatBasket"></param>
        /// <returns></returns>
        int UpdateEtatPanier(int structureId, int basketId, List<string> etatBasketInit, string etatBasket);

        bool SetEtapeCreateCommande(int structureId, BasketDTO bask, string etat, EtapeCreationCmd debutOuFin, string comment);

        bool RestartStepPanier(int structureId, int basketId);
        void GetAboEntriesDataTable(BasketDTO basket, ref DataTable entriesDtaTable);

        Task<string> InitUrlBasketForReservationPaymentAsync(int structureId, int orderId, int webUserId, int identiteId, string langCode);

        #endregion

        void GetIsMontantCorrect(int structureId, BasketDTO bask);

        //void FillFromWT(BasketDTO bask, int structureId, bool? withHistoPlaces);
    }


}
