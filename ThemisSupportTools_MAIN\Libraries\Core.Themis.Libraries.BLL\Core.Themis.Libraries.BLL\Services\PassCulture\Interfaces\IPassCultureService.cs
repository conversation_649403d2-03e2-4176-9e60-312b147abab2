﻿using Core.Themis.Libraries.DTO.Lookup;
using Core.Themis.Libraries.DTO.PassCulture;
using Core.Themis.Libraries.DTO.PassCulture.ApiExchange;
using Core.Themis.Libraries.DTO.PassCulture.ApiExchange.Response;
using Core.Themis.Libraries.DTO.PassCulture.Error;
using Core.Themis.Libraries.DTO.PassCulture.Forms;
using Core.Themis.Libraries.DTO.WTObjects;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace Core.Themis.Libraries.BLL.Services.PassCulture.Interfaces
{
    public interface IPassCultureService
    {
        public int Limit { get; }

        #region Settings

        Task<PassCultureResponse<bool>> SaveSettingsAsync(int structureId, int buyerProfilId, int passCultureVenueId);

        Task<PassCultureSettingsForm> GetSettingsFormAsync(int structureId);

        Task<SettingsErrorsDetail> GetSettingsDetails(int structureId);

        #endregion

        #region Providers

        /// <summary>
        /// Return your provider information.
        /// </summary>
        /// <param name="structureId"></param>
        /// <returns></returns>
        /// <exception cref="Exception"></exception>
        Task<Provider?> GetProviderAsync(int structureId);

        /// <summary>
        /// Endpoint to set the urls used by our notification system to notify/request your solution.
        /// </summary>
        /// <param name="structureId"></param>
        /// <param name="provider"></param>
        /// <returns></returns>
        /// <exception cref="Exception"></exception>
        Task<Provider?> UpdateProviderAsync(int structureId, Provider provider);

        /// <summary>
        /// Endpoint to set the urls used by our messaging system to notify/request your solution.
        /// </summary>
        /// <param name="structureId"></param>
        /// <param name="venueId"></param>
        /// <param name="provider"></param>
        /// <returns></returns>
        /// <exception cref="Exception"></exception>
        Task<bool> UpdateVenueExternalUrlsAsync(int structureId, int venueId, Provider provider);

        #endregion

        #region Venues

        /// <summary>
        /// Return all the venues attached to the API key for given offerer.
        /// </summary>
        /// <param name="siren"></param>
        /// <returns></returns>
        Task<List<VenuesInfo>> GetVenuesAsync(int structureId, string? siren = null);

        /// <summary>
        /// Return venue corresponding to the given SIRET number (Système d'identification du répertoire des établissements)
        /// </summary>
        /// <param name="structureId"></param>
        /// <param name="siret"></param>
        /// <returns></returns>
        Task<Venue?> GetVenueBySiretAsync(int structureId, string siret);

        /// <summary>
        /// Return venue by id, attached to the API key for given offerer.
        /// </summary>
        /// <param name="structureId"></param>
        /// <param name="venueId"></param>
        /// <returns></returns>
        Task<Venue?> GetVenueByIdAsync(int structureId, int venueId);

        /// <summary>
        /// Get select lookup of the venues
        /// </summary>
        /// <param name="structureId"></param>
        /// <param name="siren"></param>
        /// <returns></returns>
        Task<List<SelectLookup>> SelectLookupVenuesAsync(int structureId, string? siren = null);

        /// <summary>
        /// Get venueId linked to the structure
        /// </summary>
        /// <param name="structureId"></param>
        /// <returns></returns>
        int? GetVenueIdByStructureId(int structureId);

        #endregion

        #region Addresses

        /// <summary>
        /// Return an address by id
        /// </summary>
        /// <param name="structureId"></param>
        /// <param name="addressId"></param>
        /// <returns></returns>
        Task<AddressConsultation> GetAddressAsync(int structureId, int addressId);

        /// <summary>
        /// Returns addresses that match the provided literal address. To enhance the precision of the search, you can provide optional latitude and longitude coordinates.
        /// </summary>
        /// <param name="structureId"></param>
        /// <param name="street"></param>
        /// <param name="city"></param>
        /// <param name="postalCode"></param>
        /// <param name="latitude"></param>
        /// <param name="longitude"></param>
        /// <returns></returns>
        Task<PassCultureResponse<AddressConsultation>> SearchAddressesAsync(int structureId, string street, string city, string postalCode, double? latitude = null, double? longitude = null);

        /// <summary>
        /// Add an address to the Pass Culture database.
        /// </summary>
        /// <param name="structureId"></param>
        /// <param name="address"></param>
        /// <returns></returns>
        Task<PassCultureResponse<AddressConsultation>> CreateAddressAsync(int structureId, AddressCreation address);

        /// <summary>
        /// Try to get address and save id pass culture in rodrigue, <br></br>
        /// otherwise try to create and save id pass culture in rodrigue
        /// </summary>
        /// <param name="structureId"></param>
        /// <param name="street"></param>
        /// <param name="city"></param>
        /// <param name="postalCode"></param>
        /// <returns></returns>
        Task<PassCultureResponse<AddressConsultation>> TrySetAddressAsync(int structureId, string street, string city, string postalCode);

        /// <summary>
        /// Get lookup address save in pass culture for structure
        /// </summary>
        /// <param name="structureId"></param>
        /// <returns></returns>
        Task<List<SelectLookup>> SelectLookupAddressAsync(int structureId);

        /// <summary>
        /// Get venue address id
        /// </summary>
        /// <param name="structureId"></param>
        /// <returns></returns>
        Task<int?> GetVenueAddressIdAsync(int structureId);

        #endregion

        #region Event offer

        /// <summary>
        /// Return all the events linked to given venue. Results are paginated (by default there are 50 events per page).
        /// </summary>
        /// <param name="venueId"></param>
        /// <param name="idsAtProvider">List of your ids to filter on. Example: idsAtProvider=59829,59829</param>
        /// <param name="limit">Maximum number of items per page.</param>
        /// <param name="firstIndex">Index of the first item in page.</param>
        /// <returns></returns>
        Task<List<EventOffer>> GetEventsAsync(int structureId, int venueId, string? idsAtProvider = null, int? limit = null, int? firstIndex = null);

        /// <summary>
        /// Return event offer by passCulture event id .
        /// </summary>
        /// <param name="passCultureEventId"></param>
        /// <returns></returns>
        Task<EventOffer?> GetEventByIdAsync(int structureId, int passCultureEventId);

        /// <summary>
        /// Create event offer
        /// </summary>
        /// <param name="eventOffer"></param>
        /// <returns></returns>
        Task<EventOffer?> CreateEventOfferAsync(int structureId, EventOffer eventOffer);

        /// <summary>
        /// Will update only the non-blank fields. If you some fields to keep their current values, leave them undefined.
        /// </summary>
        /// <param name="passCultureEventId"></param>
        /// <param name="eventOffer"></param>
        /// <returns></returns>
        Task<EventOffer?> UpdateEventOfferAsync(int structureId, int passCultureEventId, EventOfferUpdate eventOffer);

        #endregion

        #region Event offer Price

        /// <summary>
        /// Batch create price categories for given event.
        /// </summary>
        /// <param name="passCultureEventId"></param>
        /// <param name="priceCategories"></param>
        /// <returns></returns>
        Task<List<PriceCategory>> CreatePriceCategoriesAsync(int structureId, int passCultureEventId, List<PriceCategory> priceCategories);

        /// <summary>
        /// Will update only the non-blank field. If you want one of the field to remain unchanged, leave it undefined.
        /// </summary>
        /// <param name="passCultureEventId"></param>
        /// <param name="passCulturePriceId"></param>
        /// <param name="priceCategory"></param>
        /// <returns></returns>
        Task<PriceCategory?> UpdatePriceCategoryAsync(int structureId, int passCultureEventId, int passCulturePriceId, PriceCategory priceCategory);

        #endregion

        #region Event offer Stocks

        /// <summary>
        /// Return all stocks for given event. Results are paginated (by default there are 50 date per page).
        /// </summary>
        /// <param name="passCultureEventId"></param>
        /// <param name="limit">Maximum number of items per page.</param>
        /// <param name="firstIndex">Index of the first item in page.</param>
        /// <returns></returns>
        Task<List<Date>> GetEventStocksByIdEventAsync(int structureId, int passCultureEventId, int? limit = null, int? firstIndex = null, string? idsAtProvider = null);

        /// <summary>
        /// Add stocks to given event. Each stock is attached to a price category and to a date. 
        /// For a given date, you will have one stock per price category.
        /// </summary>
        /// <param name="passCultureEventId"></param>
        /// <param name="dates"></param>
        /// <returns></returns>
        Task<List<Date>> AddStocksToAnEventAsync(int structureId, int passCultureEventId, List<Date> dates);

        /// <summary>
        /// Update the price category and the beginning time of an event stock.
        /// </summary>
        /// <param name="passCultureEventId"></param>
        /// <param name="passCultureStockId"></param>
        /// <param name="dates"></param>
        /// <returns></returns>
        Task<Date?> UpdateEventStockAsync(int structureId, int passCultureEventId, int passCultureStockId, DateForUpdate date);

        /// <summary>
        /// When an event stock is deleted, all cancellable bookings (i.e not used) are cancelled. 
        /// To prevent from further bookings, you may alternatively update the stock's quantity to the bookedQuantity (but not below).
        /// </summary>
        /// <param name="passCultureEventId"></param>
        /// <param name="passCultureStockId"></param>
        /// <returns></returns>
        Task<bool> DeleteEventStockAsync(int structureId, int passCultureEventId, int passCultureStockId);

        #endregion

        #region Offer attributes

        /// <summary>
        /// Return all the categories available, with their conditional fields, and whether they are required.
        /// </summary>
        /// <returns></returns>
        /// <exception cref="Exception"></exception>
        Task<List<EventCategory>> GetEventCategoriesAsync(int structureId);

        /// <summary>
        /// Return music types. Not all the music types returned by this endpoint are suitable for events
        /// </summary>
        /// <returns></returns>
        /// <exception cref="Exception"></exception>
        Task<List<PassCultureType>> GetAllMusicTypesAsync(int structureId);

        /// <summary>
        /// Return eligible music types for events.
        /// </summary>
        /// <returns></returns>
        /// <exception cref="Exception"></exception>
        Task<List<PassCultureType>> GetEventsMusicTypesAsync(int structureId);

        /// <summary>
        /// Return show types.
        /// </summary>
        /// <returns></returns>
        /// <exception cref="Exception"></exception>
        Task<List<PassCultureType>> GetShowTypesAsync(int structureId);

        #endregion

        #region Booking/Cancellation

        /// <summary>
        /// Create booking in rodrigue
        /// </summary>
        /// <returns></returns>
        ResponseBase CreateBookingInRodrigue(int structureId, BookingPayload bookingInfos, WebUser webUser);

        /// <summary>
        /// Cancel booking in rodrigue
        /// </summary>
        /// <returns></returns>
        CancellationSuccess CancelBookingInRodrigue(int structureId, CancellationPayload cancellationInfos);

        /// <summary>
        /// Cancel order in rodrigue by date id pass culture
        /// </summary>
        /// <param name="structureId"></param>
        /// <param name="dateId"></param>
        /// <returns></returns>
        Task CancelOrdersByDateIdInRodrigueAsync(int structureId, int dateId);

        #endregion

        #region Collective offers

        /// <summary>
        /// Return collective offers. It will only return collective offers created by API (collective offers created manually in the pro interface won't show up).
        /// </summary>
        /// <param name="venueId"></param>
        /// <param name="status"></param>
        /// <param name="periodBeginningDate">Period beginning date. The expected format is ISO 8601 (standard format for timezone aware datetime)</param>
        /// <param name="periodEndingDate">Period ending date. The expected format is ISO 8601 (standard format for timezone aware datetime).</param>
        /// <returns></returns>
        /// <exception cref="Exception"></exception>
        Task<List<CollectiveOfferConsultation>> GetCollectiveOffersAsync(int structureId, int? venueId = null, string? status = null, string? periodBeginningDate = null, string? periodEndingDate = null);

        /// <summary>
        /// Return one collective offer using provided id.
        /// </summary>
        /// <param name="passCultureOfferId"></param>
        /// <returns></returns>
        /// <exception cref="Exception"></exception>
        Task<CollectiveOfferConsultation?> GetCollectiveOfferByIdAsync(int structureId, int passCultureOfferId);

        /// <summary>
        /// Create collective offer
        /// </summary>
        /// <param name="collectiveOffer"></param>
        /// <returns></returns>
        /// <exception cref="Exception"></exception>
        Task<PassCultureResponse<CollectiveOfferConsultation>> CreateCollectiveOfferAsync(int structureId, CollectiveOfferCreation collectiveOffer);

        /// <summary>
        /// Update collective offer
        /// </summary>
        /// <param name="passCultureOfferId"></param>
        /// <param name="collectiveOffer"></param>
        /// <returns></returns>
        /// <exception cref="Exception"></exception>
        Task<PassCultureResponse<CollectiveOfferConsultation>> UpdateCollectiveOfferAsync(int structureId, int passCultureOfferId, CollectiveOfferCreation collectiveOffer);

        /// <summary>
        /// Archive multiple collective offers
        /// </summary>
        /// <param name="structureId"></param>
        /// <param name="collectiveOfferIds"></param>
        /// <returns></returns>
        Task<bool> ArchiveCollectiveOffersAsync(int structureId, int[] collectiveOfferIds);

        #endregion

        #region Collective offer attributes

        /// <summary>
        /// Return the educational domains that can be linked to a collective offer.
        /// </summary>
        /// <returns></returns>
        /// <exception cref="Exception"></exception>
        Task<List<EducationalDomain>> GetEducationalDomainsAsync(int structureId);

        /// <summary>
        /// Get all educational institutions
        /// </summary>
        /// <param name="id"></param>
        /// <param name="name"></param>
        /// <param name="institutionType"></param>
        /// <param name="city"></param>
        /// <param name="postalCode"></param>
        /// <param name="uai"></param>
        /// <param name="limit"></param>
        /// <returns></returns>
        /// <exception cref="Exception"></exception>
        Task<List<EducationalInstitution>> GetAllEducationalInstitutionAsync(int structureId, EducationalInstitutionSearchForm search);

        /// <summary>
        /// Get all known national programs
        /// </summary>
        /// <returns></returns>
        /// <exception cref="Exception"></exception>
        Task<List<NationalProgram>> GetAllKnownNationalProgramsAsync(int structureId);

        /// <summary>
        /// Get student levels eligible for collective offers
        /// </summary>
        /// <returns></returns>
        /// <exception cref="Exception"></exception>
        Task<List<StudentLevel>> GetStudentLevelsEligibleAsync(int structureId);

        Task<List<SelectLookup>> SelectLookupEducationalDomainsAsync(int structureId);

        Task<List<SelectLookup>> SelectLookupEducationalInstitutionAsync(int structureId, EducationalInstitutionSearchForm search);

        Task<List<SelectLookup>> SelectLookupNationalProgramsAsync(int structureId);

        Task<List<SelectLookup>> SelectLookupStudentLevelsAsync(int structureId);

        #endregion

        #region Adage Mock

        /// <summary>
        /// Mock collective offer booking.<br></br>
        /// Like this could happen within the Adage platform. <br></br>
        /// <b>WARNING</b>: this endpoint is not available from the production environment as it is a mock meant to ease the test of your integrations.
        /// </summary>
        /// <param name="structureId"></param>
        /// <param name="offerId"></param>
        /// <returns></returns>
        Task<bool> AdageMockCollectiveOfferBookingAsync(int structureId, int offerId);

        /// <summary>
        /// Mock collective booking cancellation. <br></br>
        /// Like this could happen within the Adage platform. <br></br>
        /// <b>WARNING</b>: this endpoint is not available from the production environment as it is a mock meant to ease the test of your integrations.
        /// </summary>
        /// <param name="structureId"></param>
        /// <param name="bookingId"></param>
        /// <returns></returns>
        Task<bool> AdageMockCollectiveOfferBookingCancellationAsync(int structureId, int bookingId);

        /// <summary>
        /// Mock collective booking confirmation.<br></br>
        /// Like this could happen within the Adage platform. <br></br>
        /// <b>WARNING</b>: this endpoint is not available from the production environment as it is a mock meant to ease the test of your integrations.
        /// </summary>
        /// <param name="structureId"></param>
        /// <param name="bookingId"></param>
        /// <returns></returns>
        Task<bool> AdageMockCollectiveOfferBookingConfirmationAsync(int structureId, int bookingId);

        /// <summary>
        /// Mock collective booking reimbursement.<br></br>
        /// Like this could happen within the Adage platform.<br></br>
        /// <b>WARNING</b>: this endpoint is not available from the production environment as it is a mock meant to ease the test of your integrations.
        /// </summary>
        /// <param name="structureId"></param>
        /// <param name="bookingId"></param>
        /// <returns></returns>
        Task<bool> AdageMockCollectiveOfferBookingReimbursementAsync(int structureId, int bookingId);

        /// <summary>
        /// Mock collective booking use.<br></br>
        /// Mark collective booking as used to mock what would automatically happen 48h after the event.<br></br>
        /// <b>WARNING</b>: this endpoint is not available from the production environment as it is a mock meant to ease the test of your integrations.
        /// </summary>
        /// <param name="structureId"></param>
        /// <param name="bookingId"></param>
        /// <returns></returns>
        Task<bool> AdageMockCollectiveOfferBookingUseAsync(int structureId, int bookingId);

        /// <summary>
        /// Mock reset collective booking back to pending state.<br></br>
        /// Like this could happen within the Adage platform.<br></br>
        /// <b>WARNING</b>: this endpoint is not available from the production environment as it is a mock meant to ease the test of your integrations.
        /// </summary>
        /// <param name="structureId"></param>
        /// <param name="bookingId"></param>
        /// <returns></returns>
        Task<bool> AdageMockCollectiveOfferBookingPendingAsync(int structureId, int bookingId);

        #endregion
    }
}
